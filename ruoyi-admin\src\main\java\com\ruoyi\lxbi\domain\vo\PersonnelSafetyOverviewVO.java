package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 人员安全概览VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonnelSafetyOverviewVO {
    
    /**
     * 下井人次
     */
    private Long downWellCount;
    
    /**
     * 区域超时
     */
    private Long areaTimeoutCount;
    
    /**
     * 教育培训
     */
    private Long educationTrainingCount;
    
    /**
     * 违规行为
     */
    private Long violationCount;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 统计周期
     */
    private String period;
}
