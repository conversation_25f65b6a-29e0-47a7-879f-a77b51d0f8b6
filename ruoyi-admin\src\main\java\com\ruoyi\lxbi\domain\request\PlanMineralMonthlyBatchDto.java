package com.ruoyi.lxbi.domain.request;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 选矿整体月计划批量操作DTO
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Data
public class PlanMineralMonthlyBatchDto {

    /** 主键ID */
    private Long id;

    /** 计划月份 */
    private String planMonth;

    /** 处理矿量（吨） */
    private BigDecimal oreQuantity;

    /** 处理品位（g/t） */
    private BigDecimal oreGrade;

    /** 黄金产量（kg） */
    private BigDecimal goldOutput;

    /** 碎矿处理量（吨） */
    private BigDecimal crushingQuantity;

    /** 磨矿处理量（吨） */
    private BigDecimal grindingQuantity;

    /** 浮选处理量（吨） */
    private BigDecimal flotationQuantity;

    /** 精矿细度(-500目含量) */
    private BigDecimal concentrateFineness;

    /** 铁精粉水分 */
    private BigDecimal ironConcentrateMoisture;

    /** 综合选比 */
    private BigDecimal comprehensiveRatio;

    /** 入磨选比 */
    private BigDecimal grindingRatio;

    /** 尾矿搅拌槽-尾矿粒级(-200目) */
    private BigDecimal tailingsAgitatorTank;

    /** 尾矿脱水筛上量 */
    private BigDecimal overflowOfTailings;

    /** 原矿品位-mFe */
    private BigDecimal rawOreGradeMfe;

    /** 尾矿品位-mFe */
    private BigDecimal tailingGradeMfe;

    /** 备注 */
    private String remark;

    /** 操作类型（新增、更新、删除） */
    private String operationType;

}
