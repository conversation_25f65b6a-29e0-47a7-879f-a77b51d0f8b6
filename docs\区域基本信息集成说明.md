# 区域基本信息集成说明

## 概述

本文档说明了PeoplePos_Location_Info队列的集成实现，该队列包含区域基本信息（RYQY），用于支持人员超时统计时的区域信息查询和展示。

## 数据字段映射

### Kafka队列字段

| 中文字段名 | 数据库字段名 | 类型 | 说明 |
|-----------|-------------|------|------|
| 煤矿编码 | mine_code | VARCHAR(50) | 煤矿的唯一标识 |
| 矿井名称 | mine_name | VARCHAR(100) | 矿井的名称 |
| 数据上传时间 | data_upload_time | TIMESTAMP | 数据上传的时间戳 |
| 区域类型 | area_type | VARCHAR(50) | 区域的分类类型 |
| 区域编码 | area_code | VARCHAR(50) | 区域的唯一标识 |
| 区域核定人数 | area_approved_personnel | BIGINT | 该区域允许的最大人数 |
| 区域名称 | area_name | VARCHAR(100) | 区域的显示名称 |

## 系统架构

### 1. 数据流程

```
Kafka队列 → KafkaListener → Service解析 → 数据库存储 → 业务查询
```

### 2. 核心组件

**数据模型：**
- `KafkaPeopleLocationInfo` - 区域基本信息实体类

**数据访问：**
- `KafkaPeopleLocationInfoMapper` - 数据访问接口
- `KafkaPeopleLocationInfoMapper.xml` - SQL映射文件

**业务逻辑：**
- `IKafkaPeopleLocationInfoService` - 服务接口
- `KafkaPeopleLocationInfoServiceImpl` - 服务实现

**消息处理：**
- `PeopleLocationInfoKafkaListener` - Kafka消息监听器

## 数据库设计

### 表结构

```sql
CREATE TABLE kafka_people_location_info (
    id BIGSERIAL PRIMARY KEY,
    mine_code VARCHAR(50),
    mine_name VARCHAR(100),
    data_upload_time TIMESTAMP,
    area_type VARCHAR(50),
    area_code VARCHAR(50) NOT NULL,
    area_approved_personnel BIGINT,
    area_name VARCHAR(100),
    status BIGINT DEFAULT 1,
    is_deleted BIGINT DEFAULT 0,
    create_by VARCHAR(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500) DEFAULT NULL
);
```

### 索引设计

```sql
-- 基础索引
CREATE INDEX idx_kpli_area_code ON kafka_people_location_info(area_code);
CREATE INDEX idx_kpli_mine_code ON kafka_people_location_info(mine_code);
CREATE INDEX idx_kpli_area_type ON kafka_people_location_info(area_type);

-- 唯一性约束
CREATE UNIQUE INDEX idx_kpli_area_mine_unique 
ON kafka_people_location_info(area_code, mine_code) 
WHERE is_deleted = 0;
```

## 业务集成

### 1. 人员超时统计增强

在人员超时统计中，系统现在能够：

**显示区域名称：**
```java
// 获取区域信息并设置区域名称
String areaDisplayName = record.getAreaCode() != null ? record.getAreaCode() : "未知区域";
if (record.getAreaCode() != null) {
    KafkaPeopleLocationInfo areaInfo = kafkaPeopleLocationInfoService.selectByAreaCode(record.getAreaCode());
    if (areaInfo != null && areaInfo.getAreaName() != null) {
        areaDisplayName = areaInfo.getAreaName() + "(" + record.getAreaCode() + ")";
    }
}
personnel.setCurrentTimeoutArea(areaDisplayName);
```

**区域类型分析：**
- 作业区：中风险区域
- 安全区：低风险区域  
- 危险区：高风险区域
- 休息区：一般风险区域
- 管理区：一般风险区域

### 2. 数据处理特性

**自动去重：**
- 基于区域编码和煤矿编码的唯一性约束
- 使用PostgreSQL的UPSERT操作

**数据验证：**
- 必填字段验证（区域编码、煤矿编码）
- 数据格式验证
- 异常处理和日志记录

**缓存优化：**
- 区域信息查询缓存
- 减少数据库访问频率

## 接口说明

### 核心查询方法

```java
// 根据区域编码查询
KafkaPeopleLocationInfo selectByAreaCode(String areaCode);

// 根据区域编码和煤矿编码查询
KafkaPeopleLocationInfo selectByAreaCodeAndMineCode(String areaCode, String mineCode);

// 查询所有有效区域
List<KafkaPeopleLocationInfo> selectAllValidAreas();

// 根据区域类型查询
List<KafkaPeopleLocationInfo> selectByAreaType(String areaType);

// 统计功能
Long countTotalAreas();
Long countAreasByMineCode(String mineCode);
```

### 消息处理

```java
// 处理Kafka消息
boolean processKafkaMessage(String kafkaMessage);

// 解析Kafka消息
KafkaPeopleLocationInfo parseKafkaMessage(String kafkaMessage);
```

## 测试数据

系统预置了以下测试数据：

### 区域类型分布

| 区域类型 | 数量 | 总核定人数 | 示例区域 |
|---------|------|-----------|----------|
| 作业区 | 3 | 120 | 主井口作业区、副井口作业区 |
| 安全区 | 2 | 120 | 地面安全区、井下避难硐室 |
| 危险区 | 2 | 8 | 瓦斯监测区、高压电气区 |
| 休息区 | 2 | 40 | 井下休息室、地面休息区 |
| 管理区 | 1 | 10 | 调度指挥中心 |

### 示例数据

```sql
-- 作业区示例
('MINE001', '示例煤矿', 'AREA001', '主井口作业区', '作业区', 50)

-- 危险区示例  
('MINE001', '示例煤矿', 'DANGER001', '瓦斯监测区', '危险区', 5)

-- 安全区示例
('MINE001', '示例煤矿', 'SAFE001', '地面安全区', '安全区', 100)
```

## 监控和维护

### 1. 数据质量监控

```sql
-- 检查区域数据完整性
SELECT 
    COUNT(*) as total_areas,
    COUNT(CASE WHEN area_name IS NULL THEN 1 END) as missing_names,
    COUNT(CASE WHEN area_approved_personnel IS NULL THEN 1 END) as missing_personnel
FROM kafka_people_location_info 
WHERE is_deleted = 0;
```

### 2. 性能监控

```sql
-- 查看索引使用情况
SELECT 
    indexname,
    idx_scan,
    idx_tup_read
FROM pg_stat_user_indexes 
WHERE relname = 'kafka_people_location_info';
```

### 3. 业务监控

```sql
-- 区域类型统计
SELECT 
    area_type,
    COUNT(*) as area_count,
    SUM(area_approved_personnel) as total_capacity
FROM kafka_people_location_info 
WHERE is_deleted = 0
GROUP BY area_type;
```

## 故障排除

### 1. 常见问题

**消息解析失败：**
- 检查JSON格式是否正确
- 验证字段名称是否匹配
- 查看日志中的详细错误信息

**数据重复：**
- 检查唯一性约束是否生效
- 验证区域编码和煤矿编码的组合

**查询性能问题：**
- 检查索引是否正确创建
- 分析查询执行计划
- 考虑添加复合索引

### 2. 日志分析

```bash
# 查看Kafka消息处理日志
grep "区域基本信息" application.log

# 查看数据库操作日志
grep "KafkaPeopleLocationInfo" application.log
```

## 扩展功能

### 1. 区域风险评估

基于区域类型和人员数量进行风险评估：

```java
public String assessAreaRisk(KafkaPeopleLocationInfo area) {
    if ("危险区".equals(area.getAreaType())) {
        return "高风险";
    } else if ("作业区".equals(area.getAreaType())) {
        return "中风险";
    } else if ("安全区".equals(area.getAreaType())) {
        return "低风险";
    }
    return "一般风险";
}
```

### 2. 区域容量监控

监控区域人员数量是否超过核定人数：

```java
public boolean isAreaOverCapacity(String areaCode, int currentPersonnel) {
    KafkaPeopleLocationInfo area = selectByAreaCode(areaCode);
    if (area != null && area.getAreaApprovedPersonnel() != null) {
        return currentPersonnel > area.getAreaApprovedPersonnel();
    }
    return false;
}
```

## 总结

区域基本信息的集成为人员安全统计系统提供了重要的基础数据支持，使得：

1. **数据展示更友好**：显示区域名称而不仅仅是编码
2. **风险评估更准确**：基于区域类型进行风险分级
3. **容量管理更有效**：监控区域人员数量限制
4. **统计分析更全面**：支持按区域类型的多维度分析

系统现在能够提供更丰富、更有意义的人员安全统计信息，为安全管理决策提供更好的数据支持。
