package com.ruoyi.lxbi.mapper;

import java.util.List;
import java.util.Date;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.lxbi.domain.DataDrilling;
import com.ruoyi.lxbi.domain.response.DataDrillingVo;

/**
 * 钻孔施工数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface DataDrillingMapper 
{
    /**
     * 查询钻孔施工数据
     * 
     * @param id 钻孔施工数据主键
     * @return 钻孔施工数据
     */
    public DataDrilling selectDataDrillingById(Long id);

    /**
     * 查询钻孔施工数据列表
     *
     * @param dataDrilling 钻孔施工数据
     * @return 钻孔施工数据集合
     */
    public List<DataDrillingVo> selectDataDrillingList(DataDrilling dataDrilling);

    /**
     * 新增钻孔施工数据
     * 
     * @param dataDrilling 钻孔施工数据
     * @return 结果
     */
    public int insertDataDrilling(DataDrilling dataDrilling);

    /**
     * 修改钻孔施工数据
     * 
     * @param dataDrilling 钻孔施工数据
     * @return 结果
     */
    public int updateDataDrilling(DataDrilling dataDrilling);

    /**
     * 删除钻孔施工数据
     * 
     * @param id 钻孔施工数据主键
     * @return 结果
     */
    public int deleteDataDrillingById(Long id);

    /**
     * 批量删除钻孔施工数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataDrillingByIds(Long[] ids);

    /**
     * 根据作业日期查询钻孔施工数据列表
     *
     * @param operationDate 作业日期
     * @return 钻孔施工数据集合
     */
    public List<DataDrillingVo> selectDataDrillingByOperationDate(@Param("operationDate") Date operationDate);

    /**
     * 批量新增钻孔施工数据
     *
     * @param dataDrillingList 钻孔施工数据列表
     * @return 结果
     */
    public int batchInsertDataDrilling(List<DataDrilling> dataDrillingList);
}
