package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaBaseStationRealTimeData;

/**
 * 基站实时数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface IKafkaBaseStationRealTimeDataService 
{
    /**
     * 查询基站实时数据
     * 
     * @param id 基站实时数据主键
     * @return 基站实时数据
     */
    public KafkaBaseStationRealTimeData selectKafkaBaseStationRealTimeDataById(Long id);

    /**
     * 查询基站实时数据列表
     * 
     * @param kafkaBaseStationRealTimeData 基站实时数据
     * @return 基站实时数据集合
     */
    public List<KafkaBaseStationRealTimeData> selectKafkaBaseStationRealTimeDataList(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData);

    /**
     * 新增基站实时数据
     * 
     * @param kafkaBaseStationRealTimeData 基站实时数据
     * @return 结果
     */
    public int insertKafkaBaseStationRealTimeData(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData);

    /**
     * 修改基站实时数据
     * 
     * @param kafkaBaseStationRealTimeData 基站实时数据
     * @return 结果
     */
    public int updateKafkaBaseStationRealTimeData(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData);

    /**
     * 批量删除基站实时数据
     * 
     * @param ids 需要删除的基站实时数据主键集合
     * @return 结果
     */
    public int deleteKafkaBaseStationRealTimeDataByIds(Long[] ids);

    /**
     * 删除基站实时数据信息
     *
     * @param id 基站实时数据主键
     * @return 结果
     */
    public int deleteKafkaBaseStationRealTimeDataById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaBaseStationRealTimeData parseKafkaMessage(String kafkaMessage);
}
