package com.ruoyi.lxbi.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.lxbi.domain.KafkaVehicleSpeedOver;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 车辆超速告警数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Mapper
public interface KafkaVehicleSpeedOverMapper
{
    /**
     * 查询车辆超速告警数据
     *
     * @param id 车辆超速告警数据主键
     * @return 车辆超速告警数据
     */
    public KafkaVehicleSpeedOver selectKafkaVehicleSpeedOverById(Long id);

    /**
     * 查询车辆超速告警数据列表
     *
     * @param kafkaVehicleSpeedOver 车辆超速告警数据
     * @return 车辆超速告警数据集合
     */
    public List<KafkaVehicleSpeedOver> selectKafkaVehicleSpeedOverList(KafkaVehicleSpeedOver kafkaVehicleSpeedOver);

    /**
     * 统计指定时间范围内的车辆告警数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 有告警的车辆数量
     */
    public Long countAlarmVehiclesByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计指定时间范围内的车辆告警部门分布
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 部门告警分布统计结果
     */
    public List<Map<String, Object>> selectAlarmDepartmentDistribution(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计指定时间范围内的车辆告警类型分布
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 告警类型分布统计结果
     */
    public List<Map<String, Object>> selectAlarmTypeDistribution(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询指定时间范围内的车辆告警记录（包含车辆信息）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 告警记录列表
     */
    public List<Map<String, Object>> selectAlarmRecordsWithVehicleInfo(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 新增车辆超速告警数据
     * 
     * @param kafkaVehicleSpeedOver 车辆超速告警数据
     * @return 结果
     */
    public int insertKafkaVehicleSpeedOver(KafkaVehicleSpeedOver kafkaVehicleSpeedOver);

    /**
     * 修改车辆超速告警数据
     * 
     * @param kafkaVehicleSpeedOver 车辆超速告警数据
     * @return 结果
     */
    public int updateKafkaVehicleSpeedOver(KafkaVehicleSpeedOver kafkaVehicleSpeedOver);

    /**
     * 删除车辆超速告警数据
     * 
     * @param id 车辆超速告警数据主键
     * @return 结果
     */
    public int deleteKafkaVehicleSpeedOverById(Long id);

    /**
     * 批量删除车辆超速告警数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaVehicleSpeedOverByIds(Long[] ids);
}
