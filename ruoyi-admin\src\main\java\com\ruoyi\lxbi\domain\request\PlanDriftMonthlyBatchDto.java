package com.ruoyi.lxbi.domain.request;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 掘进月计划批量操作DTO
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Data
public class PlanDriftMonthlyBatchDto {

    /** 主键ID */
    private Long id;

    /** 计划月份 */
    private String planMonth;

    /** 掘进米数（米） */
    private BigDecimal driftDistance;

    /** 项目部ID */
    private Long departmentId;

    /** 岩石方量（立方米） */
    private BigDecimal rockVolume;

    /** 工作面ID */
    private Long workingFaceId;

    /** 采场ID */
    private Long stopeId;

    /** 备注 */
    private String remark;

    /** 操作类型（新增、更新、删除） */
    private String operationType;

}
