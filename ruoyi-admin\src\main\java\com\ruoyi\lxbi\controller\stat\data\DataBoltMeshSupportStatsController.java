package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.response.DataSupportTypeTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportTypeDepartmentWithPlanStats;
import com.ruoyi.lxbi.service.IDataBoltMeshSupportStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 锚网支护数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/data/stats/boltmesh")
public class DataBoltMeshSupportStatsController {
    @Autowired
    private IDataBoltMeshSupportStatsService dataBoltMeshSupportStatsService;

    /**
     * 查询总体锚网支护统计数据（含计划量）
     * 对应图表一：总体锚网支护统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:boltmesh:01a')")
    @GetMapping("/01a")
    public R<List<DataSupportTypeTotalWithPlanStats>> totalBoltMeshSupportWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                                    @RequestParam(value = "startDate", required = false) String startDate,
                                                                                    @RequestParam(value = "endDate", required = false) String endDate) {
        DataSupportStatsRequest request = new DataSupportStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataSupportTypeTotalWithPlanStats> stats = dataBoltMeshSupportStatsService.selectTotalWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询按部门分组的锚网支护统计数据（含计划量）
     * 对应图表二：按部门分组的锚网支护统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:boltmesh:01b')")
    @GetMapping("/01b")
    public R<List<DataSupportTypeDepartmentWithPlanStats>> departmentBoltMeshSupportWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                                              @RequestParam(value = "startDate", required = false) String startDate,
                                                                                              @RequestParam(value = "endDate", required = false) String endDate) {
        DataSupportStatsRequest request = new DataSupportStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataSupportTypeDepartmentWithPlanStats> stats = dataBoltMeshSupportStatsService.selectDepartmentWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }
}
