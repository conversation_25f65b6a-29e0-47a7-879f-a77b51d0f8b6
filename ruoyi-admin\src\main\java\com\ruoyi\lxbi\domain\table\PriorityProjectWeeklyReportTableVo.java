package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.core.table.TableColumnDataBase;
import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 重点工程周报表格VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableConfig(code = "priority_project_weekly_report", name = "重点工程周报", description = "重点工程数据周报统计表格")
public class PriorityProjectWeeklyReportTableVo extends TableColumnDataBase {

    @TableHeader(label = "序号", order = 1, width = 5)
    private String serialNumber;

    @TableHeader(label = "项目", order = 2, enableRowMerge = true)
    private String name;

    @TableHeader(label = "单位", order = 3, width = 5)
    private String unit;

    @TableHeader(label = "周计划", order = 4)
    private BigDecimal weeklyPlan;

    @TableHeader(label = "周完成", order = 5)
    private BigDecimal weeklyCompleted;

    @TableHeader(label = "周完成率", order = 6)
    private String weeklyCompletionRate;

    @TableHeader(label = "周超欠", order = 7)
    private BigDecimal weeklyOverUnder;
}
