package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanDthMonthly;
import com.ruoyi.lxbi.domain.response.PlanDthMonthlyVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 潜孔月计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper
public interface PlanDthMonthlyMapper 
{
    /**
     * 查询潜孔月计划
     * 
     * @param id 潜孔月计划主键
     * @return 潜孔月计划
     */
    public PlanDthMonthlyVo selectPlanDthMonthlyById(Long id);

    /**
     * 查询潜孔月计划列表
     * 
     * @param planDthMonthly 潜孔月计划
     * @return 潜孔月计划集合
     */
    public List<PlanDthMonthlyVo> selectPlanDthMonthlyList(PlanDthMonthly planDthMonthly);

    /**
     * 新增潜孔月计划
     * 
     * @param planDthMonthly 潜孔月计划
     * @return 结果
     */
    public int insertPlanDthMonthly(PlanDthMonthly planDthMonthly);

    /**
     * 修改潜孔月计划
     * 
     * @param planDthMonthly 潜孔月计划
     * @return 结果
     */
    public int updatePlanDthMonthly(PlanDthMonthly planDthMonthly);

    /**
     * 删除潜孔月计划
     * 
     * @param id 潜孔月计划主键
     * @return 结果
     */
    public int deletePlanDthMonthlyById(Long id);

    /**
     * 批量删除潜孔月计划
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanDthMonthlyByIds(Long[] ids);
}
