package com.ruoyi.lxbi.admin.service;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.admin.domain.SurfaceMonitoringData;

/**
 * 地表监测数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
public interface ISurfaceMonitoringDataService {
    
    /**
     * 查询地表监测数据
     * 
     * @param id 地表监测数据主键
     * @return 地表监测数据
     */
    public SurfaceMonitoringData selectSurfaceMonitoringDataById(Long id);

    /**
     * 查询地表监测数据列表
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 地表监测数据集合
     */
    public List<SurfaceMonitoringData> selectSurfaceMonitoringDataList(SurfaceMonitoringData surfaceMonitoringData);

    /**
     * 新增地表监测数据
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 结果
     */
    public int insertSurfaceMonitoringData(SurfaceMonitoringData surfaceMonitoringData);

    /**
     * 修改地表监测数据
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 结果
     */
    public int updateSurfaceMonitoringData(SurfaceMonitoringData surfaceMonitoringData);

    /**
     * 批量删除地表监测数据
     * 
     * @param ids 需要删除的地表监测数据主键集合
     * @return 结果
     */
    public int deleteSurfaceMonitoringDataByIds(Long[] ids);

    /**
     * 删除地表监测数据信息
     * 
     * @param id 地表监测数据主键
     * @return 结果
     */
    public int deleteSurfaceMonitoringDataById(Long id);

    /**
     * 根据日期、基站名称和终端编号查询地表监测数据
     * 
     * @param date 日期
     * @param stationName 基站名称
     * @param wgbh 终端编号
     * @return 地表监测数据
     */
    public SurfaceMonitoringData selectByDateStationWgbh(Date date, String stationName, String wgbh);

    /**
     * 根据原始数据ID查询地表监测数据
     * 
     * @param originalId 原始数据ID
     * @return 地表监测数据
     */
    public SurfaceMonitoringData selectByOriginalId(Integer originalId);

    /**
     * 同步第三方地表监测数据
     * 
     * @return 同步结果
     */
    public java.util.Map<String, Object> syncSurfaceMonitoringData();

    /**
     * 处理第三方API地表监测数据（插入或更新）
     * 
     * @param apiData 第三方API数据
     * @return 处理结果
     */
    public boolean processApiData(java.util.Map<String, Object> apiData);

    /**
     * 批量处理第三方API地表监测数据
     * 
     * @param apiDataList 第三方API数据列表
     * @return 处理结果统计
     */
    public java.util.Map<String, Object> batchProcessApiData(java.util.List<java.util.Map<String, Object>> apiDataList);

    /**
     * UPSERT操作（根据日期、基站名称、终端编号插入或更新）
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 结果
     */
    public int upsertSurfaceMonitoringData(SurfaceMonitoringData surfaceMonitoringData);

    /**
     * 解析第三方API数据为实体对象
     * 
     * @param apiData 第三方API数据
     * @return 解析后的实体对象
     */
    public SurfaceMonitoringData parseApiData(java.util.Map<String, Object> apiData);

    /**
     * 获取地表监测统计数据
     * 
     * @return 统计结果
     */
    public java.util.Map<String, Object> getStatistics();

    /**
     * 按日期范围查询地表监测数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 地表监测数据集合
     */
    public List<SurfaceMonitoringData> selectByDateRange(Date startDate, Date endDate);

    /**
     * 按基站名称查询地表监测数据
     * 
     * @param stationName 基站名称
     * @return 地表监测数据集合
     */
    public List<SurfaceMonitoringData> selectByStationName(String stationName);

    /**
     * 获取最新的监测数据
     * 
     * @param limit 限制数量
     * @return 最新监测数据
     */
    public List<SurfaceMonitoringData> selectLatestData(Integer limit);

    /**
     * 获取偏移异常数据
     * 
     * @param threshold 偏移阈值
     * @return 异常数据列表
     */
    public List<SurfaceMonitoringData> selectAbnormalOffsetData(Double threshold);

    /**
     * 按日期获取统计信息
     * 
     * @param date 日期
     * @return 统计信息
     */
    public java.util.Map<String, Object> getStatisticsByDate(Date date);
}
