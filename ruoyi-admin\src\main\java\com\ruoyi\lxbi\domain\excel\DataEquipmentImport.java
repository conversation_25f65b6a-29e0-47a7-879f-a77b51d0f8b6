package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import com.ruoyi.common.utils.excel.BaseDateConverter;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;

/**
 * 设备数据Excel导入对象
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@ExcelImportTemplate(
        key = "data_equipment",
        name = "设备数据导入",
        description = "用于导入设备数据",
        sheetName = "设备数据"
)
public class DataEquipmentImport {

    /**
     * 运行日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "运行日期", index = 0)
    @ExcelRequired(message = "运行日期不能为空")
    @ExcelSelected(prompt = "请输入运行日期，格式：yyyy-MM-dd，例如：2025-01-15")
    private Date operationDate;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号", index = 1)
    @ExcelRequired(message = "设备编号不能为空")
    @ExcelSelected(prompt = "请输入设备编号")
    private String equipmentNo;

    /**
     * 编组数量
     */
    @ExcelProperty(value = "编组数量", index = 2)
    @ExcelSelected(prompt = "请输入编组数量")
    private Long mineCarsNumber;

    /**
     * 运行列次
     */
    @ExcelProperty(value = "运行列次", index = 3)
    @ExcelSelected(prompt = "请输入运行列次")
    private BigDecimal numberOfRunningTrains;

    /**
     * 运行时长(分钟)
     */
    @ExcelProperty(value = "运行时长(分钟)", index = 4)
    @ExcelSelected(prompt = "请输入运行时长，单位：分钟")
    private Integer operationTime;

    /**
     * 单次处理量
     */
    @ExcelProperty(value = "单次处理量", index = 5)
    @ExcelSelected(prompt = "请输入单次处理量")
    private BigDecimal singleProcessingVolume;

    /**
     * 总处理量
     */
    @ExcelProperty(value = "总处理量", index = 6)
    @ExcelSelected(prompt = "请输入总处理量")
    private BigDecimal totalProcessingVolume;

    /**
     * 运行开始时间
     */
    @ExcelProperty(value = "运行开始时间", index = 7, converter = BaseDateConverter.LocalTimeConverter.class)
    @ExcelSelected(prompt = "请输入运行开始时间，格式：HH:mm:ss")
    private LocalTime startTime;

    /**
     * 运行结束时间
     */
    @ExcelProperty(value = "运行结束时间", index = 8, converter = BaseDateConverter.LocalTimeConverter.class)
    @ExcelSelected(prompt = "请输入运行结束时间，格式：HH:mm:ss")
    private LocalTime endTime;

    /**
     * 设备类型ID（存储值）
     */
    @ExcelSelected(
            optionKey = "baseEquipment",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long equipmentType;

    /**
     * 设备类型名称（显示值）
     */
    @ExcelProperty(value = "设备类型", index = 9)
    @ExcelSelected(
            optionKey = "baseEquipment",
            prompt = "请选择设备类型",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String equipmentTypeName;

}
