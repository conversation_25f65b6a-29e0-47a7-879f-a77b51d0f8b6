package com.ruoyi.lxbi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 重点工程对象 base_priority_project
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BasePriorityProject extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 采场ID */
    private Long id;

    /** 采场ID */
    private Long stopeId;

    /** 工作面ID */
    private Long workingFaceId;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 采场开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "采场开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 采场结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "采场结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 是否删除 */
    private Long isDelete;

}
