<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataIronConcentrateMapper">
    
    <resultMap type="DataIronConcentrate" id="DataIronConcentrateResult">
        <result property="id"    column="id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="tfeContent"    column="tfe_content"    />
        <result property="finenessMinus500"    column="fineness_minus_500"    />
        <result property="productionVolume"    column="production_volume"    />
        <result property="moistureContent"    column="moisture_content"    />
        <result property="remarks"    column="remarks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataIronConcentrateVo">
        select id, operation_date, tfe_content, fineness_minus_500, production_volume, moisture_content, remarks, create_by, create_time, update_by, update_time from data_iron_concentrate
    </sql>

    <select id="selectDataIronConcentrateList" parameterType="DataIronConcentrate" resultMap="DataIronConcentrateResult">
        <include refid="selectDataIronConcentrateVo"/>
        <where>
            <if test="operationDate != null "> and operation_date = #{operationDate}</if>
            <if test="params.startDate != null"> and operation_date &gt;= #{params.startDate}</if>
            <if test="params.endDate != null"> and operation_date &lt;= #{params.endDate}</if>
        </where>
        order by operation_date desc
    </select>
    
    <select id="selectDataIronConcentrateById" parameterType="Long" resultMap="DataIronConcentrateResult">
        <include refid="selectDataIronConcentrateVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataIronConcentrate" parameterType="DataIronConcentrate" useGeneratedKeys="true" keyProperty="id">
        insert into data_iron_concentrate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">operation_date,</if>
            <if test="tfeContent != null">tfe_content,</if>
            <if test="finenessMinus500 != null">fineness_minus_500,</if>
            <if test="productionVolume != null">production_volume,</if>
            <if test="moistureContent != null">moisture_content,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">#{operationDate},</if>
            <if test="tfeContent != null">#{tfeContent},</if>
            <if test="finenessMinus500 != null">#{finenessMinus500},</if>
            <if test="productionVolume != null">#{productionVolume},</if>
            <if test="moistureContent != null">#{moistureContent},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataIronConcentrate" parameterType="DataIronConcentrate">
        update data_iron_concentrate
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="tfeContent != null">tfe_content = #{tfeContent},</if>
            <if test="finenessMinus500 != null">fineness_minus_500 = #{finenessMinus500},</if>
            <if test="productionVolume != null">production_volume = #{productionVolume},</if>
            <if test="moistureContent != null">moisture_content = #{moistureContent},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataIronConcentrateById" parameterType="Long">
        delete from data_iron_concentrate where id = #{id}
    </delete>

    <delete id="deleteDataIronConcentrateByIds" parameterType="String">
        delete from data_iron_concentrate where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>