package com.ruoyi.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Excel导入模板配置注解
 * 用于标识和配置导入模板
 * 
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ExcelImportTemplate {
    
    /**
     * 模板唯一标识key
     */
    String key();
    
    /**
     * 模板名称
     */
    String name();
    
    /**
     * 模板描述
     */
    String description() default "";
    
    /**
     * 工作表名称
     */
    String sheetName() default "Sheet1";
    
    /**
     * 标题行数（从第几行开始是数据）
     */
    int titleRows() default 1;
    
    /**
     * 最大导入行数
     */
    int maxRows() default 1048575;
    
    /**
     * 是否启用
     */
    boolean enabled() default true;
}
