package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.BasePriorityProject;
import com.ruoyi.lxbi.domain.request.DataTunnelingStatsRequest;
import com.ruoyi.lxbi.domain.response.BasePriorityProjectVo;
import com.ruoyi.lxbi.domain.response.DataTunnelingStopeStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.table.PriorityProjectWeeklyReportTableVo;
import com.ruoyi.lxbi.service.IBasePriorityProjectService;
import com.ruoyi.lxbi.service.IDataTunnelingStatsService;
import com.ruoyi.lxbi.table.params.PriorityProjectWeeklyReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 重点工程周报表格处理器
 */
@Component
public class PriorityProjectWeeklyReportTableHandler extends BaseTableHandler<PriorityProjectWeeklyReportTableVo, PriorityProjectWeeklyReportQueryParams> {

    @Autowired
    private IDataTunnelingStatsService dataTunnelingStatsService;

    @Autowired
    private IBasePriorityProjectService basePriorityProjectService;

    @Override
    public List<PriorityProjectWeeklyReportTableVo> queryTableData(PriorityProjectWeeklyReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date queryDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取财务周范围（周四到周三）
        Date[] weekRange = FinancialDateUtils.getFinancialWeekRange(queryDate);
        Date weekStart = weekRange[0];
        Date weekEnd = weekRange[1];

        // 获取重点工程数据
        PriorityProjectWeeklyResult priorityProjectResult = getPriorityProjectWeeklyData(weekStart, weekEnd, queryDate);

        // 构建表格数据
        return buildTableData(priorityProjectResult);
    }

    /**
     * 获取重点工程周数据
     */
    private PriorityProjectWeeklyResult getPriorityProjectWeeklyData(Date weekStart, Date weekEnd, Date queryDate) {
        DataTunnelingStatsRequest request = new DataTunnelingStatsRequest();
        request.setStartDate(weekStart);
        request.setEndDate(weekEnd);

        // 获取重点工程总体统计数据
        List<DataTunnelingTotalWithPlanStats> priorityTotalStats = dataTunnelingStatsService.selectPriorityProjectTotalWithPlanStatsList(request, "daily");

        // 计算重点工程总体周完成
        BigDecimal priorityTotalWeeklyCompleted = priorityTotalStats.stream()
                .filter(stats -> stats.getTotalTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算重点工程总体周计划（月计划的1/4）
        BigDecimal priorityTotalWeeklyPlan = getWeeklyPlanFromMonthly(queryDate);

        // 获取重点工程列表
        List<BasePriorityProjectVo> priorityProjects = basePriorityProjectService.selectBasePriorityProjectListAll(new BasePriorityProject());

        // 获取重点工程按采场的统计数据
        List<DataTunnelingStopeStats> priorityStopeStats = dataTunnelingStatsService.selectPriorityProjectStopeStatsList(request, "daily");

        return new PriorityProjectWeeklyResult(priorityTotalWeeklyCompleted, priorityTotalWeeklyPlan,
                priorityProjects, priorityStopeStats);
    }

    /**
     * 从月计划获取周计划（月计划/4）
     */
    private BigDecimal getWeeklyPlanFromMonthly(Date queryDate) {
        // 获取财务月的开始日期
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(queryDate);

        DataTunnelingStatsRequest request = new DataTunnelingStatsRequest();
        request.setStartDate(financialMonthStart);
        request.setEndDate(queryDate);

        // 获取重点工程总体统计数据中的计划
        List<DataTunnelingTotalWithPlanStats> priorityTotalStats = dataTunnelingStatsService.selectPriorityProjectTotalWithPlanStatsList(request, "daily");

        BigDecimal monthlyPlan = priorityTotalStats.stream()
                .filter(stats -> stats.getPlanTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getPlanTunnelingLength()))
                .findFirst()
                .orElse(BigDecimal.ZERO);

        // 周计划 = 月计划 / 4
        return monthlyPlan.divide(new BigDecimal("4"), 2, RoundingMode.HALF_UP);
    }

    /**
     * 构建表格数据
     */
    private List<PriorityProjectWeeklyReportTableVo> buildTableData(PriorityProjectWeeklyResult priorityProjectResult) {
        List<PriorityProjectWeeklyReportTableVo> result = new ArrayList<>();

        // 添加重点工程总计行
        PriorityProjectWeeklyReportTableVo summary = new PriorityProjectWeeklyReportTableVo();
        summary.setBold(List.of("serialNumber", "name", "weeklyPlan", "weeklyCompleted", "weeklyCompletionRate", "weeklyOverUnder"));
        summary.setSerialNumber("总计");
        summary.setName("重点工程");
        summary.setUnit("m");
        summary.setWeeklyPlan(priorityProjectResult.getTotalWeeklyPlan());
        summary.setWeeklyCompleted(priorityProjectResult.getTotalWeeklyCompleted());

        // 计算总完成率
        if (priorityProjectResult.getTotalWeeklyPlan().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal rate = priorityProjectResult.getTotalWeeklyCompleted()
                    .divide(priorityProjectResult.getTotalWeeklyPlan(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            summary.setWeeklyCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
            summary.setWeeklyOverUnder(priorityProjectResult.getTotalWeeklyCompleted()
                    .subtract(priorityProjectResult.getTotalWeeklyPlan()));
        } else {
            summary.setWeeklyCompletionRate("　");
            summary.setWeeklyOverUnder(null);
        }

        result.add(summary);

        // 添加重点工程明细项目
        int subSerialNumber = 1;
        for (BasePriorityProjectVo project : priorityProjectResult.getPriorityProjects()) {
            PriorityProjectWeeklyReportTableVo vo = new PriorityProjectWeeklyReportTableVo();
            vo.setSerialNumber(String.valueOf(subSerialNumber++));
            vo.setName(getProjectDisplayName(project));
            vo.setUnit("m");

            // 从真实数据中获取计划、产量等数据（基于采场）
            vo.setWeeklyPlan(getProjectWeeklyPlan(project, priorityProjectResult));
            vo.setWeeklyCompleted(getProjectWeeklyCompleted(project, priorityProjectResult));

            // 计算完成率
            if (vo.getWeeklyPlan() != null && vo.getWeeklyPlan().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = vo.getWeeklyCompleted().divide(vo.getWeeklyPlan(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                vo.setWeeklyCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
                vo.setWeeklyOverUnder(vo.getWeeklyCompleted().subtract(vo.getWeeklyPlan()));
            } else {
                vo.setWeeklyCompletionRate("　");
                vo.setWeeklyOverUnder(null);
            }

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取重点工程项目显示名称
     */
    private String getProjectDisplayName(BasePriorityProjectVo project) {
        String projectName = "";
        if (project.getStopeName() != null && !project.getStopeName().trim().isEmpty()) {
            projectName = project.getStopeName();
        }
        if (project.getWorkingFaceName() != null && !project.getWorkingFaceName().trim().isEmpty()) {
            projectName = project.getWorkingFaceName() + "-" + projectName ;
        }
        return projectName;
    }

    /**
     * 获取重点工程项目周计划（基于采场）
     */
    private BigDecimal getProjectWeeklyPlan(BasePriorityProjectVo project, PriorityProjectWeeklyResult result) {
        // 根据重点工程的采场ID获取计划
        if (project.getStopeId() != null) {
            // TODO: 从计划数据中获取该采场的周计划数据
            // 这里需要实现基于采场的计划查询
            return BigDecimal.ZERO;
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取重点工程项目周完成（基于采场）
     */
    private BigDecimal getProjectWeeklyCompleted(BasePriorityProjectVo project, PriorityProjectWeeklyResult result) {
        // 根据重点工程的采场ID获取周完成
        if (project.getStopeId() != null) {
            return result.getPriorityStopeStats().stream()
                    .filter(stats -> project.getStopeId().equals(stats.getStopeId()))
                    .filter(stats -> stats.getTotalTunnelingLength() != null)
                    .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                    .reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 重点工程周结果包装类
     */
    private static class PriorityProjectWeeklyResult {
        private final BigDecimal totalWeeklyCompleted;
        private final BigDecimal totalWeeklyPlan;
        private final List<BasePriorityProjectVo> priorityProjects;
        private final List<DataTunnelingStopeStats> priorityStopeStats;

        public PriorityProjectWeeklyResult(BigDecimal totalWeeklyCompleted,
                                           BigDecimal totalWeeklyPlan,
                                           List<BasePriorityProjectVo> priorityProjects,
                                           List<DataTunnelingStopeStats> priorityStopeStats) {
            this.totalWeeklyCompleted = totalWeeklyCompleted;
            this.totalWeeklyPlan = totalWeeklyPlan;
            this.priorityProjects = priorityProjects;
            this.priorityStopeStats = priorityStopeStats;
        }

        public BigDecimal getTotalWeeklyCompleted() {
            return totalWeeklyCompleted;
        }

        public BigDecimal getTotalWeeklyPlan() {
            return totalWeeklyPlan;
        }

        public List<BasePriorityProjectVo> getPriorityProjects() {
            return priorityProjects;
        }

        public List<DataTunnelingStopeStats> getPriorityStopeStats() {
            return priorityStopeStats;
        }
    }
}
