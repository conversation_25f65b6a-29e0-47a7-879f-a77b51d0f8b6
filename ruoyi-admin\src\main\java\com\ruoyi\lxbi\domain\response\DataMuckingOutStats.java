package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 出矿数据详细统计对象 - 统一日/周/月/年统计（按作业时段、项目部门、采场）
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class DataMuckingOutStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 周开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekStartDate;

    /**
     * 周结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekEndDate;

    /**
     * 班次ID
     */
    @Excel(name = "班次ID")
    private Long workingPeriodId;

    /**
     * 项目部门ID
     */
    @Excel(name = "项目部门ID")
    private Long projectDepartmentId;

    /**
     * 采场ID
     */
    @Excel(name = "采场ID")
    private Long stopeId;

    /**
     * 总出矿量
     */
    @Excel(name = "总出矿量")
    private Double totalTons;
}
