package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaSubstationBasicInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 分站基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface KafkaSubstationBasicInfoMapper
{
    /**
     * 查询分站基本信息
     * 
     * @param id 分站基本信息主键
     * @return 分站基本信息
     */
    public KafkaSubstationBasicInfo selectKafkaSubstationBasicInfoById(Long id);

    /**
     * 查询分站基本信息列表
     * 
     * @param kafkaSubstationBasicInfo 分站基本信息
     * @return 分站基本信息集合
     */
    public List<KafkaSubstationBasicInfo> selectKafkaSubstationBasicInfoList(KafkaSubstationBasicInfo kafkaSubstationBasicInfo);

    /**
     * 新增分站基本信息
     * 
     * @param kafkaSubstationBasicInfo 分站基本信息
     * @return 结果
     */
    public int insertKafkaSubstationBasicInfo(KafkaSubstationBasicInfo kafkaSubstationBasicInfo);

    /**
     * 修改分站基本信息
     * 
     * @param kafkaSubstationBasicInfo 分站基本信息
     * @return 结果
     */
    public int updateKafkaSubstationBasicInfo(KafkaSubstationBasicInfo kafkaSubstationBasicInfo);

    /**
     * 删除分站基本信息
     * 
     * @param id 分站基本信息主键
     * @return 结果
     */
    public int deleteKafkaSubstationBasicInfoById(Long id);

    /**
     * 批量删除分站基本信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaSubstationBasicInfoByIds(Long[] ids);
}
