package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.BasePriorityProject;
import com.ruoyi.lxbi.domain.response.BasePriorityProjectVo;

/**
 * 重点工程Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface BasePriorityProjectMapper 
{
    /**
     * 查询重点工程
     * 
     * @param id 重点工程主键
     * @return 重点工程
     */
    public BasePriorityProject selectBasePriorityProjectById(Long id);

    /**
     * 查询重点工程列表
     *
     * @param basePriorityProject 重点工程
     * @return 重点工程集合
     */
    public List<BasePriorityProjectVo> selectBasePriorityProjectList(BasePriorityProject basePriorityProject);

    /**
     * 查询所有重点工程列表（不分页）
     *
     * @param basePriorityProject 重点工程
     * @return 重点工程集合
     */
    public List<BasePriorityProjectVo> selectBasePriorityProjectListAll(BasePriorityProject basePriorityProject);

    /**
     * 新增重点工程
     * 
     * @param basePriorityProject 重点工程
     * @return 结果
     */
    public int insertBasePriorityProject(BasePriorityProject basePriorityProject);

    /**
     * 修改重点工程
     * 
     * @param basePriorityProject 重点工程
     * @return 结果
     */
    public int updateBasePriorityProject(BasePriorityProject basePriorityProject);

    /**
     * 删除重点工程
     *
     * @param id 重点工程主键
     * @return 结果
     */
    public int deleteBasePriorityProjectById(Long id);

    /**
     * 批量逻辑删除重点工程
     *
     * @param ids 需要删除的重点工程主键集合
     * @return 结果
     */
    public int logicDeleteBasePriorityProjectByIds(Long[] ids);

    /**
     * 批量删除重点工程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasePriorityProjectByIds(Long[] ids);
}
