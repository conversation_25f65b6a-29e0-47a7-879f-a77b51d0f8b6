package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanDriftMonthly;
import com.ruoyi.lxbi.domain.response.PlanDriftMonthlyVo;

/**
 * 掘进月计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface PlanDriftMonthlyMapper 
{
    /**
     * 查询掘进月计划
     * 
     * @param id 掘进月计划主键
     * @return 掘进月计划
     */
    public PlanDriftMonthly selectPlanDriftMonthlyById(Long id);

    /**
     * 查询掘进月计划列表
     * 
     * @param planDriftMonthly 掘进月计划
     * @return 掘进月计划集合
     */
    public List<PlanDriftMonthlyVo> selectPlanDriftMonthlyList(PlanDriftMonthly planDriftMonthly);

    /**
     * 新增掘进月计划
     * 
     * @param planDriftMonthly 掘进月计划
     * @return 结果
     */
    public int insertPlanDriftMonthly(PlanDriftMonthly planDriftMonthly);

    /**
     * 修改掘进月计划
     * 
     * @param planDriftMonthly 掘进月计划
     * @return 结果
     */
    public int updatePlanDriftMonthly(PlanDriftMonthly planDriftMonthly);

    /**
     * 删除掘进月计划
     * 
     * @param id 掘进月计划主键
     * @return 结果
     */
    public int deletePlanDriftMonthlyById(Long id);

    /**
     * 批量删除掘进月计划
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanDriftMonthlyByIds(Long[] ids);
}
