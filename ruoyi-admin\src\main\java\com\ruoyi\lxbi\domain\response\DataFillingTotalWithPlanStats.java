package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 充填数据总体统计对象（含计划量） - 统一日/周/月/年统计
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class DataFillingTotalWithPlanStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 周开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekStartDate;

    /**
     * 周结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekEndDate;

    /**
     * 总浆液体积（立方米）
     */
    @Excel(name = "总浆液体积")
    private Double totalSlurryVolume;

    /**
     * 总水泥重量（吨）
     */
    @Excel(name = "总水泥重量")
    private Double totalCementWeight;

    /**
     * 平均充填浓度（%）
     */
    @Excel(name = "平均充填浓度")
    private Double avgFillingConcentration;

    /**
     * 计划浆液体积（立方米）
     */
    @Excel(name = "计划浆液体积")
    private Double planSlurryVolume;
}
