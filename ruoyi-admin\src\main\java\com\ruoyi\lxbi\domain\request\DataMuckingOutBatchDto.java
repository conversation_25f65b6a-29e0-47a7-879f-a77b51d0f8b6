package com.ruoyi.lxbi.domain.request;

import com.ruoyi.lxbi.domain.DataMuckingOut;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 出矿数据批量操作DTO
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataMuckingOutBatchDto extends DataMuckingOut {
    
    /** 是否为新增数据 */
    private Boolean isNew;
    
    /** 作业时段名称 */
    private String workingPeriodName;
    
    /** 项目部门名称 */
    private String projectDepartmentName;
    
    /** 采场名称 */
    private String stopeName;
}
