package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 掘进数据工作面统计对象 - 统一日/周/月/年工作面统计
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
public class DataTunnelingWorkingFaceStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 周开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekStartDate;

    /**
     * 周结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekEndDate;

    /**
     * 工作面ID
     */
    @Excel(name = "工作面ID")
    private Long workingFaceId;

    /**
     * 工作面名称
     */
    @Excel(name = "工作面名称")
    private String workingFaceName;

    /**
     * 总掘进长度
     */
    @Excel(name = "总掘进长度")
    private Double totalTunnelingLength;

    /**
     * 总掘进体积
     */
    @Excel(name = "总掘进体积")
    private Double totalTunnelingVolume;
}
