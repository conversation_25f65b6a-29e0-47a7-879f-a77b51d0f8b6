package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataMineHoisting;
import com.ruoyi.lxbi.domain.response.DataMineHoistingVo;
import com.ruoyi.lxbi.domain.request.DataMineHoistingBatchDto;
import com.ruoyi.lxbi.service.IDataMineHoistingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 矿井提升数据Controller
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/data/hoisting")
public class DataMineHoistingController extends BaseController {
    @Autowired
    private IDataMineHoistingService dataMineHoistingService;

    /**
     * 查询矿井提升数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:hoisting:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataMineHoisting dataMineHoisting) {
        startPage();
        List<DataMineHoistingVo> list = dataMineHoistingService.selectDataMineHoistingList(dataMineHoisting);
        return getDataTable(list);
    }

    /**
     * 导出矿井提升数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:hoisting:export')")
    @Log(title = "矿井提升数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataMineHoisting dataMineHoisting) {
        List<DataMineHoistingVo> list = dataMineHoistingService.selectDataMineHoistingList(dataMineHoisting);
        ExcelUtil<DataMineHoistingVo> util = new ExcelUtil<DataMineHoistingVo>(DataMineHoistingVo.class);
        util.exportExcel(response, list, "矿井提升数据数据");
    }

    /**
     * 获取矿井提升数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:hoisting:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataMineHoistingService.selectDataMineHoistingById(id));
    }

    /**
     * 新增矿井提升数据
     */
    @PreAuthorize("@ss.hasPermi('data:hoisting:add')")
    @Log(title = "矿井提升数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataMineHoisting dataMineHoisting)
    {
        return toAjax(dataMineHoistingService.insertDataMineHoisting(dataMineHoisting));
    }

    /**
     * 修改矿井提升数据
     */
    @PreAuthorize("@ss.hasPermi('data:hoisting:edit')")
    @Log(title = "矿井提升数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataMineHoisting dataMineHoisting)
    {
        return toAjax(dataMineHoistingService.updateDataMineHoisting(dataMineHoisting));
    }

    /**
     * 删除矿井提升数据
     */
    @PreAuthorize("@ss.hasPermi('data:hoisting:remove')")
    @Log(title = "矿井提升数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataMineHoistingService.deleteDataMineHoistingByIds(ids));
    }

    /**
     * 批量保存矿井提升数据（增删改查）
     * 传入批量列表，验证是否同一个日期的数据，然后查询这个日期的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('data:hoisting:edit')")
    @Log(title = "矿井提升数据批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<DataMineHoistingBatchDto> batchDataList)
    {
        try {
            int result = dataMineHoistingService.batchSaveDataMineHoisting(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
