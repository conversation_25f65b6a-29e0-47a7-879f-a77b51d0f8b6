package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataTunnelingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataTunnelingDepartmentWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingStopeStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingWorkingFaceStats;
import com.ruoyi.lxbi.mapper.DataTunnelingStatsMapper;
import com.ruoyi.lxbi.service.IDataTunnelingStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 掘进数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class DataTunnelingStatsServiceImpl implements IDataTunnelingStatsService {
    @Autowired
    private DataTunnelingStatsMapper dataTunnelingStatsMapper;

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    @Override
    public List<DataTunnelingTotalWithPlanStats> selectTotalWithPlanStatsList(DataTunnelingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataTunnelingStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认月统计
            return dataTunnelingStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合（含计划量）
     */
    @Override
    public List<DataTunnelingDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataTunnelingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataTunnelingStatsMapper.selectDailyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectWeeklyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectYearlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认月统计
            return dataTunnelingStatsMapper.selectMonthlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询重点工程总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 重点工程总体统计数据集合（含计划量）
     */
    @Override
    public List<DataTunnelingTotalWithPlanStats> selectPriorityProjectTotalWithPlanStatsList(DataTunnelingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataTunnelingStatsMapper.selectDailyPriorityProjectTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectWeeklyPriorityProjectTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectYearlyPriorityProjectTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认月统计
            return dataTunnelingStatsMapper.selectMonthlyPriorityProjectTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询重点工程项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 重点工程项目部门统计数据集合（含计划量）
     */
    @Override
    public List<DataTunnelingDepartmentWithPlanStats> selectPriorityProjectDepartmentWithPlanStatsList(DataTunnelingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataTunnelingStatsMapper.selectDailyPriorityProjectDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectWeeklyPriorityProjectDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectYearlyPriorityProjectDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认月统计
            return dataTunnelingStatsMapper.selectMonthlyPriorityProjectDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询重点工程掘进数据采场统计列表
     */
    @Override
    public List<DataTunnelingStopeStats> selectPriorityProjectStopeStatsList(DataTunnelingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataTunnelingStatsMapper.selectDailyPriorityProjectStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectWeeklyPriorityProjectStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectYearlyPriorityProjectStopeStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认月统计
            return dataTunnelingStatsMapper.selectMonthlyPriorityProjectStopeStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询重点工程掘进数据工作面统计列表
     */
    @Override
    public List<DataTunnelingWorkingFaceStats> selectPriorityProjectWorkingFaceStatsList(DataTunnelingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataTunnelingStatsMapper.selectDailyPriorityProjectWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectWeeklyPriorityProjectWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataTunnelingStatsMapper.selectYearlyPriorityProjectWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认月统计
            return dataTunnelingStatsMapper.selectMonthlyPriorityProjectWorkingFaceStats(request.getStartDate(), request.getEndDate());
        }
    }
}
