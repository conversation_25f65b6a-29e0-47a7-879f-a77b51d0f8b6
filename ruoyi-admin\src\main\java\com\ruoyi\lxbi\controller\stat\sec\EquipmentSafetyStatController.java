package com.ruoyi.lxbi.controller.stat.sec;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.admin.service.IEquipmentSafetyStatService;
import com.ruoyi.lxbi.domain.vo.*;
import com.ruoyi.lxbi.utils.DateRangeCalculator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备安全统计Controller
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@RestController
@RequestMapping("/sec/stat/equipment-safety")
public class EquipmentSafetyStatController {

    @Autowired
    private IEquipmentSafetyStatService equipmentSafetyStatService;

    /**
     * 获取设备安全概览统计
     */
    @Anonymous
    @GetMapping("/overview")
    public R<EquipmentSafetyOverviewVO> getOverviewStatistics(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        EquipmentSafetyOverviewVO overview = equipmentSafetyStatService.getOverviewStatistics(viewType, startDate, endDate);
        return R.ok(overview);
    }

    /**
     * 获取设备状态分布统计
     */
    @Anonymous
    @GetMapping("/status-distribution")
    public R<List<EquipmentStatusDistributionVO>> getStatusDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<EquipmentStatusDistributionVO> distribution = equipmentSafetyStatService.getStatusDistribution(viewType, startDate, endDate);
        return R.ok(distribution);
    }

    /**
     * 获取设备报警趋势统计
     */
    @Anonymous
    @GetMapping("/alarm-trend")
    public R<List<EquipmentAlarmTrendVO>> getAlarmTrend(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<EquipmentAlarmTrendVO> trend = equipmentSafetyStatService.getAlarmTrend(viewType, startDate, endDate);
        return R.ok(trend);
    }

    /**
     * 获取设备故障记录列表
     */
    @Anonymous
    @GetMapping("/fault-records")
    public R<List<EquipmentFaultRecordVO>> getFaultRecords(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<EquipmentFaultRecordVO> records = equipmentSafetyStatService.getFaultRecords(viewType, startDate, endDate);
        return R.ok(records);
    }

    /**
     * 获取设备系统分布统计 (雷达图数据)
     */
    @Anonymous
    @GetMapping("/system-distribution")
    public R<List<EquipmentSystemDistributionVO>> getSystemDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String areaType) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 验证区域类型参数
        if (areaType != null && !areaType.isEmpty() &&
            !areaType.equals("采场") && !areaType.equals("选厂") &&
            !areaType.equals("MINING") && !areaType.equals("PROCESSING")) {
            return R.fail("无效的区域类型，支持的类型: 采场, 选厂, MINING, PROCESSING");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<EquipmentSystemDistributionVO> distribution = equipmentSafetyStatService.getSystemDistribution(viewType, startDate, endDate);

        // 根据区域类型筛选数据
        if (areaType != null && !areaType.isEmpty()) {
            distribution = distribution.stream()
                    .filter(item -> areaType.equals(item.getAreaType()) || areaType.equals(item.getAreaTypeCode()))
                    .collect(Collectors.toList());
        }

        return R.ok(distribution);
    }

    /**
     * 获取设备监测次数统计
     */
    @Anonymous
    @GetMapping("/monitoring-frequency")
    public R<List<EquipmentMonitoringFrequencyVO>> getMonitoringFrequency(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String areaType) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 验证区域类型参数
        if (areaType != null && !areaType.isEmpty() &&
            !areaType.equals("采场") && !areaType.equals("选厂") &&
            !areaType.equals("MINING") && !areaType.equals("PROCESSING")) {
            return R.fail("无效的区域类型，支持的类型: 采场, 选厂, MINING, PROCESSING");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<EquipmentMonitoringFrequencyVO> frequency = equipmentSafetyStatService.getMonitoringFrequency(viewType, startDate, endDate);

        // 根据区域类型筛选数据
        if (areaType != null && !areaType.isEmpty()) {
            frequency = frequency.stream()
                    .filter(item -> areaType.equals(item.getAreaType()) || areaType.equals(item.getAreaTypeCode()))
                    .collect(Collectors.toList());
        }

        return R.ok(frequency);
    }
}
