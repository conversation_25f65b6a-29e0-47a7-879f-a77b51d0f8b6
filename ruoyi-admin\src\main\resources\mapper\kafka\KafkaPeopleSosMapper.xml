<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.admin.mapper.KafkaPeopleSosMapper">
    
    <resultMap type="KafkaPeopleSos" id="KafkaPeopleSosResult">
        <result property="id"    column="id"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="personCardCode"    column="person_card_code"    />
        <result property="personName"    column="person_name"    />
        <result property="sosStartTime"    column="sos_start_time"    />
        <result property="sosEndTime"    column="sos_end_time"    />
        <result property="enterWellTime"    column="enter_well_time"    />
        <result property="currentAreaCode"    column="current_area_code"    />
        <result property="enterCurrentAreaTime"    column="enter_current_area_time"    />
        <result property="currentBaseStationCode"    column="current_base_station_code"    />
        <result property="enterCurrentBaseStationTime"    column="enter_current_base_station_time"    />
        <result property="status"    column="status"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKafkaPeopleSosVo">
        select id, mine_code, mine_name, data_upload_time, person_card_code, person_name, sos_start_time, sos_end_time, enter_well_time, current_area_code, enter_current_area_time, current_base_station_code, enter_current_base_station_time, status, is_deleted, create_by, create_time, update_by, update_time, remark from kafka_people_sos
    </sql>

    <select id="selectKafkaPeoplesosList" parameterType="KafkaPeopleSos" resultMap="KafkaPeopleSosResult">
        <include refid="selectKafkaPeopleSosVo"/>
        <where>  
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="dataUploadTime != null "> and data_upload_time = #{dataUploadTime}</if>
            <if test="personCardCode != null  and personCardCode != ''"> and person_card_code = #{personCardCode}</if>
            <if test="personName != null  and personName != ''"> and person_name like concat('%', #{personName}, '%')</if>
            <if test="sosStartTime != null "> and sos_start_time = #{sosStartTime}</if>
            <if test="sosEndTime != null "> and sos_end_time = #{sosEndTime}</if>
            <if test="enterWellTime != null "> and enter_well_time = #{enterWellTime}</if>
            <if test="currentAreaCode != null  and currentAreaCode != ''"> and current_area_code = #{currentAreaCode}</if>
            <if test="enterCurrentAreaTime != null "> and enter_current_area_time = #{enterCurrentAreaTime}</if>
            <if test="currentBaseStationCode != null  and currentBaseStationCode != ''"> and current_base_station_code = #{currentBaseStationCode}</if>
            <if test="enterCurrentBaseStationTime != null "> and enter_current_base_station_time = #{enterCurrentBaseStationTime}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
        </where>
        order by sos_start_time desc
    </select>
    
    <select id="selectKafkaPeopleSosById" parameterType="Long" resultMap="KafkaPeopleSosResult">
        <include refid="selectKafkaPeopleSosVo"/>
        where id = #{id}
    </select>

    <select id="selectByPersonCardCodeAndSosStartTime" resultMap="KafkaPeopleSosResult">
        <include refid="selectKafkaPeopleSosVo"/>
        where person_card_code = #{personCardCode} 
        and TO_CHAR(sos_start_time, 'YYYY-MM-DD HH24:MI:SS') = #{sosStartTime}
        and is_deleted = 0
    </select>

    <select id="countSosCallsByDateRange" resultType="Long">
        select count(*) 
        from kafka_people_sos 
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
    </select>

    <select id="countSosPersonnelByDateRange" resultType="Long">
        select count(distinct person_card_code) 
        from kafka_people_sos 
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
    </select>

    <select id="selectSosRecordsByDateRange" resultMap="KafkaPeopleSosResult">
        <include refid="selectKafkaPeopleSosVo"/>
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
        order by sos_start_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="selectSosCountByDate" resultType="java.util.Map">
        select 
            data_upload_time::date as date,
            count(*) as sos_count,
            count(distinct person_card_code) as sos_personnel_count
        from kafka_people_sos 
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
        group by data_upload_time::date
        order by date
    </select>

    <select id="selectRecentSosRecords" resultMap="KafkaPeopleSosResult">
        <include refid="selectKafkaPeopleSosVo"/>
        where is_deleted = 0
        order by sos_start_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="selectSosDistributionByArea" resultType="java.util.Map">
        select 
            current_area_code as area_code,
            count(*) as sos_count,
            count(distinct person_card_code) as sos_personnel_count
        from kafka_people_sos 
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
        group by current_area_code
        order by sos_count desc
    </select>
        
    <insert id="insertKafkaPeopleSos" parameterType="KafkaPeopleSos" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_people_sos
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mineCode != null">mine_code,</if>
            <if test="mineName != null">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="personCardCode != null">person_card_code,</if>
            <if test="personName != null">person_name,</if>
            <if test="sosStartTime != null">sos_start_time,</if>
            <if test="sosEndTime != null">sos_end_time,</if>
            <if test="enterWellTime != null">enter_well_time,</if>
            <if test="currentAreaCode != null">current_area_code,</if>
            <if test="enterCurrentAreaTime != null">enter_current_area_time,</if>
            <if test="currentBaseStationCode != null">current_base_station_code,</if>
            <if test="enterCurrentBaseStationTime != null">enter_current_base_station_time,</if>
            <if test="status != null">status,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mineCode != null">#{mineCode},</if>
            <if test="mineName != null">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="personCardCode != null">#{personCardCode},</if>
            <if test="personName != null">#{personName},</if>
            <if test="sosStartTime != null">#{sosStartTime},</if>
            <if test="sosEndTime != null">#{sosEndTime},</if>
            <if test="enterWellTime != null">#{enterWellTime},</if>
            <if test="currentAreaCode != null">#{currentAreaCode},</if>
            <if test="enterCurrentAreaTime != null">#{enterCurrentAreaTime},</if>
            <if test="currentBaseStationCode != null">#{currentBaseStationCode},</if>
            <if test="enterCurrentBaseStationTime != null">#{enterCurrentBaseStationTime},</if>
            <if test="status != null">#{status},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKafkaPeopleSos" parameterType="KafkaPeopleSos">
        update kafka_people_sos
        <trim prefix="SET" suffixOverrides=",">
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="mineName != null">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="personCardCode != null">person_card_code = #{personCardCode},</if>
            <if test="personName != null">person_name = #{personName},</if>
            <if test="sosStartTime != null">sos_start_time = #{sosStartTime},</if>
            <if test="sosEndTime != null">sos_end_time = #{sosEndTime},</if>
            <if test="enterWellTime != null">enter_well_time = #{enterWellTime},</if>
            <if test="currentAreaCode != null">current_area_code = #{currentAreaCode},</if>
            <if test="enterCurrentAreaTime != null">enter_current_area_time = #{enterCurrentAreaTime},</if>
            <if test="currentBaseStationCode != null">current_base_station_code = #{currentBaseStationCode},</if>
            <if test="enterCurrentBaseStationTime != null">enter_current_base_station_time = #{enterCurrentBaseStationTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaPeopleSosById" parameterType="Long">
        delete from kafka_people_sos where id = #{id}
    </delete>

    <delete id="deleteKafkaPeoplesosByIds" parameterType="String">
        delete from kafka_people_sos where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- PostgreSQL UPSERT 操作 -->
    <insert id="upsertKafkaPeopleSos" parameterType="KafkaPeopleSos">
        INSERT INTO kafka_people_sos (
            mine_code, mine_name, data_upload_time, person_card_code, person_name,
            sos_start_time, sos_end_time, enter_well_time, current_area_code,
            enter_current_area_time, current_base_station_code, enter_current_base_station_time,
            status, is_deleted, create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{mineCode}, #{mineName}, #{dataUploadTime}, #{personCardCode}, #{personName},
            #{sosStartTime}, #{sosEndTime}, #{enterWellTime}, #{currentAreaCode},
            #{enterCurrentAreaTime}, #{currentBaseStationCode}, #{enterCurrentBaseStationTime},
            #{status}, #{isDeleted}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
        ON CONFLICT (person_card_code, sos_start_time)
        DO UPDATE SET
            mine_name = EXCLUDED.mine_name,
            data_upload_time = EXCLUDED.data_upload_time,
            person_name = EXCLUDED.person_name,
            sos_end_time = EXCLUDED.sos_end_time,
            enter_well_time = EXCLUDED.enter_well_time,
            current_area_code = EXCLUDED.current_area_code,
            enter_current_area_time = EXCLUDED.enter_current_area_time,
            current_base_station_code = EXCLUDED.current_base_station_code,
            enter_current_base_station_time = EXCLUDED.enter_current_base_station_time,
            status = EXCLUDED.status,
            is_deleted = EXCLUDED.is_deleted,
            update_by = EXCLUDED.update_by,
            update_time = EXCLUDED.update_time,
            remark = EXCLUDED.remark
    </insert>
</mapper>
