# DataEquipment统计接口实现说明

## 概述

为DataEquipmentController新增了两个数据统计查询接口：台效统计接口和作业率统计接口。这两个接口支持按设备类型、时间范围和时间聚合类型进行灵活的数据统计分析。

## 新增功能

### 1. 创建统计VO类

#### DataEquipmentEfficiencyStats（台效统计）
**文件路径**: `ruoyi-admin/src/main/java/com/ruoyi/lxbi/domain/response/DataEquipmentEfficiencyStats.java`

```java
@Data
public class DataEquipmentEfficiencyStats {
    private Integer year;                    // 年份
    private Integer month;                   // 月份
    private Integer weekNumber;              // 周数
    private Long equipmentType;              // 设备类型ID
    private String equipmentTypeName;        // 设备类型名称
    private Date startDate;                  // 统计开始日期
    private Date endDate;                    // 统计结束日期
    private BigDecimal totalProcessingVolume; // 总处理量
    private Integer totalOperationTime;      // 总运行时长（分钟）
    private BigDecimal efficiency;           // 台效（总处理量/运行时长）
    private String timeType;                 // 统计时间类型
}
```

#### DataEquipmentUtilizationStats（作业率统计）
**文件路径**: `ruoyi-admin/src/main/java/com/ruoyi/lxbi/domain/response/DataEquipmentUtilizationStats.java`

```java
@Data
public class DataEquipmentUtilizationStats {
    private Integer year;                    // 年份
    private Integer month;                   // 月份
    private Integer weekNumber;              // 周数
    private Long equipmentType;              // 设备类型ID
    private String equipmentTypeName;        // 设备类型名称
    private Date startDate;                  // 统计开始日期
    private Date endDate;                    // 统计结束日期
    private Integer actualWorkTime;          // 实际工作时间（分钟）
    private Integer totalTime;               // 总时间（分钟）
    private BigDecimal utilizationRate;      // 作业率（百分比）
    private String timeType;                 // 统计时间类型
}
```

### 2. Controller接口

#### 台效统计接口
```java
@GetMapping("/efficiency")
public TableDataInfo getEfficiencyStats(
    @RequestParam(required = false) Long equipmentType,
    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
    @RequestParam(defaultValue = "month") String timeType
)
```

#### 作业率统计接口
```java
@GetMapping("/utilization")
public TableDataInfo getUtilizationStats(
    @RequestParam(required = false) Long equipmentType,
    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
    @RequestParam(defaultValue = "month") String timeType
)
```

### 3. 接口参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| equipmentType | Long | 否 | null | 设备类型ID，为空时查询所有设备类型 |
| startTime | Date | 是 | - | 统计开始时间，格式：yyyy-MM-dd |
| endTime | Date | 是 | - | 统计结束时间，格式：yyyy-MM-dd |
| timeType | String | 否 | month | 时间聚合类型：week/month/year |

### 4. 统计逻辑说明

#### 台效统计逻辑
- **总处理量**: 按时间聚合类型汇总total_processing_volume字段
- **总运行时长**: 按时间聚合类型汇总operation_time字段（分钟）
- **台效计算**: 台效 = 总处理量 ÷ 总运行时长
- **分组维度**: 根据timeType按年/月/周 + 设备类型进行分组

#### 作业率统计逻辑
- **实际工作时间**: 按时间聚合类型汇总operation_time字段（分钟）
- **总时间计算**: 
  - 按周: 统计期间的天数 × 24小时 × 60分钟
  - 按月: 统计期间的天数 × 24小时 × 60分钟  
  - 按年: 统计期间的天数 × 24小时 × 60分钟
- **作业率计算**: 作业率 = (实际工作时间 ÷ 总时间) × 100%
- **分组维度**: 根据timeType按年/月/周 + 设备类型进行分组

### 5. SQL实现特点

#### 时间聚合处理
```sql
-- 按周统计
EXTRACT(YEAR FROM de.operation_date) as year,
EXTRACT(WEEK FROM de.operation_date) as weekNumber

-- 按月统计
EXTRACT(YEAR FROM de.operation_date) as year,
EXTRACT(MONTH FROM de.operation_date) as month

-- 按年统计
EXTRACT(YEAR FROM de.operation_date) as year
```

#### 台效计算
```sql
CASE 
    WHEN SUM(COALESCE(de.operation_time, 0)) > 0 
    THEN SUM(COALESCE(de.total_processing_volume, 0)) / SUM(COALESCE(de.operation_time, 0))
    ELSE 0 
END as efficiency
```

#### 作业率计算
```sql
CASE 
    WHEN (COUNT(DISTINCT de.operation_date) * 24 * 60) > 0 
    THEN (SUM(COALESCE(de.operation_time, 0)) * 100.0) / (COUNT(DISTINCT de.operation_date) * 24 * 60)
    ELSE 0 
END as utilizationRate
```

## 使用示例

### 1. 台效统计接口调用

```javascript
// 查询2025年1月所有设备的月度台效统计
GET /data/dataEquipment/efficiency?startTime=2025-01-01&endTime=2025-01-31&timeType=month

// 查询特定设备类型的周度台效统计
GET /data/dataEquipment/efficiency?equipmentType=1&startTime=2025-01-01&endTime=2025-01-31&timeType=week

// 查询年度台效统计
GET /data/dataEquipment/efficiency?startTime=2025-01-01&endTime=2025-12-31&timeType=year
```

### 2. 作业率统计接口调用

```javascript
// 查询2025年1月所有设备的月度作业率统计
GET /data/dataEquipment/utilization?startTime=2025-01-01&endTime=2025-01-31&timeType=month

// 查询特定设备类型的周度作业率统计
GET /data/dataEquipment/utilization?equipmentType=1&startTime=2025-01-01&endTime=2025-01-31&timeType=week
```

### 3. 返回数据示例

#### 台效统计返回示例
```json
{
    "total": 2,
    "rows": [
        {
            "year": 2025,
            "month": 1,
            "weekNumber": null,
            "equipmentType": 1,
            "equipmentTypeName": "挖掘机",
            "startDate": "2025-01-01",
            "endDate": "2025-01-31",
            "totalProcessingVolume": 15000.00,
            "totalOperationTime": 2400,
            "efficiency": 6.25,
            "timeType": "month"
        }
    ]
}
```

#### 作业率统计返回示例
```json
{
    "total": 2,
    "rows": [
        {
            "year": 2025,
            "month": 1,
            "weekNumber": null,
            "equipmentType": 1,
            "equipmentTypeName": "挖掘机",
            "startDate": "2025-01-01",
            "endDate": "2025-01-31",
            "actualWorkTime": 2400,
            "totalTime": 44640,
            "utilizationRate": 5.38,
            "timeType": "month"
        }
    ]
}
```

## 技术特点

### 1. 灵活的时间聚合
- 支持按周、月、年三种时间维度进行统计
- 自动计算统计期间的开始和结束日期
- 根据时间类型返回相应的年份、月份、周数信息

### 2. 设备类型关联
- 自动关联base_equipment表获取设备类型名称
- 支持按设备类型过滤或查询所有设备类型
- 返回数据包含设备类型ID和名称

### 3. 数据安全性
- 使用COALESCE函数处理NULL值
- 除零保护，避免运行时错误
- 参数验证和类型转换

### 4. 性能优化
- 使用索引友好的查询条件
- 合理的GROUP BY和ORDER BY
- 避免不必要的数据传输

### 5. Excel导出支持
- VO类配置了@Excel注解
- 支持直接导出统计结果
- 格式化日期和数值显示

## 业务价值

### 1. 台效分析
- 评估设备处理效率
- 识别高效和低效设备
- 支持设备性能优化决策

### 2. 作业率分析
- 监控设备利用率
- 发现设备闲置问题
- 优化设备调度和配置

### 3. 多维度统计
- 支持不同时间粒度的分析
- 便于趋势分析和对比
- 满足不同层级的管理需求

## 总结

通过新增的两个统计接口，DataEquipmentController现在提供了：

1. ✅ **完整的台效统计**: 支持多时间维度的设备效率分析
2. ✅ **全面的作业率统计**: 支持设备利用率的深度分析
3. ✅ **灵活的查询参数**: 支持设备类型、时间范围、聚合类型的组合查询
4. ✅ **标准的返回格式**: 统一的分页返回格式，支持前端表格展示
5. ✅ **Excel导出支持**: 配置完整的导出注解，支持数据导出

这些功能为设备管理提供了强大的数据分析能力，有助于提升设备使用效率和管理水平。
