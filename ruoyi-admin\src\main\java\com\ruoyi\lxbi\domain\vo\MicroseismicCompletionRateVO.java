package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 微震P波完成率VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MicroseismicCompletionRateVO {
    
    /**
     * 完成率百分比
     */
    private BigDecimal completionRate;
    
    /**
     * 已完成数量
     */
    private Long completedCount;
    
    /**
     * 总数量
     */
    private Long totalCount;
    
    /**
     * 完成率类型 (P波完成率)
     */
    private String rateType;
}
