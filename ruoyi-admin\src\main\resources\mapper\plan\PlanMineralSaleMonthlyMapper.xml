<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.PlanMineralSaleMonthlyMapper">
    
    <resultMap type="PlanMineralSaleMonthly" id="PlanMineralSaleMonthlyResult">
        <result property="id"    column="id"    />
        <result property="ironConcentrateVolume"    column="iron_concentrate_volume"    />
        <result property="concentratorBinsStockVolume"    column="concentrator_bins_stock_volume"    />
        <result property="serviceShaftSurfaceStockVolume"    column="service_shaft_surface_stock_volume"    />
        <result property="rawOreGradeTfe"    column="raw_ore_grade_tfe"    />
        <result property="stockVolume"    column="stock_volume"    />
        <result property="planDate"    column="plan_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPlanMineralSaleMonthlyVo">
        select id, iron_concentrate_volume, concentrator_bins_stock_volume, service_shaft_surface_stock_volume, raw_ore_grade_tfe, stock_volume, plan_date, create_by, create_time, update_by, update_time from plan_mineral_sale_monthly
    </sql>

    <select id="selectPlanMineralSaleMonthlyList" parameterType="PlanMineralSaleMonthly" resultMap="PlanMineralSaleMonthlyResult">
        <include refid="selectPlanMineralSaleMonthlyVo"/>
        <where>  
            <if test="planDate != null  and planDate != ''"> and plan_date = #{planDate}</if>
        </where>
    </select>
    
    <select id="selectPlanMineralSaleMonthlyById" parameterType="Long" resultMap="PlanMineralSaleMonthlyResult">
        <include refid="selectPlanMineralSaleMonthlyVo"/>
        where id = #{id}
    </select>

    <insert id="insertPlanMineralSaleMonthly" parameterType="PlanMineralSaleMonthly" useGeneratedKeys="true" keyProperty="id">
        insert into plan_mineral_sale_monthly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ironConcentrateVolume != null">iron_concentrate_volume,</if>
            <if test="concentratorBinsStockVolume != null">concentrator_bins_stock_volume,</if>
            <if test="serviceShaftSurfaceStockVolume != null">service_shaft_surface_stock_volume,</if>
            <if test="rawOreGradeTfe != null">raw_ore_grade_tfe,</if>
            <if test="stockVolume != null">stock_volume,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ironConcentrateVolume != null">#{ironConcentrateVolume},</if>
            <if test="concentratorBinsStockVolume != null">#{concentratorBinsStockVolume},</if>
            <if test="serviceShaftSurfaceStockVolume != null">#{serviceShaftSurfaceStockVolume},</if>
            <if test="rawOreGradeTfe != null">#{rawOreGradeTfe},</if>
            <if test="stockVolume != null">#{stockVolume},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePlanMineralSaleMonthly" parameterType="PlanMineralSaleMonthly">
        update plan_mineral_sale_monthly
        <trim prefix="SET" suffixOverrides=",">
            <if test="ironConcentrateVolume != null">iron_concentrate_volume = #{ironConcentrateVolume},</if>
            <if test="concentratorBinsStockVolume != null">concentrator_bins_stock_volume = #{concentratorBinsStockVolume},</if>
            <if test="serviceShaftSurfaceStockVolume != null">service_shaft_surface_stock_volume = #{serviceShaftSurfaceStockVolume},</if>
            <if test="rawOreGradeTfe != null">raw_ore_grade_tfe = #{rawOreGradeTfe},</if>
            <if test="stockVolume != null">stock_volume = #{stockVolume},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanMineralSaleMonthlyById" parameterType="Long">
        delete from plan_mineral_sale_monthly where id = #{id}
    </delete>

    <delete id="deletePlanMineralSaleMonthlyByIds" parameterType="String">
        delete from plan_mineral_sale_monthly where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>