-- 人员求救数据表 (PostgreSQL)
CREATE TABLE kafka_people_sos (
    id BIGSERIAL PRIMARY KEY,
    mine_code VARCHAR(50),
    mine_name VARCHAR(100),
    data_upload_time TIMESTAMP,
    person_card_code VARCHAR(50) NOT NULL,
    person_name VARCHAR(100),
    sos_start_time TIMESTAMP NOT NULL,
    sos_end_time TIMESTAMP,
    enter_well_time TIMESTAMP,
    current_area_code VARCHAR(50),
    enter_current_area_time TIMESTAMP,
    current_base_station_code VARCHAR(50),
    enter_current_base_station_time TIMESTAMP,
    status BIGINT DEFAULT 1,
    is_deleted BIGINT DEFAULT 0,
    create_by VA<PERSON>HA<PERSON>(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500) DEFAULT NULL
);

-- 添加字段注释 (PostgreSQL语法)
COMMENT ON TABLE kafka_people_sos IS '人员求救数据表';
COMMENT ON COLUMN kafka_people_sos.id IS '主键ID';
COMMENT ON COLUMN kafka_people_sos.mine_code IS '煤矿编码';
COMMENT ON COLUMN kafka_people_sos.mine_name IS '矿井名称';
COMMENT ON COLUMN kafka_people_sos.data_upload_time IS '数据上传时间';
COMMENT ON COLUMN kafka_people_sos.person_card_code IS '人员卡编码';
COMMENT ON COLUMN kafka_people_sos.person_name IS '姓名';
COMMENT ON COLUMN kafka_people_sos.sos_start_time IS '求救开始时间';
COMMENT ON COLUMN kafka_people_sos.sos_end_time IS '求救结束时间';
COMMENT ON COLUMN kafka_people_sos.enter_well_time IS '入井时间';
COMMENT ON COLUMN kafka_people_sos.current_area_code IS '当前所在区域编码';
COMMENT ON COLUMN kafka_people_sos.enter_current_area_time IS '进入当前区域时刻';
COMMENT ON COLUMN kafka_people_sos.current_base_station_code IS '当前所在基站编码';
COMMENT ON COLUMN kafka_people_sos.enter_current_base_station_time IS '进入当前所处基站时刻';
COMMENT ON COLUMN kafka_people_sos.status IS '状态(1:正常 0:异常)';
COMMENT ON COLUMN kafka_people_sos.is_deleted IS '是否删除(0:未删除 1:已删除)';
COMMENT ON COLUMN kafka_people_sos.create_by IS '创建者';
COMMENT ON COLUMN kafka_people_sos.create_time IS '创建时间';
COMMENT ON COLUMN kafka_people_sos.update_by IS '更新者';
COMMENT ON COLUMN kafka_people_sos.update_time IS '更新时间';
COMMENT ON COLUMN kafka_people_sos.remark IS '备注';

-- 创建索引 (PostgreSQL)
CREATE INDEX idx_kps_person_card_code ON kafka_people_sos(person_card_code);
CREATE INDEX idx_kps_sos_start_time ON kafka_people_sos(sos_start_time);
CREATE INDEX idx_kps_data_upload_time ON kafka_people_sos(data_upload_time);
CREATE INDEX idx_kps_current_area_code ON kafka_people_sos(current_area_code);
CREATE INDEX idx_kps_mine_code ON kafka_people_sos(mine_code);
CREATE INDEX idx_kps_status_deleted ON kafka_people_sos(status, is_deleted);

-- 创建表级唯一约束
ALTER TABLE kafka_people_sos 
ADD CONSTRAINT uk_person_sos_time UNIQUE (person_card_code, sos_start_time);

-- 创建求救统计视图
CREATE OR REPLACE VIEW v_sos_statistics AS
SELECT 
    current_area_code,
    COUNT(*) as total_sos_count,
    COUNT(DISTINCT person_card_code) as unique_personnel_count,
    AVG(EXTRACT(EPOCH FROM (sos_end_time - sos_start_time))/60) as avg_response_time_minutes,
    MIN(sos_start_time) as first_sos_time,
    MAX(sos_start_time) as latest_sos_time
FROM kafka_people_sos 
WHERE is_deleted = 0 AND status = 1
GROUP BY current_area_code;

COMMENT ON VIEW v_sos_statistics IS '求救统计视图';

-- 创建求救详情视图
CREATE OR REPLACE VIEW v_sos_details AS
SELECT 
    kps.person_card_code,
    kps.person_name,
    kps.sos_start_time,
    kps.sos_end_time,
    kps.current_area_code,
    kli.area_name,
    kli.area_type,
    EXTRACT(EPOCH FROM (kps.sos_end_time - kps.sos_start_time))/60 as response_time_minutes,
    CASE 
        WHEN EXTRACT(EPOCH FROM (kps.sos_end_time - kps.sos_start_time))/60 <= 5 THEN '快速响应'
        WHEN EXTRACT(EPOCH FROM (kps.sos_end_time - kps.sos_start_time))/60 <= 15 THEN '正常响应'
        ELSE '响应较慢'
    END as response_level
FROM kafka_people_sos kps
LEFT JOIN kafka_people_location_info kli ON kps.current_area_code = kli.area_code
WHERE kps.is_deleted = 0 AND kps.status = 1;

COMMENT ON VIEW v_sos_details IS '求救详情视图';

-- 查看创建的视图
SELECT * FROM v_sos_statistics ORDER BY total_sos_count DESC;
SELECT * FROM v_sos_details ORDER BY sos_start_time DESC LIMIT 10;
