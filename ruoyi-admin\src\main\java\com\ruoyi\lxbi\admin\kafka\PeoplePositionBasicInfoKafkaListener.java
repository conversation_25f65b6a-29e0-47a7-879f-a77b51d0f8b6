package com.ruoyi.lxbi.admin.kafka;

import com.ruoyi.lxbi.admin.service.IKafkaPeoplePositionBasicInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

/**
 * 人员基础信息Kafka监听器
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Slf4j
@Component
public class PeoplePositionBasicInfoKafkaListener {

    @Autowired
    private IKafkaPeoplePositionBasicInfoService kafkaPeoplePositionBasicInfoService;

    /**
     * 监听人员基础信息主题
     * 
     * @param record Kafka消息记录
     * @param ack 手动确认
     */
    @KafkaListener(topics = "PeoplePos_People_BasicInfo")
    public void handlePeopleBasicInfo(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try {
            String message = record.value();
            log.info("接收到人员基础信息Kafka消息，主题: {}, 分区: {}, 偏移量: {}", 
                record.topic(), record.partition(), record.offset());
            log.debug("消息内容: {}", message);

            // 处理消息
            boolean success = kafkaPeoplePositionBasicInfoService.processKafkaMessage(message);
            
            if (success) {
                log.info("人员基础信息消息处理成功，偏移量: {}", record.offset());
                // 手动确认消息
                if (ack != null) {
                    ack.acknowledge();
                }
            } else {
                log.error("人员基础信息消息处理失败，偏移量: {}, 消息: {}", record.offset(), message);
            }

        } catch (Exception e) {
            log.error("处理人员基础信息Kafka消息异常，偏移量: {}", record.offset(), e);
        }
    }

}
