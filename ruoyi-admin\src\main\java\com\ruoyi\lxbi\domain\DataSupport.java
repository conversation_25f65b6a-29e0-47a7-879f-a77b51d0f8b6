package com.ruoyi.lxbi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 支护数据对象 data_support
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataSupport extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 支护数据ID */
    private Long id;

    /** 作业日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "作业日期", width = 30, dateFormat = "yyyy-MM-dd", sort = 1)
    private Date operationDate;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 工作面ID */
    private Long workingFaceId;

    /** 采场ID */
    private Long stopeId;

    /** 作业时段ID */
    private Long workingPeriodId;

    /** 支护类型：1-锚网支护、2-喷浆支护 */
    @Excel(name = "支护类型：1-锚网支护、2-喷浆支护", sort = 7)
    private String supportType;

    /** 支护长度(m) */
    @Excel(name = "支护长度(m)", sort = 8)
    private Double supportLength;

    /** 支护体积(m³) */
    @Excel(name = "支护体积(m³)", sort = 9)
    private Double supportVolume;

    /** 备注 */
    @Excel(name = "备注", sort = 10)
    private String remarks;

}
