<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.admin.mapper.HiddenTroubleStatMapper">

    <!-- 获取隐患总数 -->
    <select id="getTotalHiddenTroubleCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM api_hidden_trouble_record
    </select>

    <!-- 获取重大隐患数 -->
    <select id="getMajorHiddenTroubleCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM api_hidden_trouble_record
        WHERE trouble_grade = '1'
    </select>

    <!-- 获取危险源数量（用隐患类别数量代替） -->
    <select id="getRiskSourceCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT trouble_category)
        FROM api_hidden_trouble_record
        WHERE trouble_category IS NOT NULL
          AND trouble_category != ''
    </select>

    <!-- 获取风险管控数量（用已整改隐患数代替） -->
    <select id="getRiskControlCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM api_hidden_trouble_record
        WHERE status = 2
    </select>

    <!-- 按日期范围获取隐患总数 -->
    <select id="getTotalHiddenTroubleCountByDate" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM api_hidden_trouble_record
        WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
    </select>

    <!-- 按日期范围获取重大隐患数 -->
    <select id="getMajorHiddenTroubleCountByDate" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM api_hidden_trouble_record
        WHERE trouble_grade = '1'
          AND trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
    </select>

    <!-- 按日期范围获取危险源数量（用隐患类别数量代替） -->
    <select id="getRiskSourceCountByDate" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT trouble_category)
        FROM api_hidden_trouble_record
        WHERE trouble_category IS NOT NULL
          AND trouble_category != ''
          AND trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
    </select>

    <!-- 按日期范围获取风险管控数量（用已整改隐患数代替） -->
    <select id="getRiskControlCountByDate" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM api_hidden_trouble_record
        WHERE status = 2
          AND trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
    </select>

    <!-- 获取部门隐患分布统计 -->
    <select id="getDepartmentDistribution" resultType="java.util.Map">
        SELECT
            responsible_department as department,
            COUNT(*) as count,
            COUNT(CASE WHEN trouble_grade = '1' THEN 1 END) as major_count,
            COUNT(CASE WHEN trouble_grade = '0' THEN 1 END) as general_count
        FROM api_hidden_trouble_record
        WHERE responsible_department IS NOT NULL
          AND responsible_department != ''
        GROUP BY responsible_department
        ORDER BY count DESC
        LIMIT 10
    </select>

    <!-- 按日期范围获取部门隐患分布统计 -->
    <select id="getDepartmentDistributionByDate" resultType="java.util.Map">
        SELECT
            responsible_department as department,
            COUNT(*) as count,
            COUNT(CASE WHEN trouble_grade = '1' THEN 1 END) as major_count,
            COUNT(CASE WHEN trouble_grade = '0' THEN 1 END) as general_count
        FROM api_hidden_trouble_record
        WHERE responsible_department IS NOT NULL
          AND responsible_department != ''
          AND trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY responsible_department
        ORDER BY count DESC
        LIMIT 10
    </select>

    <!-- 获取高频发隐患位置统计 -->
    <select id="getLocationFrequency" resultType="java.util.Map">
        SELECT
            trouble_location as location,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_hidden_trouble_record), 2) as percentage
        FROM api_hidden_trouble_record
        WHERE trouble_location IS NOT NULL
          AND trouble_location != ''
        GROUP BY trouble_location
        ORDER BY count DESC
        LIMIT 5
    </select>

    <!-- 按日期范围获取高频发隐患位置统计 -->
    <select id="getLocationFrequencyByDate" resultType="java.util.Map">
        SELECT
            trouble_location as location,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date), 2) as percentage
        FROM api_hidden_trouble_record
        WHERE trouble_location IS NOT NULL
          AND trouble_location != ''
          AND trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY trouble_location
        ORDER BY count DESC
        LIMIT 5
    </select>

    <!-- 按日统计隐患数量 -->
    <select id="getDailyStatistics" resultType="java.util.Map">
        SELECT
            TO_CHAR(trouble_date, 'YYYY-MM-DD') as date,
            TO_CHAR(trouble_date, 'MM-DD') as short_date,
            COUNT(*) as total_count,
            COUNT(CASE WHEN trouble_grade = '1' THEN 1 END) as major_count,
            COUNT(CASE WHEN trouble_grade = '0' THEN 1 END) as general_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY trouble_date
        ORDER BY trouble_date
    </select>

    <!-- 按周统计隐患数量 -->
    <select id="getWeeklyStatistics" resultType="java.util.Map">
        SELECT
            TO_CHAR(DATE_TRUNC('week', trouble_date), 'YYYY-MM-DD') as week_start,
            TO_CHAR(DATE_TRUNC('week', trouble_date) + INTERVAL '6 days', 'YYYY-MM-DD') as week_end,
            EXTRACT(YEAR FROM trouble_date) || '年第' || EXTRACT(WEEK FROM trouble_date) || '周' as week_label,
            COUNT(*) as total_count,
            COUNT(CASE WHEN trouble_grade = '1' THEN 1 END) as major_count,
            COUNT(CASE WHEN trouble_grade = '0' THEN 1 END) as general_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY DATE_TRUNC('week', trouble_date), EXTRACT(YEAR FROM trouble_date), EXTRACT(WEEK FROM trouble_date)
        ORDER BY DATE_TRUNC('week', trouble_date)
    </select>

    <!-- 按月统计隐患数量 -->
    <select id="getMonthlyStatistics" resultType="java.util.Map">
        SELECT
            TO_CHAR(DATE_TRUNC('month', trouble_date), 'YYYY-MM') as month,
            TO_CHAR(DATE_TRUNC('month', trouble_date), 'YYYY年MM月') as month_label,
            COUNT(*) as total_count,
            COUNT(CASE WHEN trouble_grade = '1' THEN 1 END) as major_count,
            COUNT(CASE WHEN trouble_grade = '0' THEN 1 END) as general_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY DATE_TRUNC('month', trouble_date)
        ORDER BY DATE_TRUNC('month', trouble_date)
    </select>

    <!-- 获取隐患状态分布统计 -->
    <select id="getStatusDistribution" resultType="java.util.Map">
        SELECT
            status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_hidden_trouble_record ), 2) as percentage,
            CASE
                WHEN status = 0 THEN '待整改'
                WHEN status = 1 THEN '已驳回'
                WHEN status = 2 THEN '已整改'
                WHEN status = 3 THEN '待复查'
                WHEN status = 4 THEN '已超期'
                ELSE '未知'
            END as status_name
        FROM api_hidden_trouble_record GROUP BY status
        ORDER BY status
    </select>

    <!-- 按日期范围获取隐患状态分布统计 -->
    <select id="getStatusDistributionByDate" resultType="java.util.Map">
        SELECT
            status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date), 2) as percentage,
            CASE
                WHEN status = 0 THEN '待整改'
                WHEN status = 1 THEN '已驳回'
                WHEN status = 2 THEN '已整改'
                WHEN status = 3 THEN '待复查'
                WHEN status = 4 THEN '已超期'
                ELSE '未知'
            END as status_name
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY status
        ORDER BY status
    </select>

    <!-- 获取隐患等级分布统计 -->
    <select id="getGradeDistribution" resultType="java.util.Map">
        SELECT
            trouble_grade as grade,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_hidden_trouble_record ), 2) as percentage,
            CASE
                WHEN trouble_grade = '0' THEN '一般隐患'
                WHEN trouble_grade = '1' THEN '重大隐患'
                ELSE '未知'
            END as grade_name
        FROM api_hidden_trouble_record GROUP BY trouble_grade
        ORDER BY trouble_grade
    </select>

    <!-- 按日期范围获取隐患等级分布统计 -->
    <select id="getGradeDistributionByDate" resultType="java.util.Map">
        SELECT
            trouble_grade as grade,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date), 2) as percentage,
            CASE
                WHEN trouble_grade = '0' THEN '一般隐患'
                WHEN trouble_grade = '1' THEN '重大隐患'
                ELSE '未知'
            END as grade_name
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY trouble_grade
        ORDER BY trouble_grade
    </select>

    <!-- 获取隐患类别分布统计 -->
    <select id="getCategoryDistribution" resultType="java.util.Map">
        SELECT
            trouble_category as category,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_hidden_trouble_record ), 2) as percentage
        FROM api_hidden_trouble_record WHERE trouble_category IS NOT NULL
          AND trouble_category != ''
        GROUP BY trouble_category
        ORDER BY count DESC
        LIMIT 10
    </select>

    <!-- 按日期范围获取隐患类别分布统计 -->
    <select id="getCategoryDistributionByDate" resultType="java.util.Map">
        SELECT
            trouble_category as category,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date), 2) as percentage
        FROM api_hidden_trouble_record WHERE trouble_category IS NOT NULL
          AND trouble_category != ''
          AND trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY trouble_category
        ORDER BY count DESC
        LIMIT 10
    </select>

    <!-- 获取责任人隐患统计 -->
    <select id="getResponsiblePersonStatistics" resultType="java.util.Map">
        SELECT
            responsible_person as person,
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count,
            ROUND(COUNT(CASE WHEN status = 2 THEN 1 END) * 100.0 / COUNT(*), 2) as completion_rate
        FROM api_hidden_trouble_record WHERE responsible_person IS NOT NULL
          AND responsible_person != ''
        GROUP BY responsible_person
        ORDER BY total_count DESC
        LIMIT 10
    </select>

    <!-- 按日期范围获取责任人隐患统计 -->
    <select id="getResponsiblePersonStatisticsByDate" resultType="java.util.Map">
        SELECT
            responsible_person as person,
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count,
            ROUND(COUNT(CASE WHEN status = 2 THEN 1 END) * 100.0 / COUNT(*), 2) as completion_rate
        FROM api_hidden_trouble_record WHERE responsible_person IS NOT NULL
          AND responsible_person != ''
          AND trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY responsible_person
        ORDER BY total_count DESC
        LIMIT 10
    </select>

    <!-- 获取整改完成率统计 -->
    <select id="getRectificationCompletionRate" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            ROUND(COUNT(CASE WHEN status = 2 THEN 1 END) * 100.0 / COUNT(*), 2) as completion_rate
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
    </select>

    <!-- 获取超期隐患统计 -->
    <select id="getOverdueStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count,
            ROUND(COUNT(CASE WHEN status = 4 THEN 1 END) * 100.0 / COUNT(*), 2) as overdue_rate
        FROM api_hidden_trouble_record </select>

    <!-- 按日期范围获取超期隐患统计 -->
    <select id="getOverdueStatisticsByDate" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count,
            ROUND(COUNT(CASE WHEN status = 4 THEN 1 END) * 100.0 / COUNT(*), 2) as overdue_rate
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
    </select>

    <!-- 按部门和等级统计隐患数量 -->
    <select id="getDepartmentGradeStatistics" resultType="java.util.Map">
        SELECT
            responsible_department as department,
            trouble_grade as grade,
            COUNT(*) as count,
            CASE
                WHEN trouble_grade = '0' THEN '一般隐患'
                WHEN trouble_grade = '1' THEN '重大隐患'
                ELSE '未知'
            END as grade_name
        FROM api_hidden_trouble_record WHERE responsible_department IS NOT NULL
          AND responsible_department != ''
        GROUP BY responsible_department, trouble_grade
        ORDER BY responsible_department, trouble_grade
    </select>

    <!-- 获取隐患趋势对比数据（重大隐患vs一般隐患） -->
    <select id="getTrendComparisonData" resultType="java.util.Map">
        SELECT
            TO_CHAR(trouble_date, 'YYYY-MM-DD') as date,
            COUNT(CASE WHEN trouble_grade = '1' THEN 1 END) as major_count,
            COUNT(CASE WHEN trouble_grade = '0' THEN 1 END) as general_count
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY trouble_date
        ORDER BY trouble_date
    </select>

    <!-- 获取检查人员工作量统计 -->
    <select id="getInspectorWorkloadStatistics" resultType="java.util.Map">
        SELECT
            inspector,
            COUNT(*) as inspection_count,
            COUNT(DISTINCT responsible_department) as department_count
        FROM api_hidden_trouble_record WHERE inspector IS NOT NULL
          AND inspector != ''
        GROUP BY inspector
        ORDER BY inspection_count DESC
        LIMIT 10
    </select>

    <!-- 获取复查人员工作量统计 -->
    <select id="getReviewerWorkloadStatistics" resultType="java.util.Map">
        SELECT
            reviewer,
            COUNT(*) as review_count,
            COUNT(DISTINCT responsible_department) as department_count
        FROM api_hidden_trouble_record WHERE reviewer IS NOT NULL
          AND reviewer != ''
        GROUP BY reviewer
        ORDER BY review_count DESC
        LIMIT 10
    </select>

    <!-- 获取隐患处理时效统计 -->
    <select id="getProcessingTimeStatistics" resultType="java.util.Map">
        SELECT
            CASE
                WHEN rectification_completion_date IS NULL THEN '未完成'
                WHEN rectification_completion_date &lt;= rectification_deadline THEN '按时完成'
                ELSE '超期完成'
            END as processing_status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_hidden_trouble_record ), 2) as percentage
        FROM api_hidden_trouble_record WHERE rectification_deadline IS NOT NULL
        GROUP BY
            CASE
                WHEN rectification_completion_date IS NULL THEN '未完成'
                WHEN rectification_completion_date &lt;= rectification_deadline THEN '按时完成'
                ELSE '超期完成'
            END
        ORDER BY count DESC
    </select>

    <!-- 获取近期隐患趋势（最近30天） -->
    <select id="getRecentTrendData" resultType="java.util.Map">
        WITH date_series AS (
            SELECT generate_series(
                CURRENT_DATE - INTERVAL '29 days',
                CURRENT_DATE,
                INTERVAL '1 day'
            )::date as date
        )
        SELECT
            TO_CHAR(ds.date, 'YYYY-MM-DD') as date,
            TO_CHAR(ds.date, 'MM-DD') as short_date,
            COALESCE(COUNT(ht.id), 0) as count
        FROM date_series ds
        LEFT JOIN api_hidden_trouble_record ht ON ht.trouble_date = ds.date
        GROUP BY ds.date
        ORDER BY ds.date
    </select>

    <!-- 按日期范围获取近期隐患趋势 -->
    <select id="getRecentTrendDataByDate" resultType="java.util.Map">
        SELECT
            TO_CHAR(trouble_date, 'YYYY-MM-DD') as date,
            TO_CHAR(trouble_date, 'MM-DD') as short_date,
            COUNT(*) as count
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY trouble_date
        ORDER BY trouble_date
    </select>

    <!-- 按日统计超期隐患趋势 -->
    <select id="getOverdueDailyStatistics" resultType="java.util.Map">
        SELECT
            TO_CHAR(trouble_date, 'YYYY-MM-DD') as date,
            TO_CHAR(trouble_date, 'MM-DD') as short_date,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count,
            COUNT(*) as total_count,
            ROUND(COUNT(CASE WHEN status = 4 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as overdue_rate
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY trouble_date
        ORDER BY trouble_date
    </select>

    <!-- 按周统计超期隐患趋势 -->
    <select id="getOverdueWeeklyStatistics" resultType="java.util.Map">
        SELECT
            TO_CHAR(DATE_TRUNC('week', trouble_date), 'YYYY-MM-DD') as week_start,
            TO_CHAR(DATE_TRUNC('week', trouble_date) + INTERVAL '6 days', 'YYYY-MM-DD') as week_end,
            TO_CHAR(DATE_TRUNC('week', trouble_date), 'YYYY年第WW周') as week_label,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count,
            COUNT(*) as total_count,
            ROUND(COUNT(CASE WHEN status = 4 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as overdue_rate
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY DATE_TRUNC('week', trouble_date)
        ORDER BY DATE_TRUNC('week', trouble_date)
    </select>

    <!-- 按月统计超期隐患趋势 -->
    <select id="getOverdueMonthlyStatistics" resultType="java.util.Map">
        SELECT
            TO_CHAR(DATE_TRUNC('month', trouble_date), 'YYYY-MM') as month,
            TO_CHAR(DATE_TRUNC('month', trouble_date), 'YYYY年MM月') as month_label,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count,
            COUNT(*) as total_count,
            ROUND(COUNT(CASE WHEN status = 4 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as overdue_rate
        FROM api_hidden_trouble_record WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY DATE_TRUNC('month', trouble_date)
        ORDER BY DATE_TRUNC('month', trouble_date)
    </select>

    <!-- 获取隐患热力图数据（按小时分布） -->
    <select id="getHourlyHeatmapData" resultType="java.util.Map">
        SELECT
            EXTRACT(HOUR FROM create_time) as hour,
            EXTRACT(DOW FROM create_time) as day_of_week,
            COUNT(*) as count
        FROM api_hidden_trouble_record WHERE create_time &gt;= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY EXTRACT(HOUR FROM create_time), EXTRACT(DOW FROM create_time)
        ORDER BY day_of_week, hour
    </select>

    <!-- 获取隐患严重程度分析 -->
    <select id="getSeverityAnalysis" resultType="java.util.Map">
        SELECT
            trouble_grade as grade,
            responsible_department as department,
            COUNT(*) as count,
            AVG(CASE
                WHEN rectification_completion_date IS NOT NULL AND rectification_deadline IS NOT NULL
                THEN EXTRACT(DAYS FROM (rectification_completion_date - rectification_deadline))
                ELSE NULL
            END) as avg_delay_days
        FROM api_hidden_trouble_record GROUP BY trouble_grade, responsible_department
        ORDER BY trouble_grade DESC, count DESC
    </select>

    <!-- 获取隐患整改效率统计 -->
    <select id="getRectificationEfficiency" resultType="java.util.Map">
        SELECT
            responsible_department as department,
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            AVG(CASE
                WHEN rectification_completion_date IS NOT NULL AND trouble_date IS NOT NULL
                THEN EXTRACT(DAYS FROM (rectification_completion_date - trouble_date))
                ELSE NULL
            END) as avg_completion_days,
            ROUND(COUNT(CASE WHEN status = 2 THEN 1 END) * 100.0 / COUNT(*), 2) as completion_rate
        FROM api_hidden_trouble_record WHERE responsible_department IS NOT NULL
          AND responsible_department != ''
        GROUP BY responsible_department
        ORDER BY completion_rate DESC, total_count DESC
    </select>

    <!-- 按日期范围获取隐患整改效率统计 -->
    <select id="getRectificationEfficiencyByDate" resultType="java.util.Map">
        SELECT
            responsible_department as department,
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            AVG(CASE
                WHEN rectification_completion_date IS NOT NULL AND trouble_date IS NOT NULL
                THEN EXTRACT(DAYS FROM (rectification_completion_date - trouble_date))
                ELSE NULL
            END) as avg_completion_days,
            ROUND(COUNT(CASE WHEN status = 2 THEN 1 END) * 100.0 / COUNT(*), 2) as completion_rate
        FROM api_hidden_trouble_record WHERE responsible_department IS NOT NULL
          AND responsible_department != ''
          AND trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY responsible_department
        ORDER BY completion_rate DESC, total_count DESC
    </select>

    <!-- 按日期范围获取安全小结统计 -->
    <select id="getSafetySummaryByDate" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_count,
            COUNT(CASE WHEN trouble_grade = '1' THEN 1 END) as major_count,
            COUNT(CASE WHEN status = 0 THEN 1 END) as pending_count,
            COUNT(CASE WHEN status = 4 THEN 1 END) as overdue_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 3 THEN 1 END) as review_pending_count,
            COUNT(CASE WHEN status = 1 THEN 1 END) as rejected_count
        FROM api_hidden_trouble_record
        WHERE trouble_date BETWEEN #{startDate}::date AND #{endDate}::date
    </select>

</mapper>
