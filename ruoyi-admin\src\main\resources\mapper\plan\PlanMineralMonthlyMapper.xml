<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.PlanMineralMonthlyMapper">
    
    <resultMap type="PlanMineralMonthly" id="PlanMineralMonthlyResult">
        <result property="id"    column="id"    />
        <result property="rawOreProcessingVolume"    column="raw_ore_processing_volume"    />
        <result property="drySeparationVolume"    column="dry_separation_volume"    />
        <result property="grindingFeedVolume"    column="grinding_feed_volume"    />
        <result property="rawOreGradeTfe"    column="raw_ore_grade_tfe"    />
        <result property="concentrateGrade"    column="concentrate_grade"    />
        <result property="tailingGradeTfe"    column="tailing_grade_tfe"    />
        <result property="concentrateVolume"    column="concentrate_volume"    />
        <result property="planDate"    column="plan_date"    />
        <result property="concentrateFineness" column="concentrate_fineness" />
        <result property="ironConcentrateMoisture" column="iron_concentrate_moisture" />
        <result property="comprehensiveRatio" column="comprehensive_ratio" />
        <result property="grindingRatio" column="grinding_ratio" />
        <result property="tailingsAgitatorTank" column="tailings_agitator_tank" />
        <result property="overflowOfTailings" column="overflow_of_tailings" />
        <result property="rawOreGradeMfe" column="raw_ore_grade_mfe" />
        <result property="tailingGradeMfe" column="tailing_grade_mfe" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPlanMineralMonthlyVo">
        select id, raw_ore_processing_volume, dry_separation_volume, grinding_feed_volume, raw_ore_grade_tfe, concentrate_grade, tailing_grade_tfe, concentrate_volume, plan_date,
        concentrate_fineness, iron_concentrate_moisture, comprehensive_ratio, grinding_ratio, tailings_agitator_tank, overflow_of_tailings, raw_ore_grade_mfe, tailing_grade_mfe,
        create_by, create_time, update_by, update_time from plan_mineral_monthly
    </sql>

    <select id="selectPlanMineralMonthlyList" parameterType="PlanMineralMonthly" resultMap="PlanMineralMonthlyResult">
        <include refid="selectPlanMineralMonthlyVo"/>
        <where>  
            <if test="planDate != null  and planDate != ''"> and plan_date = #{planDate}</if>
        </where>
    </select>
    
    <select id="selectPlanMineralMonthlyById" parameterType="Long" resultMap="PlanMineralMonthlyResult">
        <include refid="selectPlanMineralMonthlyVo"/>
        where id = #{id}
    </select>

    <insert id="insertPlanMineralMonthly" parameterType="PlanMineralMonthly" useGeneratedKeys="true" keyProperty="id">
        insert into plan_mineral_monthly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rawOreProcessingVolume != null">raw_ore_processing_volume,</if>
            <if test="drySeparationVolume != null">dry_separation_volume,</if>
            <if test="grindingFeedVolume != null">grinding_feed_volume,</if>
            <if test="rawOreGradeTfe != null">raw_ore_grade_tfe,</if>
            <if test="concentrateGrade != null">concentrate_grade,</if>
            <if test="tailingGradeTfe != null">tailing_grade_tfe,</if>
            <if test="concentrateVolume != null">concentrate_volume,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="concentrateFineness != null">concentrate_fineness,</if>
            <if test="ironConcentrateMoisture != null">iron_concentrate_moisture,</if>
            <if test="comprehensiveRatio != null">comprehensive_ratio,</if>
            <if test="grindingRatio != null">grinding_ratio,</if>
            <if test="tailingsAgitatorTank != null">tailings_agitator_tank,</if>
            <if test="overflowOfTailings != null">overflow_of_tailings,</if>
            <if test="rawOreGradeMfe != null">raw_ore_grade_mfe,</if>
            <if test="tailingGradeMfe != null">tailing_grade_mfe,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rawOreProcessingVolume != null">#{rawOreProcessingVolume},</if>
            <if test="drySeparationVolume != null">#{drySeparationVolume},</if>
            <if test="grindingFeedVolume != null">#{grindingFeedVolume},</if>
            <if test="rawOreGradeTfe != null">#{rawOreGradeTfe},</if>
            <if test="concentrateGrade != null">#{concentrateGrade},</if>
            <if test="tailingGradeTfe != null">#{tailingGradeTfe},</if>
            <if test="concentrateVolume != null">#{concentrateVolume},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="concentrateFineness != null">#{concentrateFineness},</if>
            <if test="ironConcentrateMoisture != null">#{ironConcentrateMoisture},</if>
            <if test="comprehensiveRatio != null">#{comprehensiveRatio},</if>
            <if test="grindingRatio != null">#{grindingRatio},</if>
            <if test="tailingsAgitatorTank != null">#{tailingsAgitatorTank},</if>
            <if test="overflowOfTailings != null">#{overflowOfTailings},</if>
            <if test="rawOreGradeMfe != null">#{rawOreGradeMfe},</if>
            <if test="tailingGradeMfe != null">#{tailingGradeMfe},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePlanMineralMonthly" parameterType="PlanMineralMonthly">
        update plan_mineral_monthly
        <trim prefix="SET" suffixOverrides=",">
            <if test="rawOreProcessingVolume != null">raw_ore_processing_volume = #{rawOreProcessingVolume},</if>
            <if test="drySeparationVolume != null">dry_separation_volume = #{drySeparationVolume},</if>
            <if test="grindingFeedVolume != null">grinding_feed_volume = #{grindingFeedVolume},</if>
            <if test="rawOreGradeTfe != null">raw_ore_grade_tfe = #{rawOreGradeTfe},</if>
            <if test="concentrateGrade != null">concentrate_grade = #{concentrateGrade},</if>
            <if test="tailingGradeTfe != null">tailing_grade_tfe = #{tailingGradeTfe},</if>
            <if test="concentrateVolume != null">concentrate_volume = #{concentrateVolume},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="concentrateFineness != null">concentrate_fineness = #{concentrateFineness},</if>
            <if test="ironConcentrateMoisture != null">iron_concentrate_moisture = #{ironConcentrateMoisture},</if>
            <if test="comprehensiveRatio != null">comprehensive_ratio = #{comprehensiveRatio},</if>
            <if test="grindingRatio != null">grinding_ratio = #{grindingRatio},</if>
            <if test="tailingsAgitatorTank != null">tailings_agitator_tank = #{tailingsAgitatorTank},</if>
            <if test="overflowOfTailings != null">overflow_of_tailings = #{overflowOfTailings},</if>
            <if test="rawOreGradeMfe != null">raw_ore_grade_mfe = #{rawOreGradeMfe},</if>
            <if test="tailingGradeMfe != null">tailing_grade_mfe = #{tailingGradeMfe},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanMineralMonthlyById" parameterType="Long">
        delete from plan_mineral_monthly where id = #{id}
    </delete>

    <delete id="deletePlanMineralMonthlyByIds" parameterType="String">
        delete from plan_mineral_monthly where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>