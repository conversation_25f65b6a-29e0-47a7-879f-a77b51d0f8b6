package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 车辆报警部门分布VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleAlarmDepartmentDistributionVO {
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 报警数量
     */
    private Long alarmCount;
    
    /**
     * 占比百分比
     */
    private BigDecimal percentage;
    
    /**
     * 部门代码
     */
    private String departmentCode;
}
