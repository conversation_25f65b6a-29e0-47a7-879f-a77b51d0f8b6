package com.ruoyi.lxbi.mapper;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.domain.DataSupport;
import com.ruoyi.lxbi.domain.response.DataSupportVo;
import org.apache.ibatis.annotations.Param;

/**
 * 支护数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface DataSupportMapper 
{
    /**
     * 查询支护数据
     * 
     * @param id 支护数据主键
     * @return 支护数据
     */
    public DataSupport selectDataSupportById(Long id);

    /**
     * 查询支护数据列表
     *
     * @param dataSupport 支护数据
     * @return 支护数据集合
     */
    public List<DataSupport> selectDataSupportList(DataSupport dataSupport);

    /**
     * 查询支护数据列表（含关联字段翻译）
     *
     * @param dataSupport 支护数据
     * @return 支护数据集合（含关联字段翻译）
     */
    public List<DataSupportVo> selectDataSupportVoList(DataSupport dataSupport);

    /**
     * 新增支护数据
     * 
     * @param dataSupport 支护数据
     * @return 结果
     */
    public int insertDataSupport(DataSupport dataSupport);

    /**
     * 修改支护数据
     * 
     * @param dataSupport 支护数据
     * @return 结果
     */
    public int updateDataSupport(DataSupport dataSupport);

    /**
     * 删除支护数据
     * 
     * @param id 支护数据主键
     * @return 结果
     */
    public int deleteDataSupportById(Long id);

    /**
     * 批量删除支护数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataSupportByIds(Long[] ids);

    /**
     * 根据作业日期查询支护数据列表（含关联字段翻译）
     *
     * @param operationDate 作业日期
     * @return 支护数据集合（含关联字段翻译）
     */
    public List<DataSupportVo> selectDataSupportVoByOperationDate(@Param("operationDate") Date operationDate);

    /**
     * 批量插入支护数据
     *
     * @param dataSupportList 支护数据列表
     * @return 结果
     */
    public int batchInsertDataSupport(@Param("list") List<DataSupport> dataSupportList);

    /**
     * 批量更新支护数据
     *
     * @param dataSupportList 支护数据列表
     * @return 结果
     */
    public int batchUpdateDataSupport(@Param("list") List<DataSupport> dataSupportList);
}
