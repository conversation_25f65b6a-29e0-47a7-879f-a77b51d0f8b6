package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysLayoutConfigMapper;
import com.ruoyi.system.domain.SysLayoutConfig;
import com.ruoyi.system.service.ISysLayoutConfigService;

/**
 * 布局配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
public class SysLayoutConfigServiceImpl implements ISysLayoutConfigService 
{
    @Autowired
    private SysLayoutConfigMapper sysLayoutConfigMapper;

    /**
     * 查询布局配置
     * 
     * @param id 布局配置主键
     * @return 布局配置
     */
    @Override
    public SysLayoutConfig selectSysLayoutConfigById(Long id)
    {
        return sysLayoutConfigMapper.selectSysLayoutConfigById(id);
    }

    /**
     * 查询布局配置列表
     * 
     * @param sysLayoutConfig 布局配置
     * @return 布局配置
     */
    @Override
    public List<SysLayoutConfig> selectSysLayoutConfigList(SysLayoutConfig sysLayoutConfig)
    {
        return sysLayoutConfigMapper.selectSysLayoutConfigList(sysLayoutConfig);
    }

    /**
     * 新增布局配置
     * 
     * @param sysLayoutConfig 布局配置
     * @return 结果
     */
    @Override
    public int insertSysLayoutConfig(SysLayoutConfig sysLayoutConfig)
    {
        sysLayoutConfig.setCreateTime(DateUtils.getNowDate());
        return sysLayoutConfigMapper.insertSysLayoutConfig(sysLayoutConfig);
    }

    /**
     * 修改布局配置
     * 
     * @param sysLayoutConfig 布局配置
     * @return 结果
     */
    @Override
    public int updateSysLayoutConfig(SysLayoutConfig sysLayoutConfig)
    {
        sysLayoutConfig.setUpdateTime(DateUtils.getNowDate());
        return sysLayoutConfigMapper.updateSysLayoutConfig(sysLayoutConfig);
    }

    /**
     * 批量删除布局配置
     * 
     * @param ids 需要删除的布局配置主键
     * @return 结果
     */
    @Override
    public int deleteSysLayoutConfigByIds(Long[] ids)
    {
        return sysLayoutConfigMapper.deleteSysLayoutConfigByIds(ids);
    }

    /**
     * 删除布局配置信息
     * 
     * @param id 布局配置主键
     * @return 结果
     */
    @Override
    public int deleteSysLayoutConfigById(Long id)
    {
        return sysLayoutConfigMapper.deleteSysLayoutConfigById(id);
    }
}
