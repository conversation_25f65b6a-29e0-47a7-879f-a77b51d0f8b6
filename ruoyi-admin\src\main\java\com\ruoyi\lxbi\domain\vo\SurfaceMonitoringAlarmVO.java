package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 地表监测报警事件VO
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurfaceMonitoringAlarmVO {

    /**
     * 报警ID
     */
    private Long alarmId;

    /**
     * 站点ID
     */
    private Long stationId;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 站点编号
     */
    private String stationCode;

    /**
     * 报警类型
     */
    private String alarmType;

    /**
     * 报警等级
     */
    private String alarmLevel;

    /**
     * 报警标题
     */
    private String alarmTitle;

    /**
     * 报警描述
     */
    private String alarmDescription;

    /**
     * 报警时间
     */
    private String alarmTime;

    /**
     * 触发值
     */
    private BigDecimal triggerValue;

    /**
     * 阈值
     */
    private BigDecimal thresholdValue;

    /**
     * 单位
     */
    private String unit;

    /**
     * X偏移量(mm)
     */
    private BigDecimal xOffset;

    /**
     * Y偏移量(mm)
     */
    private BigDecimal yOffset;

    /**
     * H偏移量(mm)
     */
    private BigDecimal hOffset;

    /**
     * 总偏移量(mm)
     */
    private BigDecimal totalOffset;

    /**
     * 处理状态
     */
    private String handleStatus;

    /**
     * 处理人员
     */
    private String handlePerson;

    /**
     * 处理时间
     */
    private String handleTime;

    /**
     * 处理备注
     */
    private String handleRemark;

    /**
     * 是否已确认
     */
    private Boolean isConfirmed;

    /**
     * 确认时间
     */
    private String confirmTime;

    /**
     * 确认人员
     */
    private String confirmPerson;
}
