package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataMuckingOut;
import com.ruoyi.lxbi.domain.response.DataMuckingOutVo;
import com.ruoyi.lxbi.domain.request.DataMuckingOutBatchDto;
import com.ruoyi.lxbi.service.IDataMuckingOutService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出矿数据Controller
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/data/out")
public class DataMuckingOutController extends BaseController {
    @Autowired
    private IDataMuckingOutService dataMuckingOutService;

    /**
     * 查询出矿数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:out:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataMuckingOut dataMuckingOut) {
        startPage();
        List<DataMuckingOutVo> list = dataMuckingOutService.selectDataMuckingOutList(dataMuckingOut);
        return getDataTable(list);
    }

    /**
     * 导出出矿数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:out:export')")
    @Log(title = "出矿数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataMuckingOut dataMuckingOut) {
        List<DataMuckingOutVo> list = dataMuckingOutService.selectDataMuckingOutList(dataMuckingOut);
        ExcelUtil<DataMuckingOutVo> util = new ExcelUtil<DataMuckingOutVo>(DataMuckingOutVo.class);
        util.exportExcel(response, list, "出矿数据数据");
    }

    /**
     * 获取出矿数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:out:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataMuckingOutService.selectDataMuckingOutById(id));
    }

    /**
     * 新增出矿数据
     */
    @PreAuthorize("@ss.hasPermi('data:out:add')")
    @Log(title = "出矿数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataMuckingOut dataMuckingOut)
    {
        return toAjax(dataMuckingOutService.insertDataMuckingOut(dataMuckingOut));
    }

    /**
     * 修改出矿数据
     */
    @PreAuthorize("@ss.hasPermi('data:out:edit')")
    @Log(title = "出矿数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataMuckingOut dataMuckingOut)
    {
        return toAjax(dataMuckingOutService.updateDataMuckingOut(dataMuckingOut));
    }

    /**
     * 删除出矿数据
     */
    @PreAuthorize("@ss.hasPermi('data:out:remove')")
    @Log(title = "出矿数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataMuckingOutService.deleteDataMuckingOutByIds(ids));
    }

    /**
     * 批量保存出矿数据（增删改查）
     * 传入批量列表，验证是否同一个日期和项目部的数据，然后查询这个日期和项目部的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('data:out:edit')")
    @Log(title = "出矿数据批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<DataMuckingOutBatchDto> batchDataList)
    {
        try {
            int result = dataMuckingOutService.batchSaveDataMuckingOut(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
