package com.ruoyi.lxbi.admin.service;

import java.util.List;
import com.ruoyi.lxbi.admin.domain.KafkaPeopleSos;

/**
 * 人员求救数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IKafkaPeopleSosService 
{
    /**
     * 查询人员求救数据
     * 
     * @param id 人员求救数据主键
     * @return 人员求救数据
     */
    public KafkaPeopleSos selectKafkaPeopleSosById(Long id);

    /**
     * 查询人员求救数据列表
     * 
     * @param kafkaPeopleSos 人员求救数据
     * @return 人员求救数据集合
     */
    public List<KafkaPeopleSos> selectKafkaPeoplesosList(KafkaPeopleSos kafkaPeopleSos);

    /**
     * 新增人员求救数据
     * 
     * @param kafkaPeopleSos 人员求救数据
     * @return 结果
     */
    public int insertKafkaPeopleSos(KafkaPeopleSos kafkaPeopleSos);

    /**
     * 修改人员求救数据
     * 
     * @param kafkaPeopleSos 人员求救数据
     * @return 结果
     */
    public int updateKafkaPeopleSos(KafkaPeopleSos kafkaPeopleSos);

    /**
     * 批量删除人员求救数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaPeoplesosByIds(Long[] ids);

    /**
     * 删除人员求救数据信息
     * 
     * @param id 人员求救数据主键
     * @return 结果
     */
    public int deleteKafkaPeopleSosById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     * 根据人员卡编码和求救开始时间唯一性，如果存在则更新，不存在则新增
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaPeopleSos parseKafkaMessage(String kafkaMessage);

    /**
     * 统计指定日期范围内的求救次数
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 求救次数
     */
    public Long countSosCallsByDateRange(String startDate, String endDate);

    /**
     * 统计指定日期范围内的求救人员数量
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 求救人员数量
     */
    public Long countSosPersonnelByDateRange(String startDate, String endDate);

    /**
     * 查询指定日期范围内的求救记录
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 求救记录列表
     */
    public List<KafkaPeopleSos> selectSosRecordsByDateRange(String startDate, String endDate, Integer limit);

    /**
     * 按日期统计求救次数（用于趋势分析）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期和求救次数的映射
     */
    public List<java.util.Map<String, Object>> selectSosCountByDate(String startDate, String endDate);

    /**
     * 获取最近的求救记录（用于实时监控）
     * 
     * @param limit 限制数量
     * @return 最近的求救记录
     */
    public List<KafkaPeopleSos> selectRecentSosRecords(Integer limit);

    /**
     * 统计各区域的求救次数分布
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 区域求救统计列表
     */
    public List<java.util.Map<String, Object>> selectSosDistributionByArea(String startDate, String endDate);
}
