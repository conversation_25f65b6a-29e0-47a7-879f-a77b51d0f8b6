package com.ruoyi.lxbi.admin.mapper;

import java.util.List;
import com.ruoyi.lxbi.admin.domain.ApiHiddenTroubleRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 隐患数据记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-23
 */
@Mapper
public interface ApiHiddenTroubleRecordMapper
{
    /**
     * 查询隐患数据记录
     * 
     * @param id 隐患数据记录主键
     * @return 隐患数据记录
     */
    public ApiHiddenTroubleRecord selectApiHiddenTroubleRecordById(Long id);

    /**
     * 查询隐患数据记录列表
     * 
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 隐患数据记录集合
     */
    public List<ApiHiddenTroubleRecord> selectApiHiddenTroubleRecordList(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * 新增隐患数据记录
     * 
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    public int insertApiHiddenTroubleRecord(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * 修改隐患数据记录
     * 
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    public int updateApiHiddenTroubleRecord(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * 删除隐患数据记录
     * 
     * @param id 隐患数据记录主键
     * @return 结果
     */
    public int deleteApiHiddenTroubleRecordById(Long id);

    /**
     * 批量删除隐患数据记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApiHiddenTroubleRecordByIds(Long[] ids);

    /**
     * 根据通知编号查询隐患数据记录
     *
     * @param noticeNumber 通知编号
     * @return 隐患数据记录
     */
    public ApiHiddenTroubleRecord selectByNoticeNumber(@Param("noticeNumber") String noticeNumber);

    /**
     * 根据通知编号和隐患日期查询隐患数据记录
     *
     * @param noticeNumber 通知编号
     * @param troubleDate 隐患日期
     * @return 隐患数据记录
     */
    public ApiHiddenTroubleRecord selectByNoticeNumberAndDate(@Param("noticeNumber") String noticeNumber, @Param("troubleDate") java.util.Date troubleDate);

    /**
     * 根据通知编号更新隐患数据记录
     *
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    public int updateByNoticeNumber(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * 根据通知编号和隐患日期更新隐患数据记录
     *
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    public int updateByNoticeNumberAndDate(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * UPSERT操作（根据通知编号插入或更新）
     *
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    public int upsertApiHiddenTroubleRecord(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * 按状态统计隐患数量
     *
     * @return 统计结果
     */
    public List<java.util.Map<String, Object>> countByStatus();

    /**
     * 按部门统计隐患数量
     *
     * @return 统计结果
     */
    public List<java.util.Map<String, Object>> countByDepartment();

    /**
     * 按隐患等级统计数量
     *
     * @return 统计结果
     */
    public List<java.util.Map<String, Object>> countByGrade();
}
