package com.ruoyi.lxbi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.lxbi.domain.ApiAiAlarm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI报警数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Mapper
public interface ApiAiAlarmMapper extends BaseMapper<ApiAiAlarm> {

    /**
     * 根据外部ID查询报警数据
     * 
     * @param externalId 外部ID
     * @return 报警数据
     */
    ApiAiAlarm selectByExternalId(@Param("externalId") String externalId);

    /**
     * 根据时间范围查询报警数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警数据列表
     */
    List<ApiAiAlarm> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 根据报警类型查询数据
     * 
     * @param alarmType 报警类型
     * @return 报警数据列表
     */
    List<ApiAiAlarm> selectByAlarmType(@Param("alarmType") String alarmType);

    /**
     * 根据设备ID查询数据
     * 
     * @param deviceId 设备ID
     * @return 报警数据列表
     */
    List<ApiAiAlarm> selectByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 批量插入报警数据
     * 
     * @param alarmList 报警数据列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<ApiAiAlarm> alarmList);

    /**
     * 根据外部ID更新数据
     * 
     * @param alarm 报警数据
     * @return 更新数量
     */
    int updateByExternalId(ApiAiAlarm alarm);

    /**
     * 删除指定时间之前的数据
     * 
     * @param beforeTime 时间点
     * @return 删除数量
     */
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 统计指定时间范围内的报警数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警数量
     */
    Long countByTimeRange(@Param("startTime") LocalDateTime startTime, 
                          @Param("endTime") LocalDateTime endTime);

    /**
     * 根据同步状态查询数据
     * 
     * @param syncStatus 同步状态
     * @return 报警数据列表
     */
    List<ApiAiAlarm> selectBySyncStatus(@Param("syncStatus") String syncStatus);

    /**
     * 获取最新的同步时间
     * 
     * @return 最新同步时间
     */
    LocalDateTime getLatestSyncTime();
}
