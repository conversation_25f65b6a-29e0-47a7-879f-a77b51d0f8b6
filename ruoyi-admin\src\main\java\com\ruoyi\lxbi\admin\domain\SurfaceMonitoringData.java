package com.ruoyi.lxbi.admin.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 地表监测数据对象 surface_monitoring_data
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
public class SurfaceMonitoringData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date date;

    /** 基站名称 */
    @Excel(name = "基站名称")
    private String stationName;

    /** 终端编号 */
    @Excel(name = "终端编号")
    private String wgbh;

    /** x偏移在0-2.5mm的次数占比 */
    @Excel(name = "x偏移在0-2.5mm的次数占比")
    private Integer x1;

    /** x偏移在2.5-5mm的次数占比 */
    @Excel(name = "x偏移在2.5-5mm的次数占比")
    private Integer x2;

    /** x偏移在5-10mm的次数占比 */
    @Excel(name = "x偏移在5-10mm的次数占比")
    private Integer x3;

    /** x偏移>10mm的次数占比 */
    @Excel(name = "x偏移>10mm的次数占比")
    private Integer x4;

    /** y偏移在0-2.5mm的次数占比 */
    @Excel(name = "y偏移在0-2.5mm的次数占比")
    private Integer y1;

    /** y偏移在2.5-5mm的次数占比 */
    @Excel(name = "y偏移在2.5-5mm的次数占比")
    private Integer y2;

    /** y偏移在5-10mm的次数占比 */
    @Excel(name = "y偏移在5-10mm的次数占比")
    private Integer y3;

    /** y偏移>10mm的次数占比 */
    @Excel(name = "y偏移>10mm的次数占比")
    private Integer y4;

    /** 高度偏移在0-2.5mm的次数占比 */
    @Excel(name = "高度偏移在0-2.5mm的次数占比")
    private Integer h1;

    /** 高度偏移在2.5-5mm的次数占比 */
    @Excel(name = "高度偏移在2.5-5mm的次数占比")
    private Integer h2;

    /** 高度偏移在5-10mm的次数占比 */
    @Excel(name = "高度偏移在5-10mm的次数占比")
    private Integer h3;

    /** 高度偏移>10mm的次数占比 */
    @Excel(name = "高度偏移>10mm的次数占比")
    private Integer h4;

    /** 当日y偏移距离总和 */
    @Excel(name = "当日y偏移距离总和")
    private BigDecimal ystackedTotalOffset;

    /** 当日高度偏移距离总和 */
    @Excel(name = "当日高度偏移距离总和")
    private BigDecimal hstackedTotalOffset;

    /** 当日x偏移距离总和 */
    @Excel(name = "当日x偏移距离总和")
    private BigDecimal xstackedTotalOffset;

    /** 原始数据ID */
    private Integer originalId;

    /** 原始数据(JSON格式) */
    private String originalData;

    /** 数据来源 */
    private String dataSource;

    /** 同步时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDate(Date date) 
    {
        this.date = date;
    }

    public Date getDate() 
    {
        return date;
    }

    public void setStationName(String stationName) 
    {
        this.stationName = stationName;
    }

    public String getStationName() 
    {
        return stationName;
    }

    public void setWgbh(String wgbh) 
    {
        this.wgbh = wgbh;
    }

    public String getWgbh() 
    {
        return wgbh;
    }

    public void setX1(Integer x1) 
    {
        this.x1 = x1;
    }

    public Integer getX1() 
    {
        return x1;
    }

    public void setX2(Integer x2) 
    {
        this.x2 = x2;
    }

    public Integer getX2() 
    {
        return x2;
    }

    public void setX3(Integer x3) 
    {
        this.x3 = x3;
    }

    public Integer getX3() 
    {
        return x3;
    }

    public void setX4(Integer x4) 
    {
        this.x4 = x4;
    }

    public Integer getX4() 
    {
        return x4;
    }

    public void setY1(Integer y1) 
    {
        this.y1 = y1;
    }

    public Integer getY1() 
    {
        return y1;
    }

    public void setY2(Integer y2) 
    {
        this.y2 = y2;
    }

    public Integer getY2() 
    {
        return y2;
    }

    public void setY3(Integer y3) 
    {
        this.y3 = y3;
    }

    public Integer getY3() 
    {
        return y3;
    }

    public void setY4(Integer y4) 
    {
        this.y4 = y4;
    }

    public Integer getY4() 
    {
        return y4;
    }

    public void setH1(Integer h1) 
    {
        this.h1 = h1;
    }

    public Integer getH1() 
    {
        return h1;
    }

    public void setH2(Integer h2) 
    {
        this.h2 = h2;
    }

    public Integer getH2() 
    {
        return h2;
    }

    public void setH3(Integer h3) 
    {
        this.h3 = h3;
    }

    public Integer getH3() 
    {
        return h3;
    }

    public void setH4(Integer h4) 
    {
        this.h4 = h4;
    }

    public Integer getH4() 
    {
        return h4;
    }

    public void setYstackedTotalOffset(BigDecimal ystackedTotalOffset) 
    {
        this.ystackedTotalOffset = ystackedTotalOffset;
    }

    public BigDecimal getYstackedTotalOffset() 
    {
        return ystackedTotalOffset;
    }

    public void setHstackedTotalOffset(BigDecimal hstackedTotalOffset) 
    {
        this.hstackedTotalOffset = hstackedTotalOffset;
    }

    public BigDecimal getHstackedTotalOffset() 
    {
        return hstackedTotalOffset;
    }

    public void setXstackedTotalOffset(BigDecimal xstackedTotalOffset) 
    {
        this.xstackedTotalOffset = xstackedTotalOffset;
    }

    public BigDecimal getXstackedTotalOffset() 
    {
        return xstackedTotalOffset;
    }

    public void setOriginalId(Integer originalId) 
    {
        this.originalId = originalId;
    }

    public Integer getOriginalId() 
    {
        return originalId;
    }

    public void setOriginalData(String originalData) 
    {
        this.originalData = originalData;
    }

    public String getOriginalData() 
    {
        return originalData;
    }

    public void setDataSource(String dataSource) 
    {
        this.dataSource = dataSource;
    }

    public String getDataSource() 
    {
        return dataSource;
    }

    public void setSyncTime(Date syncTime) 
    {
        this.syncTime = syncTime;
    }

    public Date getSyncTime() 
    {
        return syncTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("date", getDate())
            .append("stationName", getStationName())
            .append("wgbh", getWgbh())
            .append("x1", getX1())
            .append("x2", getX2())
            .append("x3", getX3())
            .append("x4", getX4())
            .append("y1", getY1())
            .append("y2", getY2())
            .append("y3", getY3())
            .append("y4", getY4())
            .append("h1", getH1())
            .append("h2", getH2())
            .append("h3", getH3())
            .append("h4", getH4())
            .append("ystackedTotalOffset", getYstackedTotalOffset())
            .append("hstackedTotalOffset", getHstackedTotalOffset())
            .append("xstackedTotalOffset", getXstackedTotalOffset())
            .append("originalId", getOriginalId())
            .append("originalData", getOriginalData())
            .append("dataSource", getDataSource())
            .append("syncTime", getSyncTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
