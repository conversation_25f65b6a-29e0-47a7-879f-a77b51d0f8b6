package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.domain.PlanDriftMonthly;
import com.ruoyi.lxbi.domain.request.DataTunnelingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataTunnelingDepartmentWithPlanStats;
import com.ruoyi.lxbi.domain.response.PlanDriftMonthlyVo;
import com.ruoyi.lxbi.domain.table.TunnelingDailyReportTableVo;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;
import com.ruoyi.lxbi.service.IDataTunnelingStatsService;
import com.ruoyi.lxbi.service.IPlanDriftMonthlyService;
import com.ruoyi.lxbi.table.params.TunnelingDailyReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 掘进数据日报表格处理器
 */
@Component
public class TunnelingDailyReportTableHandler extends BaseTableHandler<TunnelingDailyReportTableVo, TunnelingDailyReportQueryParams> {

    @Autowired
    private IDataTunnelingStatsService dataTunnelingStatsService;

    @Autowired
    private IPlanDriftMonthlyService planDriftMonthlyService;

    @Autowired
    private IBaseProjectDepartmentService baseProjectDepartmentService;

    @Override
    public List<TunnelingDailyReportTableVo> queryTableData(TunnelingDailyReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date operationDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取月度计划数据
        MonthlyPlansResult monthlyPlans = getMonthlyPlans(operationDate);

        // 获取月累计数据和当日数据（一次查询获取）
        MonthlyStatsResult monthlyResult = getMonthlyAccumulatedWithDaily(operationDate);

        // 构建表格数据
        return buildTableData(monthlyResult, monthlyPlans);
    }

    /**
     * 获取月度计划数据（按财务月：上月29号到本月28号）
     */
    private MonthlyPlansResult getMonthlyPlans(Date operationDate) {
        // 计算财务月
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        // 获取掘进月度计划
        PlanDriftMonthly driftQuery = new PlanDriftMonthly();
        driftQuery.setPlanDate(financialMonth);
        List<PlanDriftMonthlyVo> driftPlans = planDriftMonthlyService.selectPlanDriftMonthlyList(driftQuery);

        // 按项目部门ID汇总掘进计划
        Map<Long, BigDecimal> driftPlansByDepartment = driftPlans.stream()
                .filter(plan -> plan.getDriftMeter() != null)
                .collect(Collectors.groupingBy(
                        PlanDriftMonthly::getProjectDepartmentId,
                        Collectors.reducing(BigDecimal.ZERO,
                                PlanDriftMonthly::getDriftMeter,
                                BigDecimal::add)
                ));

        return new MonthlyPlansResult(driftPlansByDepartment);
    }

    /**
     * 获取月累计数据和当日数据（按财务月：上月29号到本月28号）
     */
    private MonthlyStatsResult getMonthlyAccumulatedWithDaily(Date operationDate) {
        // 获取财务月的开始日期到查询日期的累计数据
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(operationDate);

        DataTunnelingStatsRequest request = new DataTunnelingStatsRequest();
        request.setStartDate(financialMonthStart);
        request.setEndDate(operationDate);

        // 获取掘进统计数据
        List<DataTunnelingDepartmentWithPlanStats> monthlyStats = dataTunnelingStatsService.selectDepartmentWithPlanStatsList(request, "daily");
        List<DataTunnelingDepartmentWithPlanStats> dailyStats = monthlyStats.stream()
                .filter(stats -> FinancialDateUtils.isSameDay(stats.getOperationDate(), operationDate))
                .collect(Collectors.toList());

        // 按项目部门ID汇总累计数据
        Map<Long, BigDecimal> monthlyAccumulated = monthlyStats.stream()
                .collect(Collectors.groupingBy(
                        DataTunnelingDepartmentWithPlanStats::getProjectDepartmentId,
                        Collectors.summingDouble(stats -> stats.getTotalTunnelingLength() != null ? stats.getTotalTunnelingLength() : 0.0)
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> BigDecimal.valueOf(entry.getValue())
                ));

        return new MonthlyStatsResult(dailyStats, monthlyAccumulated);
    }

    /**
     * 构建表格数据
     */
    private List<TunnelingDailyReportTableVo> buildTableData(MonthlyStatsResult monthlyResult, MonthlyPlansResult monthlyPlans) {
        List<TunnelingDailyReportTableVo> result = new ArrayList<>();

        // 构建掘进数据
        buildTunnelingData(result, "总计", "巷道掘进",
                monthlyResult.getDailyStats(),
                monthlyResult.getMonthlyAccumulated(),
                monthlyPlans.getDriftPlansByDepartment());

        return result;
    }

    /**
     * 构建掘进数据
     */
    private void buildTunnelingData(List<TunnelingDailyReportTableVo> result,
                                    String serialNumber, String typeName,
                                    List<DataTunnelingDepartmentWithPlanStats> dailyStats,
                                    Map<Long, BigDecimal> monthlyAccumulated,
                                    Map<Long, BigDecimal> monthlyPlans) {

        // 计算总计数据
        BigDecimal totalDailyOutput = BigDecimal.ZERO;
        BigDecimal totalMonthlyPlan = BigDecimal.ZERO;
        BigDecimal totalMonthlyAccumulated = BigDecimal.ZERO;

        // 按项目部门ID分组，避免重复
        Map<Long, DataTunnelingDepartmentWithPlanStats> departmentStatsMap = dailyStats.stream()
                .collect(Collectors.toMap(
                        DataTunnelingDepartmentWithPlanStats::getProjectDepartmentId,
                        stats -> stats,
                        (existing, replacement) -> {
                            // 如果有重复，累加掘进长度
                            existing.setTotalTunnelingLength((existing.getTotalTunnelingLength() != null ? existing.getTotalTunnelingLength() : 0.0) +
                                    (replacement.getTotalTunnelingLength() != null ? replacement.getTotalTunnelingLength() : 0.0));
                            return existing;
                        }
                ));

        // 获取所有涉及的项目部门ID（包括有计划但当日无产量的）
        Set<Long> allDepartmentIds = new HashSet<>();
        allDepartmentIds.addAll(departmentStatsMap.keySet());
        allDepartmentIds.addAll(monthlyPlans.keySet());
        allDepartmentIds.addAll(monthlyAccumulated.keySet());

        // 批量获取项目部门名称
        Map<Long, String> departmentNameMap = getDepartmentNameMap(allDepartmentIds);

        int subSerialNumber = 1;
        for (Long departmentId : allDepartmentIds) {
            DataTunnelingDepartmentWithPlanStats stats = departmentStatsMap.get(departmentId);
            BigDecimal dailyOutput = stats != null && stats.getTotalTunnelingLength() != null ?
                    BigDecimal.valueOf(stats.getTotalTunnelingLength()) : BigDecimal.ZERO;
            BigDecimal monthlyPlan = monthlyPlans.getOrDefault(departmentId, BigDecimal.ZERO);
            BigDecimal accumulated = monthlyAccumulated.getOrDefault(departmentId, BigDecimal.ZERO);

            TunnelingDailyReportTableVo vo = new TunnelingDailyReportTableVo();
            vo.setSerialNumber(String.valueOf(subSerialNumber++));
            vo.setName(getDepartmentNameFromMap(departmentId, stats, departmentNameMap));
            vo.setSubName(getDepartmentNameFromMap(departmentId, stats, departmentNameMap));
            vo.setUnit("m");

            // 设置月计划，如果为0则显示"/"
            if (monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                vo.setMonthlyPlan(monthlyPlan);
            } else {
                vo.setMonthlyPlan(null); // 前端可以根据null显示"/"
            }

            vo.setDailyOutput(dailyOutput);
            vo.setMonthlyAccumulated(accumulated);

            // 计算完成率
            if (monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = accumulated.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                vo.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
            } else {
                vo.setCompletionRate("　");
            }

            // 计算超欠量
            if (monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                vo.setMonthlyOverUnder(accumulated.subtract(monthlyPlan));
            } else {
                vo.setMonthlyOverUnder(null); // 前端可以根据null显示"　"
            }

            result.add(vo);

            // 累加总计
            totalDailyOutput = totalDailyOutput.add(dailyOutput);
            totalMonthlyPlan = totalMonthlyPlan.add(monthlyPlan);
            totalMonthlyAccumulated = totalMonthlyAccumulated.add(accumulated);
        }

        // 添加总计行
        if (!result.isEmpty() || totalMonthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
            TunnelingDailyReportTableVo summary = new TunnelingDailyReportTableVo();
            summary.setBold(List.of("serialNumber", "name", "monthlyPlan", "dailyOutput", "monthlyAccumulated", "completionRate", "monthlyOverUnder"));
            summary.setSerialNumber(serialNumber);
            summary.setName(typeName);
            summary.setSubName(typeName);
            summary.setUnit("m");
            summary.setMonthlyPlan(totalMonthlyPlan);
            summary.setDailyOutput(totalDailyOutput);
            summary.setMonthlyAccumulated(totalMonthlyAccumulated);

            // 计算总完成率
            if (totalMonthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = totalMonthlyAccumulated.divide(totalMonthlyPlan, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                summary.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
                summary.setMonthlyOverUnder(totalMonthlyAccumulated.subtract(totalMonthlyPlan));
            } else {
                summary.setCompletionRate("　");
                summary.setMonthlyOverUnder(null); // 前端可以根据null显示"　"
            }

            // 插入到当前类型数据的开头
            int insertIndex = result.size() - allDepartmentIds.size();
            if (insertIndex < 0) insertIndex = 0;
            result.add(insertIndex, summary);
        }
    }

    /**
     * 批量获取项目部门名称映射
     */
    private Map<Long, String> getDepartmentNameMap(Set<Long> departmentIds) {
        Map<Long, String> departmentNameMap = new HashMap<>();

        if (departmentIds == null || departmentIds.isEmpty()) {
            return departmentNameMap;
        }

        // 批量查询项目部门信息
        for (Long departmentId : departmentIds) {
            try {
                BaseProjectDepartment department = baseProjectDepartmentService.selectBaseProjectDepartmentByProjectDepartmentId(departmentId);
                if (department != null && department.getProjectDepartmentName() != null) {
                    departmentNameMap.put(departmentId, department.getProjectDepartmentName());
                }
            } catch (Exception e) {
                // 查询失败时记录日志，但不影响整体流程
                System.err.println("获取项目部门名称失败，项目部门ID: " + departmentId + ", 错误: " + e.getMessage());
            }
        }

        return departmentNameMap;
    }

    /**
     * 从映射中获取项目部门名称
     */
    private String getDepartmentNameFromMap(Long departmentId, DataTunnelingDepartmentWithPlanStats stats, Map<Long, String> departmentNameMap) {
        // 优先使用统计数据中的项目部门名称
        if (stats != null && stats.getProjectDepartmentName() != null && !stats.getProjectDepartmentName().trim().isEmpty()) {
            return stats.getProjectDepartmentName();
        }

        // 其次使用基础数据表中的项目部门名称
        String departmentNameFromBase = departmentNameMap.get(departmentId);
        if (departmentNameFromBase != null && !departmentNameFromBase.trim().isEmpty()) {
            return departmentNameFromBase;
        }

        // 最后使用默认格式（但这种情况应该很少出现）
        return "未知项目部门-" + departmentId;
    }

    /**
     * 月度计划结果包装类
     */
    private static class MonthlyPlansResult {
        private final Map<Long, BigDecimal> driftPlansByDepartment;

        public MonthlyPlansResult(Map<Long, BigDecimal> driftPlansByDepartment) {
            this.driftPlansByDepartment = driftPlansByDepartment;
        }

        public Map<Long, BigDecimal> getDriftPlansByDepartment() {
            return driftPlansByDepartment;
        }
    }

    /**
     * 月度统计结果包装类
     */
    private static class MonthlyStatsResult {
        private final List<DataTunnelingDepartmentWithPlanStats> dailyStats;
        private final Map<Long, BigDecimal> monthlyAccumulated;

        public MonthlyStatsResult(List<DataTunnelingDepartmentWithPlanStats> dailyStats,
                                  Map<Long, BigDecimal> monthlyAccumulated) {
            this.dailyStats = dailyStats;
            this.monthlyAccumulated = monthlyAccumulated;
        }

        public List<DataTunnelingDepartmentWithPlanStats> getDailyStats() {
            return dailyStats;
        }

        public Map<Long, BigDecimal> getMonthlyAccumulated() {
            return monthlyAccumulated;
        }
    }
}
