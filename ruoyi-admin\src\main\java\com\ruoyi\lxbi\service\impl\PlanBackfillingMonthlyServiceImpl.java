package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.PlanBackfillingMonthlyMapper;
import com.ruoyi.lxbi.domain.PlanBackfillingMonthly;
import com.ruoyi.lxbi.domain.request.PlanBackfillingMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanBackfillingMonthlyVo;
import com.ruoyi.lxbi.service.IPlanBackfillingMonthlyService;

/**
 * 充填月计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class PlanBackfillingMonthlyServiceImpl implements IPlanBackfillingMonthlyService 
{
    @Autowired
    private PlanBackfillingMonthlyMapper planBackfillingMonthlyMapper;

    /**
     * 查询充填月计划
     * 
     * @param id 充填月计划主键
     * @return 充填月计划
     */
    @Override
    public PlanBackfillingMonthly selectPlanBackfillingMonthlyById(Long id)
    {
        return planBackfillingMonthlyMapper.selectPlanBackfillingMonthlyById(id);
    }

    /**
     * 查询充填月计划列表
     *
     * @param planBackfillingMonthly 充填月计划
     * @return 充填月计划
     */
    @Override
    public List<PlanBackfillingMonthlyVo> selectPlanBackfillingMonthlyList(PlanBackfillingMonthly planBackfillingMonthly)
    {
        return planBackfillingMonthlyMapper.selectPlanBackfillingMonthlyList(planBackfillingMonthly);
    }

    /**
     * 新增充填月计划
     * 
     * @param planBackfillingMonthly 充填月计划
     * @return 结果
     */
    @Override
    public int insertPlanBackfillingMonthly(PlanBackfillingMonthly planBackfillingMonthly)
    {
        planBackfillingMonthly.setCreateTime(DateUtils.getNowDate());
        return planBackfillingMonthlyMapper.insertPlanBackfillingMonthly(planBackfillingMonthly);
    }

    /**
     * 修改充填月计划
     * 
     * @param planBackfillingMonthly 充填月计划
     * @return 结果
     */
    @Override
    public int updatePlanBackfillingMonthly(PlanBackfillingMonthly planBackfillingMonthly)
    {
        planBackfillingMonthly.setUpdateTime(DateUtils.getNowDate());
        return planBackfillingMonthlyMapper.updatePlanBackfillingMonthly(planBackfillingMonthly);
    }

    /**
     * 批量删除充填月计划
     * 
     * @param ids 需要删除的充填月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanBackfillingMonthlyByIds(Long[] ids)
    {
        return planBackfillingMonthlyMapper.deletePlanBackfillingMonthlyByIds(ids);
    }

    /**
     * 删除充填月计划信息
     *
     * @param id 充填月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanBackfillingMonthlyById(Long id)
    {
        return planBackfillingMonthlyMapper.deletePlanBackfillingMonthlyById(id);
    }

    /**
     * 批量保存充填月计划
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePlanBackfillingMonthly(List<PlanBackfillingMonthlyBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new RuntimeException("批量数据不能为空");
        }

        // 验证是否为同一个月份的数据
        String planMonth = batchDataList.get(0).getPlanMonth();
        if (StringUtils.isEmpty(planMonth)) {
            throw new RuntimeException("计划月份不能为空");
        }
        // 验证是否同一个日期和项目部的数据
        Long projectDepartmentId = batchDataList.get(0).getProjectDepartmentId();
        if (projectDepartmentId == null) {
            throw new ServiceException("项目部门不能为空");
        }
        boolean allSameDateAndProject = batchDataList.stream()
                .allMatch(data -> planMonth.equals(data.getPlanMonth())
                        && projectDepartmentId.equals(data.getProjectDepartmentId()));
        if (!allSameDateAndProject) {
            throw new ServiceException("批量数据必须是同一个作业日期和项目部门");
        }

        // 查询该月份的现有数据
        PlanBackfillingMonthly queryParam = new PlanBackfillingMonthly();
        queryParam.setPlanDate(planMonth);
        List<PlanBackfillingMonthly> existingDataList = planBackfillingMonthlyMapper.selectPlanBackfillingMonthlyListForBatch(queryParam);
        Map<Long, PlanBackfillingMonthly> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(PlanBackfillingMonthly::getId, data -> data));

        List<PlanBackfillingMonthly> toInsert = new ArrayList<>();
        List<PlanBackfillingMonthly> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (PlanBackfillingMonthlyBatchDto batchData : batchDataList) {
            if ("add".equals(batchData.getOperationType())) {
                // 新增数据
                PlanBackfillingMonthly newData = new PlanBackfillingMonthly();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if ("edit".equals(batchData.getOperationType()) && batchData.getId() != null) {
                // 更新现有数据
                if (existingDataMap.containsKey(batchData.getId())) {
                    PlanBackfillingMonthly updateData = new PlanBackfillingMonthly();
                    copyProperties(batchData, updateData);
                    updateData.setId(batchData.getId());
                    updateData.setUpdateBy(SecurityUtils.getUsername());
                    updateData.setUpdateTime(DateUtils.getNowDate());
                    toUpdate.add(updateData);
                    toDelete.remove(batchData.getId());
                }
            }
        }

        int totalAffected = 0;

        // 执行批量插入
        if (!toInsert.isEmpty()) {
            totalAffected += planBackfillingMonthlyMapper.batchInsertPlanBackfillingMonthly(toInsert);
        }

        // 执行批量更新
        if (!toUpdate.isEmpty()) {
            for (PlanBackfillingMonthly updateData : toUpdate) {
                totalAffected += planBackfillingMonthlyMapper.updatePlanBackfillingMonthly(updateData);
            }
        }

        // 执行批量删除
        if (!toDelete.isEmpty()) {
            totalAffected += planBackfillingMonthlyMapper.deletePlanBackfillingMonthlyByIds(toDelete.toArray(new Long[0]));
        }

        return totalAffected;
    }

    /**
     * 复制属性
     */
    private void copyProperties(PlanBackfillingMonthlyBatchDto source, PlanBackfillingMonthly target) {
        target.setPlanDate(source.getPlanMonth());
        target.setProjectDepartmentId(source.getProjectDepartmentId());
        target.setWorkingFaceId(source.getWorkingFaceId());
        target.setStopeId(source.getStopeId());
        target.setFillingVolume(source.getFillingVolume());
        if (StringUtils.isNotBlank(source.getRemark())) {
            target.setRemark(source.getRemark());
        }
    }
}
