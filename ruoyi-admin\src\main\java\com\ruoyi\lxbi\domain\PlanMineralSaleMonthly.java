package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 选矿销售月计划对象 plan_mineral_sale_monthly
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanMineralSaleMonthly extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 铁精粉量 */
    @Excel(name = "铁精粉量")
    private BigDecimal ironConcentrateVolume;

    /** 选矿厂矿仓存矿销售 */
    @Excel(name = "选矿厂矿仓存矿销售")
    private BigDecimal concentratorBinsStockVolume;

    /** 入措施井地表存矿销售 */
    @Excel(name = "入措施井地表存矿销售")
    private BigDecimal serviceShaftSurfaceStockVolume;

    /** 原矿品位-TFe */
    @Excel(name = "原矿品位-TFe", suffix = "%")
    private BigDecimal rawOreGrade;

    /** 库存 */
    @Excel(name = "库存")
    private BigDecimal stockVolume;

    /** 计划月份 */
    @Excel(name = "计划月份", sort = 1, mergeByValue = true)
    private String planDate;

}
