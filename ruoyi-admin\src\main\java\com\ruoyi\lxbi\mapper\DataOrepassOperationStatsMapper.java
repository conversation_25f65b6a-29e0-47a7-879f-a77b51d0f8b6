package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationPeriodStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationOrepassStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationDepartmentStats;

/**
 * 溜井放矿数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface DataOrepassOperationStatsMapper 
{
    /**
     * 查询统计数据列表 (日)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据集合
     */
    public List<DataOrepassOperationStats> selectDailyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询统计数据列表 (周)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据集合
     */
    public List<DataOrepassOperationStats> selectWeeklyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询统计数据列表 (月)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据集合
     */
    public List<DataOrepassOperationStats> selectMonthlyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 查询班次统计数据列表 (日)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 班次统计数据集合
     */
    public List<DataOrepassOperationPeriodStats> selectDailyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (周)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 班次统计数据集合
     */
    public List<DataOrepassOperationPeriodStats> selectWeeklyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (月)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 班次统计数据集合
     */
    public List<DataOrepassOperationPeriodStats> selectMonthlyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询溜井统计数据列表 (日)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 溜井统计数据集合
     */
    public List<DataOrepassOperationOrepassStats> selectDailyOrepassStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询溜井统计数据列表 (周)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 溜井统计数据集合
     */
    public List<DataOrepassOperationOrepassStats> selectWeeklyOrepassStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询溜井统计数据列表 (月)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 溜井统计数据集合
     */
    public List<DataOrepassOperationOrepassStats> selectMonthlyOrepassStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (日)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 项目部门统计数据集合
     */
    public List<DataOrepassOperationDepartmentStats> selectDailyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (周)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 项目部门统计数据集合
     */
    public List<DataOrepassOperationDepartmentStats> selectWeeklyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (月)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 项目部门统计数据集合
     */
    public List<DataOrepassOperationDepartmentStats> selectMonthlyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询统计数据列表 (年)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据集合
     */
    public List<DataOrepassOperationStats> selectYearlyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (年)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 班次统计数据集合
     */
    public List<DataOrepassOperationPeriodStats> selectYearlyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询溜井统计数据列表 (年)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 溜井统计数据集合
     */
    public List<DataOrepassOperationOrepassStats> selectYearlyOrepassStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (年)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 项目部门统计数据集合
     */
    public List<DataOrepassOperationDepartmentStats> selectYearlyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

}
