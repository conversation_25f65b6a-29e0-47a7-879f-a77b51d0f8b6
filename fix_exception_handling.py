#!/usr/bin/env python3
"""
修复Service实现类中的异常处理
将返回空结果改为抛出异常
"""

import re
import os

def fix_service_file(file_path):
    """修复Service实现类的异常处理"""
    print(f"正在修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 记录原始内容长度
    original_length = len(content)
    
    # 修复模式1: 返回空ArrayList的异常处理
    pattern1 = r'(\s+)\} catch \(Exception e\) \{\s*log\.error\([^;]+\);\s*return new ArrayList<>\(\);\s*\}'
    replacement1 = r'\1} catch (Exception e) {\n\1    log.error("方法执行失败", e);\n\1    throw new RuntimeException("数据查询失败: " + e.getMessage(), e);\n\1}'
    content = re.sub(pattern1, replacement1, content, flags=re.MULTILINE | re.DOTALL)
    
    # 修复模式2: 返回默认VO对象的异常处理
    pattern2 = r'(\s+)\} catch \(Exception e\) \{\s*log\.error\([^;]+\);\s*return new ([A-Za-z]+VO)\([^)]*\);\s*\}'
    replacement2 = r'\1} catch (Exception e) {\n\1    log.error("方法执行失败", e);\n\1    throw new RuntimeException("数据查询失败: " + e.getMessage(), e);\n\1}'
    content = re.sub(pattern2, replacement2, content, flags=re.MULTILINE | re.DOTALL)
    
    # 修复模式3: 返回new DashboardDataVO()的异常处理
    pattern3 = r'(\s+)\} catch \(Exception e\) \{\s*log\.error\([^;]+\);\s*return new DashboardDataVO\(\);\s*\}'
    replacement3 = r'\1} catch (Exception e) {\n\1    log.error("方法执行失败", e);\n\1    throw new RuntimeException("数据查询失败: " + e.getMessage(), e);\n\1}'
    content = re.sub(pattern3, replacement3, content, flags=re.MULTILINE | re.DOTALL)
    
    # 检查是否有修改
    if len(content) != original_length:
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 文件已修复: {file_path}")
        return True
    else:
        print(f"ℹ️  文件无需修复: {file_path}")
        return False

def main():
    """主函数"""
    # 需要修复的文件
    file_path = 'ruoyi-admin/src/main/java/com/ruoyi/lxbi/admin/service/impl/HiddenTroubleStatServiceImpl.java'
    
    if os.path.exists(file_path):
        if fix_service_file(file_path):
            print(f"\n🎉 修复完成!")
        else:
            print(f"\n📝 无需修复")
    else:
        print(f"❌ 文件不存在: {file_path}")

if __name__ == "__main__":
    main()
