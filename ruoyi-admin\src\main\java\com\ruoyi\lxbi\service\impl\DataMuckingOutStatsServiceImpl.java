package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataMuckingOutStatsRequest;
import com.ruoyi.lxbi.domain.response.DataMuckingOutTotalStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutPeriodStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutDepartmentStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStopeStats;
import com.ruoyi.lxbi.mapper.DataMuckingOutStatsMapper;
import com.ruoyi.lxbi.service.IDataMuckingOutStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 出矿数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DataMuckingOutStatsServiceImpl implements IDataMuckingOutStatsService {
    @Autowired
    private DataMuckingOutStatsMapper dataMuckingOutStatsMapper;
    
    /**
     * 查询总体统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合
     */
    @Override
    public List<DataMuckingOutTotalStats> selectTotalStatsList(DataMuckingOutStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectDailyTotalStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectWeeklyTotalStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectYearlyTotalStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataMuckingOutStatsMapper.selectMonthlyTotalStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    @Override
    public List<DataMuckingOutTotalWithPlanStats> selectTotalWithPlanStatsList(DataMuckingOutStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataMuckingOutStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }
    
    /**
     * 查询详细统计数据列表 (日/周/月/年) - 按作业时段、项目部门、采场
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 详细统计数据集合
     */
    @Override
    public List<DataMuckingOutStats> selectStatsList(DataMuckingOutStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectDailyStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectWeeklyStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectYearlyStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataMuckingOutStatsMapper.selectMonthlyStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    @Override
    public List<DataMuckingOutPeriodStats> selectPeriodStatsList(DataMuckingOutStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectDailyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectWeeklyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectYearlyPeriodStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataMuckingOutStatsMapper.selectMonthlyPeriodStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询项目部门统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合
     */
    @Override
    public List<DataMuckingOutDepartmentStats> selectDepartmentStatsList(DataMuckingOutStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectDailyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectWeeklyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectYearlyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataMuckingOutStatsMapper.selectMonthlyDepartmentStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询采场统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 采场统计数据集合
     */
    @Override
    public List<DataMuckingOutStopeStats> selectStopeStatsList(DataMuckingOutStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectDailyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectWeeklyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataMuckingOutStatsMapper.selectYearlyStopeStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataMuckingOutStatsMapper.selectMonthlyStopeStats(request.getStartDate(), request.getEndDate());
        }
    }

}
