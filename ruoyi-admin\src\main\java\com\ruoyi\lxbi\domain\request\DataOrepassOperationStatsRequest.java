package com.ruoyi.lxbi.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 溜井放矿数据统计请求参数
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class DataOrepassOperationStatsRequest {
    /**
     * 统计类型：daily-日统计, weekly-周统计, monthly-月统计, 
     * daily_period-日班次统计, weekly_period-周班次统计, monthly_period-月班次统计
     */
    private String statsType;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
}
