package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import lombok.Data;

/**
 * 复杂多层表头示例VO
 */
@Data
@TableConfig(code = "complex_table", name = "复杂表头示例", description = "演示复杂多层表头功能")
public class ComplexTableVo {
    
    // 四层表头示例：销售数据/季度/月份/指标
    @TableHeader(label = "销量", order = 1, parentPath = {"销售数据", "Q1", "1月"})
    private String q1m1Sales;
    
    @TableHeader(label = "金额", order = 2, parentPath = {"销售数据", "Q1", "1月"})
    private String q1m1Amount;
    
    @TableHeader(label = "销量", order = 3, parentPath = {"销售数据", "Q1", "2月"})
    private String q1m2Sales;
    
    @TableHeader(label = "金额", order = 4, parentPath = {"销售数据", "Q1", "2月"})
    private String q1m2Amount;
    
    @TableHeader(label = "销量", order = 5, parentPath = {"销售数据", "Q2", "4月"})
    private String q2m4Sales;
    
    @TableHeader(label = "金额", order = 6, parentPath = {"销售数据", "Q2", "4月"})
    private String q2m4Amount;
    
    // 三层表头示例：成本数据/类型/指标
    @TableHeader(label = "人工", order = 7, parentPath = {"成本数据", "直接成本"})
    private String directLabor;
    
    @TableHeader(label = "材料", order = 8, parentPath = {"成本数据", "直接成本"})
    private String directMaterial;
    
    @TableHeader(label = "管理", order = 9, parentPath = {"成本数据", "间接成本"})
    private String indirectManagement;
    
    // 二层表头示例
    @TableHeader(label = "总计", order = 10, parentPath = {"汇总"})
    private String total;
    
    // 一层表头示例
    @TableHeader(label = "备注", order = 11)
    private String remark;
}