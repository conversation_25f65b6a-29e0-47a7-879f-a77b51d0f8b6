package com.ruoyi.lxbi.controller.kafka;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.KafkaVehicleSpeedOver;
import com.ruoyi.lxbi.service.IKafkaVehicleSpeedOverService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 车辆超速告警数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@RestController
@RequestMapping("/kafka/VehicleSpeedOver")
public class KafkaVehicleSpeedOverController extends BaseController {
    @Autowired
    private IKafkaVehicleSpeedOverService kafkaVehicleSpeedOverService;

    /**
     * 查询车辆超速告警数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleSpeedOver:list')")
    @GetMapping("/list")
    public TableDataInfo list(KafkaVehicleSpeedOver kafkaVehicleSpeedOver) {
        startPage();
        List<KafkaVehicleSpeedOver> list = kafkaVehicleSpeedOverService.selectKafkaVehicleSpeedOverList(kafkaVehicleSpeedOver);
        return getDataTable(list);
    }

    /**
     * 导出车辆超速告警数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleSpeedOver:export')")
    @Log(title = "车辆超速告警数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KafkaVehicleSpeedOver kafkaVehicleSpeedOver) {
        List<KafkaVehicleSpeedOver> list = kafkaVehicleSpeedOverService.selectKafkaVehicleSpeedOverList(kafkaVehicleSpeedOver);
        ExcelUtil<KafkaVehicleSpeedOver> util = new ExcelUtil<KafkaVehicleSpeedOver>(KafkaVehicleSpeedOver.class);
        util.exportExcel(response, list, "车辆超速告警数据数据");
    }

    /**
     * 获取车辆超速告警数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleSpeedOver:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kafkaVehicleSpeedOverService.selectKafkaVehicleSpeedOverById(id));
    }

    /**
     * 新增车辆超速告警数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleSpeedOver:add')")
    @Log(title = "车辆超速告警数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KafkaVehicleSpeedOver kafkaVehicleSpeedOver)
    {
        return toAjax(kafkaVehicleSpeedOverService.insertKafkaVehicleSpeedOver(kafkaVehicleSpeedOver));
    }

    /**
     * 修改车辆超速告警数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleSpeedOver:edit')")
    @Log(title = "车辆超速告警数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KafkaVehicleSpeedOver kafkaVehicleSpeedOver)
    {
        return toAjax(kafkaVehicleSpeedOverService.updateKafkaVehicleSpeedOver(kafkaVehicleSpeedOver));
    }

    /**
     * 删除车辆超速告警数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleSpeedOver:remove')")
    @Log(title = "车辆超速告警数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kafkaVehicleSpeedOverService.deleteKafkaVehicleSpeedOverByIds(ids));
    }
}
