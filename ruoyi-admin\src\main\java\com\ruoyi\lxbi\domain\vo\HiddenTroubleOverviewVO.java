package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 隐患分析概览VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HiddenTroubleOverviewVO {
    
    /**
     * 隐患总数
     */
    private Long totalCount;
    
    /**
     * 重大隐患数
     */
    private Long majorCount;
    
    /**
     * 危险源数
     */
    private Long riskSourceCount;
    
    /**
     * 风险管控数
     */
    private Long riskControlCount;
}
