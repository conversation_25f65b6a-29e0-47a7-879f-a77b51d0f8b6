package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaMonitoringPointBasicInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 测点基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface KafkaMonitoringPointBasicInfoMapper
{
    /**
     * 查询测点基本信息
     * 
     * @param id 测点基本信息主键
     * @return 测点基本信息
     */
    public KafkaMonitoringPointBasicInfo selectKafkaMonitoringPointBasicInfoById(Long id);

    /**
     * 查询测点基本信息列表
     * 
     * @param kafkaMonitoringPointBasicInfo 测点基本信息
     * @return 测点基本信息集合
     */
    public List<KafkaMonitoringPointBasicInfo> selectKafkaMonitoringPointBasicInfoList(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo);

    /**
     * 新增测点基本信息
     * 
     * @param kafkaMonitoringPointBasicInfo 测点基本信息
     * @return 结果
     */
    public int insertKafkaMonitoringPointBasicInfo(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo);

    /**
     * 修改测点基本信息
     * 
     * @param kafkaMonitoringPointBasicInfo 测点基本信息
     * @return 结果
     */
    public int updateKafkaMonitoringPointBasicInfo(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo);

    /**
     * 删除测点基本信息
     * 
     * @param id 测点基本信息主键
     * @return 结果
     */
    public int deleteKafkaMonitoringPointBasicInfoById(Long id);

    /**
     * 批量删除测点基本信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaMonitoringPointBasicInfoByIds(Long[] ids);
}
