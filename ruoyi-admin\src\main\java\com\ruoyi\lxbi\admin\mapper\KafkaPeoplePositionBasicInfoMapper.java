package com.ruoyi.lxbi.admin.mapper;

import java.util.List;
import com.ruoyi.lxbi.admin.domain.KafkaPeoplePositionBasicInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * KAFKA人员基础信息数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-23
 */
@Mapper
public interface KafkaPeoplePositionBasicInfoMapper
{
    /**
     * 查询KAFKA人员基础信息数据
     * 
     * @param id KAFKA人员基础信息数据主键
     * @return KAFKA人员基础信息数据
     */
    public KafkaPeoplePositionBasicInfo selectKafkaPeoplePositionBasicInfoById(Long id);

    /**
     * 查询KAFKA人员基础信息数据列表
     * 
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return KAFKA人员基础信息数据集合
     */
    public List<KafkaPeoplePositionBasicInfo> selectKafkaPeoplePositionBasicInfoList(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo);

    /**
     * 新增KAFKA人员基础信息数据
     * 
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    public int insertKafkaPeoplePositionBasicInfo(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo);

    /**
     * 修改KAFKA人员基础信息数据
     * 
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    public int updateKafkaPeoplePositionBasicInfo(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo);

    /**
     * 删除KAFKA人员基础信息数据
     * 
     * @param id KAFKA人员基础信息数据主键
     * @return 结果
     */
    public int deleteKafkaPeoplePositionBasicInfoById(Long id);

    /**
     * 批量删除KAFKA人员基础信息数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaPeoplePositionBasicInfoByIds(Long[] ids);

    /**
     * 根据人员卡编码查询
     *
     * @param personCardCode 人员卡编码
     * @return KAFKA人员基础信息数据
     */
    public KafkaPeoplePositionBasicInfo selectByPersonCardCode(@Param("personCardCode") String personCardCode);

    /**
     * 根据人员卡编码更新
     *
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    public int updateByPersonCardCode(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo);

    /**
     * 批量插入或更新（PostgreSQL UPSERT）
     *
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    public int upsertKafkaPeoplePositionBasicInfo(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo);

    /**
     * 统计指定日期范围内的活跃人员数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 活跃人员数量
     */
    public Long countActivePersonnelByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
