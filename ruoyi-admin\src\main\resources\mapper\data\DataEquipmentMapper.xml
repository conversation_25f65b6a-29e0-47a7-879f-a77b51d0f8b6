<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataEquipmentMapper">
    
    <resultMap type="DataEquipment" id="DataEquipmentResult">
        <result property="id"    column="id"    />
        <result property="equipmentNo"    column="equipment_no"    />
        <result property="mineCarsNumber"    column="mine_cars_number"    />
        <result property="numberOfRunningTrains"    column="number_of_running_trains"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="singleProcessingVolume"    column="single_processing_volume"    />
        <result property="totalProcessingVolume"    column="total_processing_volume"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="equipmentType"    column="equipment_type"    />
    </resultMap>

    <sql id="selectDataEquipmentVo">
        select id, equipment_no, mine_cars_number, number_of_running_trains, operation_time, single_processing_volume, total_processing_volume, operation_date, create_by, create_time, update_by, update_time, start_time, end_time, equipment_type from data_equipment
    </sql>

    <select id="selectDataEquipmentList" parameterType="DataEquipment" resultMap="DataEquipmentResult">
        <include refid="selectDataEquipmentVo"/>
        <where>  
            <if test="equipmentNo != null  and equipmentNo != ''"> and equipment_no = #{equipmentNo}</if>
            <if test="params.beginOperationDate != null and params.beginOperationDate != '' and params.endOperationDate != null and params.endOperationDate != ''"> and operation_date between #{params.beginOperationDate}::date and #{params.endOperationDate}::date</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and start_time between #{params.beginStartTime}::date and #{params.endStartTime}::date</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and end_time between #{params.beginEndTime}::date and #{params.endEndTime}::date</if>
            <if test="equipmentType != null "> and equipment_type = #{equipmentType}</if>
             <if test="operationDate != null "> and operation_date = #{operationDate}</if>
        </where>
    </select>
    
    <select id="selectDataEquipmentById" parameterType="Long" resultMap="DataEquipmentResult">
        <include refid="selectDataEquipmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataEquipment" parameterType="DataEquipment" useGeneratedKeys="true" keyProperty="id">
        insert into data_equipment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipmentNo != null">equipment_no,</if>
            <if test="mineCarsNumber != null">mine_cars_number,</if>
            <if test="numberOfRunningTrains != null">number_of_running_trains,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="singleProcessingVolume != null">single_processing_volume,</if>
            <if test="totalProcessingVolume != null">total_processing_volume,</if>
            <if test="operationDate != null">operation_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="equipmentType != null">equipment_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipmentNo != null">#{equipmentNo},</if>
            <if test="mineCarsNumber != null">#{mineCarsNumber},</if>
            <if test="numberOfRunningTrains != null">#{numberOfRunningTrains},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="singleProcessingVolume != null">#{singleProcessingVolume},</if>
            <if test="totalProcessingVolume != null">#{totalProcessingVolume},</if>
            <if test="operationDate != null">#{operationDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="equipmentType != null">#{equipmentType},</if>
         </trim>
    </insert>

    <update id="updateDataEquipment" parameterType="DataEquipment">
        update data_equipment
        <trim prefix="SET" suffixOverrides=",">
            <if test="equipmentNo != null">equipment_no = #{equipmentNo},</if>
            <if test="mineCarsNumber != null">mine_cars_number = #{mineCarsNumber},</if>
            <if test="numberOfRunningTrains != null">number_of_running_trains = #{numberOfRunningTrains},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="singleProcessingVolume != null">single_processing_volume = #{singleProcessingVolume},</if>
            <if test="totalProcessingVolume != null">total_processing_volume = #{totalProcessingVolume},</if>
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="equipmentType != null">equipment_type = #{equipmentType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataEquipmentById" parameterType="Long">
        delete from data_equipment where id = #{id}
    </delete>

    <delete id="deleteDataEquipmentByIds" parameterType="String">
        delete from data_equipment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertDataEquipment" parameterType="java.util.List">
        insert into data_equipment (operation_date, equipment_no, mine_cars_number, number_of_running_trains, operation_time, single_processing_volume, total_processing_volume, start_time, end_time, equipment_type, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.operationDate}, #{item.equipmentNo}, #{item.mineCarsNumber}, #{item.numberOfRunningTrains}, #{item.operationTime}, #{item.singleProcessingVolume}, #{item.totalProcessingVolume}, #{item.startTime}, #{item.endTime}, #{item.equipmentType}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 设备台效统计查询 -->
    <select id="getEfficiencyStats" resultType="com.ruoyi.lxbi.domain.response.DataEquipmentEfficiencyStats">
        <choose>
            <!-- 按周统计 -->
            <when test="timeType == 'week'">
                SELECT
                    EXTRACT(YEAR FROM de.operation_date) as year,
                    NULL as month,
                    EXTRACT(WEEK FROM de.operation_date) as weekNumber,
                    de.equipment_type as equipmentType,
                    be.equipment_no as equipmentNo,
                    MIN(de.operation_date) as startDate,
                    MAX(de.operation_date) as endDate,
                    SUM(COALESCE(de.total_processing_volume, 0)) as totalProcessingVolume,
                    SUM(COALESCE(de.operation_time, 0)) as totalOperationTime,
                    CASE
                        WHEN SUM(COALESCE(de.operation_time, 0)) > 0
                        THEN SUM(COALESCE(de.total_processing_volume, 0)) / SUM(COALESCE(de.operation_time, 0))
                        ELSE 0
                    END as efficiency,
                    'week' as timeType
                FROM data_equipment de
                LEFT JOIN base_equipment be ON de.equipment_type = be.id
                WHERE de.operation_date BETWEEN #{startTime} AND #{endTime}
                <if test="equipmentType != null">
                    AND de.equipment_type = #{equipmentType}
                </if>
                GROUP BY EXTRACT(YEAR FROM de.operation_date), EXTRACT(WEEK FROM de.operation_date), de.equipment_type, be.equipment_no
                ORDER BY year, weekNumber, equipmentType
            </when>
            <!-- 按年统计 -->
            <when test="timeType == 'year'">
                SELECT
                    EXTRACT(YEAR FROM de.operation_date) as year,
                    NULL as month,
                    NULL as weekNumber,
                    de.equipment_type as equipmentType,
                    be.equipment_no as equipmentNo,
                    MIN(de.operation_date) as startDate,
                    MAX(de.operation_date) as endDate,
                    SUM(COALESCE(de.total_processing_volume, 0)) as totalProcessingVolume,
                    SUM(COALESCE(de.operation_time, 0)) as totalOperationTime,
                    CASE
                        WHEN SUM(COALESCE(de.operation_time, 0)) > 0
                        THEN SUM(COALESCE(de.total_processing_volume, 0)) / SUM(COALESCE(de.operation_time, 0))
                        ELSE 0
                    END as efficiency,
                    'year' as timeType
                FROM data_equipment de
                LEFT JOIN base_equipment be ON de.equipment_type = be.id
                WHERE de.operation_date BETWEEN #{startTime} AND #{endTime}
                <if test="equipmentType != null">
                    AND de.equipment_type = #{equipmentType}
                </if>
                GROUP BY EXTRACT(YEAR FROM de.operation_date), de.equipment_type, be.equipment_no
                ORDER BY year, equipmentType
            </when>
            <!-- 按月统计（默认） -->
            <otherwise>
                SELECT
                    EXTRACT(YEAR FROM de.operation_date) as year,
                    EXTRACT(MONTH FROM de.operation_date) as month,
                    NULL as weekNumber,
                    de.equipment_type as equipmentType,
                    be.equipment_no as equipmentNo,
                    MIN(de.operation_date) as startDate,
                    MAX(de.operation_date) as endDate,
                    SUM(COALESCE(de.total_processing_volume, 0)) as totalProcessingVolume,
                    SUM(COALESCE(de.operation_time, 0)) as totalOperationTime,
                    CASE
                        WHEN SUM(COALESCE(de.operation_time, 0)) > 0
                        THEN SUM(COALESCE(de.total_processing_volume, 0)) / SUM(COALESCE(de.operation_time, 0))
                        ELSE 0
                    END as efficiency,
                    'month' as timeType
                FROM data_equipment de
                LEFT JOIN base_equipment be ON de.equipment_type = be.id
                WHERE de.operation_date BETWEEN #{startTime} AND #{endTime}
                <if test="equipmentType != null">
                    AND de.equipment_type = #{equipmentType}
                </if>
                GROUP BY EXTRACT(YEAR FROM de.operation_date), EXTRACT(MONTH FROM de.operation_date), de.equipment_type, be.equipment_no
                ORDER BY year, month, equipmentType
            </otherwise>
        </choose>
    </select>

    <!-- 设备作业率统计查询 -->
    <select id="getUtilizationStats" resultType="com.ruoyi.lxbi.domain.response.DataEquipmentUtilizationStats">
        <choose>
            <!-- 按周统计 -->
            <when test="timeType == 'week'">
                SELECT
                    EXTRACT(YEAR FROM de.operation_date) as year,
                    NULL as month,
                    EXTRACT(WEEK FROM de.operation_date) as weekNumber,
                    de.equipment_type as equipmentType,
                    be.equipment_no as equipmentNo,
                    MIN(de.operation_date) as startDate,
                    MAX(de.operation_date) as endDate,
                    SUM(COALESCE(de.operation_time, 0)) as actualWorkTime,
                    (COUNT(DISTINCT de.operation_date) * 24 * 60) as totalTime,
                    CASE
                        WHEN (COUNT(DISTINCT de.operation_date) * 24 * 60) > 0
                        THEN (SUM(COALESCE(de.operation_time, 0)) * 100.0) / (COUNT(DISTINCT de.operation_date) * 24 * 60)
                        ELSE 0
                    END as utilizationRate,
                    'week' as timeType
                FROM data_equipment de
                LEFT JOIN base_equipment be ON de.equipment_type = be.id
                WHERE de.operation_date BETWEEN #{startTime} AND #{endTime}
                <if test="equipmentType != null">
                    AND de.equipment_type = #{equipmentType}
                </if>
                GROUP BY EXTRACT(YEAR FROM de.operation_date), EXTRACT(WEEK FROM de.operation_date), de.equipment_type, be.equipment_no
                ORDER BY year, weekNumber, equipmentType
            </when>
            <!-- 按年统计 -->
            <when test="timeType == 'year'">
                SELECT
                    EXTRACT(YEAR FROM de.operation_date) as year,
                    NULL as month,
                    NULL as weekNumber,
                    de.equipment_type as equipmentType,
                    be.equipment_no as equipmentNo,
                    MIN(de.operation_date) as startDate,
                    MAX(de.operation_date) as endDate,
                    SUM(COALESCE(de.operation_time, 0)) as actualWorkTime,
                    (COUNT(DISTINCT de.operation_date) * 24 * 60) as totalTime,
                    CASE
                        WHEN (COUNT(DISTINCT de.operation_date) * 24 * 60) > 0
                        THEN (SUM(COALESCE(de.operation_time, 0)) * 100.0) / (COUNT(DISTINCT de.operation_date) * 24 * 60)
                        ELSE 0
                    END as utilizationRate,
                    'year' as timeType
                FROM data_equipment de
                LEFT JOIN base_equipment be ON de.equipment_type = be.id
                WHERE de.operation_date BETWEEN #{startTime} AND #{endTime}
                <if test="equipmentType != null">
                    AND de.equipment_type = #{equipmentType}
                </if>
                GROUP BY EXTRACT(YEAR FROM de.operation_date), de.equipment_type, be.equipment_no
                ORDER BY year, equipmentType
            </when>
            <!-- 按月统计（默认） -->
            <otherwise>
                SELECT
                    EXTRACT(YEAR FROM de.operation_date) as year,
                    EXTRACT(MONTH FROM de.operation_date) as month,
                    NULL as weekNumber,
                    de.equipment_type as equipmentType,
                    be.equipment_no as equipmentNo,
                    MIN(de.operation_date) as startDate,
                    MAX(de.operation_date) as endDate,
                    SUM(COALESCE(de.operation_time, 0)) as actualWorkTime,
                    (COUNT(DISTINCT de.operation_date) * 24 * 60) as totalTime,
                    CASE
                        WHEN (COUNT(DISTINCT de.operation_date) * 24 * 60) > 0
                        THEN (SUM(COALESCE(de.operation_time, 0)) * 100.0) / (COUNT(DISTINCT de.operation_date) * 24 * 60)
                        ELSE 0
                    END as utilizationRate,
                    'month' as timeType
                FROM data_equipment de
                LEFT JOIN base_equipment be ON de.equipment_type = be.id
                WHERE de.operation_date BETWEEN #{startTime} AND #{endTime}
                <if test="equipmentType != null">
                    AND de.equipment_type = #{equipmentType}
                </if>
                GROUP BY EXTRACT(YEAR FROM de.operation_date), EXTRACT(MONTH FROM de.operation_date), de.equipment_type, be.equipment_no
                ORDER BY year, month, equipmentType
            </otherwise>
        </choose>
    </select>
</mapper>