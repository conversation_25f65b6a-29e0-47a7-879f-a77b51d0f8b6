package com.ruoyi.lxbi.domain.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采矿整体月计划批量操作DTO
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Data
public class PlanMiningMonthlyBatchDto {

    /** 主键ID */
    private Long id;

    /** 计划月份 */
    private String planMonth;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 掘进米数 */
    private BigDecimal driftMeter;

    /** 原矿量 */
    private BigDecimal rawOreVolume;

    /** 支护米数 */
    private BigDecimal supportMeter;

    /** 充填量 */
    private BigDecimal fillingVolume;

    /** 潜孔米数 */
    private BigDecimal dthMeter;

    /** 中深孔米数 */
    private BigDecimal deepHoleMeter;

    /** 出矿量 */
    private BigDecimal oreOutputVolume;

    /** 备注 */
    private String remark;

    /** 操作类型（新增、更新、删除） */
    private String operationType;

}
