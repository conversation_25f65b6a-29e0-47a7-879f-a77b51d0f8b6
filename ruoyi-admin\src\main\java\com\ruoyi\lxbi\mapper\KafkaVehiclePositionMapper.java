package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaVehiclePosition;
import org.apache.ibatis.annotations.Mapper;

/**
 * 车辆定位数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Mapper
public interface KafkaVehiclePositionMapper
{
    /**
     * 查询车辆定位数据
     * 
     * @param id 车辆定位数据主键
     * @return 车辆定位数据
     */
    public KafkaVehiclePosition selectKafkaVehiclePositionById(Long id);

    /**
     * 查询车辆定位数据列表
     * 
     * @param kafkaVehiclePosition 车辆定位数据
     * @return 车辆定位数据集合
     */
    public List<KafkaVehiclePosition> selectKafkaVehiclePositionList(KafkaVehiclePosition kafkaVehiclePosition);

    /**
     * 新增车辆定位数据
     * 
     * @param kafkaVehiclePosition 车辆定位数据
     * @return 结果
     */
    public int insertKafkaVehiclePosition(KafkaVehiclePosition kafkaVehiclePosition);

    /**
     * 修改车辆定位数据
     * 
     * @param kafkaVehiclePosition 车辆定位数据
     * @return 结果
     */
    public int updateKafkaVehiclePosition(KafkaVehiclePosition kafkaVehiclePosition);

    /**
     * 删除车辆定位数据
     * 
     * @param id 车辆定位数据主键
     * @return 结果
     */
    public int deleteKafkaVehiclePositionById(Long id);

    /**
     * 批量删除车辆定位数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaVehiclePositionByIds(Long[] ids);
}
