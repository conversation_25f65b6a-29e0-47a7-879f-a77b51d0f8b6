package com.ruoyi.lxbi.mapper;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.domain.DataMineHoisting;
import com.ruoyi.lxbi.domain.response.DataMineHoistingVo;
import org.apache.ibatis.annotations.Param;

/**
 * 矿井提升数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface DataMineHoistingMapper 
{
    /**
     * 查询矿井提升数据
     * 
     * @param id 矿井提升数据主键
     * @return 矿井提升数据
     */
    public DataMineHoisting selectDataMineHoistingById(Long id);

    /**
     * 查询矿井提升数据列表
     *
     * @param dataMineHoisting 矿井提升数据
     * @return 矿井提升数据集合
     */
    public List<DataMineHoistingVo> selectDataMineHoistingList(DataMineHoisting dataMineHoisting);

    /**
     * 新增矿井提升数据
     * 
     * @param dataMineHoisting 矿井提升数据
     * @return 结果
     */
    public int insertDataMineHoisting(DataMineHoisting dataMineHoisting);

    /**
     * 修改矿井提升数据
     * 
     * @param dataMineHoisting 矿井提升数据
     * @return 结果
     */
    public int updateDataMineHoisting(DataMineHoisting dataMineHoisting);

    /**
     * 删除矿井提升数据
     * 
     * @param id 矿井提升数据主键
     * @return 结果
     */
    public int deleteDataMineHoistingById(Long id);

    /**
     * 批量删除矿井提升数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataMineHoistingByIds(Long[] ids);

    /**
     * 根据作业日期查询矿井提升数据列表
     *
     * @param operationDate 作业日期
     * @return 矿井提升数据集合
     */
    public List<DataMineHoistingVo> selectDataMineHoistingByOperationDate(@Param("operationDate") Date operationDate);

    /**
     * 批量新增矿井提升数据
     *
     * @param dataMineHoistingList 矿井提升数据列表
     * @return 结果
     */
    public int batchInsertDataMineHoisting(List<DataMineHoisting> dataMineHoistingList);
}
