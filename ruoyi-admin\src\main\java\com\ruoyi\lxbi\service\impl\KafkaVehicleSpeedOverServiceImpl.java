package com.ruoyi.lxbi.service.impl;

import java.text.SimpleDateFormat;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaVehicleSpeedOverMapper;
import com.ruoyi.lxbi.domain.KafkaVehicleSpeedOver;
import com.ruoyi.lxbi.service.IKafkaVehicleSpeedOverService;

/**
 * 车辆超速告警数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class KafkaVehicleSpeedOverServiceImpl implements IKafkaVehicleSpeedOverService
{
    @Autowired
    private KafkaVehicleSpeedOverMapper kafkaVehicleSpeedOverMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询车辆超速告警数据
     * 
     * @param id 车辆超速告警数据主键
     * @return 车辆超速告警数据
     */
    @Override
    public KafkaVehicleSpeedOver selectKafkaVehicleSpeedOverById(Long id)
    {
        return kafkaVehicleSpeedOverMapper.selectKafkaVehicleSpeedOverById(id);
    }

    /**
     * 查询车辆超速告警数据列表
     * 
     * @param kafkaVehicleSpeedOver 车辆超速告警数据
     * @return 车辆超速告警数据
     */
    @Override
    public List<KafkaVehicleSpeedOver> selectKafkaVehicleSpeedOverList(KafkaVehicleSpeedOver kafkaVehicleSpeedOver)
    {
        return kafkaVehicleSpeedOverMapper.selectKafkaVehicleSpeedOverList(kafkaVehicleSpeedOver);
    }

    /**
     * 新增车辆超速告警数据
     * 
     * @param kafkaVehicleSpeedOver 车辆超速告警数据
     * @return 结果
     */
    @Override
    public int insertKafkaVehicleSpeedOver(KafkaVehicleSpeedOver kafkaVehicleSpeedOver)
    {
        kafkaVehicleSpeedOver.setCreateTime(DateUtils.getNowDate());
        return kafkaVehicleSpeedOverMapper.insertKafkaVehicleSpeedOver(kafkaVehicleSpeedOver);
    }

    /**
     * 修改车辆超速告警数据
     * 
     * @param kafkaVehicleSpeedOver 车辆超速告警数据
     * @return 结果
     */
    @Override
    public int updateKafkaVehicleSpeedOver(KafkaVehicleSpeedOver kafkaVehicleSpeedOver)
    {
        kafkaVehicleSpeedOver.setUpdateTime(DateUtils.getNowDate());
        return kafkaVehicleSpeedOverMapper.updateKafkaVehicleSpeedOver(kafkaVehicleSpeedOver);
    }

    /**
     * 批量删除车辆超速告警数据
     * 
     * @param ids 需要删除的车辆超速告警数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaVehicleSpeedOverByIds(Long[] ids)
    {
        return kafkaVehicleSpeedOverMapper.deleteKafkaVehicleSpeedOverByIds(ids);
    }

    /**
     * 删除车辆超速告警数据信息
     *
     * @param id 车辆超速告警数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaVehicleSpeedOverById(Long id)
    {
        return kafkaVehicleSpeedOverMapper.deleteKafkaVehicleSpeedOverById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka车辆超速告警消息");

            // 解析Kafka消息
            KafkaVehicleSpeedOver speedOver = parseKafkaMessage(kafkaMessage);
            if (speedOver == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(speedOver.getVehicleIdentificationCard())) {
                log.warn("车辆标识卡为空，跳过处理");
                return false;
            }

            // 执行插入操作
            int result = insertKafkaVehicleSpeedOver(speedOver);

            if (result > 0) {
                log.info("成功插入车辆超速告警数据，车辆标识卡: {}, 煤矿代码: {}",
                    speedOver.getVehicleIdentificationCard(), speedOver.getMineCode());
                return true;
            } else {
                log.warn("插入车辆超速告警数据失败，车辆标识卡: {}",
                    speedOver.getVehicleIdentificationCard());
                return false;
            }

        } catch (Exception e) {
            log.error("处理Kafka车辆超速告警消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaVehicleSpeedOver parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaVehicleSpeedOver speedOver = new KafkaVehicleSpeedOver();

            // 基础信息
            speedOver.setFileEncoding(getStringValue(jsonNode, "文件编码"));
            speedOver.setMineCode(getStringValue(jsonNode, "煤矿代码"));
            speedOver.setDataGenerateTime(getDateValue(jsonNode, "数据生成时间"));
            speedOver.setVehicleIdentificationCard(getStringValue(jsonNode, "车辆标识卡"));
            speedOver.setInMineTime(getDateValue(jsonNode, "进矿时间"));
            speedOver.setAlarmStartTime(getDateValue(jsonNode, "告警开始时间"));
            speedOver.setAlarmEndTime(getDateValue(jsonNode, "告警结束时间"));

            // 默认值
            speedOver.setIsDeleted(0L);
            speedOver.setCreateTime(DateUtils.getNowDate());
            speedOver.setUpdateTime(DateUtils.getNowDate());

            return speedOver;

        } catch (Exception e) {
            log.error("解析Kafka车辆超速告警消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null && !node.isNull() ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private java.util.Date getDateValue(JsonNode jsonNode, String fieldName) {
        try {
            String dateStr = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            }
        } catch (Exception e) {
            log.warn("解析日期字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }
}
