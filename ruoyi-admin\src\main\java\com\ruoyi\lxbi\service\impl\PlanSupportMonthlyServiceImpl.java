package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.PlanSupportMonthlyMapper;
import com.ruoyi.lxbi.domain.PlanSupportMonthly;
import com.ruoyi.lxbi.domain.request.PlanSupportMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanSupportMonthlyVo;
import com.ruoyi.lxbi.service.IPlanSupportMonthlyService;

/**
 * 支护月计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class PlanSupportMonthlyServiceImpl implements IPlanSupportMonthlyService 
{
    @Autowired
    private PlanSupportMonthlyMapper planSupportMonthlyMapper;

    /**
     * 查询支护月计划
     * 
     * @param id 支护月计划主键
     * @return 支护月计划
     */
    @Override
    public PlanSupportMonthly selectPlanSupportMonthlyById(Long id)
    {
        return planSupportMonthlyMapper.selectPlanSupportMonthlyById(id);
    }

    /**
     * 查询支护月计划列表
     *
     * @param planSupportMonthly 支护月计划
     * @return 支护月计划
     */
    @Override
    public List<PlanSupportMonthlyVo> selectPlanSupportMonthlyList(PlanSupportMonthly planSupportMonthly)
    {
        return planSupportMonthlyMapper.selectPlanSupportMonthlyList(planSupportMonthly);
    }

    /**
     * 新增支护月计划
     * 
     * @param planSupportMonthly 支护月计划
     * @return 结果
     */
    @Override
    public int insertPlanSupportMonthly(PlanSupportMonthly planSupportMonthly)
    {
        planSupportMonthly.setCreateTime(DateUtils.getNowDate());
        return planSupportMonthlyMapper.insertPlanSupportMonthly(planSupportMonthly);
    }

    /**
     * 修改支护月计划
     * 
     * @param planSupportMonthly 支护月计划
     * @return 结果
     */
    @Override
    public int updatePlanSupportMonthly(PlanSupportMonthly planSupportMonthly)
    {
        planSupportMonthly.setUpdateTime(DateUtils.getNowDate());
        return planSupportMonthlyMapper.updatePlanSupportMonthly(planSupportMonthly);
    }

    /**
     * 批量删除支护月计划
     * 
     * @param ids 需要删除的支护月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanSupportMonthlyByIds(Long[] ids)
    {
        return planSupportMonthlyMapper.deletePlanSupportMonthlyByIds(ids);
    }

    /**
     * 删除支护月计划信息
     *
     * @param id 支护月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanSupportMonthlyById(Long id)
    {
        return planSupportMonthlyMapper.deletePlanSupportMonthlyById(id);
    }

    /**
     * 批量保存支护月计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePlanSupportMonthly(List<PlanSupportMonthlyBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个月份的数据
        String planMonth = batchDataList.get(0).getPlanMonth();
        if (StringUtils.isBlank(planMonth)) {
            throw new ServiceException("计划月份不能为空");
        }
        // 验证是否同一个日期和项目部的数据
        Long projectDepartmentId = batchDataList.get(0).getProjectDepartmentId();
        if (projectDepartmentId == null) {
            throw new ServiceException("项目部门不能为空");
        }
        boolean allSameDateAndProject = batchDataList.stream()
                .allMatch(data -> planMonth.equals(data.getPlanMonth())
                        && projectDepartmentId.equals(data.getProjectDepartmentId()));
        if (!allSameDateAndProject) {
            throw new ServiceException("批量数据必须是同一个作业日期和项目部门");
        }

        // 查询该月份的现有数据
        PlanSupportMonthly queryParam = new PlanSupportMonthly();
        queryParam.setPlanDate(planMonth);
        queryParam.setProjectDepartmentId(projectDepartmentId);
        List<PlanSupportMonthlyVo> existingDataList = planSupportMonthlyMapper.selectPlanSupportMonthlyList(queryParam);
        Map<Long, PlanSupportMonthly> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(PlanSupportMonthly::getId, data -> data));

        List<PlanSupportMonthly> toInsert = new ArrayList<>();
        List<PlanSupportMonthly> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (PlanSupportMonthlyBatchDto batchData : batchDataList) {
            if ("add".equals(batchData.getOperationType())) {
                // 新增数据
                PlanSupportMonthly newData = new PlanSupportMonthly();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if ("edit".equals(batchData.getOperationType()) && batchData.getId() != null) {
                // 更新现有数据
                if (existingDataMap.containsKey(batchData.getId())) {
                    PlanSupportMonthly updateData = new PlanSupportMonthly();
                    copyProperties(batchData, updateData);
                    updateData.setId(batchData.getId());
                    updateData.setUpdateBy(SecurityUtils.getUsername());
                    updateData.setUpdateTime(DateUtils.getNowDate());
                    toUpdate.add(updateData);
                    toDelete.remove(batchData.getId());
                }
            }
        }

        int totalProcessed = 0;

        // 执行批量操作
        if (!toInsert.isEmpty()) {
            for (PlanSupportMonthly data : toInsert) {
                totalProcessed += planSupportMonthlyMapper.insertPlanSupportMonthly(data);
            }
        }

        if (!toUpdate.isEmpty()) {
            for (PlanSupportMonthly data : toUpdate) {
                totalProcessed += planSupportMonthlyMapper.updatePlanSupportMonthly(data);
            }
        }

        if (!toDelete.isEmpty()) {
            totalProcessed += planSupportMonthlyMapper.deletePlanSupportMonthlyByIds(toDelete.toArray(new Long[0]));
        }

        return totalProcessed;
    }

    /**
     * 复制属性
     */
    private void copyProperties(PlanSupportMonthlyBatchDto source, PlanSupportMonthly target) {
        target.setPlanDate(source.getPlanMonth());
        target.setProjectDepartmentId(source.getProjectDepartmentId());
        target.setBoltMeshSupportMeter(source.getBoltMeshSupportMeter());
        target.setShotcreteSupportMeter(source.getShotcreteSupportMeter());
        if (StringUtils.isNotBlank(source.getRemark())) {
            target.setRemark(source.getRemark());
        }
    }
}
