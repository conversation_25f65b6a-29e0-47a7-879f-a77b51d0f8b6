<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.ApiAiAlarmMapper">

    <resultMap type="com.ruoyi.lxbi.domain.ApiAiAlarm" id="ApiAiAlarmResult">
        <id property="id" column="id"/>
        <result property="externalId" column="external_id"/>
        <result property="alarmType" column="alarm_type"/>
        <result property="alarmLevel" column="alarm_level"/>
        <result property="alarmStatus" column="alarm_status"/>
        <result property="alarmTitle" column="alarm_title"/>
        <result property="alarmDescription" column="alarm_description"/>
        <result property="alarmTime" column="alarm_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="locationName" column="location_name"/>
        <result property="locationCode" column="location_code"/>
        <result property="coordinateX" column="coordinate_x"/>
        <result property="coordinateY" column="coordinate_y"/>
        <result property="coordinateZ" column="coordinate_z"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceType" column="device_type"/>
        <result property="alarmValue" column="alarm_value"/>
        <result property="thresholdValue" column="threshold_value"/>
        <result property="unit" column="unit"/>
        <result property="handlerName" column="handler_name"/>
        <result property="handleTime" column="handle_time"/>
        <result property="handleStatus" column="handle_status"/>
        <result property="handleRemark" column="handle_remark"/>
        <result property="imageUrl" column="image_url"/>
        <result property="videoUrl" column="video_url"/>
        <result property="extraData" column="extra_data"/>
        <result property="rawData" column="raw_data"/>
        <result property="syncTime" column="sync_time"/>
        <result property="syncStatus" column="sync_status"/>
        <result property="syncSource" column="sync_source"/>
        <result property="dataVersion" column="data_version"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="selectApiAiAlarmVo">
        select id, external_id, alarm_type, alarm_level, alarm_status, alarm_title, alarm_description,
               alarm_time, create_time, update_time, location_name, location_code, coordinate_x, coordinate_y, coordinate_z,
               device_id, device_name, device_type, alarm_value, threshold_value, unit,
               handler_name, handle_time, handle_status, handle_remark, image_url, video_url,
               extra_data, raw_data, sync_time, sync_status, sync_source, data_version, is_deleted,
               created_by, created_at, updated_by, updated_at
        from api_ai_alarm
    </sql>

    <!-- 根据外部ID查询 -->
    <select id="selectByExternalId" parameterType="String" resultMap="ApiAiAlarmResult">
        <include refid="selectApiAiAlarmVo"/>
        where external_id = #{externalId} and is_deleted = false
    </select>

    <!-- 根据时间范围查询 -->
    <select id="selectByTimeRange" resultMap="ApiAiAlarmResult">
        <include refid="selectApiAiAlarmVo"/>
        where alarm_time between #{startTime} and #{endTime}
        and is_deleted = false
        order by alarm_time desc
    </select>

    <!-- 根据报警类型查询 -->
    <select id="selectByAlarmType" parameterType="String" resultMap="ApiAiAlarmResult">
        <include refid="selectApiAiAlarmVo"/>
        where alarm_type = #{alarmType} and is_deleted = false
        order by alarm_time desc
    </select>

    <!-- 根据设备ID查询 -->
    <select id="selectByDeviceId" parameterType="String" resultMap="ApiAiAlarmResult">
        <include refid="selectApiAiAlarmVo"/>
        where device_id = #{deviceId} and is_deleted = false
        order by alarm_time desc
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        insert into api_ai_alarm (
            external_id, alarm_type, alarm_level, alarm_status, alarm_title, alarm_description,
            alarm_time, create_time, update_time, location_name, location_code, coordinate_x, coordinate_y, coordinate_z,
            device_id, device_name, device_type, alarm_value, threshold_value, unit,
            handler_name, handle_time, handle_status, handle_remark, image_url, video_url,
            extra_data, raw_data, sync_time, sync_status, sync_source, data_version,
            created_by, created_at, updated_by, updated_at
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.externalId}, #{item.alarmType}, #{item.alarmLevel}, #{item.alarmStatus}, 
                #{item.alarmTitle}, #{item.alarmDescription}, #{item.alarmTime}, #{item.createTime}, #{item.updateTime},
                #{item.locationName}, #{item.locationCode}, #{item.coordinateX}, #{item.coordinateY}, #{item.coordinateZ},
                #{item.deviceId}, #{item.deviceName}, #{item.deviceType}, #{item.alarmValue}, #{item.thresholdValue}, #{item.unit},
                #{item.handlerName}, #{item.handleTime}, #{item.handleStatus}, #{item.handleRemark}, 
                #{item.imageUrl}, #{item.videoUrl}, #{item.extraData}, #{item.rawData},
                #{item.syncTime}, #{item.syncStatus}, #{item.syncSource}, #{item.dataVersion},
                #{item.createdBy}, #{item.createdAt}, #{item.updatedBy}, #{item.updatedAt}
            )
        </foreach>
    </insert>

    <!-- 根据外部ID更新 -->
    <update id="updateByExternalId" parameterType="com.ruoyi.lxbi.domain.ApiAiAlarm">
        update api_ai_alarm
        <set>
            <if test="alarmType != null">alarm_type = #{alarmType},</if>
            <if test="alarmLevel != null">alarm_level = #{alarmLevel},</if>
            <if test="alarmStatus != null">alarm_status = #{alarmStatus},</if>
            <if test="alarmTitle != null">alarm_title = #{alarmTitle},</if>
            <if test="alarmDescription != null">alarm_description = #{alarmDescription},</if>
            <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="coordinateX != null">coordinate_x = #{coordinateX},</if>
            <if test="coordinateY != null">coordinate_y = #{coordinateY},</if>
            <if test="coordinateZ != null">coordinate_z = #{coordinateZ},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="alarmValue != null">alarm_value = #{alarmValue},</if>
            <if test="thresholdValue != null">threshold_value = #{thresholdValue},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="handlerName != null">handler_name = #{handlerName},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="handleRemark != null">handle_remark = #{handleRemark},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="videoUrl != null">video_url = #{videoUrl},</if>
            <if test="extraData != null">extra_data = #{extraData},</if>
            <if test="rawData != null">raw_data = #{rawData},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
            <if test="syncStatus != null">sync_status = #{syncStatus},</if>
            <if test="syncSource != null">sync_source = #{syncSource},</if>
            <if test="dataVersion != null">data_version = #{dataVersion},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            updated_at = now()
        </set>
        where external_id = #{externalId}
    </update>

    <!-- 删除指定时间之前的数据 -->
    <delete id="deleteBeforeTime">
        delete from api_ai_alarm where created_at &lt; #{beforeTime}
    </delete>

    <!-- 统计指定时间范围内的报警数量 -->
    <select id="countByTimeRange" resultType="java.lang.Long">
        select count(*) from api_ai_alarm
        where alarm_time between #{startTime} and #{endTime}
        and is_deleted = false
    </select>

    <!-- 根据同步状态查询 -->
    <select id="selectBySyncStatus" parameterType="String" resultMap="ApiAiAlarmResult">
        <include refid="selectApiAiAlarmVo"/>
        where sync_status = #{syncStatus} and is_deleted = false
        order by sync_time desc
    </select>

    <!-- 获取最新的同步时间 -->
    <select id="getLatestSyncTime" resultType="java.time.LocalDateTime">
        select max(sync_time) from api_ai_alarm where is_deleted = false
    </select>

</mapper>
