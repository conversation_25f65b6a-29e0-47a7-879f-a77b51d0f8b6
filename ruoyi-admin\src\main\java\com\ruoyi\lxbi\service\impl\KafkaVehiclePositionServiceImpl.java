package com.ruoyi.lxbi.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaVehiclePositionMapper;
import com.ruoyi.lxbi.domain.KafkaVehiclePosition;
import com.ruoyi.lxbi.service.IKafkaVehiclePositionService;

/**
 * 车辆定位数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class KafkaVehiclePositionServiceImpl implements IKafkaVehiclePositionService
{
    @Autowired
    private KafkaVehiclePositionMapper kafkaVehiclePositionMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询车辆定位数据
     * 
     * @param id 车辆定位数据主键
     * @return 车辆定位数据
     */
    @Override
    public KafkaVehiclePosition selectKafkaVehiclePositionById(Long id)
    {
        return kafkaVehiclePositionMapper.selectKafkaVehiclePositionById(id);
    }

    /**
     * 查询车辆定位数据列表
     * 
     * @param kafkaVehiclePosition 车辆定位数据
     * @return 车辆定位数据
     */
    @Override
    public List<KafkaVehiclePosition> selectKafkaVehiclePositionList(KafkaVehiclePosition kafkaVehiclePosition)
    {
        return kafkaVehiclePositionMapper.selectKafkaVehiclePositionList(kafkaVehiclePosition);
    }

    /**
     * 新增车辆定位数据
     * 
     * @param kafkaVehiclePosition 车辆定位数据
     * @return 结果
     */
    @Override
    public int insertKafkaVehiclePosition(KafkaVehiclePosition kafkaVehiclePosition)
    {
        kafkaVehiclePosition.setCreateTime(DateUtils.getNowDate());
        return kafkaVehiclePositionMapper.insertKafkaVehiclePosition(kafkaVehiclePosition);
    }

    /**
     * 修改车辆定位数据
     * 
     * @param kafkaVehiclePosition 车辆定位数据
     * @return 结果
     */
    @Override
    public int updateKafkaVehiclePosition(KafkaVehiclePosition kafkaVehiclePosition)
    {
        kafkaVehiclePosition.setUpdateTime(DateUtils.getNowDate());
        return kafkaVehiclePositionMapper.updateKafkaVehiclePosition(kafkaVehiclePosition);
    }

    /**
     * 批量删除车辆定位数据
     * 
     * @param ids 需要删除的车辆定位数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaVehiclePositionByIds(Long[] ids)
    {
        return kafkaVehiclePositionMapper.deleteKafkaVehiclePositionByIds(ids);
    }

    /**
     * 删除车辆定位数据信息
     *
     * @param id 车辆定位数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaVehiclePositionById(Long id)
    {
        return kafkaVehiclePositionMapper.deleteKafkaVehiclePositionById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka车辆定位消息");

            // 解析Kafka消息
            KafkaVehiclePosition position = parseKafkaMessage(kafkaMessage);
            if (position == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(position.getVehicleIdentificationCard())) {
                log.warn("车辆标识卡为空，跳过处理");
                return false;
            }

            // 执行插入操作
            int result = insertKafkaVehiclePosition(position);

            if (result > 0) {
                log.info("成功插入车辆定位数据，车辆标识卡: {}, 煤矿代码: {}",
                    position.getVehicleIdentificationCard(), position.getMineCode());
                return true;
            } else {
                log.warn("插入车辆定位数据失败，车辆标识卡: {}",
                    position.getVehicleIdentificationCard());
                return false;
            }

        } catch (Exception e) {
            log.error("处理Kafka车辆定位消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaVehiclePosition parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaVehiclePosition position = new KafkaVehiclePosition();

            // 基础信息
            position.setFileEncoding(getStringValue(jsonNode, "文件编码"));
            position.setMineCode(getStringValue(jsonNode, "煤矿代码"));
            position.setDataGenerateTime(getDateValue(jsonNode, "数据生成时间"));
            position.setVehicleIdentificationCard(getStringValue(jsonNode, "车辆标识卡"));
            position.setInOrOutMineFlag(getStringValue(jsonNode, "进出矿标志"));
            position.setInMineTime(getDateValue(jsonNode, "进矿时间"));
            position.setOutMineTime(getDateValue(jsonNode, "出矿时间"));
            position.setAreaCode(getStringValue(jsonNode, "区域代码"));
            position.setEnterAreaTime(getDateValue(jsonNode, "进入区域时间"));
            position.setStationCode(getStringValue(jsonNode, "分站编码"));
            position.setEnterStationTime(getDateValue(jsonNode, "进入分站时间"));
            position.setStationXCoordinate(getBigDecimalValue(jsonNode, "分站X坐标"));
            position.setStationYCoordinate(getBigDecimalValue(jsonNode, "分站Y坐标"));
            position.setStationZCoordinate(getBigDecimalValue(jsonNode, "分站Z坐标"));

            // 默认值
            position.setIsDeleted(0L);
            position.setCreateTime(DateUtils.getNowDate());
            position.setUpdateTime(DateUtils.getNowDate());

            return position;

        } catch (Exception e) {
            log.error("解析Kafka车辆定位消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null && !node.isNull() ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private java.util.Date getDateValue(JsonNode jsonNode, String fieldName) {
        try {
            String dateStr = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            }
        } catch (Exception e) {
            log.warn("解析日期字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }

    /**
     * 从JsonNode中获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(JsonNode jsonNode, String fieldName) {
        try {
            String value = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(value)) {
                return new BigDecimal(value);
            }
        } catch (Exception e) {
            log.warn("解析BigDecimal字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }
}
