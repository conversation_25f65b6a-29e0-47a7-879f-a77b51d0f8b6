package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.PlanMiningMonthly;
import com.ruoyi.lxbi.domain.request.DataDrillingStatsRequest;
import com.ruoyi.lxbi.domain.request.DataFillingStatsRequest;
import com.ruoyi.lxbi.domain.request.DataMineHoistingStatsRequest;
import com.ruoyi.lxbi.domain.request.DataMuckingOutStatsRequest;
import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.request.DataTunnelingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataFillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataMineHoistingStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStopeStats;
import com.ruoyi.lxbi.domain.response.DataSupportTypeTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.PlanMiningMonthlyVo;
import com.ruoyi.lxbi.domain.table.MonthlyReportTableVo;
import com.ruoyi.lxbi.service.IDataDeepHoleStatsService;
import com.ruoyi.lxbi.service.IDataDrillingStatsService;
import com.ruoyi.lxbi.service.IDataDthStatsService;
import com.ruoyi.lxbi.service.IDataFillingStatsService;
import com.ruoyi.lxbi.service.IDataMineHoistingStatsService;
import com.ruoyi.lxbi.service.IDataMuckingOutStatsService;
import com.ruoyi.lxbi.service.IDataShotcreteSupportStatsService;
import com.ruoyi.lxbi.service.IDataTunnelingStatsService;
import com.ruoyi.lxbi.service.IPlanMiningMonthlyService;
import com.ruoyi.lxbi.table.params.MonthlyReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 月报数据表格处理器
 */
@Component
public class MonthlyReportTableHandler extends BaseTableHandler<MonthlyReportTableVo, MonthlyReportQueryParams> {

    @Autowired
    private IDataMineHoistingStatsService dataMineHoistingStatsService;

    @Autowired
    private IDataMuckingOutStatsService dataMuckingOutStatsService;

    @Autowired
    private IPlanMiningMonthlyService planMiningMonthlyService;

    @Autowired
    private IDataTunnelingStatsService dataTunnelingStatsService;

    @Autowired
    private IDataShotcreteSupportStatsService dataShotcreteSupportStatsService;

    @Autowired
    private IDataFillingStatsService dataFillingStatsService;

    @Autowired
    private IDataDthStatsService dataDthStatsService;

    @Autowired
    private IDataDeepHoleStatsService dataDeepHoleStatsService;

    @Autowired
    private IDataDrillingStatsService dataDrillingStatsService;

    @Override
    public List<MonthlyReportTableVo> queryTableData(MonthlyReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date queryDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取财务月范围
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(queryDate);
        Date financialMonthEnd = FinancialDateUtils.getFinancialMonthEndDate(queryDate);

        // 获取年度范围（1月1日到当前财务月结束）
        Calendar cal = Calendar.getInstance();
        cal.setTime(queryDate);
        cal.set(Calendar.MONTH, Calendar.JANUARY);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date yearStart = cal.getTime();

        // 获取当月计划数据
        Map<String, BigDecimal> monthlyPlans = getMonthlyPlans(queryDate);

        // 获取当月完成数据
        Map<String, BigDecimal> monthlyCompleted = getMonthlyCompleted(financialMonthStart, financialMonthEnd);

        // 获取年度计划数据（1-当前月的累计）
        Map<String, BigDecimal> yearlyPlans = getYearlyPlans(queryDate);

        // 获取年度预算计划数据
        Map<String, BigDecimal> yearlyBudgetPlans = getYearlyBudgetPlans(queryDate);

        // 获取年度完成数据
        Map<String, BigDecimal> yearlyCompleted = getYearlyCompleted(yearStart, financialMonthEnd);

        // 构建表格数据
        return buildTableData(monthlyPlans, monthlyCompleted, yearlyBudgetPlans, yearlyPlans, yearlyCompleted);
    }

    /**
     * 获取月度计划数据
     */
    private Map<String, BigDecimal> getMonthlyPlans(Date operationDate) {
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        PlanMiningMonthly queryParam = new PlanMiningMonthly();
        queryParam.setPlanDate(financialMonth);

        List<PlanMiningMonthlyVo> plans = planMiningMonthlyService.selectPlanMiningMonthlyList(queryParam);

        Map<String, BigDecimal> result = new HashMap<>();
        for (PlanMiningMonthlyVo plan : plans) {
            if (plan.getDriftMeter() != null) {
                result.put("drift", result.getOrDefault("drift", BigDecimal.ZERO).add(plan.getDriftMeter()));
            }
            if (plan.getRawOreVolume() != null) {
                result.put("rawOre", result.getOrDefault("rawOre", BigDecimal.ZERO).add(plan.getRawOreVolume()));
            }
            if (plan.getSupportMeter() != null) {
                result.put("support", result.getOrDefault("support", BigDecimal.ZERO).add(plan.getSupportMeter()));
            }
            if (plan.getFillingVolume() != null) {
                result.put("filling", result.getOrDefault("filling", BigDecimal.ZERO).add(plan.getFillingVolume()));
            }
            if (plan.getDthMeter() != null) {
                result.put("dth", result.getOrDefault("dth", BigDecimal.ZERO).add(plan.getDthMeter()));
            }
            if (plan.getDeepHoleMeter() != null) {
                result.put("deepHole", result.getOrDefault("deepHole", BigDecimal.ZERO).add(plan.getDeepHoleMeter()));
            }
        }

        return result;
    }

    /**
     * 获取月完成数据
     */
    private Map<String, BigDecimal> getMonthlyCompleted(Date startDate, Date endDate) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 获取提升数据（原矿量）
        DataMineHoistingStatsRequest hoistingRequest = new DataMineHoistingStatsRequest();
        hoistingRequest.setStartDate(startDate);
        hoistingRequest.setEndDate(endDate);
        List<DataMineHoistingStats> hoistingStats = dataMineHoistingStatsService.selectStatsList(hoistingRequest, "daily");
        
        BigDecimal totalRawOre = hoistingStats.stream()
                .map(stats -> stats.getTotalWeight() != null ? BigDecimal.valueOf(stats.getTotalWeight()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("rawOre", totalRawOre);

        // 获取出矿数据
        DataMuckingOutStatsRequest muckingRequest = new DataMuckingOutStatsRequest();
        muckingRequest.setStartDate(startDate);
        muckingRequest.setEndDate(endDate);
        List<DataMuckingOutStopeStats> muckingStats = dataMuckingOutStatsService.selectStopeStatsList(muckingRequest, "daily");
        
        BigDecimal totalMucking = muckingStats.stream()
                .map(stats -> stats.getTotalTons() != null ? BigDecimal.valueOf(stats.getTotalTons()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("mucking", totalMucking);

        // 获取掘进数据
        DataTunnelingStatsRequest tunnelingRequest = new DataTunnelingStatsRequest();
        tunnelingRequest.setStartDate(startDate);
        tunnelingRequest.setEndDate(endDate);
        List<DataTunnelingTotalWithPlanStats> tunnelingStats = dataTunnelingStatsService.selectTotalWithPlanStatsList(tunnelingRequest, "daily");

        BigDecimal totalTunneling = tunnelingStats.stream()
                .filter(stats -> stats.getTotalTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("drift", totalTunneling);

        // 获取支护数据
        DataSupportStatsRequest supportRequest = new DataSupportStatsRequest();
        supportRequest.setStartDate(startDate);
        supportRequest.setEndDate(endDate);
        List<DataSupportTypeTotalWithPlanStats> supportStats = dataShotcreteSupportStatsService.selectTotalWithPlanStatsList(supportRequest, "daily");

        BigDecimal totalSupport = supportStats.stream()
                .filter(stats -> stats.getTotalSupportLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSupportLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("support", totalSupport);

        // 获取充填数据
        DataFillingStatsRequest fillingRequest = new DataFillingStatsRequest();
        fillingRequest.setStartDate(startDate);
        fillingRequest.setEndDate(endDate);
        List<DataFillingTotalWithPlanStats> fillingStats = dataFillingStatsService.selectTotalWithPlanStatsList(fillingRequest, "daily");

        BigDecimal totalFilling = fillingStats.stream()
                .filter(stats -> stats.getTotalSlurryVolume() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSlurryVolume()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("filling", totalFilling);

        // 获取潜孔数据
        DataDrillingStatsRequest dthRequest = new DataDrillingStatsRequest();
        dthRequest.setStartDate(startDate);
        dthRequest.setEndDate(endDate);
        List<DataDrillingTotalWithPlanStats> dthStats = dataDthStatsService.selectTotalWithPlanStatsList(dthRequest, "daily");

        BigDecimal totalDth = dthStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("dth", totalDth);

        // 获取中深孔数据
        DataDrillingStatsRequest deepHoleRequest = new DataDrillingStatsRequest();
        deepHoleRequest.setStartDate(startDate);
        deepHoleRequest.setEndDate(endDate);
        List<DataDrillingTotalWithPlanStats> deepHoleStats = dataDeepHoleStatsService.selectTotalWithPlanStatsList(deepHoleRequest, "daily");

        BigDecimal totalDeepHole = deepHoleStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("deepHole", totalDeepHole);

        // 获取坑内探矿数据（钻孔）
        DataDrillingStatsRequest drillingRequest = new DataDrillingStatsRequest();
        drillingRequest.setStartDate(startDate);
        drillingRequest.setEndDate(endDate);
        List<DataDrillingTotalWithPlanStats> drillingStats = dataDrillingStatsService.selectTotalWithPlanStatsList(drillingRequest, "daily");

        BigDecimal totalDrilling = drillingStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("mineralExploration", totalDrilling);

        return result;
    }

    /**
     * 获取年度计划数据（1月到当前月的累计）
     */
    private Map<String, BigDecimal> getYearlyPlans(Date operationDate) {
        Map<String, BigDecimal> result = new HashMap<>();
        
        Calendar cal = Calendar.getInstance();
        cal.setTime(operationDate);
        int currentYear = cal.get(Calendar.YEAR);
        int currentMonth = cal.get(Calendar.MONTH) + 1; // Calendar.MONTH 从0开始
        
        // 获取当前财务月
        String currentFinancialMonth = FinancialDateUtils.getFinancialMonth(operationDate);
        int financialMonthInt = Integer.parseInt(currentFinancialMonth.substring(4));
        
        // 累计1月到当前财务月的计划
        for (int month = 1; month <= financialMonthInt; month++) {
            String planDate = String.format("%04d%02d", currentYear, month);
            
            PlanMiningMonthly queryParam = new PlanMiningMonthly();
            queryParam.setPlanDate(planDate);
            
            List<PlanMiningMonthlyVo> plans = planMiningMonthlyService.selectPlanMiningMonthlyList(queryParam);
            
            for (PlanMiningMonthlyVo plan : plans) {
                if (plan.getDriftMeter() != null) {
                    result.put("drift", result.getOrDefault("drift", BigDecimal.ZERO).add(plan.getDriftMeter()));
                }
                if (plan.getRawOreVolume() != null) {
                    result.put("rawOre", result.getOrDefault("rawOre", BigDecimal.ZERO).add(plan.getRawOreVolume()));
                }
                if (plan.getSupportMeter() != null) {
                    result.put("support", result.getOrDefault("support", BigDecimal.ZERO).add(plan.getSupportMeter()));
                }
                if (plan.getFillingVolume() != null) {
                    result.put("filling", result.getOrDefault("filling", BigDecimal.ZERO).add(plan.getFillingVolume()));
                }
                if (plan.getDthMeter() != null) {
                    result.put("dth", result.getOrDefault("dth", BigDecimal.ZERO).add(plan.getDthMeter()));
                }
                if (plan.getDeepHoleMeter() != null) {
                    result.put("deepHole", result.getOrDefault("deepHole", BigDecimal.ZERO).add(plan.getDeepHoleMeter()));
                }
            }
        }
        
        return result;
    }

    /**
     * 获取年度预算计划数据
     * TODO: 这里需要根据实际的预算计划数据源来实现
     */
    private Map<String, BigDecimal> getYearlyBudgetPlans(Date operationDate) {
        Map<String, BigDecimal> result = new HashMap<>();
        
        // 示例数据，实际应该从预算计划表中获取
        result.put("drift", BigDecimal.ZERO);
        result.put("rawOre", BigDecimal.ZERO);
        result.put("filling", BigDecimal.ZERO);
        result.put("mineralExploration", BigDecimal.ZERO);
        result.put("support", BigDecimal.ZERO);
        result.put("dth", BigDecimal.ZERO);
        result.put("deepHole", BigDecimal.ZERO);
        
        return result;
    }

    /**
     * 获取年度完成数据
     */
    private Map<String, BigDecimal> getYearlyCompleted(Date startDate, Date endDate) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 获取年度提升数据（原矿量）
        DataMineHoistingStatsRequest hoistingRequest = new DataMineHoistingStatsRequest();
        hoistingRequest.setStartDate(startDate);
        hoistingRequest.setEndDate(endDate);
        List<DataMineHoistingStats> hoistingStats = dataMineHoistingStatsService.selectStatsList(hoistingRequest, "daily");
        
        BigDecimal yearlyRawOre = hoistingStats.stream()
                .map(stats -> stats.getTotalWeight() != null ? BigDecimal.valueOf(stats.getTotalWeight()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("rawOre", yearlyRawOre);

        // 获取年度出矿数据
        DataMuckingOutStatsRequest muckingRequest = new DataMuckingOutStatsRequest();
        muckingRequest.setStartDate(startDate);
        muckingRequest.setEndDate(endDate);
        List<DataMuckingOutStopeStats> muckingStats = dataMuckingOutStatsService.selectStopeStatsList(muckingRequest, "daily");
        
        BigDecimal yearlyMucking = muckingStats.stream()
                .map(stats -> stats.getTotalTons() != null ? BigDecimal.valueOf(stats.getTotalTons()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("mucking", yearlyMucking);

        // 获取年度掘进数据
        DataTunnelingStatsRequest yearlyTunnelingRequest = new DataTunnelingStatsRequest();
        yearlyTunnelingRequest.setStartDate(startDate);
        yearlyTunnelingRequest.setEndDate(endDate);
        List<DataTunnelingTotalWithPlanStats> yearlyTunnelingStats = dataTunnelingStatsService.selectTotalWithPlanStatsList(yearlyTunnelingRequest, "daily");

        BigDecimal yearlyTotalTunneling = yearlyTunnelingStats.stream()
                .filter(stats -> stats.getTotalTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("drift", yearlyTotalTunneling);

        // 获取年度支护数据
        DataSupportStatsRequest yearlySupportRequest = new DataSupportStatsRequest();
        yearlySupportRequest.setStartDate(startDate);
        yearlySupportRequest.setEndDate(endDate);
        List<DataSupportTypeTotalWithPlanStats> yearlySupportStats = dataShotcreteSupportStatsService.selectTotalWithPlanStatsList(yearlySupportRequest, "daily");

        BigDecimal yearlyTotalSupport = yearlySupportStats.stream()
                .filter(stats -> stats.getTotalSupportLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSupportLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("support", yearlyTotalSupport);

        // 获取年度充填数据
        DataFillingStatsRequest yearlyFillingRequest = new DataFillingStatsRequest();
        yearlyFillingRequest.setStartDate(startDate);
        yearlyFillingRequest.setEndDate(endDate);
        List<DataFillingTotalWithPlanStats> yearlyFillingStats = dataFillingStatsService.selectTotalWithPlanStatsList(yearlyFillingRequest, "daily");

        BigDecimal yearlyTotalFilling = yearlyFillingStats.stream()
                .filter(stats -> stats.getTotalSlurryVolume() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSlurryVolume()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("filling", yearlyTotalFilling);

        // 获取年度潜孔数据
        DataDrillingStatsRequest yearlyDthRequest = new DataDrillingStatsRequest();
        yearlyDthRequest.setStartDate(startDate);
        yearlyDthRequest.setEndDate(endDate);
        List<DataDrillingTotalWithPlanStats> yearlyDthStats = dataDthStatsService.selectTotalWithPlanStatsList(yearlyDthRequest, "daily");

        BigDecimal yearlyTotalDth = yearlyDthStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("dth", yearlyTotalDth);

        // 获取年度中深孔数据
        DataDrillingStatsRequest yearlyDeepHoleRequest = new DataDrillingStatsRequest();
        yearlyDeepHoleRequest.setStartDate(startDate);
        yearlyDeepHoleRequest.setEndDate(endDate);
        List<DataDrillingTotalWithPlanStats> yearlyDeepHoleStats = dataDeepHoleStatsService.selectTotalWithPlanStatsList(yearlyDeepHoleRequest, "daily");

        BigDecimal yearlyTotalDeepHole = yearlyDeepHoleStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("deepHole", yearlyTotalDeepHole);

        // 获取年度坑内探矿数据（钻孔）
        DataDrillingStatsRequest yearlyDrillingRequest = new DataDrillingStatsRequest();
        yearlyDrillingRequest.setStartDate(startDate);
        yearlyDrillingRequest.setEndDate(endDate);
        List<DataDrillingTotalWithPlanStats> yearlyDrillingStats = dataDrillingStatsService.selectTotalWithPlanStatsList(yearlyDrillingRequest, "daily");

        BigDecimal yearlyTotalDrilling = yearlyDrillingStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("mineralExploration", yearlyTotalDrilling);

        return result;
    }

    /**
     * 构建表格数据
     */
    private List<MonthlyReportTableVo> buildTableData(Map<String, BigDecimal> monthlyPlans,
                                                      Map<String, BigDecimal> monthlyCompleted,
                                                      Map<String, BigDecimal> yearlyBudgetPlans,
                                                      Map<String, BigDecimal> yearlyPlans,
                                                      Map<String, BigDecimal> yearlyCompleted) {
        List<MonthlyReportTableVo> result = new ArrayList<>();

        // 巷道掘进
        result.add(createReportItem("1", "巷道掘进", "m",
                monthlyPlans.getOrDefault("drift", BigDecimal.ZERO),
                monthlyCompleted.getOrDefault("drift", BigDecimal.ZERO),
                yearlyBudgetPlans.getOrDefault("drift", BigDecimal.ZERO),
                yearlyPlans.getOrDefault("drift", BigDecimal.ZERO),
                yearlyCompleted.getOrDefault("drift", BigDecimal.ZERO)));

        // 原矿量
        result.add(createReportItem("2", "原矿量", "t",
                monthlyPlans.getOrDefault("rawOre", BigDecimal.ZERO),
                monthlyCompleted.getOrDefault("rawOre", BigDecimal.ZERO),
                yearlyBudgetPlans.getOrDefault("rawOre", BigDecimal.ZERO),
                yearlyPlans.getOrDefault("rawOre", BigDecimal.ZERO),
                yearlyCompleted.getOrDefault("rawOre", BigDecimal.ZERO)));

        // 充填
        result.add(createReportItem("3", "充填", "m³",
                monthlyPlans.getOrDefault("filling", BigDecimal.ZERO),
                monthlyCompleted.getOrDefault("filling", BigDecimal.ZERO),
                yearlyBudgetPlans.getOrDefault("filling", BigDecimal.ZERO),
                yearlyPlans.getOrDefault("filling", BigDecimal.ZERO),
                yearlyCompleted.getOrDefault("filling", BigDecimal.ZERO)));

        // 坑内探矿
        result.add(createReportItem("4", "坑内探矿", "m",
                monthlyPlans.getOrDefault("mineralExploration", BigDecimal.ZERO),
                monthlyCompleted.getOrDefault("mineralExploration", BigDecimal.ZERO),
                yearlyBudgetPlans.getOrDefault("mineralExploration", BigDecimal.ZERO),
                yearlyPlans.getOrDefault("mineralExploration", BigDecimal.ZERO),
                yearlyCompleted.getOrDefault("mineralExploration", BigDecimal.ZERO)));

        // 支护
        result.add(createReportItem("5", "支护", "m",
                monthlyPlans.getOrDefault("support", BigDecimal.ZERO),
                monthlyCompleted.getOrDefault("support", BigDecimal.ZERO),
                yearlyBudgetPlans.getOrDefault("support", BigDecimal.ZERO),
                yearlyPlans.getOrDefault("support", BigDecimal.ZERO),
                yearlyCompleted.getOrDefault("support", BigDecimal.ZERO)));

        // 潜孔
        result.add(createReportItem("6", "潜孔", "m",
                monthlyPlans.getOrDefault("dth", BigDecimal.ZERO),
                monthlyCompleted.getOrDefault("dth", BigDecimal.ZERO),
                yearlyBudgetPlans.getOrDefault("dth", BigDecimal.ZERO),
                yearlyPlans.getOrDefault("dth", BigDecimal.ZERO),
                yearlyCompleted.getOrDefault("dth", BigDecimal.ZERO)));

        // 中深孔
        result.add(createReportItem("7", "中深孔", "m",
                monthlyPlans.getOrDefault("deepHole", BigDecimal.ZERO),
                monthlyCompleted.getOrDefault("deepHole", BigDecimal.ZERO),
                yearlyBudgetPlans.getOrDefault("deepHole", BigDecimal.ZERO),
                yearlyPlans.getOrDefault("deepHole", BigDecimal.ZERO),
                yearlyCompleted.getOrDefault("deepHole", BigDecimal.ZERO)));

        return result;
    }

    /**
     * 创建报表项
     */
    private MonthlyReportTableVo createReportItem(String serialNumber, String name, String unit,
                                                  BigDecimal monthlyPlan, BigDecimal monthlyCompleted,
                                                  BigDecimal yearlyBudgetPlan, BigDecimal yearlyPlan,
                                                  BigDecimal yearlyCompleted) {
        MonthlyReportTableVo vo = new MonthlyReportTableVo();
        vo.setSerialNumber(serialNumber);
        vo.setName(name);
        vo.setUnit(unit);
        vo.setMonthlyPlan(monthlyPlan);
        vo.setMonthlyCompleted(monthlyCompleted);
        vo.setYearlyBudgetPlan(yearlyBudgetPlan);
        vo.setYearlyPlan(yearlyPlan);
        vo.setYearlyCompleted(yearlyCompleted);

        // 计算月完成率
        if (monthlyPlan != null && monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal monthlyRate = monthlyCompleted.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setMonthlyCompletionRate(monthlyRate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setMonthlyCompletionRate("0.00%");
        }

        // 计算年度完成率
        if (yearlyPlan != null && yearlyPlan.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal yearlyRate = yearlyCompleted.divide(yearlyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setYearlyCompletionRate(yearlyRate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setYearlyCompletionRate("0.00%");
        }

        // 计算累计超欠（年度完成 - 年度计划）
        BigDecimal cumulativeVariance = yearlyCompleted.subtract(yearlyPlan);
        vo.setCumulativeVariance(cumulativeVariance);

        return vo;
    }
}
