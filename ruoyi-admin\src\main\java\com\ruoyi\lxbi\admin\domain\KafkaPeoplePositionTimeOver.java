package com.ruoyi.lxbi.admin.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 人员超时定位数据对象 kafka_people_position_time_over
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public class KafkaPeoplePositionTimeOver extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 煤矿编码 */
    @Excel(name = "煤矿编码")
    private String mineCode;

    /** 矿井名称 */
    @Excel(name = "矿井名称")
    private String mineName;

    /** 数据上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "数据上传时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dataUploadTime;

    /** 人员卡编码 */
    @Excel(name = "人员卡编码")
    private String personCardCode;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personName;

    /** 入井时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入井时刻", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date enterWellTime;

    /** 报警开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报警开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date alarmStartTime;

    /** 报警结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报警结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date alarmEndTime;

    /** 区域编码 */
    @Excel(name = "区域编码")
    private String areaCode;

    /** 进入当前所处区域时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "进入当前所处区域时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date enterCurrentAreaTime;

    /** 基站编码 */
    @Excel(name = "基站编码")
    private String baseStationCode;

    /** 进入当前所处基站时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "进入当前所处基站时刻", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date enterCurrentBaseStationTime;

    /** 状态(1:正常 0:异常) */
    private Long status;

    /** 是否删除(0:未删除 1:已删除) */
    private Long isDeleted;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMineCode(String mineCode) 
    {
        this.mineCode = mineCode;
    }

    public String getMineCode() 
    {
        return mineCode;
    }
    public void setMineName(String mineName) 
    {
        this.mineName = mineName;
    }

    public String getMineName() 
    {
        return mineName;
    }
    public void setDataUploadTime(Date dataUploadTime) 
    {
        this.dataUploadTime = dataUploadTime;
    }

    public Date getDataUploadTime() 
    {
        return dataUploadTime;
    }
    public void setPersonCardCode(String personCardCode) 
    {
        this.personCardCode = personCardCode;
    }

    public String getPersonCardCode() 
    {
        return personCardCode;
    }
    public void setPersonName(String personName) 
    {
        this.personName = personName;
    }

    public String getPersonName() 
    {
        return personName;
    }
    public void setEnterWellTime(Date enterWellTime) 
    {
        this.enterWellTime = enterWellTime;
    }

    public Date getEnterWellTime() 
    {
        return enterWellTime;
    }
    public void setAlarmStartTime(Date alarmStartTime) 
    {
        this.alarmStartTime = alarmStartTime;
    }

    public Date getAlarmStartTime() 
    {
        return alarmStartTime;
    }
    public void setAlarmEndTime(Date alarmEndTime) 
    {
        this.alarmEndTime = alarmEndTime;
    }

    public Date getAlarmEndTime() 
    {
        return alarmEndTime;
    }
    public void setAreaCode(String areaCode) 
    {
        this.areaCode = areaCode;
    }

    public String getAreaCode() 
    {
        return areaCode;
    }
    public void setEnterCurrentAreaTime(Date enterCurrentAreaTime) 
    {
        this.enterCurrentAreaTime = enterCurrentAreaTime;
    }

    public Date getEnterCurrentAreaTime() 
    {
        return enterCurrentAreaTime;
    }
    public void setBaseStationCode(String baseStationCode) 
    {
        this.baseStationCode = baseStationCode;
    }

    public String getBaseStationCode() 
    {
        return baseStationCode;
    }
    public void setEnterCurrentBaseStationTime(Date enterCurrentBaseStationTime) 
    {
        this.enterCurrentBaseStationTime = enterCurrentBaseStationTime;
    }

    public Date getEnterCurrentBaseStationTime() 
    {
        return enterCurrentBaseStationTime;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setIsDeleted(Long isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Long getIsDeleted() 
    {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("mineCode", getMineCode())
            .append("mineName", getMineName())
            .append("dataUploadTime", getDataUploadTime())
            .append("personCardCode", getPersonCardCode())
            .append("personName", getPersonName())
            .append("enterWellTime", getEnterWellTime())
            .append("alarmStartTime", getAlarmStartTime())
            .append("alarmEndTime", getAlarmEndTime())
            .append("areaCode", getAreaCode())
            .append("enterCurrentAreaTime", getEnterCurrentAreaTime())
            .append("baseStationCode", getBaseStationCode())
            .append("enterCurrentBaseStationTime", getEnterCurrentBaseStationTime())
            .append("status", getStatus())
            .append("isDeleted", getIsDeleted())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
