package com.ruoyi.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 第三方API配置属性
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
@Component
@ConfigurationProperties(prefix = "external.api")
public class ExternalApiProperties {

    /** 隐患管理系统API配置 */
    private HiddenTroubleApi hiddenTrouble = new HiddenTroubleApi();

    /** AI平台报警系统API配置 */
    private AiAlarmApi aiAlarm = new AiAlarmApi();

    @Data
    public static class HiddenTroubleApi {
        /** API基础地址 */
        private String baseUrl = "http://***************:10001";
        
        /** 统计接口路径 */
        private String statisticsPath = "/api/defense/homepagerule/statistics";
        
        /** 列表接口路径 */
        private String statisticsPagePath = "/api/defense/homepagerule/statisticsPage";
        
        /** 连接超时时间（毫秒） */
        private Integer connectTimeout = 30000;
        
        /** 读取超时时间（毫秒） */
        private Integer readTimeout = 60000;
        
        /** 是否启用 */
        private Boolean enabled = true;

        /**
         * 获取完整的统计接口URL
         */
        public String getStatisticsUrl() {
            return baseUrl + statisticsPath;
        }

        /**
         * 获取完整的列表接口URL
         */
        public String getStatisticsPageUrl() {
            return baseUrl + statisticsPagePath;
        }
    }

    @Data
    public static class AiAlarmApi {
        /** API基础地址 */
        private String baseUrl = "http://10.10.30.51:3001";

        /** 登录接口路径 */
        private String loginPath = "/login";

        /** 报警数据接口路径 */
        private String alarmPath = "/alarm";

        /** 用户名 */
        private String account = "api";

        /** 密码 */
        private String password = "123456";

        /** 连接超时时间（毫秒） */
        private Integer connectTimeout = 30000;

        /** 读取超时时间（毫秒） */
        private Integer readTimeout = 60000;

        /** 是否启用 */
        private Boolean enabled = true;

        /** Token缓存时间（秒） */
        private Integer tokenCacheTime = 3600;

        /**
         * 获取完整的登录接口URL
         */
        public String getLoginUrl() {
            return baseUrl + loginPath;
        }

        /**
         * 获取完整的报警数据接口URL
         */
        public String getAlarmUrl() {
            return baseUrl + alarmPath;
        }
    }
}
