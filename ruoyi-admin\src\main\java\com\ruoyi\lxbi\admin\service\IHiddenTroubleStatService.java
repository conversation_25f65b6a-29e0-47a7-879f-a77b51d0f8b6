package com.ruoyi.lxbi.admin.service;

import java.util.List;
import com.ruoyi.lxbi.domain.vo.*;

/**
 * 隐患统计Service接口
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
public interface IHiddenTroubleStatService {

    /**
     * 获取隐患分析概览统计
     * 包括：隐患总数、重大隐患数、危险源数、风险管控数
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 概览统计数据
     */
    HiddenTroubleOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取部门隐患分布统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 部门分布统计数据
     */
    List<DepartmentDistributionVO> getDepartmentDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取高频发隐患位置统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 位置频率统计数据
     */
    List<LocationFrequencyVO> getLocationFrequency(String viewType, String startDate, String endDate);

    /**
     * 获取隐患趋势统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 趋势统计数据
     */
    List<TrendStatisticsVO> getTrendStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取隐患状态分布统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 状态分布统计数据
     */
    List<StatusDistributionVO> getStatusDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取隐患等级分布统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 等级分布统计数据
     */
    List<GradeDistributionVO> getGradeDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取综合统计数据（用于仪表板）
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 综合统计数据
     */
    DashboardDataVO getDashboardData(String viewType, String startDate, String endDate);

    /**
     * 按日统计隐患数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日统计数据
     */
    List<TrendStatisticsVO> getDailyStatistics(String startDate, String endDate);

    /**
     * 按周统计隐患数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 周统计数据
     */
    List<TrendStatisticsVO> getWeeklyStatistics(String startDate, String endDate);

    /**
     * 按月统计隐患数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 月统计数据
     */
    List<TrendStatisticsVO> getMonthlyStatistics(String startDate, String endDate);

    /**
     * 获取隐患类别分布统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 类别分布统计数据
     */
    List<LocationFrequencyVO> getCategoryDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取责任人隐患统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 责任人统计数据
     */
    List<ResponsiblePersonStatVO> getResponsiblePersonStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取整改完成率统计
     *
     * @param viewType 统计周期
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 整改完成率统计数据
     */
    CompletionRateVO getRectificationCompletionRate(String viewType, String startDate, String endDate);

    /**
     * 获取超期隐患统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 超期隐患统计数据
     */
    OverdueStatisticsVO getOverdueStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取近期隐患趋势（最近30天）
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 近期趋势数据
     */
    List<TrendStatisticsVO> getRecentTrendData(String viewType, String startDate, String endDate);

    /**
     * 获取超期隐患趋势统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 超期隐患趋势数据
     */
    List<TrendStatisticsVO> getOverdueTrendStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取隐患整改效率统计
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 整改效率统计数据
     */
    List<RectificationEfficiencyVO> getRectificationEfficiency(String viewType, String startDate, String endDate);

    /**
     * 获取安全小结统计
     * 包括：隐患总数、待整改数、超时数等
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 安全小结统计数据
     */
    SafetySummaryVO getSafetySummary(String viewType, String startDate, String endDate);

    /**
     * 生成安全小结文本描述
     * 根据统计数据生成文字说明
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *@return 安全小结文本数据
     */
    SafetySummaryTextVO generateSafetySummaryText(String viewType, String startDate, String endDate);
}
