package com.ruoyi.lxbi.task;

import com.ruoyi.lxbi.service.IApiAiAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * AI报警数据同步定时任务
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
@Component
public class AiAlarmSyncTask {

    @Autowired
    private IApiAiAlarmService apiAiAlarmService;

    /**
     * 每天凌晨2点执行，同步前一天的AI报警数据
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncPreviousDayData() {
        log.info("开始执行AI报警数据定时同步任务");
        
        try {
            Map<String, Object> result = apiAiAlarmService.syncPreviousDayData();
            
            if ((Boolean) result.getOrDefault("success", false)) {
                log.info("AI报警数据定时同步成功: {}", result);
            } else {
                log.error("AI报警数据定时同步失败: {}", result.get("message"));
            }
            
        } catch (Exception e) {
            log.error("AI报警数据定时同步异常", e);
        }
        
        log.info("AI报警数据定时同步任务执行完成");
    }

    /**
     * 每小时执行一次，重新同步失败的数据
     */
    @Scheduled(cron = "0 30 * * * ?")
    public void resyncFailedData() {
        log.info("开始执行AI报警数据重新同步任务");
        
        try {
            Map<String, Object> result = apiAiAlarmService.resyncFailedData();
            
            if ((Boolean) result.getOrDefault("success", false)) {
                int totalCount = (Integer) result.getOrDefault("totalCount", 0);
                if (totalCount > 0) {
                    log.info("AI报警数据重新同步成功: {}", result);
                }
            } else {
                log.error("AI报警数据重新同步失败: {}", result.get("message"));
            }
            
        } catch (Exception e) {
            log.error("AI报警数据重新同步异常", e);
        }
        
        log.info("AI报警数据重新同步任务执行完成");
    }

    /**
     * 手动触发同步任务（用于测试）
     */
    public void manualSync() {
        log.info("手动触发AI报警数据同步任务");
        syncPreviousDayData();
    }

    /**
     * 手动触发重新同步任务（用于测试）
     */
    public void manualResync() {
        log.info("手动触发AI报警数据重新同步任务");
        resyncFailedData();
    }
}
