package com.ruoyi.lxbi.controller.kafka;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.KafkaMonitoringPointBasicInfo;
import com.ruoyi.lxbi.service.IKafkaMonitoringPointBasicInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 测点基本信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@RestController
@RequestMapping("/kafka/MonitoringPointBasicInfo")
public class KafkaMonitoringPointBasicInfoController extends BaseController {
    @Autowired
    private IKafkaMonitoringPointBasicInfoService kafkaMonitoringPointBasicInfoService;

    /**
     * 查询测点基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitoringPointBasicInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo) {
        startPage();
        List<KafkaMonitoringPointBasicInfo> list = kafkaMonitoringPointBasicInfoService.selectKafkaMonitoringPointBasicInfoList(kafkaMonitoringPointBasicInfo);
        return getDataTable(list);
    }

    /**
     * 导出测点基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitoringPointBasicInfo:export')")
    @Log(title = "测点基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo) {
        List<KafkaMonitoringPointBasicInfo> list = kafkaMonitoringPointBasicInfoService.selectKafkaMonitoringPointBasicInfoList(kafkaMonitoringPointBasicInfo);
        ExcelUtil<KafkaMonitoringPointBasicInfo> util = new ExcelUtil<KafkaMonitoringPointBasicInfo>(KafkaMonitoringPointBasicInfo.class);
        util.exportExcel(response, list, "测点基本信息数据");
    }

    /**
     * 获取测点基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitoringPointBasicInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kafkaMonitoringPointBasicInfoService.selectKafkaMonitoringPointBasicInfoById(id));
    }

    /**
     * 新增测点基本信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitoringPointBasicInfo:add')")
    @Log(title = "测点基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo)
    {
        return toAjax(kafkaMonitoringPointBasicInfoService.insertKafkaMonitoringPointBasicInfo(kafkaMonitoringPointBasicInfo));
    }

    /**
     * 修改测点基本信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitoringPointBasicInfo:edit')")
    @Log(title = "测点基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo)
    {
        return toAjax(kafkaMonitoringPointBasicInfoService.updateKafkaMonitoringPointBasicInfo(kafkaMonitoringPointBasicInfo));
    }

    /**
     * 删除测点基本信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitoringPointBasicInfo:remove')")
    @Log(title = "测点基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kafkaMonitoringPointBasicInfoService.deleteKafkaMonitoringPointBasicInfoByIds(ids));
    }
}
