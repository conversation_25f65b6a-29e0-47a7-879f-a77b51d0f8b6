package com.ruoyi.framework.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * MQTT配置类 - 支持多个broker连接
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "mqtt")
@Data
public class MqttConfig {

    /**
     * 多个MQTT连接配置
     */
    private Map<String, MqttConnectionConfig> connections;

    /**
     * 通用配置
     */
    private MqttCommonConfig common;

    /**
     * MQTT连接配置
     */
    @Data
    public static class MqttConnectionConfig {
        private String brokerUrl;
        private String clientId;
        private java.util.List<String> topics;
    }

    /**
     * MQTT通用配置
     */
    @Data
    public static class MqttCommonConfig {
        private int connectionTimeout = 30;
        private int keepAliveInterval = 60;
        private boolean automaticReconnect = true;
        private boolean cleanSession = true;
        private String username;
        private String password;
    }

}
