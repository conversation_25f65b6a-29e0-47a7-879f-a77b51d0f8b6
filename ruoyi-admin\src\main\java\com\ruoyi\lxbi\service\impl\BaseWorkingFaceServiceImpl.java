package com.ruoyi.lxbi.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.lxbi.mapper.BaseWorkingFaceMapper;
import com.ruoyi.lxbi.domain.BaseWorkingFace;
import com.ruoyi.lxbi.service.IBaseWorkingFaceService;

/**
 * 中段-工作面配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Service
public class BaseWorkingFaceServiceImpl implements IBaseWorkingFaceService 
{
    @Autowired
    private BaseWorkingFaceMapper baseWorkingFaceMapper;

    /**
     * 查询中段-工作面配置
     * 
     * @param workingFaceId 中段-工作面配置主键
     * @return 中段-工作面配置
     */
    @Override
    public BaseWorkingFace selectBaseWorkingFaceByWorkingFaceId(Long workingFaceId)
    {
        return baseWorkingFaceMapper.selectBaseWorkingFaceByWorkingFaceId(workingFaceId);
    }

    /**
     * 查询中段-工作面配置列表
     * 
     * @param baseWorkingFace 中段-工作面配置
     * @return 中段-工作面配置
     */
    @Override
    public List<BaseWorkingFace> selectBaseWorkingFaceList(BaseWorkingFace baseWorkingFace)
    {
        return baseWorkingFaceMapper.selectBaseWorkingFaceList(baseWorkingFace);
    }

    /**
     * 新增中段-工作面配置
     * 
     * @param baseWorkingFace 中段-工作面配置
     * @return 结果
     */
    @Override
    public int insertBaseWorkingFace(BaseWorkingFace baseWorkingFace)
    {
        baseWorkingFace.setCreateTime(DateUtils.getNowDate());
        return baseWorkingFaceMapper.insertBaseWorkingFace(baseWorkingFace);
    }

    /**
     * 修改中段-工作面配置
     * 
     * @param baseWorkingFace 中段-工作面配置
     * @return 结果
     */
    @Override
    public int updateBaseWorkingFace(BaseWorkingFace baseWorkingFace)
    {
        baseWorkingFace.setUpdateTime(DateUtils.getNowDate());
        return baseWorkingFaceMapper.updateBaseWorkingFace(baseWorkingFace);
    }

    /**
     * 批量删除中段-工作面配置
     * 
     * @param workingFaceIds 需要删除的中段-工作面配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseWorkingFaceByWorkingFaceIds(Long[] workingFaceIds)
    {
        if (workingFaceIds == null || workingFaceIds.length == 0) {
            throw new ServiceException("删除中段-工作面配置失败，未选择数据");
        }
        if (baseWorkingFaceMapper.getBaseWorkingFaceByWorkingFaceIds(workingFaceIds) > 0) {
            throw new ServiceException("存在使用中的中段-工作面配置，无法删除");
        }
        return baseWorkingFaceMapper.deleteBaseWorkingFaceByWorkingFaceIds(workingFaceIds);
    }

    /**
     * 删除中段-工作面配置信息
     * 
     * @param workingFaceId 中段-工作面配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseWorkingFaceByWorkingFaceId(Long workingFaceId)
    {
        return baseWorkingFaceMapper.deleteBaseWorkingFaceByWorkingFaceId(workingFaceId);
    }
}
