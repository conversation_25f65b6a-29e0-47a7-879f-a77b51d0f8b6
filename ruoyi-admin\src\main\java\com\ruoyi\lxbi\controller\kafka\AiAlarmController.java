package com.ruoyi.lxbi.controller.kafka;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.vo.AiAlarmDataVO;
import com.ruoyi.lxbi.domain.vo.AiAlarmResponseVO;
import com.ruoyi.web.service.AiAlarmExternalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI平台报警数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@RestController
@RequestMapping("/ai/alarm")
public class AiAlarmController extends BaseController {

    @Autowired
    private AiAlarmExternalService aiAlarmExternalService;

    /**
     * 测试AI平台API连接
     */
    @Anonymous
    @GetMapping("/test")
    @Log(title = "AI平台API连接测试", businessType = BusinessType.OTHER)
    public R<Boolean> testConnection() {
        try {
            log.info("开始测试AI平台API连接");
            boolean result = aiAlarmExternalService.testConnection();
            return R.ok(result, result ? "AI平台API连接成功" : "AI平台API连接失败");
        } catch (Exception e) {
            log.error("AI平台API连接测试异常", e);
            return R.fail("AI平台API连接测试异常: " + e.getMessage());
        }
    }

    /**
     * 获取报警数据
     *
     * @param pageNum 页数
     * @param pageSize 每页数据长度
     * @param filterDateTime 时间范围（可选）
     * @param filterAlgorithmList 根据算法筛选（可选）
     */
    @Anonymous
    @GetMapping("/list")
    @Log(title = "获取AI平台报警数据", businessType = BusinessType.OTHER)
    public R<AiAlarmResponseVO> getAlarmData(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String filterDateTime,
            @RequestParam(required = false) String filterAlgorithmList) {
        try {
            log.info("获取AI平台报警数据，页数: {}, 每页数量: {}, 时间范围: {}, 算法筛选: {}", 
                    pageNum, pageSize, filterDateTime, filterAlgorithmList);

            AiAlarmResponseVO result = aiAlarmExternalService.getAlarmData(
                    pageNum, pageSize, filterDateTime, filterAlgorithmList);
            
            if (result != null) {
                return R.ok(result, "获取报警数据成功");
            } else {
                return R.fail("获取报警数据失败");
            }
        } catch (Exception e) {
            log.error("获取AI平台报警数据异常", e);
            return R.fail("获取报警数据异常: " + e.getMessage());
        }
    }

    /**
     * 获取火焰烟雾检测报警数据
     */
    @Anonymous
    @GetMapping("/fire")
    @Log(title = "获取火焰烟雾检测报警", businessType = BusinessType.OTHER)
    public R<List<AiAlarmDataVO>> getFireAlarmData(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            log.info("获取火焰烟雾检测报警数据");
            List<AiAlarmDataVO> result = aiAlarmExternalService.getAlarmDataByAlgorithm(pageNum, pageSize, "1");
            return R.ok(result, "获取火焰烟雾检测报警数据成功");
        } catch (Exception e) {
            log.error("获取火焰烟雾检测报警数据异常", e);
            return R.fail("获取火焰烟雾检测报警数据异常: " + e.getMessage());
        }
    }

    /**
     * 获取安全帽检测报警数据
     */
    @Anonymous
    @GetMapping("/helmet")
    @Log(title = "获取安全帽检测报警", businessType = BusinessType.OTHER)
    public R<List<AiAlarmDataVO>> getHelmetAlarmData(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            log.info("获取安全帽检测报警数据");
            List<AiAlarmDataVO> result = aiAlarmExternalService.getAlarmDataByAlgorithm(pageNum, pageSize, "2");
            return R.ok(result, "获取安全帽检测报警数据成功");
        } catch (Exception e) {
            log.error("获取安全帽检测报警数据异常", e);
            return R.fail("获取安全帽检测报警数据异常: " + e.getMessage());
        }
    }

    /**
     * 获取离岗检测报警数据
     */
    @Anonymous
    @GetMapping("/absence")
    @Log(title = "获取离岗检测报警", businessType = BusinessType.OTHER)
    public R<List<AiAlarmDataVO>> getAbsenceAlarmData(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            log.info("获取离岗检测报警数据");
            List<AiAlarmDataVO> result = aiAlarmExternalService.getAlarmDataByAlgorithm(pageNum, pageSize, "10");
            return R.ok(result, "获取离岗检测报警数据成功");
        } catch (Exception e) {
            log.error("获取离岗检测报警数据异常", e);
            return R.fail("获取离岗检测报警数据异常: " + e.getMessage());
        }
    }

    /**
     * 获取自救器检测报警数据
     */
    @Anonymous
    @GetMapping("/respirator")
    @Log(title = "获取自救器检测报警", businessType = BusinessType.OTHER)
    public R<List<AiAlarmDataVO>> getRespiratorAlarmData(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            log.info("获取自救器检测报警数据");
            List<AiAlarmDataVO> result = aiAlarmExternalService.getAlarmDataByAlgorithm(pageNum, pageSize, "24");
            return R.ok(result, "获取自救器检测报警数据成功");
        } catch (Exception e) {
            log.error("获取自救器检测报警数据异常", e);
            return R.fail("获取自救器检测报警数据异常: " + e.getMessage());
        }
    }

    /**
     * 根据时间范围获取报警数据
     */
    @Anonymous
    @GetMapping("/timerange")
    @Log(title = "根据时间范围获取报警数据", businessType = BusinessType.OTHER)
    public R<List<AiAlarmDataVO>> getAlarmDataByTimeRange(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam String startTime,
            @RequestParam String endTime) {
        try {
            log.info("根据时间范围获取报警数据，开始时间: {}, 结束时间: {}", startTime, endTime);
            List<AiAlarmDataVO> result = aiAlarmExternalService.getAlarmDataByTimeRange(
                    pageNum, pageSize, startTime, endTime);
            return R.ok(result, "根据时间范围获取报警数据成功");
        } catch (Exception e) {
            log.error("根据时间范围获取报警数据异常", e);
            return R.fail("根据时间范围获取报警数据异常: " + e.getMessage());
        }
    }

}
