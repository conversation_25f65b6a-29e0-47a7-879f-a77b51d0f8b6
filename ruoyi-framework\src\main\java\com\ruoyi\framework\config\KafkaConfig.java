package com.ruoyi.framework.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka配置类
 * 
 * <AUTHOR>
 * @date 2025-08-16
 */
@Slf4j
@Configuration
@EnableKafka
public class KafkaConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${spring.kafka.consumer.group-id}")
    private String groupId;

    /**
     * Kafka管理客户端
     */
    @Bean
    public AdminClient kafkaAdminClient() {
        log.info("创建Kafka管理客户端 - Bootstrap Servers: {}", bootstrapServers);
        Map<String, Object> configs = new HashMap<>();
        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        return AdminClient.create(configs);
    }

    /**
     * Kafka消费者工厂
     */
    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false); // 禁用自动提交，使用手动提交
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 100); // 每次拉取的记录数

        // 优化网络和超时配置 - 设置为5分钟超时
        props.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, 300000); // 请求超时5分钟
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 300000); // 会话超时5分钟
        props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10000); // 心跳间隔10秒
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 300000); // 最大轮询间隔5分钟
        props.put(ConsumerConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG, 300000); // 连接最大空闲时间5分钟
        props.put(ConsumerConfig.RECONNECT_BACKOFF_MS_CONFIG, 1000); // 重连退避时间1秒
        props.put(ConsumerConfig.RETRY_BACKOFF_MS_CONFIG, 1000); // 重试退避时间1秒
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1); // 最小拉取字节数
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 300000); // 最大等待时间5分钟

        log.info("创建Kafka消费者工厂 - Group ID: {}", groupId);
        return new DefaultKafkaConsumerFactory<>(props);
    }

    /**
     * Kafka监听器容器工厂
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
            new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());

        // 设置并发级别（减少并发，提高稳定性）
        factory.setConcurrency(1);

        // 使用手动确认模式，确保消息被正确处理（但我们使用独立消费者，这个配置主要用于@KafkaListener）
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        
        // 配置重试和错误处理
        factory.getContainerProperties().setPollTimeout(300000); // 轮询超时5分钟
        factory.setCommonErrorHandler(new org.springframework.kafka.listener.DefaultErrorHandler());

        log.info("创建Kafka监听器容器工厂");
        return factory;
    }

}
