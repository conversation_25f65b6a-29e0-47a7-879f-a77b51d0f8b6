package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.domain.PlanStopeMonthly;
import com.ruoyi.lxbi.domain.request.DataMuckingOutStatsRequest;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStopeStats;
import com.ruoyi.lxbi.domain.response.PlanStopeMonthlyVo;
import com.ruoyi.lxbi.domain.table.MineOutputTableVo;
import com.ruoyi.lxbi.service.IBaseStopeService;
import com.ruoyi.lxbi.service.IDataMuckingOutStatsService;
import com.ruoyi.lxbi.service.IPlanStopeMonthlyService;
import com.ruoyi.lxbi.table.params.MineOutputQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采场出矿数据表格处理器（日报）
 */
@Component
public class MineOutputTableHandler extends BaseTableHandler<MineOutputTableVo, MineOutputQueryParams> {

    @Autowired
    private IDataMuckingOutStatsService dataMuckingOutStatsService;

    @Autowired
    private IPlanStopeMonthlyService planStopeMonthlyService;

    @Autowired
    private IBaseStopeService baseStopeService;

    @Override
    public List<MineOutputTableVo> queryTableData(MineOutputQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date operationDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取月度计划数据
        Map<Long, BigDecimal> monthlyPlans = getMonthlyPlans(operationDate);

        // 获取月累计数据和当日数据（一次查询获取）
        MonthlyStatsResult monthlyResult = getMonthlyAccumulatedWithDaily(operationDate);

        // 构建表格数据
        return buildTableData(monthlyResult.getDailyStats(), monthlyPlans, monthlyResult.getMonthlyAccumulated());
    }

    /**
     * 获取月度计划数据（按财务月：上月29号到本月28号）
     */
    private Map<Long, BigDecimal> getMonthlyPlans(Date operationDate) {
        // 计算财务月
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        PlanStopeMonthly queryParam = new PlanStopeMonthly();
        queryParam.setPlanDate(financialMonth);

        List<PlanStopeMonthlyVo> plans = planStopeMonthlyService.selectPlanStopeMonthlyList(queryParam);

        return plans.stream()
                .filter(plan -> plan.getOreOutput() != null && !plan.getOreOutput().trim().isEmpty())
                .collect(Collectors.toMap(
                        PlanStopeMonthly::getStopeId,
                        plan -> {
                            try {
                                return new BigDecimal(plan.getOreOutput().trim());
                            } catch (NumberFormatException e) {
                                return BigDecimal.ZERO;
                            }
                        },
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 获取月累计数据和当日数据（按财务月：上月29号到本月28号）
     */
    private MonthlyStatsResult getMonthlyAccumulatedWithDaily(Date operationDate) {
        // 获取财务月的开始日期到查询日期的累计数据
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(operationDate);

        DataMuckingOutStatsRequest request = new DataMuckingOutStatsRequest();
        request.setStartDate(financialMonthStart);
        request.setEndDate(operationDate);

        List<DataMuckingOutStopeStats> monthlyStats = dataMuckingOutStatsService.selectStopeStatsList(request, "daily");

        // 提取当日数据（最新一天的数据）
        List<DataMuckingOutStopeStats> dailyStats = monthlyStats.stream()
                .filter(stats -> FinancialDateUtils.isSameDay(stats.getOperationDate(), operationDate))
                .collect(Collectors.toList());

        // 按采场ID汇总累计数据
        Map<Long, BigDecimal> monthlyAccumulated = monthlyStats.stream()
                .collect(Collectors.groupingBy(
                        DataMuckingOutStopeStats::getStopeId,
                        Collectors.summingDouble(stats -> stats.getTotalTons() != null ? stats.getTotalTons() : 0.0)
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> BigDecimal.valueOf(entry.getValue())
                ));

        return new MonthlyStatsResult(dailyStats, monthlyAccumulated);
    }

    /**
     * 构建表格数据
     */
    private List<MineOutputTableVo> buildTableData(List<DataMuckingOutStopeStats> dailyStats,
                                                   Map<Long, BigDecimal> monthlyPlans,
                                                   Map<Long, BigDecimal> monthlyAccumulated) {
        List<MineOutputTableVo> result = new ArrayList<>();

        // 计算总计数据
        BigDecimal totalDailyOutput = BigDecimal.ZERO;
        BigDecimal totalMonthlyPlan = BigDecimal.ZERO;
        BigDecimal totalMonthlyAccumulated = BigDecimal.ZERO;

        // 按采场ID分组，避免重复
        Map<Long, DataMuckingOutStopeStats> stopeStatsMap = dailyStats.stream()
                .collect(Collectors.toMap(
                        DataMuckingOutStopeStats::getStopeId,
                        stats -> stats,
                        (existing, replacement) -> {
                            // 如果有重复，累加出矿量
                            existing.setTotalTons((existing.getTotalTons() != null ? existing.getTotalTons() : 0.0) +
                                    (replacement.getTotalTons() != null ? replacement.getTotalTons() : 0.0));
                            return existing;
                        }
                ));

        // 获取所有涉及的采场ID（包括有计划但当日无产量的）
        Set<Long> allStopeIds = new HashSet<>();
        allStopeIds.addAll(stopeStatsMap.keySet());
        allStopeIds.addAll(monthlyPlans.keySet());
        allStopeIds.addAll(monthlyAccumulated.keySet());

        // 批量获取采场名称
        Map<Long, String> stopeNameMap = getStopeNameMap(allStopeIds);

        int serialNumber = 1;
        for (Long stopeId : allStopeIds) {
            DataMuckingOutStopeStats stats = stopeStatsMap.get(stopeId);
            BigDecimal dailyOutput = stats != null && stats.getTotalTons() != null ?
                    BigDecimal.valueOf(stats.getTotalTons()) : BigDecimal.ZERO;
            BigDecimal monthlyPlan = monthlyPlans.getOrDefault(stopeId, BigDecimal.ZERO);
            BigDecimal accumulated = monthlyAccumulated.getOrDefault(stopeId, BigDecimal.ZERO);

            MineOutputTableVo vo = new MineOutputTableVo();
            vo.setSerialNumber(String.valueOf(serialNumber++));
            vo.setName(getStopeNameFromMap(stopeId, stats, stopeNameMap));
            vo.setSubName(getStopeNameFromMap(stopeId, stats, stopeNameMap));
            vo.setUnit("t");
            vo.setMonthlyPlan(monthlyPlan);
            vo.setDailyOutput(dailyOutput);
            vo.setMonthlyAccumulated(accumulated);

            // 计算完成率
            if (monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = accumulated.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                vo.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
            } else {
                vo.setCompletionRate("0.00%");
            }

            // 计算超欠量
            vo.setMonthlyOverUnder(accumulated.subtract(monthlyPlan));

            result.add(vo);

            // 累加总计
            totalDailyOutput = totalDailyOutput.add(dailyOutput);
            totalMonthlyPlan = totalMonthlyPlan.add(monthlyPlan);
            totalMonthlyAccumulated = totalMonthlyAccumulated.add(accumulated);
        }

        // 添加总计行
        if (!result.isEmpty()) {
            MineOutputTableVo summary = new MineOutputTableVo();
            summary.setBold(List.of("serialNumber", "name", "monthlyPlan", "dailyOutput", "monthlyAccumulated", "completionRate", "monthlyOverUnder"));
            summary.setSerialNumber("总计");
            summary.setName("采场出矿");
            summary.setSubName("采场出矿");
            summary.setUnit("t");
            summary.setMonthlyPlan(totalMonthlyPlan);
            summary.setDailyOutput(totalDailyOutput);
            summary.setMonthlyAccumulated(totalMonthlyAccumulated);
            // 计算总完成率
            if (totalMonthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = totalMonthlyAccumulated.divide(totalMonthlyPlan, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                summary.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
            } else {
                summary.setCompletionRate("0.00%");
            }
            summary.setMonthlyOverUnder(totalMonthlyAccumulated.subtract(totalMonthlyPlan));
            result.addFirst(summary); // 插入到第一行
        }

        return result;
    }



    /**
     * 月度统计结果包装类
     */
    private static class MonthlyStatsResult {
        private final List<DataMuckingOutStopeStats> dailyStats;
        private final Map<Long, BigDecimal> monthlyAccumulated;

        public MonthlyStatsResult(List<DataMuckingOutStopeStats> dailyStats, Map<Long, BigDecimal> monthlyAccumulated) {
            this.dailyStats = dailyStats;
            this.monthlyAccumulated = monthlyAccumulated;
        }

        public List<DataMuckingOutStopeStats> getDailyStats() {
            return dailyStats;
        }

        public Map<Long, BigDecimal> getMonthlyAccumulated() {
            return monthlyAccumulated;
        }
    }

    /**
     * 批量获取采场名称映射
     */
    private Map<Long, String> getStopeNameMap(Set<Long> stopeIds) {
        Map<Long, String> stopeNameMap = new HashMap<>();

        if (stopeIds == null || stopeIds.isEmpty()) {
            return stopeNameMap;
        }

        // 批量查询采场信息
        for (Long stopeId : stopeIds) {
            try {
                BaseStope stope = baseStopeService.selectBaseStopeByStopeId(stopeId);
                if (stope != null && stope.getStopeName() != null) {
                    stopeNameMap.put(stopeId, stope.getStopeName());
                }
            } catch (Exception e) {
                // 查询失败时记录日志，但不影响整体流程
                System.err.println("获取采场名称失败，采场ID: " + stopeId + ", 错误: " + e.getMessage());
            }
        }

        return stopeNameMap;
    }

    /**
     * 从映射中获取采场名称
     */
    private String getStopeNameFromMap(Long stopeId, DataMuckingOutStopeStats stats, Map<Long, String> stopeNameMap) {
        // 优先使用统计数据中的采场名称
        if (stats != null && stats.getStopeName() != null && !stats.getStopeName().trim().isEmpty()) {
            return stats.getStopeName();
        }

        // 其次使用基础数据表中的采场名称
        String stopeNameFromBase = stopeNameMap.get(stopeId);
        if (stopeNameFromBase != null && !stopeNameFromBase.trim().isEmpty()) {
            return stopeNameFromBase;
        }

        // 最后使用默认格式（但这种情况应该很少出现）
        return "未知采场-" + stopeId;
    }
}
