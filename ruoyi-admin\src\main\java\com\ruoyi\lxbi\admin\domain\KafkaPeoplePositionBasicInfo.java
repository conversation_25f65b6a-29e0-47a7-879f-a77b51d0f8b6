package com.ruoyi.lxbi.admin.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * KAFKA人员基础信息数据对象 kafka_people_position_basic_info
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KafkaPeoplePositionBasicInfo extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 煤矿编码 */
    @Excel(name = "煤矿编码")
    private String mineCode;

    /** 矿井名称 */
    @Excel(name = "矿井名称")
    private String mineName;

    /** 核定下井人数 */
    @Excel(name = "核定下井人数")
    private String approvedUndergroundCount;

    /** 系统型号 */
    @Excel(name = "系统型号")
    private String systemModel;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String systemName;

    /** 生产厂家名称 */
    @Excel(name = "生产厂家名称")
    private String manufacturerName;

    /** 安标有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "安标有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date safetyCertificateExpiry;

    /** 数据上传时间 */
    @Excel(name = "数据上传时间")
    private Date dataUploadTime;

    /** 人员卡编码 */
    private String personCardCode;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personName;

    /** 工种 */
    @Excel(name = "工种")
    private String jobType;

    /** 职务 */
    @Excel(name = "职务")
    private String position;

    /** 队组班组/部门 */
    @Excel(name = "队组班组/部门")
    private String department;

    /** 出生年月 */
    @Excel(name = "出生年月")
    private String birthDate;

    /** 学历 */
    @Excel(name = "学历")
    private String education;

    /** 是否矿领导(0:否 1:是) */
    private Long isMineLeader;

    /** 是否特种人员(0:否 1:是) */
    private Long isSpecialPersonnel;

    /** 状态(0:禁用 1:启用) */
    private Long status;

    /** 是否删除(0:否 1:是) */
    private Long isDeleted;

}
