package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaVehicleSpeedOver;

/**
 * 车辆超速告警数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IKafkaVehicleSpeedOverService 
{
    /**
     * 查询车辆超速告警数据
     * 
     * @param id 车辆超速告警数据主键
     * @return 车辆超速告警数据
     */
    public KafkaVehicleSpeedOver selectKafkaVehicleSpeedOverById(Long id);

    /**
     * 查询车辆超速告警数据列表
     * 
     * @param kafkaVehicleSpeedOver 车辆超速告警数据
     * @return 车辆超速告警数据集合
     */
    public List<KafkaVehicleSpeedOver> selectKafkaVehicleSpeedOverList(KafkaVehicleSpeedOver kafkaVehicleSpeedOver);

    /**
     * 新增车辆超速告警数据
     * 
     * @param kafkaVehicleSpeedOver 车辆超速告警数据
     * @return 结果
     */
    public int insertKafkaVehicleSpeedOver(KafkaVehicleSpeedOver kafkaVehicleSpeedOver);

    /**
     * 修改车辆超速告警数据
     * 
     * @param kafkaVehicleSpeedOver 车辆超速告警数据
     * @return 结果
     */
    public int updateKafkaVehicleSpeedOver(KafkaVehicleSpeedOver kafkaVehicleSpeedOver);

    /**
     * 批量删除车辆超速告警数据
     * 
     * @param ids 需要删除的车辆超速告警数据主键集合
     * @return 结果
     */
    public int deleteKafkaVehicleSpeedOverByIds(Long[] ids);

    /**
     * 删除车辆超速告警数据信息
     *
     * @param id 车辆超速告警数据主键
     * @return 结果
     */
    public int deleteKafkaVehicleSpeedOverById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaVehicleSpeedOver parseKafkaMessage(String kafkaMessage);
}
