package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import lombok.Data;

/**
 * 多分组列合并示例VO
 */
@Data
@TableConfig(code = "multi_group_table", name = "多分组列合并示例", description = "演示多个列合并分组功能")
public class MultiGroupTableVo {
    
    // a, b, c 属于 group1 合并分组
    @TableHeader(label = "A列", order = 1, colMergeGroup = {"group1"})
    private String a;
    
    @TableHeader(label = "B列", order = 2, colMergeGroup = {"group1"})
    private String b;
    
    @TableHeader(label = "C列", order = 3, colMergeGroup = {"group1", "group2"})
    private String c;
    
    // c, d 属于 group2 合并分组
    @TableHeader(label = "D列", order = 4, colMergeGroup = {"group2"})
    private String d;
    
    // e, f 属于 group3 合并分组
    @TableHeader(label = "E列", order = 5, colMergeGroup = {"group3"})
    private String e;
    
    @TableHeader(label = "F列", order = 6, colMergeGroup = {"group3"})
    private String f;
    
    // g 不属于任何合并分组
    @TableHeader(label = "G列", order = 7)
    private String g;
}