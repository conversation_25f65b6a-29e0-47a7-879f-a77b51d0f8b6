package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanBackfillingMonthly;
import com.ruoyi.lxbi.domain.request.PlanBackfillingMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanBackfillingMonthlyVo;

/**
 * 充填月计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IPlanBackfillingMonthlyService 
{
    /**
     * 查询充填月计划
     * 
     * @param id 充填月计划主键
     * @return 充填月计划
     */
    public PlanBackfillingMonthly selectPlanBackfillingMonthlyById(Long id);

    /**
     * 查询充填月计划列表
     *
     * @param planBackfillingMonthly 充填月计划
     * @return 充填月计划集合
     */
    public List<PlanBackfillingMonthlyVo> selectPlanBackfillingMonthlyList(PlanBackfillingMonthly planBackfillingMonthly);

    /**
     * 新增充填月计划
     * 
     * @param planBackfillingMonthly 充填月计划
     * @return 结果
     */
    public int insertPlanBackfillingMonthly(PlanBackfillingMonthly planBackfillingMonthly);

    /**
     * 修改充填月计划
     * 
     * @param planBackfillingMonthly 充填月计划
     * @return 结果
     */
    public int updatePlanBackfillingMonthly(PlanBackfillingMonthly planBackfillingMonthly);

    /**
     * 批量删除充填月计划
     * 
     * @param ids 需要删除的充填月计划主键集合
     * @return 结果
     */
    public int deletePlanBackfillingMonthlyByIds(Long[] ids);

    /**
     * 删除充填月计划信息
     *
     * @param id 充填月计划主键
     * @return 结果
     */
    public int deletePlanBackfillingMonthlyById(Long id);

    /**
     * 批量保存充填月计划
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    public int batchSavePlanBackfillingMonthly(List<PlanBackfillingMonthlyBatchDto> batchDataList);
}
