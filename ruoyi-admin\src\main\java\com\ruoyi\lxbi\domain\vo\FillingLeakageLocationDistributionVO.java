package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 充填漏浆检测位置分布VO (雷达图数据)
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FillingLeakageLocationDistributionVO {
    
    /**
     * 位置名称
     */
    private String locationName;
    
    /**
     * 深度标识 (如: -992m, -960m等)
     */
    private String depthLevel;
    
    /**
     * 报警点数量
     */
    private Long alarmPointCount;
    
    /**
     * X坐标 (用于雷达图定位)
     */
    private Double xCoordinate;
    
    /**
     * Y坐标 (用于雷达图定位)
     */
    private Double yCoordinate;
    
    /**
     * 位置代码
     */
    private String locationCode;
}
