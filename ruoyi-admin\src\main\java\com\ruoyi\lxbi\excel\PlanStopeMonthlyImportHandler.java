package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.domain.PlanStopeMonthly;
import com.ruoyi.lxbi.domain.excel.PlanStopeMonthlyImport;
import com.ruoyi.lxbi.service.IBaseStopeService;
import com.ruoyi.lxbi.service.IPlanStopeMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 采场月度计划导入处理器
 *
 * <AUTHOR>
 */
@Component
public class PlanStopeMonthlyImportHandler extends ExcelImportHandler<PlanStopeMonthlyImport> {

    @Autowired
    private IBaseStopeService baseStopeService;

    @Autowired
    private IPlanStopeMonthlyService planStopeMonthlyService;

    @Override
    protected Class<PlanStopeMonthlyImport> getEntityClass() {
        return PlanStopeMonthlyImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置采场选项
        List<ExcelOptionInfo> stopes = new ArrayList<>();
        BaseStope stopeQueryParam = new BaseStope();
        List<BaseStope> stopeList = baseStopeService.selectBaseStopeListAll(stopeQueryParam);

        for (BaseStope stope : stopeList) {
            stopes.add(new ExcelOptionInfo(
                    stope.getStopeId(),
                    stope.getStopeName()
            ));
        }
        context.setOptions("stope", stopes);
    }

    @Override
    protected void validateData(ExcelDataInfo<PlanStopeMonthlyImport> dataInfo, ExcelImportContext context) {
        PlanStopeMonthlyImport data = dataInfo.getData();

        // 验证计划月份格式
        if (data.getPlanDate() != null) {
            Pattern pattern = Pattern.compile("^\\d{4}\\d{2}$");
            if (!pattern.matcher(data.getPlanDate()).matches()) {
                dataInfo.addError("planDate", "计划月份格式不正确，应为yyyyMM格式");
            }
        }

        // 验证出矿量（这里是字符串类型，可以根据实际需求进行验证）
        if (data.getOreOutput() != null && data.getOreOutput().trim().isEmpty()) {
            dataInfo.addError("oreOutput", "出矿量不能为空");
        }
    }

    @Override
    protected void saveData(PlanStopeMonthlyImport data, ExcelImportContext context) {
        // 转换为实体对象
        PlanStopeMonthly entity = new PlanStopeMonthly();
        entity.setStopeId(data.getStopeId());
        entity.setPlanDate(data.getPlanDate());
        entity.setOreOutput(data.getOreOutput());
        
        // 保存到数据库
        planStopeMonthlyService.insertPlanStopeMonthly(entity);
    }

    @Override
    public List<PlanStopeMonthlyImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("采场月度计划验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("采场月度计划导入完成，总行数: " + ctx.getTotalRows());
    }
}
