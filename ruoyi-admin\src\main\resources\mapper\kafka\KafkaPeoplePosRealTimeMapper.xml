<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaPeoplePosRealTimeMapper">
    
    <resultMap type="KafkaPeoplePosRealTime" id="KafkaPeoplePosRealTimeResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="personnelCardCode"    column="personnel_card_code"    />
        <result property="personnelName"    column="personnel_name"    />
        <result property="inOutWellFlag"    column="in_out_well_flag"    />
        <result property="inWellTime"    column="in_well_time"    />
        <result property="outWellTime"    column="out_well_time"    />
        <result property="areaCode"    column="area_code"    />
        <result property="enterCurrentAreaTime"    column="enter_current_area_time"    />
        <result property="baseStationCode"    column="base_station_code"    />
        <result property="enterCurrentBaseStationTime"    column="enter_current_base_station_time"    />
        <result property="laborOrganizationMode"    column="labor_organization_mode"    />
        <result property="distanceFromBaseStation"    column="distance_from_base_station"    />
        <result property="personnelWorkStatus"    column="personnel_work_status"    />
        <result property="isMineLeader"    column="is_mine_leader"    />
        <result property="isSpecialPersonnel"    column="is_special_personnel"    />
        <result property="trajectoryBaseStationTimeSet"    column="trajectory_base_station_time_set"    />
        <result property="initDate"    column="init_date"    />
    </resultMap>

    <sql id="selectKafkaPeoplePosRealTimeVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, mine_name, data_upload_time, personnel_card_code, personnel_name, in_out_well_flag, in_well_time, out_well_time, area_code, enter_current_area_time, base_station_code, enter_current_base_station_time, labor_organization_mode, distance_from_base_station, personnel_work_status, is_mine_leader, is_special_personnel, trajectory_base_station_time_set, init_date from kafka_people_pos_real_time
    </sql>

    <select id="selectKafkaPeoplePosRealTimeList" parameterType="KafkaPeoplePosRealTime" resultMap="KafkaPeoplePosRealTimeResult">
        <include refid="selectKafkaPeoplePosRealTimeVo"/>
        <where>  
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="dataUploadTime != null "> and data_upload_time = #{dataUploadTime}</if>
            <if test="personnelCardCode != null  and personnelCardCode != ''"> and personnel_card_code = #{personnelCardCode}</if>
            <if test="personnelName != null  and personnelName != ''"> and personnel_name like concat('%', #{personnelName}, '%')</if>
            <if test="inOutWellFlag != null  and inOutWellFlag != ''"> and in_out_well_flag = #{inOutWellFlag}</if>
            <if test="inWellTime != null "> and in_well_time = #{inWellTime}</if>
            <if test="outWellTime != null "> and out_well_time = #{outWellTime}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="enterCurrentAreaTime != null "> and enter_current_area_time = #{enterCurrentAreaTime}</if>
            <if test="baseStationCode != null  and baseStationCode != ''"> and base_station_code = #{baseStationCode}</if>
            <if test="enterCurrentBaseStationTime != null "> and enter_current_base_station_time = #{enterCurrentBaseStationTime}</if>
            <if test="laborOrganizationMode != null  and laborOrganizationMode != ''"> and labor_organization_mode = #{laborOrganizationMode}</if>
            <if test="distanceFromBaseStation != null "> and distance_from_base_station = #{distanceFromBaseStation}</if>
            <if test="personnelWorkStatus != null  and personnelWorkStatus != ''"> and personnel_work_status = #{personnelWorkStatus}</if>
            <if test="isMineLeader != null  and isMineLeader != ''"> and is_mine_leader = #{isMineLeader}</if>
            <if test="isSpecialPersonnel != null  and isSpecialPersonnel != ''"> and is_special_personnel = #{isSpecialPersonnel}</if>
            <if test="trajectoryBaseStationTimeSet != null  and trajectoryBaseStationTimeSet != ''"> and trajectory_base_station_time_set = #{trajectoryBaseStationTimeSet}</if>
            <if test="initDate != null "> and init_date = #{initDate}</if>
        </where>
    </select>
    
    <select id="selectKafkaPeoplePosRealTimeById" parameterType="Long" resultMap="KafkaPeoplePosRealTimeResult">
        <include refid="selectKafkaPeoplePosRealTimeVo"/>
        where id = #{id}
    </select>

    <!-- 根据人员卡编码查询井下作业人员实时数据 -->
    <select id="selectByPersonnelCardCode" parameterType="String" resultMap="KafkaPeoplePosRealTimeResult">
        <include refid="selectKafkaPeoplePosRealTimeVo"/>
        where personnel_card_code = #{personnelCardCode}
        order by data_upload_time desc
        limit 1
    </select>

    <!-- 统计截止日期前的下井人次 (按初始日期统计) -->
    <select id="countDownWellPersonTimesByEndDate" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT personnel_card_code)
        FROM kafka_people_pos_real_time
        WHERE 1=1
            <if test="endDate != null and endDate != ''">
                AND init_date &lt;= #{endDate}::date + INTERVAL '1 day' - INTERVAL '1 second'
            </if>
            AND personnel_card_code IS NOT NULL
            AND personnel_card_code != ''
            AND init_date IS NOT NULL
            AND in_out_well_flag = '1'
    </select>

    <!-- 统计指定时间范围内的下井人次 -->
    <select id="countDownWellPersonTimesByDateRange" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT personnel_card_code)
        FROM kafka_people_pos_real_time
        WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND in_well_time >= #{startDate}::date
            </if>
            <if test="endDate != null and endDate != ''">
                AND in_well_time &lt;= #{endDate}::date + INTERVAL '1 day' - INTERVAL '1 second'
            </if>
            AND personnel_card_code IS NOT NULL
            AND personnel_card_code != ''
            AND in_out_well_flag = '1'
            AND in_well_time IS NOT NULL
    </select>

    <!-- 统计指定时间范围内的井下人员分布 -->
    <select id="selectUndergroundPersonnelDistribution" resultType="java.util.Map">
        SELECT 
            COALESCE(area_code, '未知区域') as area_name,
            COUNT(DISTINCT personnel_card_code) as personnel_count
        FROM kafka_people_pos_real_time
        WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND data_upload_time >= #{startDate}::date
            </if>
            <if test="endDate != null and endDate != ''">
                AND data_upload_time &lt;= #{endDate}::date + INTERVAL '1 day' - INTERVAL '1 second'
            </if>
            AND personnel_card_code IS NOT NULL
            AND personnel_card_code != ''
            AND in_out_well_flag = '1'
        GROUP BY area_code
        HAVING COUNT(DISTINCT personnel_card_code) > 0
        ORDER BY personnel_count DESC
    </select>
        
    <insert id="insertKafkaPeoplePosRealTime" parameterType="KafkaPeoplePosRealTime" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_people_pos_real_time
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null">file_encoding,</if>
            <if test="mineCode != null">mine_code,</if>
            <if test="mineName != null">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="personnelCardCode != null">personnel_card_code,</if>
            <if test="personnelName != null">personnel_name,</if>
            <if test="inOutWellFlag != null">in_out_well_flag,</if>
            <if test="inWellTime != null">in_well_time,</if>
            <if test="outWellTime != null">out_well_time,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="enterCurrentAreaTime != null">enter_current_area_time,</if>
            <if test="baseStationCode != null">base_station_code,</if>
            <if test="enterCurrentBaseStationTime != null">enter_current_base_station_time,</if>
            <if test="laborOrganizationMode != null">labor_organization_mode,</if>
            <if test="distanceFromBaseStation != null">distance_from_base_station,</if>
            <if test="personnelWorkStatus != null">personnel_work_status,</if>
            <if test="isMineLeader != null">is_mine_leader,</if>
            <if test="isSpecialPersonnel != null">is_special_personnel,</if>
            <if test="trajectoryBaseStationTimeSet != null">trajectory_base_station_time_set,</if>
            <if test="initDate != null">init_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null">#{fileEncoding},</if>
            <if test="mineCode != null">#{mineCode},</if>
            <if test="mineName != null">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="personnelCardCode != null">#{personnelCardCode},</if>
            <if test="personnelName != null">#{personnelName},</if>
            <if test="inOutWellFlag != null">#{inOutWellFlag},</if>
            <if test="inWellTime != null">#{inWellTime},</if>
            <if test="outWellTime != null">#{outWellTime},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="enterCurrentAreaTime != null">#{enterCurrentAreaTime},</if>
            <if test="baseStationCode != null">#{baseStationCode},</if>
            <if test="enterCurrentBaseStationTime != null">#{enterCurrentBaseStationTime},</if>
            <if test="laborOrganizationMode != null">#{laborOrganizationMode},</if>
            <if test="distanceFromBaseStation != null">#{distanceFromBaseStation},</if>
            <if test="personnelWorkStatus != null">#{personnelWorkStatus},</if>
            <if test="isMineLeader != null">#{isMineLeader},</if>
            <if test="isSpecialPersonnel != null">#{isSpecialPersonnel},</if>
            <if test="trajectoryBaseStationTimeSet != null">#{trajectoryBaseStationTimeSet},</if>
            <if test="initDate != null">#{initDate},</if>
         </trim>
    </insert>

    <update id="updateKafkaPeoplePosRealTime" parameterType="KafkaPeoplePosRealTime">
        update kafka_people_pos_real_time
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="mineName != null">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="personnelCardCode != null">personnel_card_code = #{personnelCardCode},</if>
            <if test="personnelName != null">personnel_name = #{personnelName},</if>
            <if test="inOutWellFlag != null">in_out_well_flag = #{inOutWellFlag},</if>
            <if test="inWellTime != null">in_well_time = #{inWellTime},</if>
            <if test="outWellTime != null">out_well_time = #{outWellTime},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="enterCurrentAreaTime != null">enter_current_area_time = #{enterCurrentAreaTime},</if>
            <if test="baseStationCode != null">base_station_code = #{baseStationCode},</if>
            <if test="enterCurrentBaseStationTime != null">enter_current_base_station_time = #{enterCurrentBaseStationTime},</if>
            <if test="laborOrganizationMode != null">labor_organization_mode = #{laborOrganizationMode},</if>
            <if test="distanceFromBaseStation != null">distance_from_base_station = #{distanceFromBaseStation},</if>
            <if test="personnelWorkStatus != null">personnel_work_status = #{personnelWorkStatus},</if>
            <if test="isMineLeader != null">is_mine_leader = #{isMineLeader},</if>
            <if test="isSpecialPersonnel != null">is_special_personnel = #{isSpecialPersonnel},</if>
            <if test="trajectoryBaseStationTimeSet != null">trajectory_base_station_time_set = #{trajectoryBaseStationTimeSet},</if>
            <if test="initDate != null">init_date = #{initDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaPeoplePosRealTimeById" parameterType="Long">
        delete from kafka_people_pos_real_time where id = #{id}
    </delete>

    <delete id="deleteKafkaPeoplePosRealTimeByIds" parameterType="String">
        delete from kafka_people_pos_real_time where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
