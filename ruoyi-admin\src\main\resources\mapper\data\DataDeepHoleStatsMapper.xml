<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataDeepHoleStatsMapper">

    <!-- 总体统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataDrillingTotalStats" id="DataDrillingTotalStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="totalProgressMeters"  column="total_progress_meters"  />
    </resultMap>

    <!-- 总体统计结果映射（含计划量） -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataDrillingTotalWithPlanStats" id="DataDrillingTotalWithPlanStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="totalProgressMeters"  column="total_progress_meters"  />
        <result property="planProgressMeters"   column="plan_progress_meters"   />
    </resultMap>

    <!-- 班次统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataDrillingPeriodStats" id="DataDrillingPeriodStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="workingPeriodId"      column="working_period_id"      />
        <result property="workingPeriodName"    column="working_period_name"    />
        <result property="totalProgressMeters"  column="total_progress_meters"  />
    </resultMap>

    <!-- 项目部门统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataDrillingDepartmentStats" id="DataDrillingDepartmentStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="projectDepartmentId"  column="project_department_id"  />
        <result property="projectDepartmentName" column="project_department_name" />
        <result property="totalProgressMeters"  column="total_progress_meters"  />
    </resultMap>

    <!-- 采场统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataDrillingStopeStats" id="DataDrillingStopeStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="stopeId"              column="stope_id"               />
        <result property="stopeName"            column="stope_name"             />
        <result property="totalProgressMeters"  column="total_progress_meters"  />
    </resultMap>

    <!-- 工作面统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataDrillingWorkingFaceStats" id="DataDrillingWorkingFaceStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="workingFaceId"        column="working_face_id"        />
        <result property="workingFaceName"      column="working_face_name"      />
        <result property="totalProgressMeters"  column="total_progress_meters"  />
    </resultMap>

    <!-- 项目部门统计结果映射（含计划量） -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataDrillingDepartmentWithPlanStats" id="DataDrillingDepartmentWithPlanStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="projectDepartmentId"  column="project_department_id"  />
        <result property="projectDepartmentName" column="project_department_name" />
        <result property="totalProgressMeters"  column="total_progress_meters"  />
        <result property="planProgressMeters"   column="plan_progress_meters"   />
    </resultMap>
   
    <!-- ========== 总体统计查询方法 ========== -->
    
    <select id="selectDailyTotalStats" resultMap="DataDrillingTotalStatsResult">
        select operation_date, total_progress_meters
        from vdata_drilling_daily_type_stats
        where drilling_type = '2'
        <if test="startDate != null">
            AND operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND operation_date &lt;= #{endDate}
        </if>
        order by operation_date
    </select>

    <select id="selectWeeklyTotalStats" resultMap="DataDrillingTotalStatsResult">
        select year, week_number, week_start_date, week_end_date, total_progress_meters
        from vdata_drilling_weekly_type_stats
        where drilling_type = '2'
        <if test="startDate != null">
            AND week_start_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND week_end_date &lt;= #{endDate}
        </if>
        order by year, week_number
    </select>

    <select id="selectMonthlyTotalStats" resultMap="DataDrillingTotalStatsResult">
        select year, month, total_progress_meters
        from vdata_drilling_monthly_type_stats
        where drilling_type = '2'
        <if test="startDate != null">
            AND MAKE_DATE(year, month, 1) &gt;= DATE_TRUNC('month', #{startDate}::date)
        </if>
        <if test="endDate != null">
            AND MAKE_DATE(year, month, 1) &lt;= DATE_TRUNC('month', #{endDate}::date)
        </if>
        order by year, month
    </select>

    <select id="selectYearlyTotalStats" resultMap="DataDrillingTotalStatsResult">
        select year, total_progress_meters
        from vdata_drilling_yearly_type_stats
        where drilling_type = '2'
        <if test="startDate != null">
            AND year &gt;= EXTRACT(YEAR FROM #{startDate})
        </if>
        <if test="endDate != null">
            AND year &lt;= EXTRACT(YEAR FROM #{endDate})
        </if>
        order by year
    </select>

    <!-- ========== 总体统计查询方法（含计划量） ========== -->

    <select id="selectDailyTotalWithPlanStats" resultMap="DataDrillingTotalWithPlanStatsResult">
        SELECT
            da.operation_date,
            da.total_progress_meters,
            ROUND(pdh.deep_hole_meter / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_progress_meters
        FROM vdata_drilling_daily_type_stats da
        CROSS JOIN get_financial_month(da.operation_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(deep_hole_meter) as deep_hole_meter
            FROM plan_deep_hole_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM'))
        ) pdh ON fm.financial_year = pdh.year AND fm.financial_month = pdh.month
        WHERE da.drilling_type = '2'
        <if test="startDate != null">
            AND operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND operation_date &lt;= #{endDate}
        </if>
        ORDER BY da.operation_date
    </select>

    <select id="selectWeeklyTotalWithPlanStats" resultMap="DataDrillingTotalWithPlanStatsResult">
        SELECT
            da.year,
            da.week_number,
            da.week_start_date,
            da.week_end_date,
            da.total_progress_meters,
            ROUND(pdh.deep_hole_meter / 4, 2) as plan_progress_meters
        FROM vdata_drilling_weekly_type_stats da
        CROSS JOIN get_financial_month(da.week_start_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(deep_hole_meter) as deep_hole_meter
            FROM plan_deep_hole_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM'))
        ) pdh ON fm.financial_year = pdh.year AND fm.financial_month = pdh.month
        WHERE da.drilling_type = '2'
        <if test="startDate != null">
            AND week_start_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND week_end_date &lt;= #{endDate}
        </if>
        ORDER BY da.year, da.week_number
    </select>

    <select id="selectMonthlyTotalWithPlanStats" resultMap="DataDrillingTotalWithPlanStatsResult">
        SELECT
            da.year,
            da.month,
            da.total_progress_meters,
            pdh.deep_hole_meter as plan_progress_meters
        FROM vdata_drilling_monthly_type_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(deep_hole_meter) as deep_hole_meter
            FROM plan_deep_hole_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM'))
        ) pdh ON da.year = pdh.year AND da.month = pdh.month
        WHERE da.drilling_type = '2'
        <if test="startDate != null">
            AND MAKE_DATE(da.year, da.month, 1) &gt;= DATE_TRUNC('month', #{startDate}::date)
        </if>
        <if test="endDate != null">
            AND MAKE_DATE(da.year, da.month, 1) &lt;= DATE_TRUNC('month', #{endDate}::date)
        </if>
        ORDER BY da.year, da.month
    </select>

    <select id="selectYearlyTotalWithPlanStats" resultMap="DataDrillingTotalWithPlanStatsResult">
        SELECT
            da.year,
            da.total_progress_meters,
            pdh.deep_hole_meter as plan_progress_meters
        FROM vdata_drilling_yearly_type_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                SUM(deep_hole_meter) as deep_hole_meter
            FROM plan_deep_hole_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM'))
        ) pdh ON da.year = pdh.year
        WHERE da.drilling_type = '2'
        <if test="startDate != null">
            AND da.year &gt;= EXTRACT(YEAR FROM #{startDate})
        </if>
        <if test="endDate != null">
            AND da.year &lt;= EXTRACT(YEAR FROM #{endDate})
        </if>
        ORDER BY da.year
    </select>

    <!-- ========== 班次统计查询方法 ========== -->

    <select id="selectDailyPeriodStats" resultMap="DataDrillingPeriodStatsResult">
        select operation_date, working_period_id, working_period_name, total_progress_meters
        from vdata_drilling_daily_period_type_stats
        where drilling_type = '2'
        <if test="startDate != null">
            AND operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND operation_date &lt;= #{endDate}
        </if>
        order by operation_date, working_period_id
    </select>

    <select id="selectWeeklyPeriodStats" resultMap="DataDrillingPeriodStatsResult">
        SELECT
            wk.week_year AS year,
            wk.week_number,
            wk.week_start_date,
            wk.week_end_date,
            d.working_period_id,
            wp.working_period_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_week_thu_to_wed(d.operation_date) AS wk
        LEFT JOIN base_working_period wp ON d.working_period_id = wp.working_period_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, d.working_period_id, wp.working_period_name
        ORDER BY year, week_number, working_period_id
    </select>

    <select id="selectMonthlyPeriodStats" resultMap="DataDrillingPeriodStatsResult">
        SELECT
            fm.financial_year AS year,
            fm.financial_month AS month,
            d.working_period_id,
            wp.working_period_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_financial_month(d.operation_date) AS fm
        LEFT JOIN base_working_period wp ON d.working_period_id = wp.working_period_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY fm.financial_year, fm.financial_month, d.working_period_id, wp.working_period_name
        ORDER BY year, month, working_period_id
    </select>

    <select id="selectYearlyPeriodStats" resultMap="DataDrillingPeriodStatsResult">
        SELECT
            fy.financial_year AS year,
            d.working_period_id,
            wp.working_period_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_financial_year(d.operation_date) AS fy
        LEFT JOIN base_working_period wp ON d.working_period_id = wp.working_period_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY fy.financial_year, d.working_period_id, wp.working_period_name
        ORDER BY year, working_period_id
    </select>

    <!-- ========== 项目部门统计查询方法 ========== -->

    <select id="selectDailyDepartmentStats" resultMap="DataDrillingDepartmentStatsResult">
        select operation_date, project_department_id, project_department_name, total_progress_meters
        from vdata_drilling_daily_department_type_stats
        where drilling_type = '2'
        <if test="startDate != null">
            AND operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND operation_date &lt;= #{endDate}
        </if>
        order by operation_date, project_department_id
    </select>

    <select id="selectWeeklyDepartmentStats" resultMap="DataDrillingDepartmentStatsResult">
        SELECT
            wk.week_year AS year,
            wk.week_number,
            wk.week_start_date,
            wk.week_end_date,
            d.project_department_id,
            pd.project_department_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_week_thu_to_wed(d.operation_date) AS wk
        LEFT JOIN base_project_department pd ON d.project_department_id = pd.project_department_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, d.project_department_id, pd.project_department_name
        ORDER BY year, week_number, project_department_id
    </select>

    <select id="selectMonthlyDepartmentStats" resultMap="DataDrillingDepartmentStatsResult">
        SELECT
            fm.financial_year AS year,
            fm.financial_month AS month,
            d.project_department_id,
            pd.project_department_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_financial_month(d.operation_date) AS fm
        LEFT JOIN base_project_department pd ON d.project_department_id = pd.project_department_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY fm.financial_year, fm.financial_month, d.project_department_id, pd.project_department_name
        ORDER BY year, month, project_department_id
    </select>

    <select id="selectYearlyDepartmentStats" resultMap="DataDrillingDepartmentStatsResult">
        SELECT
            fy.financial_year AS year,
            d.project_department_id,
            pd.project_department_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_financial_year(d.operation_date) AS fy
        LEFT JOIN base_project_department pd ON d.project_department_id = pd.project_department_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY fy.financial_year, d.project_department_id, pd.project_department_name
        ORDER BY year, project_department_id
    </select>

    <!-- ========== 采场统计查询方法 ========== -->

    <select id="selectDailyStopeStats" resultMap="DataDrillingStopeStatsResult">
        select operation_date, stope_id, stope_name, total_progress_meters
        from vdata_drilling_daily_stope_type_stats
        where drilling_type = '2'
        <if test="startDate != null">
            AND operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND operation_date &lt;= #{endDate}
        </if>
        order by operation_date, stope_id
    </select>

    <select id="selectWeeklyStopeStats" resultMap="DataDrillingStopeStatsResult">
        SELECT
            wk.week_year AS year,
            wk.week_number,
            wk.week_start_date,
            wk.week_end_date,
            d.stope_id,
            s.stope_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_week_thu_to_wed(d.operation_date) AS wk
        LEFT JOIN base_stope s ON d.stope_id = s.stope_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, d.stope_id, s.stope_name
        ORDER BY year, week_number, stope_id
    </select>

    <select id="selectMonthlyStopeStats" resultMap="DataDrillingStopeStatsResult">
        SELECT
            fm.financial_year AS year,
            fm.financial_month AS month,
            d.stope_id,
            s.stope_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_financial_month(d.operation_date) AS fm
        LEFT JOIN base_stope s ON d.stope_id = s.stope_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY fm.financial_year, fm.financial_month, d.stope_id, s.stope_name
        ORDER BY year, month, stope_id
    </select>

    <select id="selectYearlyStopeStats" resultMap="DataDrillingStopeStatsResult">
        SELECT
            fy.financial_year AS year,
            d.stope_id,
            s.stope_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_financial_year(d.operation_date) AS fy
        LEFT JOIN base_stope s ON d.stope_id = s.stope_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY fy.financial_year, d.stope_id, s.stope_name
        ORDER BY year, stope_id
    </select>

    <!-- ========== 工作面统计查询方法 ========== -->

    <select id="selectDailyWorkingFaceStats" resultMap="DataDrillingWorkingFaceStatsResult">
        select operation_date, working_face_id, working_face_name, total_progress_meters
        from vdata_drilling_daily_working_face_type_stats
        where drilling_type = '2'
        <if test="startDate != null">
            AND operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND operation_date &lt;= #{endDate}
        </if>
        order by operation_date, working_face_id
    </select>

    <select id="selectWeeklyWorkingFaceStats" resultMap="DataDrillingWorkingFaceStatsResult">
        SELECT
            wk.week_year AS year,
            wk.week_number,
            wk.week_start_date,
            wk.week_end_date,
            d.working_face_id,
            wf.working_face_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_week_thu_to_wed(d.operation_date) AS wk
        LEFT JOIN base_working_face wf ON d.working_face_id = wf.working_face_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY wk.week_year, wk.week_number, wk.week_start_date, wk.week_end_date, d.working_face_id, wf.working_face_name
        ORDER BY year, week_number, working_face_id
    </select>

    <select id="selectMonthlyWorkingFaceStats" resultMap="DataDrillingWorkingFaceStatsResult">
        SELECT
            fm.financial_year AS year,
            fm.financial_month AS month,
            d.working_face_id,
            wf.working_face_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_financial_month(d.operation_date) AS fm
        LEFT JOIN base_working_face wf ON d.working_face_id = wf.working_face_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY fm.financial_year, fm.financial_month, d.working_face_id, wf.working_face_name
        ORDER BY year, month, working_face_id
    </select>

    <select id="selectYearlyWorkingFaceStats" resultMap="DataDrillingWorkingFaceStatsResult">
        SELECT
            fy.financial_year AS year,
            d.working_face_id,
            wf.working_face_name,
            SUM(d.progress_meters) AS total_progress_meters
        FROM data_drilling d
        CROSS JOIN get_financial_year(d.operation_date) AS fy
        LEFT JOIN base_working_face wf ON d.working_face_id = wf.working_face_id
        WHERE d.drilling_type = '2'
        <if test="startDate != null">
            AND d.operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND d.operation_date &lt;= #{endDate}
        </if>
        GROUP BY fy.financial_year, d.working_face_id, wf.working_face_name
        ORDER BY year, working_face_id
    </select>

    <!-- ========== 项目部门统计查询方法（含计划量） ========== -->

    <select id="selectDailyDepartmentWithPlanStats" resultMap="DataDrillingDepartmentWithPlanStatsResult">
        SELECT
            da.operation_date,
            da.project_department_id,
            da.project_department_name,
            da.total_progress_meters,
            ROUND(pdh.deep_hole_meter / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_progress_meters
        FROM vdata_drilling_daily_department_type_stats da
        CROSS JOIN get_financial_month(da.operation_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                project_department_id,
                SUM(deep_hole_meter) as deep_hole_meter
            FROM plan_deep_hole_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) pdh ON fm.financial_year = pdh.year AND fm.financial_month = pdh.month AND da.project_department_id = pdh.project_department_id
        WHERE da.drilling_type = '2'
        <if test="startDate != null">
            AND operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND operation_date &lt;= #{endDate}
        </if>
        ORDER BY da.operation_date, da.project_department_id
    </select>

    <select id="selectWeeklyDepartmentWithPlanStats" resultMap="DataDrillingDepartmentWithPlanStatsResult">
        SELECT
            da.year,
            da.week_number,
            da.week_start_date,
            da.week_end_date,
            da.project_department_id,
            da.project_department_name,
            da.total_progress_meters,
            ROUND(pdh.deep_hole_meter / 4, 2) as plan_progress_meters
        FROM vdata_drilling_weekly_department_type_stats da
        CROSS JOIN get_financial_month(da.week_start_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                project_department_id,
                SUM(deep_hole_meter) as deep_hole_meter
            FROM plan_deep_hole_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) pdh ON fm.financial_year = pdh.year AND fm.financial_month = pdh.month AND da.project_department_id = pdh.project_department_id
        WHERE da.drilling_type = '2'
        <if test="startDate != null">
            AND week_start_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND week_end_date &lt;= #{endDate}
        </if>
        ORDER BY da.year, da.week_number, da.project_department_id
    </select>

    <select id="selectMonthlyDepartmentWithPlanStats" resultMap="DataDrillingDepartmentWithPlanStatsResult">
        SELECT
            da.year,
            da.month,
            da.project_department_id,
            da.project_department_name,
            da.total_progress_meters,
            pdh.deep_hole_meter as plan_progress_meters
        FROM vdata_drilling_monthly_department_type_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                project_department_id,
                SUM(deep_hole_meter) as deep_hole_meter
            FROM plan_deep_hole_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) pdh ON da.year = pdh.year AND da.month = pdh.month AND da.project_department_id = pdh.project_department_id
        WHERE da.drilling_type = '2'
        <if test="startDate != null">
            AND MAKE_DATE(da.year, da.month, 1) &gt;= DATE_TRUNC('month', #{startDate}::date)
        </if>
        <if test="endDate != null">
            AND MAKE_DATE(da.year, da.month, 1) &lt;= DATE_TRUNC('month', #{endDate}::date)
        </if>
        ORDER BY da.year, da.month, da.project_department_id
    </select>

    <select id="selectYearlyDepartmentWithPlanStats" resultMap="DataDrillingDepartmentWithPlanStatsResult">
        SELECT
            da.year,
            da.project_department_id,
            da.project_department_name,
            da.total_progress_meters,
            pdh.deep_hole_meter as plan_progress_meters
        FROM vdata_drilling_yearly_department_type_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                project_department_id,
                SUM(deep_hole_meter) as deep_hole_meter
            FROM plan_deep_hole_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) pdh ON da.year = pdh.year AND da.project_department_id = pdh.project_department_id
        WHERE da.drilling_type = '2'
        <if test="startDate != null">
            AND da.year &gt;= EXTRACT(YEAR FROM #{startDate})
        </if>
        <if test="endDate != null">
            AND da.year &lt;= EXTRACT(YEAR FROM #{endDate})
        </if>
        ORDER BY da.year, da.project_department_id
    </select>

</mapper>
