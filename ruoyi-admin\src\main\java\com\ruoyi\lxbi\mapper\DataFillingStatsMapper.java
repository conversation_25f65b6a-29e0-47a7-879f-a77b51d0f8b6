package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.lxbi.domain.response.DataFillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataFillingStopeStats;

/**
 * 充填数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface DataFillingStatsMapper 
{
    // ========== 总体统计查询方法（含计划量） ==========
    
    /**
     * 查询总体统计数据列表（含计划量） (日)
     */
    public List<DataFillingTotalWithPlanStats> selectDailyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (周)
     */
    public List<DataFillingTotalWithPlanStats> selectWeeklyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (月)
     */
    public List<DataFillingTotalWithPlanStats> selectMonthlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (年)
     */
    public List<DataFillingTotalWithPlanStats> selectYearlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 采场统计查询方法 ==========
    
    /**
     * 查询采场统计数据列表 (日)
     */
    public List<DataFillingStopeStats> selectDailyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询采场统计数据列表 (周)
     */
    public List<DataFillingStopeStats> selectWeeklyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询采场统计数据列表 (月)
     */
    public List<DataFillingStopeStats> selectMonthlyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询采场统计数据列表 (年)
     */
    public List<DataFillingStopeStats> selectYearlyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
