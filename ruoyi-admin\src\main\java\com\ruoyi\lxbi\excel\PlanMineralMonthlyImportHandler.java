package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.PlanMineralMonthly;
import com.ruoyi.lxbi.domain.excel.PlanMineralMonthlyImport;
import com.ruoyi.lxbi.service.IPlanMineralMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 选矿整体月计划导入处理器
 *
 * <AUTHOR>
 */
@Component
public class PlanMineralMonthlyImportHandler extends ExcelImportHandler<PlanMineralMonthlyImport> {

    @Autowired
    private IPlanMineralMonthlyService planMineralMonthlyService;

    @Override
    protected Class<PlanMineralMonthlyImport> getEntityClass() {
        return PlanMineralMonthlyImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 选矿整体月计划不需要额外的下拉选项
    }

    @Override
    protected void validateData(ExcelDataInfo<PlanMineralMonthlyImport> dataInfo, ExcelImportContext context) {
        PlanMineralMonthlyImport data = dataInfo.getData();

        // 验证计划月份格式
        if (data.getPlanDate() != null) {
            Pattern pattern = Pattern.compile("^\\d{4}\\d{2}$");
            if (!pattern.matcher(data.getPlanDate()).matches()) {
                dataInfo.addError("planDate", "计划月份格式不正确，应为yyyyMM格式");
            }
        }

        // 验证原矿处理量
        if (data.getRawOreProcessingVolume() != null && data.getRawOreProcessingVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("rawOreProcessingVolume", "原矿处理量不能为负数");
        }

        // 验证干选量
        if (data.getDrySeparationVolume() != null && data.getDrySeparationVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("drySeparationVolume", "干选量不能为负数");
        }

        // 验证入磨量
        if (data.getGrindingFeedVolume() != null && data.getGrindingFeedVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("grindingFeedVolume", "入磨量不能为负数");
        }

        // 验证原矿品位
        if (data.getRawOreGradeTfe() != null) {
            if (data.getRawOreGradeTfe().compareTo(BigDecimal.ZERO) < 0 || data.getRawOreGradeTfe().compareTo(new BigDecimal("100")) > 0) {
                dataInfo.addError("rawOreGradeTfe", "原矿品位必须在0-100%之间");
            }
        }

        // 验证精矿品位
        if (data.getConcentrateGrade() != null) {
            if (data.getConcentrateGrade().compareTo(BigDecimal.ZERO) < 0 || data.getConcentrateGrade().compareTo(new BigDecimal("100")) > 0) {
                dataInfo.addError("concentrateGrade", "精矿品位必须在0-100%之间");
            }
        }

        // 验证尾矿品位
        if (data.getTailingGradeTfe() != null) {
            if (data.getTailingGradeTfe().compareTo(BigDecimal.ZERO) < 0 || data.getTailingGradeTfe().compareTo(new BigDecimal("100")) > 0) {
                dataInfo.addError("tailingGradeTfe", "尾矿品位必须在0-100%之间");
            }
        }

        // 验证精矿量
        if (data.getConcentrateVolume() != null && data.getConcentrateVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("concentrateVolume", "精矿量不能为负数");
        }
    }

    @Override
    protected void saveData(PlanMineralMonthlyImport data, ExcelImportContext context) {
        // 转换为实体对象
        PlanMineralMonthly entity = new PlanMineralMonthly();
        entity.setRawOreProcessingVolume(data.getRawOreProcessingVolume());
        entity.setDrySeparationVolume(data.getDrySeparationVolume());
        entity.setGrindingFeedVolume(data.getGrindingFeedVolume());
        entity.setRawOreGradeTfe(data.getRawOreGradeTfe());
        entity.setConcentrateGrade(data.getConcentrateGrade());
        entity.setTailingGradeTfe(data.getTailingGradeTfe());
        entity.setConcentrateVolume(data.getConcentrateVolume());
        entity.setPlanDate(data.getPlanDate());
        
        // 保存到数据库
        planMineralMonthlyService.insertPlanMineralMonthly(entity);
    }

    @Override
    public List<PlanMineralMonthlyImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("选矿整体月计划验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("选矿整体月计划导入完成，总行数: " + ctx.getTotalRows());
    }
}
