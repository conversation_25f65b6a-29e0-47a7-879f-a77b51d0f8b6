package com.ruoyi.common.annotation;

import java.lang.annotation.*;

/**
 * 表头配置注解
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TableHeader {
    
    /**
     * 列标签
     */
    String label();
    
    /**
     * 列属性名（默认使用字段名）
     */
    String prop() default "";
    
    /**
     * 排序顺序
     */
    int order() default 0;

    /**
     * 列宽度
     */
    int width() default 0;
    
    /**
     * 父级分组标签路径，数组形式表示多级，如{"销售数据", "Q1", "1月"}
     */
    String[] parentPath() default {};
    
    /**
     * 是否启用行合并
     */
    boolean enableRowMerge() default false;
    
    /**
     * 行合并关联列
     */
    String relateColumn() default "";
    
    /**
     * 列合并分组
     */
    String[] colMergeGroup() default {};
}