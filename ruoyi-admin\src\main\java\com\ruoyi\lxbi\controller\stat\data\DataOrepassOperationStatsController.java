package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DataOrepassOperationStatsRequest;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationPeriodStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationOrepassStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationDepartmentStats;
import com.ruoyi.lxbi.service.IDataOrepassOperationStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 溜井放矿数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/data/stats/orepass")
public class DataOrepassOperationStatsController {
    @Autowired
    private IDataOrepassOperationStatsService dataOrepassOperationStatsService;

    /**
     * 查询月度放矿量柱状图数据
     * 对应图表一：按月度显示放矿量的柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:orepass:01a')")
    @GetMapping("/01a")
    public R<List<DataOrepassOperationStats>> volumeAll(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                         @RequestParam(value = "startDate", required = false) String startDate,
                                                         @RequestParam(value = "endDate", required = false) String endDate) {
        DataOrepassOperationStatsRequest request = new DataOrepassOperationStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataOrepassOperationStats> stats = dataOrepassOperationStatsService.selectStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询班次放矿量分布柱状图数据
     * 对应图表二：按班次分组的放矿量柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:orepass:01c')")
    @GetMapping("/01c")
    public R<List<DataOrepassOperationPeriodStats>> volumePeriod(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                  @RequestParam(value = "startDate", required = false) String startDate,
                                                                  @RequestParam(value = "endDate", required = false) String endDate) {
        DataOrepassOperationStatsRequest request = new DataOrepassOperationStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataOrepassOperationPeriodStats> stats = dataOrepassOperationStatsService.selectPeriodStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询溜井放矿量分布柱状图数据
     * 对应图表三：按溜井分组的放矿量柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:orepass:01d')")
    @GetMapping("/01d")
    public R<List<DataOrepassOperationOrepassStats>> volumeOrepass(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                    @RequestParam(value = "startDate", required = false) String startDate,
                                                                    @RequestParam(value = "endDate", required = false) String endDate) {
        DataOrepassOperationStatsRequest request = new DataOrepassOperationStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataOrepassOperationOrepassStats> stats = dataOrepassOperationStatsService.selectOrepassStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询项目部门放矿量分布柱状图数据
     * 对应图表四：按项目部门分组的放矿量柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:orepass:01b')")
    @GetMapping("/01b")
    public R<List<DataOrepassOperationDepartmentStats>> volumeDepartment(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                          @RequestParam(value = "startDate", required = false) String startDate,
                                                                          @RequestParam(value = "endDate", required = false) String endDate) {
        DataOrepassOperationStatsRequest request = new DataOrepassOperationStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataOrepassOperationDepartmentStats> stats = dataOrepassOperationStatsService.selectDepartmentStatsList(request, viewType);
        return R.ok(stats);
    }

}
