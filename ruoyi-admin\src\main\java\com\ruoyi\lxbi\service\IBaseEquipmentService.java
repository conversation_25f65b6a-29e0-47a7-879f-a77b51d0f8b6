package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.BaseEquipment;

/**
 * 设备数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IBaseEquipmentService 
{
    /**
     * 查询设备数据
     * 
     * @param id 设备数据主键
     * @return 设备数据
     */
    public BaseEquipment selectBaseEquipmentById(Long id);

    /**
     * 查询设备数据列表
     *
     * @param baseEquipment 设备数据
     * @return 设备数据集合
     */
    public List<BaseEquipment> selectBaseEquipmentList(BaseEquipment baseEquipment);

    /**
     * 查询所有设备数据列表（不分页）
     *
     * @return 设备数据集合
     */
    public List<BaseEquipment> selectBaseEquipmentListAll(BaseEquipment baseEquipment);

    /**
     * 新增设备数据
     * 
     * @param baseEquipment 设备数据
     * @return 结果
     */
    public int insertBaseEquipment(BaseEquipment baseEquipment);

    /**
     * 修改设备数据
     * 
     * @param baseEquipment 设备数据
     * @return 结果
     */
    public int updateBaseEquipment(BaseEquipment baseEquipment);

    /**
     * 批量删除设备数据
     *
     * @param ids 需要删除的设备数据主键集合
     * @return 结果
     */
    public int deleteBaseEquipmentByIds(Long[] ids);

    /**
     * 删除设备数据信息
     *
     * @param id 设备数据主键
     * @return 结果
     */
    public int deleteBaseEquipmentById(Long id);

    /**
     * 批量逻辑删除设备数据
     *
     * @param ids 需要删除的设备数据主键集合
     * @return 结果
     */
    public int logicDeleteBaseEquipmentByIds(Long[] ids);
}
