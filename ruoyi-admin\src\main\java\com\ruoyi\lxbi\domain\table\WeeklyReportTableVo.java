package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 周报数据表格VO
 */
@Data
@TableConfig(code = "mining_weekly_report", name = "生产周报", description = "生产数据周报统计表格")
public class WeeklyReportTableVo {
    
    @TableHeader(label = "序号", order = 1)
    private String serialNumber;
    
    @TableHeader(label = "名称", order = 2, enableRowMerge = true)
    private String name;
    
    @TableHeader(label = "单位", order = 3)
    private String unit;
    
    @TableHeader(label = "月计划", order = 4)
    private BigDecimal monthlyPlan;
    
    @TableHeader(label = "月累计", order = 5)
    private BigDecimal monthlyAccumulated;
    
    @TableHeader(label = "月完成率（%）", order = 6)
    private String monthlyCompletionRate;
    
    @TableHeader(label = "周计划", order = 7)
    private BigDecimal weeklyPlan;
    
    @TableHeader(label = "周完成", order = 8)
    private BigDecimal weeklyCompleted;
    
    @TableHeader(label = "周完成率（%）", order = 9)
    private String weeklyCompletionRate;
}
