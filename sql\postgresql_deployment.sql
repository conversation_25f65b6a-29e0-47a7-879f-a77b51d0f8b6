-- PostgreSQL 人员安全统计系统部署脚本
-- 执行前请确保已连接到正确的数据库

-- =====================================================
-- 1. 创建人员超时定位数据表
-- =====================================================

-- 删除表（如果存在）
DROP TABLE IF EXISTS kafka_people_position_time_over CASCADE;

-- 创建人员超时定位数据表
CREATE TABLE kafka_people_position_time_over (
    id BIGSERIAL PRIMARY KEY,
    mine_code VARCHAR(50),
    mine_name VARCHAR(100),
    data_upload_time TIMESTAMP,
    person_card_code VARCHAR(50) NOT NULL,
    person_name VARCHAR(50),
    enter_well_time TIMESTAMP,
    alarm_start_time TIMESTAMP,
    alarm_end_time TIMESTAMP,
    area_code VARCHAR(50),
    enter_current_area_time TIMESTAMP,
    base_station_code VARCHAR(50),
    enter_current_base_station_time TIMESTAMP,
    status BIGINT DEFAULT 1,
    is_deleted BIGINT DEFAULT 0,
    create_by VARCHAR(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500) DEFAULT NULL
);

-- =====================================================
-- 2. 添加表和字段注释
-- =====================================================

COMMENT ON TABLE kafka_people_position_time_over IS '人员超时定位数据表';
COMMENT ON COLUMN kafka_people_position_time_over.id IS '主键ID';
COMMENT ON COLUMN kafka_people_position_time_over.mine_code IS '煤矿编码';
COMMENT ON COLUMN kafka_people_position_time_over.mine_name IS '矿井名称';
COMMENT ON COLUMN kafka_people_position_time_over.data_upload_time IS '数据上传时间';
COMMENT ON COLUMN kafka_people_position_time_over.person_card_code IS '人员卡编码';
COMMENT ON COLUMN kafka_people_position_time_over.person_name IS '姓名';
COMMENT ON COLUMN kafka_people_position_time_over.enter_well_time IS '入井时刻';
COMMENT ON COLUMN kafka_people_position_time_over.alarm_start_time IS '报警开始时间';
COMMENT ON COLUMN kafka_people_position_time_over.alarm_end_time IS '报警结束时间';
COMMENT ON COLUMN kafka_people_position_time_over.area_code IS '区域编码';
COMMENT ON COLUMN kafka_people_position_time_over.enter_current_area_time IS '进入当前所处区域时间';
COMMENT ON COLUMN kafka_people_position_time_over.base_station_code IS '基站编码';
COMMENT ON COLUMN kafka_people_position_time_over.enter_current_base_station_time IS '进入当前所处基站时刻';
COMMENT ON COLUMN kafka_people_position_time_over.status IS '状态(1:正常 0:异常)';
COMMENT ON COLUMN kafka_people_position_time_over.is_deleted IS '是否删除(0:未删除 1:已删除)';
COMMENT ON COLUMN kafka_people_position_time_over.create_by IS '创建者';
COMMENT ON COLUMN kafka_people_position_time_over.create_time IS '创建时间';
COMMENT ON COLUMN kafka_people_position_time_over.update_by IS '更新者';
COMMENT ON COLUMN kafka_people_position_time_over.update_time IS '更新时间';
COMMENT ON COLUMN kafka_people_position_time_over.remark IS '备注';

-- =====================================================
-- 3. 创建索引
-- =====================================================

-- 基础索引
CREATE INDEX idx_kppto_person_card_code ON kafka_people_position_time_over(person_card_code);
CREATE INDEX idx_kppto_alarm_start_time ON kafka_people_position_time_over(alarm_start_time);
CREATE INDEX idx_kppto_data_upload_time ON kafka_people_position_time_over(data_upload_time);
CREATE INDEX idx_kppto_area_code ON kafka_people_position_time_over(area_code);
CREATE INDEX idx_kppto_person_name ON kafka_people_position_time_over(person_name);
CREATE INDEX idx_kppto_status_deleted ON kafka_people_position_time_over(status, is_deleted);

-- 复合索引用于唯一性检查（PostgreSQL部分索引）
CREATE UNIQUE INDEX idx_kppto_person_alarm_unique 
ON kafka_people_position_time_over(person_card_code, alarm_start_time) 
WHERE is_deleted = 0;

-- 日期范围查询优化索引
CREATE INDEX idx_kppto_date_range ON kafka_people_position_time_over(data_upload_time, is_deleted, status);

-- =====================================================
-- 4. 插入测试数据
-- =====================================================

INSERT INTO kafka_people_position_time_over (
    mine_code, mine_name, data_upload_time, person_card_code, person_name,
    enter_well_time, alarm_start_time, alarm_end_time, area_code,
    enter_current_area_time, base_station_code, enter_current_base_station_time,
    status, is_deleted, create_by, create_time, update_by, update_time
) VALUES 
-- 张三的超时记录
('MINE001', '示例煤矿', '2025-08-25 14:30:00', 'CARD001', '张三',
 '2025-08-25 08:00:00', '2025-08-25 14:00:00', '2025-08-25 14:30:00', 'AREA001',
 '2025-08-25 13:00:00', 'BASE001', '2025-08-25 13:00:00',
 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

-- 李四的超时记录
('MINE001', '示例煤矿', '2025-08-25 15:00:00', 'CARD002', '李四',
 '2025-08-25 08:30:00', '2025-08-25 14:30:00', '2025-08-25 15:00:00', 'AREA002',
 '2025-08-25 13:30:00', 'BASE002', '2025-08-25 13:30:00',
 1, 0, 'system', '2025-08-25 15:00:00', 'system', '2025-08-25 15:00:00'),

-- 王五的超时记录
('MINE001', '示例煤矿', '2025-08-25 15:30:00', 'CARD003', '王五',
 '2025-08-25 09:00:00', '2025-08-25 15:00:00', '2025-08-25 15:30:00', 'AREA001',
 '2025-08-25 14:00:00', 'BASE001', '2025-08-25 14:00:00',
 1, 0, 'system', '2025-08-25 15:30:00', 'system', '2025-08-25 15:30:00'),

-- 孙六的超时记录
('MINE001', '示例煤矿', '2025-08-25 16:00:00', 'CARD004', '孙六',
 '2025-08-25 09:30:00', '2025-08-25 15:30:00', '2025-08-25 16:00:00', 'AREA003',
 '2025-08-25 14:30:00', 'BASE003', '2025-08-25 14:30:00',
 1, 0, 'system', '2025-08-25 16:00:00', 'system', '2025-08-25 16:00:00'),

-- 高七的超时记录
('MINE001', '示例煤矿', '2025-08-25 16:30:00', 'CARD005', '高七',
 '2025-08-25 10:00:00', '2025-08-25 16:00:00', '2025-08-25 16:30:00', 'AREA002',
 '2025-08-25 15:00:00', 'BASE002', '2025-08-25 15:00:00',
 1, 0, 'system', '2025-08-25 16:30:00', 'system', '2025-08-25 16:30:00'),

-- 额外的测试数据用于组别分布测试
('MINE001', '示例煤矿', '2025-08-25 17:00:00', 'CARD006', 'Alice',
 '2025-08-25 10:30:00', '2025-08-25 16:30:00', '2025-08-25 17:00:00', 'AREA001',
 '2025-08-25 15:30:00', 'BASE001', '2025-08-25 15:30:00',
 1, 0, 'system', '2025-08-25 17:00:00', 'system', '2025-08-25 17:00:00'),

('MINE001', '示例煤矿', '2025-08-25 17:30:00', 'CARD007', 'David',
 '2025-08-25 11:00:00', '2025-08-25 17:00:00', '2025-08-25 17:30:00', 'AREA002',
 '2025-08-25 16:00:00', 'BASE002', '2025-08-25 16:00:00',
 1, 0, 'system', '2025-08-25 17:30:00', 'system', '2025-08-25 17:30:00'),

('MINE001', '示例煤矿', '2025-08-25 18:00:00', 'CARD008', 'George',
 '2025-08-25 11:30:00', '2025-08-25 17:30:00', '2025-08-25 18:00:00', 'AREA003',
 '2025-08-25 16:30:00', 'BASE003', '2025-08-25 16:30:00',
 1, 0, 'system', '2025-08-25 18:00:00', 'system', '2025-08-25 18:00:00');

-- =====================================================
-- 5. 验证数据插入
-- =====================================================

-- 查看插入的数据
SELECT 
    person_name,
    person_card_code,
    area_code,
    alarm_start_time,
    alarm_end_time,
    CASE 
        WHEN person_name ~ '^[A-C]' THEN 'A班组'
        WHEN person_name ~ '^[D-F]' THEN 'B班组'
        ELSE 'C班组'
    END as group_name
FROM kafka_people_position_time_over 
WHERE is_deleted = 0
ORDER BY alarm_start_time;

-- 统计各组别的超时次数
SELECT 
    CASE 
        WHEN person_name ~ '^[A-C]' THEN 'A班组'
        WHEN person_name ~ '^[D-F]' THEN 'B班组'
        ELSE 'C班组'
    END as group_name,
    count(*) as timeout_count
FROM kafka_people_position_time_over 
WHERE is_deleted = 0
GROUP BY group_name
ORDER BY timeout_count DESC;

-- =====================================================
-- 6. 创建视图（可选）
-- =====================================================

-- 创建人员超时统计视图
CREATE OR REPLACE VIEW v_personnel_timeout_stats AS
SELECT 
    person_card_code,
    person_name,
    area_code,
    COUNT(*) as timeout_count,
    MIN(alarm_start_time) as first_timeout,
    MAX(alarm_end_time) as last_timeout,
    EXTRACT(EPOCH FROM (MAX(alarm_end_time) - MIN(alarm_start_time)))/3600 as total_timeout_hours
FROM kafka_people_position_time_over 
WHERE is_deleted = 0
GROUP BY person_card_code, person_name, area_code;

COMMENT ON VIEW v_personnel_timeout_stats IS '人员超时统计视图';

-- =====================================================
-- 7. 权限设置（根据实际需要调整）
-- =====================================================

-- 如果需要为特定用户授权，取消下面的注释并修改用户名
-- GRANT SELECT, INSERT, UPDATE ON kafka_people_position_time_over TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE kafka_people_position_time_over_id_seq TO your_app_user;
-- GRANT SELECT ON v_personnel_timeout_stats TO your_app_user;

-- =====================================================
-- 2. 创建区域基本信息数据表
-- =====================================================

-- 删除表（如果存在）
DROP TABLE IF EXISTS kafka_people_location_info CASCADE;

-- 创建区域基本信息数据表
CREATE TABLE kafka_people_location_info (
    id BIGSERIAL PRIMARY KEY,
    mine_code VARCHAR(50),
    mine_name VARCHAR(100),
    data_upload_time TIMESTAMP,
    area_type VARCHAR(50),
    area_code VARCHAR(50) NOT NULL,
    area_approved_personnel BIGINT,
    area_name VARCHAR(100),
    status BIGINT DEFAULT 1,
    is_deleted BIGINT DEFAULT 0,
    create_by VARCHAR(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500) DEFAULT NULL
);

-- 添加字段注释
COMMENT ON TABLE kafka_people_location_info IS '区域基本信息数据表';
COMMENT ON COLUMN kafka_people_location_info.id IS '主键ID';
COMMENT ON COLUMN kafka_people_location_info.mine_code IS '煤矿编码';
COMMENT ON COLUMN kafka_people_location_info.mine_name IS '矿井名称';
COMMENT ON COLUMN kafka_people_location_info.data_upload_time IS '数据上传时间';
COMMENT ON COLUMN kafka_people_location_info.area_type IS '区域类型';
COMMENT ON COLUMN kafka_people_location_info.area_code IS '区域编码';
COMMENT ON COLUMN kafka_people_location_info.area_approved_personnel IS '区域核定人数';
COMMENT ON COLUMN kafka_people_location_info.area_name IS '区域名称';
COMMENT ON COLUMN kafka_people_location_info.status IS '状态(1:正常 0:异常)';
COMMENT ON COLUMN kafka_people_location_info.is_deleted IS '是否删除(0:未删除 1:已删除)';
COMMENT ON COLUMN kafka_people_location_info.create_by IS '创建者';
COMMENT ON COLUMN kafka_people_location_info.create_time IS '创建时间';
COMMENT ON COLUMN kafka_people_location_info.update_by IS '更新者';
COMMENT ON COLUMN kafka_people_location_info.update_time IS '更新时间';
COMMENT ON COLUMN kafka_people_location_info.remark IS '备注';

-- 创建索引
CREATE INDEX idx_kpli_area_code ON kafka_people_location_info(area_code);
CREATE INDEX idx_kpli_mine_code ON kafka_people_location_info(mine_code);
CREATE INDEX idx_kpli_area_type ON kafka_people_location_info(area_type);
CREATE INDEX idx_kpli_data_upload_time ON kafka_people_location_info(data_upload_time);
CREATE INDEX idx_kpli_status_deleted ON kafka_people_location_info(status, is_deleted);

-- 创建表级唯一约束
ALTER TABLE kafka_people_location_info
ADD CONSTRAINT uk_area_mine_code UNIQUE (area_code, mine_code);

-- 插入区域基本信息测试数据
INSERT INTO kafka_people_location_info (
    mine_code, mine_name, data_upload_time, area_type, area_code,
    area_approved_personnel, area_name, status, is_deleted,
    create_by, create_time, update_by, update_time
) VALUES
-- 主要作业区域
('MINE001', '示例煤矿', '2025-08-25 14:30:00', '作业区', 'AREA001',
 50, '主井口作业区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 14:30:00', '作业区', 'AREA002',
 30, '副井口作业区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 14:30:00', '作业区', 'AREA003',
 40, '运输巷道区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

-- 安全区域
('MINE001', '示例煤矿', '2025-08-25 14:30:00', '安全区', 'SAFE001',
 100, '地面安全区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 14:30:00', '安全区', 'SAFE002',
 20, '井下避难硐室', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

-- 危险区域
('MINE001', '示例煤矿', '2025-08-25 14:30:00', '危险区', 'DANGER001',
 5, '瓦斯监测区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 14:30:00', '危险区', 'DANGER002',
 3, '高压电气区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00');

-- =====================================================
-- 3. 创建人员求救数据表
-- =====================================================

-- 删除表（如果存在）
DROP TABLE IF EXISTS kafka_people_sos CASCADE;

-- 创建人员求救数据表
CREATE TABLE kafka_people_sos (
    id BIGSERIAL PRIMARY KEY,
    mine_code VARCHAR(50),
    mine_name VARCHAR(100),
    data_upload_time TIMESTAMP,
    person_card_code VARCHAR(50) NOT NULL,
    person_name VARCHAR(100),
    sos_start_time TIMESTAMP NOT NULL,
    sos_end_time TIMESTAMP,
    enter_well_time TIMESTAMP,
    current_area_code VARCHAR(50),
    enter_current_area_time TIMESTAMP,
    current_base_station_code VARCHAR(50),
    enter_current_base_station_time TIMESTAMP,
    status BIGINT DEFAULT 1,
    is_deleted BIGINT DEFAULT 0,
    create_by VARCHAR(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500) DEFAULT NULL
);

-- 添加字段注释
COMMENT ON TABLE kafka_people_sos IS '人员求救数据表';
COMMENT ON COLUMN kafka_people_sos.id IS '主键ID';
COMMENT ON COLUMN kafka_people_sos.mine_code IS '煤矿编码';
COMMENT ON COLUMN kafka_people_sos.mine_name IS '矿井名称';
COMMENT ON COLUMN kafka_people_sos.data_upload_time IS '数据上传时间';
COMMENT ON COLUMN kafka_people_sos.person_card_code IS '人员卡编码';
COMMENT ON COLUMN kafka_people_sos.person_name IS '姓名';
COMMENT ON COLUMN kafka_people_sos.sos_start_time IS '求救开始时间';
COMMENT ON COLUMN kafka_people_sos.sos_end_time IS '求救结束时间';
COMMENT ON COLUMN kafka_people_sos.enter_well_time IS '入井时间';
COMMENT ON COLUMN kafka_people_sos.current_area_code IS '当前所在区域编码';
COMMENT ON COLUMN kafka_people_sos.enter_current_area_time IS '进入当前区域时刻';
COMMENT ON COLUMN kafka_people_sos.current_base_station_code IS '当前所在基站编码';
COMMENT ON COLUMN kafka_people_sos.enter_current_base_station_time IS '进入当前所处基站时刻';
COMMENT ON COLUMN kafka_people_sos.status IS '状态(1:正常 0:异常)';
COMMENT ON COLUMN kafka_people_sos.is_deleted IS '是否删除(0:未删除 1:已删除)';

-- 创建索引
CREATE INDEX idx_kps_person_card_code ON kafka_people_sos(person_card_code);
CREATE INDEX idx_kps_sos_start_time ON kafka_people_sos(sos_start_time);
CREATE INDEX idx_kps_data_upload_time ON kafka_people_sos(data_upload_time);
CREATE INDEX idx_kps_current_area_code ON kafka_people_sos(current_area_code);
CREATE INDEX idx_kps_status_deleted ON kafka_people_sos(status, is_deleted);

-- 创建表级唯一约束
ALTER TABLE kafka_people_sos
ADD CONSTRAINT uk_person_sos_time UNIQUE (person_card_code, sos_start_time);

-- 插入人员求救测试数据
INSERT INTO kafka_people_sos (
    mine_code, mine_name, data_upload_time, person_card_code, person_name,
    sos_start_time, sos_end_time, enter_well_time, current_area_code,
    enter_current_area_time, current_base_station_code, enter_current_base_station_time,
    status, is_deleted, create_by, create_time, update_by, update_time
) VALUES
-- 紧急求救事件
('MINE001', '示例煤矿', '2025-08-25 14:30:00', 'CARD001', '张三',
 '2025-08-25 14:25:00', '2025-08-25 14:35:00', '2025-08-25 08:00:00', 'AREA001',
 '2025-08-25 14:20:00', 'BS001', '2025-08-25 14:20:00',
 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 15:15:00', 'CARD002', '李四',
 '2025-08-25 15:10:00', '2025-08-25 15:20:00', '2025-08-25 08:30:00', 'DANGER001',
 '2025-08-25 15:05:00', 'BS002', '2025-08-25 15:05:00',
 1, 0, 'system', '2025-08-25 15:15:00', 'system', '2025-08-25 15:15:00'),

-- 设备故障求救
('MINE001', '示例煤矿', '2025-08-24 13:20:00', 'CARD003', '王五',
 '2025-08-24 13:15:00', '2025-08-24 13:25:00', '2025-08-24 07:45:00', 'AREA002',
 '2025-08-24 13:10:00', 'BS003', '2025-08-24 13:10:00',
 1, 0, 'system', '2025-08-24 13:20:00', 'system', '2025-08-24 13:20:00');

-- =====================================================
-- 部署完成提示
-- =====================================================

SELECT 'PostgreSQL 人员安全统计系统部署完成！' as deployment_status,
       (SELECT COUNT(*) FROM kafka_people_position_time_over) as timeout_data_count,
       (SELECT COUNT(*) FROM kafka_people_location_info) as location_data_count,
       (SELECT COUNT(*) FROM kafka_people_sos) as sos_data_count;
