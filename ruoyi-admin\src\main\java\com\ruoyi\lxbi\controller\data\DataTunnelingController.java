package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataTunneling;
import com.ruoyi.lxbi.domain.request.DataTunnelingBatchDto;
import com.ruoyi.lxbi.domain.response.DataTunnelingVo;
import com.ruoyi.lxbi.service.IDataTunnelingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 掘进数据Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/data/tunneling")
public class DataTunnelingController extends BaseController {
    @Autowired
    private IDataTunnelingService dataTunnelingService;

    /**
     * 查询掘进数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:tunneling:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataTunneling dataTunneling) {
        startPage();
        List<DataTunnelingVo> list = dataTunnelingService.selectDataTunnelingList(dataTunneling);
        return getDataTable(list);
    }

    /**
     * 导出掘进数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:tunneling:export')")
    @Log(title = "掘进数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataTunneling dataTunneling) {
        List<DataTunnelingVo> list = dataTunnelingService.selectDataTunnelingList(dataTunneling);
        ExcelUtil<DataTunnelingVo> util = new ExcelUtil<DataTunnelingVo>(DataTunnelingVo.class);
        util.exportExcel(response, list, "掘进数据数据");
    }

    /**
     * 获取掘进数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:tunneling:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataTunnelingService.selectDataTunnelingById(id));
    }

    /**
     * 新增掘进数据
     */
    @PreAuthorize("@ss.hasPermi('data:tunneling:add')")
    @Log(title = "掘进数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataTunneling dataTunneling)
    {
        return toAjax(dataTunnelingService.insertDataTunneling(dataTunneling));
    }

    /**
     * 修改掘进数据
     */
    @PreAuthorize("@ss.hasPermi('data:tunneling:edit')")
    @Log(title = "掘进数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataTunneling dataTunneling)
    {
        return toAjax(dataTunnelingService.updateDataTunneling(dataTunneling));
    }

    /**
     * 删除掘进数据
     */
    @PreAuthorize("@ss.hasPermi('data:tunneling:remove')")
    @Log(title = "掘进数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataTunnelingService.deleteDataTunnelingByIds(ids));
    }

    /**
     * 批量保存掘进数据（增删改查）
     * 传入批量列表，验证是否同一个日期和项目部的数据，然后查询这个日期和项目部的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('data:tunneling:edit')")
    @Log(title = "掘进数据批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<DataTunnelingBatchDto> batchDataList)
    {
        try {
            int result = dataTunnelingService.batchSaveDataTunneling(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
