package com.ruoyi.lxbi.domain.response;

/**
 * <AUTHOR> Niu
 * @className : PlanSupportMonthlyVo
 * @description :
 * @date: 2025/7/10
 **/

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.lxbi.domain.PlanSupportMonthly;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PlanSupportMonthlyVo extends PlanSupportMonthly {

    @Excel(name = "项目部门名称", sort = 2)
    private String projectDepartmentName;

    @Excel(name = "工作面名称", sort = 3)
    private String workingFaceName;

    @Excel(name = "采场名称", sort = 4)
    private String stopeName;

}
