package com.ruoyi.lxbi.admin.service;

import java.util.List;
import com.ruoyi.lxbi.admin.domain.KafkaPeoplePositionTimeOver;

/**
 * 人员超时定位数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IKafkaPeoplePositionTimeOverService 
{
    /**
     * 查询人员超时定位数据
     * 
     * @param id 人员超时定位数据主键
     * @return 人员超时定位数据
     */
    public KafkaPeoplePositionTimeOver selectKafkaPeoplePositionTimeOverById(Long id);

    /**
     * 查询人员超时定位数据列表
     * 
     * @param kafkaPeoplePositionTimeOver 人员超时定位数据
     * @return 人员超时定位数据集合
     */
    public List<KafkaPeoplePositionTimeOver> selectKafkaPeoplePositionTimeOverList(KafkaPeoplePositionTimeOver kafkaPeoplePositionTimeOver);

    /**
     * 新增人员超时定位数据
     * 
     * @param kafkaPeoplePositionTimeOver 人员超时定位数据
     * @return 结果
     */
    public int insertKafkaPeoplePositionTimeOver(KafkaPeoplePositionTimeOver kafkaPeoplePositionTimeOver);

    /**
     * 修改人员超时定位数据
     * 
     * @param kafkaPeoplePositionTimeOver 人员超时定位数据
     * @return 结果
     */
    public int updateKafkaPeoplePositionTimeOver(KafkaPeoplePositionTimeOver kafkaPeoplePositionTimeOver);

    /**
     * 批量删除人员超时定位数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaPeoplePositionTimeOverByIds(Long[] ids);

    /**
     * 删除人员超时定位数据信息
     * 
     * @param id 人员超时定位数据主键
     * @return 结果
     */
    public int deleteKafkaPeoplePositionTimeOverById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     * 根据人员卡编码和报警开始时间唯一性，如果存在则更新，不存在则新增
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaPeoplePositionTimeOver parseKafkaMessage(String kafkaMessage);

    /**
     * 统计指定日期范围内的超时人员数量
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 超时人员数量
     */
    public Long countTimeoutPersonnelByDateRange(String startDate, String endDate);

    /**
     * 统计指定日期范围内的区域超时次数
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 区域超时次数
     */
    public Long countAreaTimeoutByDateRange(String startDate, String endDate);

    /**
     * 查询指定日期范围内的超时人员名单
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 超时人员列表
     */
    public List<KafkaPeoplePositionTimeOver> selectTimeoutPersonnelByDateRange(String startDate, String endDate, Integer limit);

    /**
     * 查询指定日期范围内的超时人员聚合统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 超时人员聚合统计列表
     */
    public List<com.ruoyi.lxbi.domain.vo.TimeoutPersonnelAggregateVO> selectTimeoutPersonnelAggregateByDateRange(String startDate, String endDate, Integer limit);

    /**
     * 统计指定日期范围内各组别的超时次数分布
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 组别超时统计列表
     */
    public List<java.util.Map<String, Object>> selectTimeoutGroupDistributionByDateRange(String startDate, String endDate);

    /**
     * 按日期统计超时次数（用于趋势分析）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期和超时次数的映射
     */
    public List<java.util.Map<String, Object>> selectTimeoutCountByDate(String startDate, String endDate);

    /**
     * 获取最近的超时记录（用于实时监控）
     *
     * @param limit 限制数量
     * @return 最近的超时记录
     */
    public List<KafkaPeoplePositionTimeOver> selectRecentTimeoutRecords(Integer limit);
}
