package com.ruoyi.lxbi.admin.service;

import com.ruoyi.lxbi.domain.vo.*;

import java.util.List;

/**
 * 地表监测统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface ISurfaceMonitoringStatService {

    /**
     * 获取地表监测概览统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 地表监测概览统计
     */
    SurfaceMonitoringOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取地表监测偏移等级分布统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 地表监测偏移等级分布统计列表
     */
    List<SurfaceMonitoringLevelDistributionVO> getLevelDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取地表监测七日偏移趋势统计
     *
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 地表监测七日偏移趋势统计列表
     */
    List<SurfaceMonitoringTrendVO> getSevenDayTrend(String viewType, String startDate, String endDate);

    /**
     * 获取监测站点列表
     *
     * @return 监测站点列表
     */
    List<SurfaceMonitoringStationVO> getMonitoringStations();

    /**
     * 获取监测站点实时状态
     *
     * @return 监测站点状态列表
     */
    List<SurfaceMonitoringStationStatusVO> getStationsStatus();

    /**
     * 获取指定监测站的详细数据
     *
     * @param stationId 站点ID
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 监测站详细数据
     */
    SurfaceMonitoringStationDetailVO getStationDetails(Long stationId, String viewType, String startDate, String endDate);

    /**
     * 获取监测数据历史趋势
     *
     * @param stationId 站点ID (可选，为null时获取所有站点)
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 数据趋势列表
     */
    List<SurfaceMonitoringDataTrendVO> getDataTrend(Long stationId, String viewType, String startDate, String endDate);

    /**
     * 获取报警事件列表
     *
     * @param stationId 站点ID (可选，为null时获取所有站点)
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报警事件列表
     */
    List<SurfaceMonitoringAlarmVO> getAlarmEvents(Long stationId, String viewType, String startDate, String endDate);

    /**
     * 获取系统健康状态
     *
     * @return 系统健康状态
     */
    SurfaceMonitoringSystemHealthVO getSystemHealth();
}
