<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.admin.mapper.ApiHiddenTroubleRecordMapper">
    
    <resultMap type="ApiHiddenTroubleRecord" id="ApiHiddenTroubleRecordResult">
        <result property="id"    column="id"    />
        <result property="status"    column="status"    />
        <result property="responsiblePerson"    column="responsible_person"    />
        <result property="troubleLocation"    column="trouble_location"    />
        <result property="responsibleDepartment"    column="responsible_department"    />
        <result property="troubleGrade"    column="trouble_grade"    />
        <result property="troubleCategory"    column="trouble_category"    />
        <result property="troubleDate"    column="trouble_date"    />
        <result property="inspector"    column="inspector"    />
        <result property="reviewer"    column="reviewer"    />
        <result property="noticeNumber"    column="notice_number"    />
        <result property="originalData"    column="original_data"    />
        <result property="troubleTitle"    column="trouble_title"    />
        <result property="troubleDescription"    column="trouble_description"    />
        <result property="rectificationDeadline"    column="rectification_deadline"    />
        <result property="rectificationCompletionDate"    column="rectification_completion_date"    />
        <result property="reviewDate"    column="review_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectApiHiddenTroubleRecordVo">
        select id, status, responsible_person, trouble_location, responsible_department, trouble_grade, trouble_category, trouble_date, inspector, reviewer, notice_number, original_data, trouble_title, trouble_description, rectification_deadline, rectification_completion_date, review_date, create_by, create_time, update_by, update_time, remark from api_hidden_trouble_record
    </sql>

    <select id="selectApiHiddenTroubleRecordList" parameterType="ApiHiddenTroubleRecord" resultMap="ApiHiddenTroubleRecordResult">
        <include refid="selectApiHiddenTroubleRecordVo"/>
        <where>  
            <if test="responsiblePerson != null  and responsiblePerson != ''"> and responsible_person = #{responsiblePerson}</if>
            <if test="troubleLocation != null  and troubleLocation != ''"> and trouble_location = #{troubleLocation}</if>
            <if test="responsibleDepartment != null  and responsibleDepartment != ''"> and responsible_department = #{responsibleDepartment}</if>
            <if test="troubleCategory != null  and troubleCategory != ''"> and trouble_category = #{troubleCategory}</if>
            <if test="troubleDate != null "> and trouble_date = #{troubleDate}</if>
            <if test="inspector != null  and inspector != ''"> and inspector = #{inspector}</if>
            <if test="reviewer != null  and reviewer != ''"> and reviewer = #{reviewer}</if>
            <if test="originalData != null  and originalData != ''"> and original_data = #{originalData}</if>
            <if test="troubleTitle != null  and troubleTitle != ''"> and trouble_title = #{troubleTitle}</if>
            <if test="troubleDescription != null  and troubleDescription != ''"> and trouble_description = #{troubleDescription}</if>
            <if test="rectificationDeadline != null "> and rectification_deadline = #{rectificationDeadline}</if>
            <if test="rectificationCompletionDate != null "> and rectification_completion_date = #{rectificationCompletionDate}</if>
            <if test="reviewDate != null "> and review_date = #{reviewDate}</if>
        </where>
    </select>
    
    <select id="selectApiHiddenTroubleRecordById" parameterType="Long" resultMap="ApiHiddenTroubleRecordResult">
        <include refid="selectApiHiddenTroubleRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertApiHiddenTroubleRecord" parameterType="ApiHiddenTroubleRecord" useGeneratedKeys="true" keyProperty="id">
        insert into api_hidden_trouble_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="status != null">status,</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">responsible_person,</if>
            <if test="troubleLocation != null and troubleLocation != ''">trouble_location,</if>
            <if test="responsibleDepartment != null and responsibleDepartment != ''">responsible_department,</if>
            <if test="troubleGrade != null">trouble_grade,</if>
            <if test="troubleCategory != null and troubleCategory != ''">trouble_category,</if>
            <if test="troubleDate != null">trouble_date,</if>
            <if test="inspector != null and inspector != ''">inspector,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="noticeNumber != null">notice_number,</if>
            <if test="originalData != null">original_data,</if>
            <if test="troubleTitle != null">trouble_title,</if>
            <if test="troubleDescription != null">trouble_description,</if>
            <if test="rectificationDeadline != null">rectification_deadline,</if>
            <if test="rectificationCompletionDate != null">rectification_completion_date,</if>
            <if test="reviewDate != null">review_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="status != null">#{status},</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">#{responsiblePerson},</if>
            <if test="troubleLocation != null and troubleLocation != ''">#{troubleLocation},</if>
            <if test="responsibleDepartment != null and responsibleDepartment != ''">#{responsibleDepartment},</if>
            <if test="troubleGrade != null">#{troubleGrade},</if>
            <if test="troubleCategory != null and troubleCategory != ''">#{troubleCategory},</if>
            <if test="troubleDate != null">#{troubleDate},</if>
            <if test="inspector != null and inspector != ''">#{inspector},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="noticeNumber != null">#{noticeNumber},</if>
            <if test="originalData != null">#{originalData},</if>
            <if test="troubleTitle != null">#{troubleTitle},</if>
            <if test="troubleDescription != null">#{troubleDescription},</if>
            <if test="rectificationDeadline != null">#{rectificationDeadline},</if>
            <if test="rectificationCompletionDate != null">#{rectificationCompletionDate},</if>
            <if test="reviewDate != null">#{reviewDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateApiHiddenTroubleRecord" parameterType="ApiHiddenTroubleRecord">
        update api_hidden_trouble_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">responsible_person = #{responsiblePerson},</if>
            <if test="troubleLocation != null and troubleLocation != ''">trouble_location = #{troubleLocation},</if>
            <if test="responsibleDepartment != null and responsibleDepartment != ''">responsible_department = #{responsibleDepartment},</if>
            <if test="troubleGrade != null">trouble_grade = #{troubleGrade},</if>
            <if test="troubleCategory != null and troubleCategory != ''">trouble_category = #{troubleCategory},</if>
            <if test="troubleDate != null">trouble_date = #{troubleDate},</if>
            <if test="inspector != null and inspector != ''">inspector = #{inspector},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="noticeNumber != null">notice_number = #{noticeNumber},</if>
            <if test="originalData != null">original_data = #{originalData},</if>
            <if test="troubleTitle != null">trouble_title = #{troubleTitle},</if>
            <if test="troubleDescription != null">trouble_description = #{troubleDescription},</if>
            <if test="rectificationDeadline != null">rectification_deadline = #{rectificationDeadline},</if>
            <if test="rectificationCompletionDate != null">rectification_completion_date = #{rectificationCompletionDate},</if>
            <if test="reviewDate != null">review_date = #{reviewDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApiHiddenTroubleRecordById" parameterType="Long">
        delete from api_hidden_trouble_record where id = #{id}
    </delete>

    <delete id="deleteApiHiddenTroubleRecordByIds" parameterType="String">
        delete from api_hidden_trouble_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据通知编号查询 -->
    <select id="selectByNoticeNumber" parameterType="String" resultMap="ApiHiddenTroubleRecordResult">
        <include refid="selectApiHiddenTroubleRecordVo"/>
        where notice_number = #{noticeNumber}
    </select>

    <!-- 根据通知编号和隐患日期查询 -->
    <select id="selectByNoticeNumberAndDate" resultMap="ApiHiddenTroubleRecordResult">
        <include refid="selectApiHiddenTroubleRecordVo"/>
        where notice_number = #{noticeNumber}
          and trouble_date = #{troubleDate}
    </select>

    <!-- 根据通知编号更新 -->
    <update id="updateByNoticeNumber" parameterType="ApiHiddenTroubleRecord">
        update api_hidden_trouble_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">responsible_person = #{responsiblePerson},</if>
            <if test="troubleLocation != null and troubleLocation != ''">trouble_location = #{troubleLocation},</if>
            <if test="responsibleDepartment != null and responsibleDepartment != ''">responsible_department = #{responsibleDepartment},</if>
            <if test="troubleGrade != null">trouble_grade = #{troubleGrade},</if>
            <if test="troubleCategory != null and troubleCategory != ''">trouble_category = #{troubleCategory},</if>
            <if test="troubleDate != null">trouble_date = #{troubleDate},</if>
            <if test="inspector != null and inspector != ''">inspector = #{inspector},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="originalData != null">original_data = #{originalData},</if>
            <if test="troubleTitle != null">trouble_title = #{troubleTitle},</if>
            <if test="troubleDescription != null">trouble_description = #{troubleDescription},</if>
            <if test="rectificationDeadline != null">rectification_deadline = #{rectificationDeadline},</if>
            <if test="rectificationCompletionDate != null">rectification_completion_date = #{rectificationCompletionDate},</if>
            <if test="reviewDate != null">review_date = #{reviewDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where notice_number = #{noticeNumber}
    </update>

    <!-- 根据通知编号和隐患日期更新 -->
    <update id="updateByNoticeNumberAndDate" parameterType="ApiHiddenTroubleRecord">
        update api_hidden_trouble_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">responsible_person = #{responsiblePerson},</if>
            <if test="troubleLocation != null and troubleLocation != ''">trouble_location = #{troubleLocation},</if>
            <if test="responsibleDepartment != null and responsibleDepartment != ''">responsible_department = #{responsibleDepartment},</if>
            <if test="troubleGrade != null">trouble_grade = #{troubleGrade},</if>
            <if test="troubleCategory != null and troubleCategory != ''">trouble_category = #{troubleCategory},</if>
            <if test="inspector != null and inspector != ''">inspector = #{inspector},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="originalData != null">original_data = #{originalData},</if>
            <if test="troubleTitle != null">trouble_title = #{troubleTitle},</if>
            <if test="troubleDescription != null">trouble_description = #{troubleDescription},</if>
            <if test="rectificationDeadline != null">rectification_deadline = #{rectificationDeadline},</if>
            <if test="rectificationCompletionDate != null">rectification_completion_date = #{rectificationCompletionDate},</if>
            <if test="reviewDate != null">review_date = #{reviewDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where notice_number = #{noticeNumber}
          and trouble_date = #{troubleDate}
    </update>

    <!-- PostgreSQL UPSERT操作 -->
    <insert id="upsertApiHiddenTroubleRecord" parameterType="ApiHiddenTroubleRecord">
        INSERT INTO api_hidden_trouble_record (
            status, responsible_person, trouble_location, responsible_department, trouble_grade,
            trouble_category, trouble_date, inspector, reviewer, notice_number, original_data,
            trouble_title, trouble_description, rectification_deadline, rectification_completion_date,
            review_date, create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{status}, #{responsiblePerson}, #{troubleLocation}, #{responsibleDepartment}, #{troubleGrade},
            #{troubleCategory}, #{troubleDate}, #{inspector}, #{reviewer}, #{noticeNumber}, #{originalData},
            #{troubleTitle}, #{troubleDescription}, #{rectificationDeadline}, #{rectificationCompletionDate},
            #{reviewDate}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
        ON CONFLICT (notice_number, trouble_date)
        DO UPDATE SET
            status = EXCLUDED.status,
            responsible_person = EXCLUDED.responsible_person,
            trouble_location = EXCLUDED.trouble_location,
            responsible_department = EXCLUDED.responsible_department,
            trouble_grade = EXCLUDED.trouble_grade,
            trouble_category = EXCLUDED.trouble_category,
            trouble_date = EXCLUDED.trouble_date,
            inspector = EXCLUDED.inspector,
            reviewer = EXCLUDED.reviewer,
            original_data = EXCLUDED.original_data,
            trouble_title = EXCLUDED.trouble_title,
            trouble_description = EXCLUDED.trouble_description,
            rectification_deadline = EXCLUDED.rectification_deadline,
            rectification_completion_date = EXCLUDED.rectification_completion_date,
            review_date = EXCLUDED.review_date,
            update_by = EXCLUDED.update_by,
            update_time = EXCLUDED.update_time,
            remark = EXCLUDED.remark
            -- 注意：不更新 create_by 和 create_time，保持原有的创建信息
    </insert>

    <!-- 按状态统计隐患数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT
            status,
            COUNT(*) as count,
            CASE
                WHEN status = 0 THEN '待整改'
                WHEN status = 1 THEN '已驳回'
                WHEN status = 2 THEN '已整改'
                WHEN status = 3 THEN '待复查'
                WHEN status = 4 THEN '已超期'
                ELSE '未知'
            END as status_name
        FROM api_hidden_trouble_record GROUP BY status
        ORDER BY status
    </select>

    <!-- 按部门统计隐患数量 -->
    <select id="countByDepartment" resultType="java.util.Map">
        SELECT
            responsible_department as department,
            COUNT(*) as count
        FROM api_hidden_trouble_record AND responsible_department IS NOT NULL
        GROUP BY responsible_department
        ORDER BY count DESC
    </select>

    <!-- 按隐患等级统计数量 -->
    <select id="countByGrade" resultType="java.util.Map">
        SELECT
            trouble_grade as grade,
            COUNT(*) as count,
            CASE
                WHEN trouble_grade = '0' THEN '一般隐患'
                WHEN trouble_grade = '1' THEN '重大隐患'
                ELSE '未知'
            END as grade_name
        FROM api_hidden_trouble_record GROUP BY trouble_grade
        ORDER BY trouble_grade
    </select>

</mapper>