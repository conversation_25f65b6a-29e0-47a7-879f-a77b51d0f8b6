package com.ruoyi.web.controller.monitoring;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.framework.config.MqttConfig;
import com.ruoyi.framework.service.MqttConnectionService;
import com.ruoyi.framework.service.KafkaMonitoringService;
import com.ruoyi.framework.service.MqttMessageService;

import java.util.HashMap;
import java.util.Map;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 监控管理控制器 - 统一管理MQTT和Kafka监控
 * 
 * <AUTHOR>
 * @date 2025-08-16
 */
@Slf4j
@RestController
@RequestMapping("/monitoring")
public class MonitoringController extends BaseController {

    @Autowired
    private MqttConfig mqttConfig;
    
    @Autowired
    private MqttConnectionService mqttConnectionService;
    
    @Autowired
    private KafkaMonitoringService kafkaMonitoringService;
    
    @Autowired
    private MqttMessageService mqttMessageService;

    /**
     * 获取系统监控概览 - 专注于Kafka监控
     */
    @GetMapping("/overview")
    public AjaxResult getMonitoringOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();

            // Kafka集群详细状态
            KafkaMonitoringService.KafkaClusterStatus kafkaStatus = kafkaMonitoringService.getDetailedClusterStatus();
            overview.put("kafka", Map.of(
                "healthy", kafkaStatus.isHealthy(),
                "nodeCount", kafkaStatus.getNodeCount(),
                "controller", kafkaStatus.getController(),
                "topicCount", kafkaStatus.getTopicCount(),
                "nodes", kafkaStatus.getNodes(),
                "lastCheckTime", kafkaStatus.getLastCheckTime(),
                "errorMessage", kafkaStatus.getErrorMessage()
            ));

            // MQTT状态（已屏蔽）
            overview.put("mqtt", Map.of(
                "status", "disabled",
                "message", "MQTT监控已暂时屏蔽，专注于Kafka监控"
            ));

            return success(overview);
        } catch (Exception e) {
            log.error("获取监控概览失败", e);
            return error("获取监控概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取MQTT配置信息
     */
    @GetMapping("/mqtt/config")
    public AjaxResult getMqttConfig() {
        try {
            return success(mqttConfig);
        } catch (Exception e) {
            log.error("获取MQTT配置失败", e);
            return error("获取MQTT配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取MQTT连接状态
     */
    @GetMapping("/mqtt/status")
    public AjaxResult getMqttStatus() {
        try {
            Map<String, Boolean> status = mqttConnectionService.getConnectionStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("connections", status);
            result.put("totalConnections", status.size());
            result.put("activeConnections", status.values().stream().mapToInt(b -> b ? 1 : 0).sum());
            
            return success(result);
        } catch (Exception e) {
            log.error("获取MQTT状态失败", e);
            return error("获取MQTT状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取Kafka集群详细状态
     */
    @GetMapping("/kafka/status")
    public AjaxResult getKafkaStatus() {
        try {
            KafkaMonitoringService.KafkaClusterStatus status = kafkaMonitoringService.getDetailedClusterStatus();
            return success(status);
        } catch (Exception e) {
            log.error("获取Kafka状态失败", e);
            return error("获取Kafka状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查Kafka集群状态
     */
    @PostMapping("/kafka/check")
    public AjaxResult checkKafkaCluster() {
        try {
            kafkaMonitoringService.checkKafkaClusterStatus();
            return success("Kafka集群状态检查完成，请查看日志");
        } catch (Exception e) {
            log.error("检查Kafka集群状态失败", e);
            return error("检查Kafka集群状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取Kafka主题详细信息
     */
    @GetMapping("/kafka/topics")
    public AjaxResult getKafkaTopics() {
        try {
            KafkaMonitoringService.KafkaClusterStatus status = kafkaMonitoringService.getDetailedClusterStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("topicCount", status.getTopicCount());
            result.put("topics", status.getTopics());
            result.put("healthy", status.isHealthy());

            return success(result);
        } catch (Exception e) {
            log.error("获取Kafka主题信息失败", e);
            return error("获取Kafka主题信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取Kafka节点信息
     */
    @GetMapping("/kafka/nodes")
    public AjaxResult getKafkaNodes() {
        try {
            KafkaMonitoringService.KafkaClusterStatus status = kafkaMonitoringService.getDetailedClusterStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("nodeCount", status.getNodeCount());
            result.put("nodes", status.getNodes());
            result.put("controller", status.getController());
            result.put("healthy", status.isHealthy());
            result.put("lastCheckTime", status.getLastCheckTime());

            return success(result);
        } catch (Exception e) {
            log.error("获取Kafka节点信息失败", e);
            return error("获取Kafka节点信息失败: " + e.getMessage());
        }
    }

    /**
     * 模拟MQTT消息测试
     */
    @PostMapping("/mqtt/test")
    public AjaxResult testMqttMessage(@RequestParam String topic, 
                                     @RequestParam String message,
                                     @RequestParam(required = false, defaultValue = "test") String connectionName) {
        try {
            log.info("模拟MQTT消息测试 - 连接: {}, 主题: {}, 消息: {}", connectionName, topic, message);
            
            // 调用消息处理服务进行测试
            mqttMessageService.processMessage(topic, message, connectionName);
            
            return success("MQTT消息测试完成");
        } catch (Exception e) {
            log.error("MQTT消息测试失败", e);
            return error("MQTT消息测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控统计信息 - 专注于Kafka
     */
    @GetMapping("/statistics")
    public AjaxResult getMonitoringStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // Kafka详细统计
            KafkaMonitoringService.KafkaClusterStatus kafkaStatus = kafkaMonitoringService.getDetailedClusterStatus();
            statistics.put("kafka", Map.of(
                "healthy", kafkaStatus.isHealthy(),
                "nodeCount", kafkaStatus.getNodeCount(),
                "topicCount", kafkaStatus.getTopicCount(),
                "controller", kafkaStatus.getController(),
                "nodes", kafkaStatus.getNodes(),
                "topics", kafkaStatus.getTopics(),
                "lastCheckTime", kafkaStatus.getLastCheckTime(),
                "errorMessage", kafkaStatus.getErrorMessage()
            ));

            // MQTT统计（已屏蔽）
            statistics.put("mqtt", Map.of(
                "status", "disabled",
                "message", "MQTT监控已暂时屏蔽"
            ));

            // 系统信息
            statistics.put("system", Map.of(
                "timestamp", System.currentTimeMillis(),
                "monitoringMode", "Kafka专用监控模式",
                "kafkaBootstrapServers", "10.10.22.100:9092,10.10.22.101:9092,10.10.22.102:9092"
            ));

            return success(statistics);
        } catch (Exception e) {
            log.error("获取监控统计信息失败", e);
            return error("获取监控统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取Kafka消费者状态
     */
    @GetMapping("/kafka/consumers")
    public AjaxResult getKafkaConsumerStatus() {
        try {
            Map<String, Object> status = kafkaMonitoringService.getConsumerStatus();
            return success(status);
        } catch (Exception e) {
            log.error("获取Kafka消费者状态失败", e);
            return error("获取Kafka消费者状态失败: " + e.getMessage());
        }
    }

    /**
     * 停止指定topic的消费者
     */
    @PostMapping("/kafka/consumer/{topic}/stop")
    public AjaxResult stopTopicConsumer(@PathVariable String topic) {
        try {
            kafkaMonitoringService.stopTopicConsumer(topic);
            return success("已停止topic " + topic + " 的消费者");
        } catch (Exception e) {
            log.error("停止topic消费者失败 - topic: {}", topic, e);
            return error("停止topic消费者失败: " + e.getMessage());
        }
    }

    /**
     * 重启指定topic的消费者
     */
    @PostMapping("/kafka/consumer/{topic}/restart")
    public AjaxResult restartTopicConsumer(@PathVariable String topic) {
        try {
            kafkaMonitoringService.restartTopicConsumer(topic);
            return success("已重启topic " + topic + " 的消费者");
        } catch (Exception e) {
            log.error("重启topic消费者失败 - topic: {}", topic, e);
            return error("重启topic消费者失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控的topic列表
     */
    @GetMapping("/kafka/monitoring-topics")
    public AjaxResult getMonitoringTopics() {
        try {
            List<String> topics = kafkaMonitoringService.getMonitoringTopics();
            Map<String, Object> result = new HashMap<>();
            result.put("topics", topics);
            result.put("totalCount", topics.size());
            return success(result);
        } catch (Exception e) {
            log.error("获取监控topic列表失败", e);
            return error("获取监控topic列表失败: " + e.getMessage());
        }
    }

    /**
     * 停止所有消费者
     */
    @PostMapping("/kafka/consumers/stop-all")
    public AjaxResult stopAllConsumers() {
        try {
            kafkaMonitoringService.stopAllConsumers();
            return success("已停止所有topic消费者");
        } catch (Exception e) {
            log.error("停止所有消费者失败", e);
            return error("停止所有消费者失败: " + e.getMessage());
        }
    }

}
