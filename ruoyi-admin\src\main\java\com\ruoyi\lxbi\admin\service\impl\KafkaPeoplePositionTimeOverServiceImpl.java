package com.ruoyi.lxbi.admin.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.admin.mapper.KafkaPeoplePositionTimeOverMapper;
import com.ruoyi.lxbi.admin.domain.KafkaPeoplePositionTimeOver;
import com.ruoyi.lxbi.admin.service.IKafkaPeoplePositionTimeOverService;
import lombok.extern.slf4j.Slf4j;

/**
 * 人员超时定位数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class KafkaPeoplePositionTimeOverServiceImpl implements IKafkaPeoplePositionTimeOverService 
{
    @Autowired
    private KafkaPeoplePositionTimeOverMapper kafkaPeoplePositionTimeOverMapper;

    /**
     * 查询人员超时定位数据
     * 
     * @param id 人员超时定位数据主键
     * @return 人员超时定位数据
     */
    @Override
    public KafkaPeoplePositionTimeOver selectKafkaPeoplePositionTimeOverById(Long id)
    {
        return kafkaPeoplePositionTimeOverMapper.selectKafkaPeoplePositionTimeOverById(id);
    }

    /**
     * 查询人员超时定位数据列表
     * 
     * @param kafkaPeoplePositionTimeOver 人员超时定位数据
     * @return 人员超时定位数据
     */
    @Override
    public List<KafkaPeoplePositionTimeOver> selectKafkaPeoplePositionTimeOverList(KafkaPeoplePositionTimeOver kafkaPeoplePositionTimeOver)
    {
        return kafkaPeoplePositionTimeOverMapper.selectKafkaPeoplePositionTimeOverList(kafkaPeoplePositionTimeOver);
    }

    /**
     * 新增人员超时定位数据
     * 
     * @param kafkaPeoplePositionTimeOver 人员超时定位数据
     * @return 结果
     */
    @Override
    public int insertKafkaPeoplePositionTimeOver(KafkaPeoplePositionTimeOver kafkaPeoplePositionTimeOver)
    {
        kafkaPeoplePositionTimeOver.setCreateTime(DateUtils.getNowDate());
        return kafkaPeoplePositionTimeOverMapper.insertKafkaPeoplePositionTimeOver(kafkaPeoplePositionTimeOver);
    }

    /**
     * 修改人员超时定位数据
     * 
     * @param kafkaPeoplePositionTimeOver 人员超时定位数据
     * @return 结果
     */
    @Override
    public int updateKafkaPeoplePositionTimeOver(KafkaPeoplePositionTimeOver kafkaPeoplePositionTimeOver)
    {
        kafkaPeoplePositionTimeOver.setUpdateTime(DateUtils.getNowDate());
        return kafkaPeoplePositionTimeOverMapper.updateKafkaPeoplePositionTimeOver(kafkaPeoplePositionTimeOver);
    }

    /**
     * 批量删除人员超时定位数据
     * 
     * @param ids 需要删除的数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeoplePositionTimeOverByIds(Long[] ids)
    {
        return kafkaPeoplePositionTimeOverMapper.deleteKafkaPeoplePositionTimeOverByIds(ids);
    }

    /**
     * 删除人员超时定位数据信息
     * 
     * @param id 人员超时定位数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeoplePositionTimeOverById(Long id)
    {
        return kafkaPeoplePositionTimeOverMapper.deleteKafkaPeoplePositionTimeOverById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage) {
        try {
            log.debug("开始处理Kafka人员超时定位消息");

            // 解析Kafka消息
            KafkaPeoplePositionTimeOver timeOver = parseKafkaMessage(kafkaMessage);
            if (timeOver == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(timeOver.getPersonCardCode())) {
                log.warn("人员卡编码为空，跳过处理");
                return false;
            }

            if (timeOver.getAlarmStartTime() == null) {
                log.warn("报警开始时间为空，人员卡编码: {}", timeOver.getPersonCardCode());
                return false;
            }

            // 检查是否已存在相同记录
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String alarmStartTimeStr = sdf.format(timeOver.getAlarmStartTime());
            
            KafkaPeoplePositionTimeOver existing = kafkaPeoplePositionTimeOverMapper
                    .selectByPersonCardCodeAndAlarmStartTime(timeOver.getPersonCardCode(), alarmStartTimeStr);

            if (existing != null) {
                // 更新现有记录
                timeOver.setId(existing.getId());
                timeOver.setUpdateTime(DateUtils.getNowDate());
                int updateResult = kafkaPeoplePositionTimeOverMapper.updateKafkaPeoplePositionTimeOver(timeOver);
                log.info("更新人员超时定位记录成功，人员卡编码: {}, 报警开始时间: {}", 
                        timeOver.getPersonCardCode(), alarmStartTimeStr);
                return updateResult > 0;
            } else {
                // 插入新记录
                timeOver.setCreateTime(DateUtils.getNowDate());
                timeOver.setUpdateTime(DateUtils.getNowDate());
                int insertResult = kafkaPeoplePositionTimeOverMapper.insertKafkaPeoplePositionTimeOver(timeOver);
                log.info("插入人员超时定位记录成功，人员卡编码: {}, 报警开始时间: {}", 
                        timeOver.getPersonCardCode(), alarmStartTimeStr);
                return insertResult > 0;
            }

        } catch (Exception e) {
            log.error("处理Kafka人员超时定位消息失败: {}", kafkaMessage, e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     */
    @Override
    public KafkaPeoplePositionTimeOver parseKafkaMessage(String kafkaMessage) {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            log.debug("解析JSON字符串: {}", jsonStr);

            // 使用Jackson解析JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonStr);

            KafkaPeoplePositionTimeOver timeOver = new KafkaPeoplePositionTimeOver();

            // 基本信息
            timeOver.setMineCode(getStringValue(jsonNode, "煤矿编码"));
            timeOver.setMineName(getStringValue(jsonNode, "矿井名称"));
            
            // 时间字段处理
            timeOver.setDataUploadTime(getDateValue(jsonNode, "数据上传时间"));
            timeOver.setEnterWellTime(getDateValue(jsonNode, "入井时刻"));
            timeOver.setAlarmStartTime(getDateValue(jsonNode, "报警开始时间"));
            timeOver.setAlarmEndTime(getDateValue(jsonNode, "报警结束时间"));
            timeOver.setEnterCurrentAreaTime(getDateValue(jsonNode, "进入当前所处区域时间"));
            timeOver.setEnterCurrentBaseStationTime(getDateValue(jsonNode, "进入当前所处基站时刻"));

            // 人员信息
            timeOver.setPersonCardCode(getStringValue(jsonNode, "人员卡编码"));
            timeOver.setPersonName(getStringValue(jsonNode, "姓名"));
            
            // 位置信息
            timeOver.setAreaCode(getStringValue(jsonNode, "区域编码"));
            timeOver.setBaseStationCode(getStringValue(jsonNode, "基站编码"));

            // 默认值
            timeOver.setStatus(1L);
            timeOver.setIsDeleted(0L);

            return timeOver;

        } catch (Exception e) {
            log.error("解析Kafka消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 统计指定日期范围内的超时人员数量
     */
    @Override
    public Long countTimeoutPersonnelByDateRange(String startDate, String endDate) {
        return kafkaPeoplePositionTimeOverMapper.countTimeoutPersonnelByDateRange(startDate, endDate);
    }

    /**
     * 统计指定日期范围内的区域超时次数
     */
    @Override
    public Long countAreaTimeoutByDateRange(String startDate, String endDate) {
        return kafkaPeoplePositionTimeOverMapper.countAreaTimeoutByDateRange(startDate, endDate);
    }

    /**
     * 查询指定日期范围内的超时人员名单
     */
    @Override
    public List<KafkaPeoplePositionTimeOver> selectTimeoutPersonnelByDateRange(String startDate, String endDate, Integer limit) {
        return kafkaPeoplePositionTimeOverMapper.selectTimeoutPersonnelByDateRange(startDate, endDate, limit);
    }

    /**
     * 统计指定日期范围内各组别的超时次数分布
     */
    @Override
    public List<Map<String, Object>> selectTimeoutGroupDistributionByDateRange(String startDate, String endDate) {
        return kafkaPeoplePositionTimeOverMapper.selectTimeoutGroupDistributionByDateRange(startDate, endDate);
    }

    /**
     * 按日期统计超时次数（用于趋势分析）
     */
    @Override
    public List<Map<String, Object>> selectTimeoutCountByDate(String startDate, String endDate) {
        return kafkaPeoplePositionTimeOverMapper.selectTimeoutCountByDate(startDate, endDate);
    }

    /**
     * 获取最近的超时记录（用于实时监控）
     */
    @Override
    public List<KafkaPeoplePositionTimeOver> selectRecentTimeoutRecords(Integer limit) {
        return kafkaPeoplePositionTimeOverMapper.selectRecentTimeoutRecords(limit);
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return (node != null && !node.isNull()) ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private Date getDateValue(JsonNode jsonNode, String fieldName) {
        String dateStr = getStringValue(jsonNode, fieldName);
        if (!StringUtils.hasText(dateStr)) {
            return null;
        }

        try {
            // 尝试多种日期格式
            String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd'T'HH:mm:ss",
                "yyyy-MM-dd HH:mm:ss.SSS",
                "yyyy-MM-dd'T'HH:mm:ss.SSS",
                "yyyy-MM-dd"
            };

            for (String pattern : patterns) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    return sdf.parse(dateStr);
                } catch (Exception ignored) {
                    // 继续尝试下一个格式
                }
            }

            log.warn("无法解析日期字段 {}: {}", fieldName, dateStr);
            return null;
        } catch (Exception e) {
            log.warn("解析日期字段 {} 失败: {}", fieldName, dateStr, e);
            return null;
        }
    }
}
