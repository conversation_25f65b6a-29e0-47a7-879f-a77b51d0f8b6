package com.ruoyi.common.core.domain.excel;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * Excel字段信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExcelFieldInfo {

    /** 字段名 */
    private String fieldName;

    /** 显示名称 */
    private String displayName;

    /** 字段类型 */
    private String fieldType;

    /** 是否必填 */
    private Boolean required;

    /** 排序 */
    private Integer sort;

    /** 是否有选项 */
    private Boolean hasOptions;

    /** 选项key */
    private String optionKey;

    /** 字典类型 */
    private String dictType;

    private String dictCode;

    public ExcelFieldInfo(String fieldName, String displayName) {
        this.fieldName = fieldName;
        this.displayName = displayName;
    }
}
