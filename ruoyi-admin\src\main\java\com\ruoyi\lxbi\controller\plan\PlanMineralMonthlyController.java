package com.ruoyi.lxbi.controller.plan;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.PlanMineralMonthly;
import com.ruoyi.lxbi.domain.request.PlanMineralMonthlyBatchDto;
import com.ruoyi.lxbi.service.IPlanMineralMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 选矿整体月计划Controller
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@RestController
@RequestMapping("/plan/planMineralMonthly")
public class PlanMineralMonthlyController extends BaseController {
    @Autowired
    private IPlanMineralMonthlyService planMineralMonthlyService;

    /**
     * 查询选矿整体月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanMineralMonthly planMineralMonthly) {
        startPage();
        List<PlanMineralMonthly> list = planMineralMonthlyService.selectPlanMineralMonthlyList(planMineralMonthly);
        return getDataTable(list);
    }

    /**
     * 导出选矿整体月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralMonthly:export')")
    @Log(title = "选矿整体月计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanMineralMonthly planMineralMonthly) {
        List<PlanMineralMonthly> list = planMineralMonthlyService.selectPlanMineralMonthlyList(planMineralMonthly);
        ExcelUtil<PlanMineralMonthly> util = new ExcelUtil<PlanMineralMonthly>(PlanMineralMonthly.class);
        util.exportExcel(response, list, "选矿整体月计划数据");
    }

    /**
     * 获取选矿整体月计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planMineralMonthlyService.selectPlanMineralMonthlyById(id));
    }

    /**
     * 新增选矿整体月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralMonthly:add')")
    @Log(title = "选矿整体月计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanMineralMonthly planMineralMonthly)
    {
        return toAjax(planMineralMonthlyService.insertPlanMineralMonthly(planMineralMonthly));
    }

    /**
     * 修改选矿整体月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralMonthly:edit')")
    @Log(title = "选矿整体月计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanMineralMonthly planMineralMonthly)
    {
        return toAjax(planMineralMonthlyService.updatePlanMineralMonthly(planMineralMonthly));
    }

    /**
     * 删除选矿整体月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralMonthly:remove')")
    @Log(title = "选矿整体月计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(planMineralMonthlyService.deletePlanMineralMonthlyByIds(ids));
    }

    /**
     * 批量保存选矿整体月计划（增删改查）
     * 传入批量列表，验证是否同一个月份的数据，然后查询这个月份的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralMonthly:edit')")
    @Log(title = "选矿整体月计划批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<PlanMineralMonthlyBatchDto> batchDataList)
    {
        try {
            int result = planMineralMonthlyService.batchSavePlanMineralMonthly(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
