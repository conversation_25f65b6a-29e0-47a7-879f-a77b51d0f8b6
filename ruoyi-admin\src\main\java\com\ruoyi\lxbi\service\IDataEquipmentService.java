package com.ruoyi.lxbi.service;

import java.util.Date;
import java.util.List;
import com.ruoyi.lxbi.domain.DataEquipment;
import com.ruoyi.lxbi.domain.request.DataEquipmentBatchDto;
import com.ruoyi.lxbi.domain.response.DataEquipmentEfficiencyStats;
import com.ruoyi.lxbi.domain.response.DataEquipmentUtilizationStats;

/**
 * 设备数据管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IDataEquipmentService 
{
    /**
     * 查询设备数据管理
     * 
     * @param id 设备数据管理主键
     * @return 设备数据管理
     */
    public DataEquipment selectDataEquipmentById(Long id);

    /**
     * 查询设备数据管理列表
     * 
     * @param dataEquipment 设备数据管理
     * @return 设备数据管理集合
     */
    public List<DataEquipment> selectDataEquipmentList(DataEquipment dataEquipment);

    /**
     * 新增设备数据管理
     * 
     * @param dataEquipment 设备数据管理
     * @return 结果
     */
    public int insertDataEquipment(DataEquipment dataEquipment);

    /**
     * 修改设备数据管理
     * 
     * @param dataEquipment 设备数据管理
     * @return 结果
     */
    public int updateDataEquipment(DataEquipment dataEquipment);

    /**
     * 批量删除设备数据管理
     * 
     * @param ids 需要删除的设备数据管理主键集合
     * @return 结果
     */
    public int deleteDataEquipmentByIds(Long[] ids);

    /**
     * 删除设备数据管理信息
     *
     * @param id 设备数据管理主键
     * @return 结果
     */
    public int deleteDataEquipmentById(Long id);

    /**
     * 批量保存设备数据
     *
     * @param dataList 设备数据列表
     * @return 结果
     */
    public int batchSaveDataEquipment(List<DataEquipmentBatchDto> dataList);

    /**
     * 查询设备台效统计数据
     *
     * @param equipmentType 设备类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param timeType 时间聚合类型（week/month/year）
     * @return 台效统计数据
     */
    public List<DataEquipmentEfficiencyStats> getEfficiencyStats(Long equipmentType, Date startTime, Date endTime, String timeType);

    /**
     * 查询设备作业率统计数据
     *
     * @param equipmentType 设备类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param timeType 时间聚合类型（week/month/year）
     * @return 作业率统计数据
     */
    public List<DataEquipmentUtilizationStats> getUtilizationStats(Long equipmentType, Date startTime, Date endTime, String timeType);
}
