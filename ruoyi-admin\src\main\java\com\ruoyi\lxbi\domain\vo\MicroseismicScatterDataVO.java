package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 微震散点图数据VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MicroseismicScatterDataVO {
    
    /**
     * X坐标
     */
    private BigDecimal xCoordinate;
    
    /**
     * Y坐标
     */
    private BigDecimal yCoordinate;
    
    /**
     * 震级大小
     */
    private BigDecimal magnitude;
    
    /**
     * 事件类型
     */
    private String eventType;
    
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 发生时间
     */
    private String occurTime;
}
