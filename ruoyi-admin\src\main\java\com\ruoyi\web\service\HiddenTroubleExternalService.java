package com.ruoyi.web.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.framework.config.ExternalApiProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 第三方隐患管理系统API对接服务
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@Service
public class HiddenTroubleExternalService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ExternalApiProperties externalApiProperties;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 调用第三方隐患统计接口
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    public Map<String, Object> getStatistics(String startTime, String endTime) {
        try {
            // 检查API是否启用
            if (!externalApiProperties.getHiddenTrouble().getEnabled()) {
                log.warn("隐患管理系统API未启用");
                return createErrorResponse("隐患管理系统API未启用");
            }

            String url = externalApiProperties.getHiddenTrouble().getStatisticsUrl();
            log.info("调用第三方隐患统计接口: {}", url);

            // 构建请求参数
            Map<String, Object> request = new HashMap<>();
            if (startTime != null) {
                request.put("startTime", startTime);
            }
            if (endTime != null) {
                request.put("endTime", endTime);
            }
            
            log.info("请求参数: {}", request);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, 
                String.class
            );

            log.info("第三方API响应状态: {}", response.getStatusCode());
            log.info("第三方API响应内容: {}", response.getBody());

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> apiResponse = objectMapper.readValue(response.getBody(), Map.class);
                log.info("隐患统计数据获取成功: {}", apiResponse);
                return apiResponse;
            } else {
                log.error("第三方API调用失败，状态码: {}", response.getStatusCode());
                return createErrorResponse("第三方API调用失败");
            }

        } catch (Exception e) {
            log.error("调用第三方隐患统计接口失败", e);
            return createErrorResponse("调用第三方API异常: " + e.getMessage());
        }
    }

    /**
     * 调用第三方隐患列表接口
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param state 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 列表结果
     */
    public Map<String, Object> getStatisticsPage(Integer current, Integer size, 
                                                String state, String startTime, String endTime) {
        try {
            // 检查API是否启用
            if (!externalApiProperties.getHiddenTrouble().getEnabled()) {
                log.warn("隐患管理系统API未启用");
                return createErrorResponse("隐患管理系统API未启用");
            }

            String url = externalApiProperties.getHiddenTrouble().getStatisticsPageUrl();
            log.info("调用第三方隐患列表接口: {}", url);

            // 构建请求参数
            Map<String, Object> request = new HashMap<>();
            request.put("current", current != null ? current : 1);
            request.put("size", size != null ? size : 10);
            if (state != null) {
                request.put("state", state);
            }
            if (startTime != null) {
                request.put("startTime", startTime);
            }
            if (endTime != null) {
                request.put("endTime", endTime);
            }
            
            log.info("请求参数: {}", request);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, 
                String.class
            );

            log.info("第三方API响应状态: {}", response.getStatusCode());
            log.info("第三方API响应内容: {}", response.getBody());

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> apiResponse = objectMapper.readValue(response.getBody(), Map.class);
                log.info("隐患列表数据获取成功");
                return apiResponse;
            } else {
                log.error("第三方API调用失败，状态码: {}", response.getStatusCode());
                return createErrorResponse("第三方API调用失败");
            }

        } catch (Exception e) {
            log.error("调用第三方隐患列表接口失败", e);
            return createErrorResponse("调用第三方API异常: " + e.getMessage());
        }
    }

    /**
     * 测试第三方API连接
     * 
     * @return 测试结果
     */
    public boolean testConnection() {
        try {
            log.info("测试第三方隐患管理系统API连接");
            Map<String, Object> response = getStatistics(null, null);
            boolean isSuccess = response != null && "0".equals(String.valueOf(response.get("code")));
            log.info("第三方API连接测试结果: {}", isSuccess ? "成功" : "失败");
            return isSuccess;
        } catch (Exception e) {
            log.error("第三方API连接测试失败", e);
            return false;
        }
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 500);
        response.put("message", message);
        response.put("data", null);
        return response;
    }
}
