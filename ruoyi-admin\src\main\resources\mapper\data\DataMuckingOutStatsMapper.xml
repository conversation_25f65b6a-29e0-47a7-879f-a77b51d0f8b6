<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataMuckingOutStatsMapper">
    
    <!-- 总体统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataMuckingOutTotalStats" id="DataMuckingOutTotalStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="totalTons"            column="total_tons"             />
    </resultMap>

    <!-- 总体统计结果映射（含计划量） -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataMuckingOutTotalWithPlanStats" id="DataMuckingOutTotalWithPlanStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="totalTons"            column="total_tons"             />
        <result property="planTons"             column="plan_tons"              />
    </resultMap>
    
    <!-- 详细统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataMuckingOutStats" id="DataMuckingOutStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="workingPeriodId"      column="working_period_id"      />
        <result property="projectDepartmentId"  column="project_department_id"  />
        <result property="stopeId"              column="stope_id"               />
        <result property="totalTons"            column="total_tons"             />
    </resultMap>

    <!-- 班次统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataMuckingOutPeriodStats" id="DataMuckingOutPeriodStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="workingPeriodId"      column="working_period_id"      />
        <result property="workingPeriodName"    column="working_period_name"    />
        <result property="totalTons"            column="total_tons"             />
    </resultMap>

    <!-- 项目部门统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataMuckingOutDepartmentStats" id="DataMuckingOutDepartmentStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="projectDepartmentId"  column="project_department_id"  />
        <result property="projectDepartmentName" column="project_department_name" />
        <result property="totalTons"            column="total_tons"             />
    </resultMap>

    <!-- 采场统计结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataMuckingOutStopeStats" id="DataMuckingOutStopeStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="stopeId"              column="stope_id"               />
        <result property="stopeName"            column="stope_name"             />
        <result property="totalTons"            column="total_tons"             />
    </resultMap>
   
    <!-- ========== 总体统计查询方法 ========== -->
    
    <select id="selectDailyTotalStats" resultMap="DataMuckingOutTotalStatsResult">
        select operation_date, total_tons
        from vdata_mucking_out_daily_total_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date
    </select>
    
    <select id="selectWeeklyTotalStats" resultMap="DataMuckingOutTotalStatsResult">
        select year, week_number, week_start_date, week_end_date, total_tons
        from vdata_mucking_out_weekly_total_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number
    </select>
    
    <select id="selectMonthlyTotalStats" resultMap="DataMuckingOutTotalStatsResult">
        select year, month, total_tons
        from vdata_mucking_out_monthly_total_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month
    </select>
    
    <select id="selectYearlyTotalStats" resultMap="DataMuckingOutTotalStatsResult">
        select year, total_tons
        from vdata_mucking_out_yearly_total_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year
    </select>

    <!-- ========== 详细统计查询方法 ========== -->
    
    <select id="selectDailyStats" resultMap="DataMuckingOutStatsResult">
        select operation_date, working_period_id, project_department_id, stope_id, total_tons
        from vdata_mucking_out_daily_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date, working_period_id, project_department_id, stope_id
    </select>
    
    <select id="selectWeeklyStats" resultMap="DataMuckingOutStatsResult">
        select year, week_number, week_start_date, week_end_date, 
               working_period_id, project_department_id, stope_id, total_tons
        from vdata_mucking_out_weekly_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number, working_period_id, project_department_id, stope_id
    </select>
    
    <select id="selectMonthlyStats" resultMap="DataMuckingOutStatsResult">
        select year, month, working_period_id, project_department_id, stope_id, total_tons
        from vdata_mucking_out_monthly_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month, working_period_id, project_department_id, stope_id
    </select>
    
    <select id="selectYearlyStats" resultMap="DataMuckingOutStatsResult">
        select year, working_period_id, project_department_id, stope_id, total_tons
        from vdata_mucking_out_yearly_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year, working_period_id, project_department_id, stope_id
    </select>

    <!-- ========== 班次统计查询方法 ========== -->

    <select id="selectDailyPeriodStats" resultMap="DataMuckingOutPeriodStatsResult">
        select operation_date, working_period_id, working_period_name, total_tons
        from vdata_mucking_out_daily_period_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date, working_period_id
    </select>

    <select id="selectWeeklyPeriodStats" resultMap="DataMuckingOutPeriodStatsResult">
        select year, week_number, week_start_date, week_end_date, working_period_id, working_period_name, total_tons
        from vdata_mucking_out_weekly_period_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number, working_period_id
    </select>

    <select id="selectMonthlyPeriodStats" resultMap="DataMuckingOutPeriodStatsResult">
        select year, month, working_period_id, working_period_name, total_tons
        from vdata_mucking_out_monthly_period_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month, working_period_id
    </select>

    <select id="selectYearlyPeriodStats" resultMap="DataMuckingOutPeriodStatsResult">
        select year, working_period_id, working_period_name, total_tons
        from vdata_mucking_out_yearly_period_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year, working_period_id
    </select>

    <!-- ========== 项目部门统计查询方法 ========== -->

    <select id="selectDailyDepartmentStats" resultMap="DataMuckingOutDepartmentStatsResult">
        select operation_date, project_department_id, project_department_name, total_tons
        from vdata_mucking_out_daily_department_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date, project_department_id
    </select>

    <select id="selectWeeklyDepartmentStats" resultMap="DataMuckingOutDepartmentStatsResult">
        select year, week_number, week_start_date, week_end_date, project_department_id, project_department_name, total_tons
        from vdata_mucking_out_weekly_department_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number, project_department_id
    </select>

    <select id="selectMonthlyDepartmentStats" resultMap="DataMuckingOutDepartmentStatsResult">
        select year, month, project_department_id, project_department_name, total_tons
        from vdata_mucking_out_monthly_department_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month, project_department_id
    </select>

    <select id="selectYearlyDepartmentStats" resultMap="DataMuckingOutDepartmentStatsResult">
        select year, project_department_id, project_department_name, total_tons
        from vdata_mucking_out_yearly_department_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year, project_department_id
    </select>

    <!-- ========== 采场统计查询方法 ========== -->

    <select id="selectDailyStopeStats" resultMap="DataMuckingOutStopeStatsResult">
        select operation_date, stope_id, stope_name, total_tons
        from vdata_mucking_out_daily_stope_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date, stope_id
    </select>

    <select id="selectWeeklyStopeStats" resultMap="DataMuckingOutStopeStatsResult">
        select year, week_number, week_start_date, week_end_date, stope_id, stope_name, total_tons
        from vdata_mucking_out_weekly_stope_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number, stope_id
    </select>

    <select id="selectMonthlyStopeStats" resultMap="DataMuckingOutStopeStatsResult">
        select year, month, stope_id, stope_name, total_tons
        from vdata_mucking_out_monthly_stope_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month, stope_id
    </select>

    <select id="selectYearlyStopeStats" resultMap="DataMuckingOutStopeStatsResult">
        select year, stope_id, stope_name, total_tons
        from vdata_mucking_out_yearly_stope_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year, stope_id
    </select>

    <!-- ========== 总体统计查询方法（含计划量） ========== -->

    <select id="selectDailyTotalWithPlanStats" resultMap="DataMuckingOutTotalWithPlanStatsResult">
        WITH monthly_plan AS (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(CAST(ore_output AS NUMERIC)) as plan_tons
            FROM plan_stope_monthly
            WHERE plan_date IS NOT NULL AND ore_output IS NOT NULL
            GROUP BY plan_date
        )
        SELECT
            da.operation_date,
            da.total_tons,
            ROUND(mp.plan_tons / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_tons
        FROM vdata_mucking_out_daily_total_stats da
        CROSS JOIN get_financial_month(da.operation_date) fm(financial_year, financial_month)
        LEFT JOIN monthly_plan mp ON fm.financial_year = mp.year AND fm.financial_month = mp.month
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY da.operation_date
    </select>

    <select id="selectWeeklyTotalWithPlanStats" resultMap="DataMuckingOutTotalWithPlanStatsResult">
        WITH monthly_plan AS (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(CAST(ore_output AS NUMERIC)) as plan_tons
            FROM plan_stope_monthly
            WHERE plan_date IS NOT NULL AND ore_output IS NOT NULL
            GROUP BY plan_date
        )
        SELECT
            wa.year,
            wa.week_number,
            wa.week_start_date,
            wa.week_end_date,
            wa.total_tons,
            ROUND(mp.plan_tons / 4, 2) as plan_tons
        FROM vdata_mucking_out_weekly_total_stats wa
        CROSS JOIN get_financial_month(wa.week_start_date) fm(financial_year, financial_month)
        LEFT JOIN monthly_plan mp ON fm.financial_year = mp.year AND fm.financial_month = mp.month
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY wa.year, wa.week_number
    </select>

    <select id="selectMonthlyTotalWithPlanStats" resultMap="DataMuckingOutTotalWithPlanStatsResult">
        WITH monthly_plan AS (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(CAST(ore_output AS NUMERIC)) as plan_tons
            FROM plan_stope_monthly
            WHERE plan_date IS NOT NULL AND ore_output IS NOT NULL
            GROUP BY plan_date
        )
        SELECT
            ma.year,
            ma.month,
            ma.total_tons,
            mp.plan_tons
        FROM vdata_mucking_out_monthly_total_stats ma
        LEFT JOIN monthly_plan mp ON ma.year = mp.year AND ma.month = mp.month
        <where>
            <if test="startDate != null">
                AND (ma.year > EXTRACT(YEAR FROM #{startDate}::date)
                OR (ma.year = EXTRACT(YEAR FROM #{startDate}::date) AND ma.month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (ma.year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                OR (ma.year = EXTRACT(YEAR FROM #{endDate}::date) AND ma.month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        ORDER BY ma.year, ma.month
    </select>

    <select id="selectYearlyTotalWithPlanStats" resultMap="DataMuckingOutTotalWithPlanStatsResult">
        WITH yearly_plan AS (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                SUM(CAST(ore_output AS NUMERIC)) as plan_tons
            FROM plan_stope_monthly
            WHERE plan_date IS NOT NULL AND ore_output IS NOT NULL
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM'))
        )
        SELECT
            ya.year,
            ya.total_tons,
            yp.plan_tons
        FROM vdata_mucking_out_yearly_total_stats ya
        LEFT JOIN yearly_plan yp ON ya.year = yp.year
        <where>
            <if test="startDate != null">
                AND ya.year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND ya.year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        ORDER BY ya.year
    </select>
</mapper>
