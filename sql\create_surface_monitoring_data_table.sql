-- 创建地表监测数据表
CREATE TABLE surface_monitoring_data (
    id BIGSERIAL PRIMARY KEY,
    
    -- 基本监测数据
    date DATE NOT NULL,
    station_name VARCHAR(100) NOT NULL,
    wgbh VARCHAR(50) NOT NULL,
    
    -- X轴偏移统计
    x1 INTEGER DEFAULT 0,  -- x偏移在0-2.5mm的次数占比
    x2 INTEGER DEFAULT 0,  -- x偏移在2.5-5mm的次数占比
    x3 INTEGER DEFAULT 0,  -- x偏移在5-10mm的次数占比
    x4 INTEGER DEFAULT 0,  -- x偏移>10mm的次数占比
    
    -- Y轴偏移统计
    y1 INTEGER DEFAULT 0,  -- y偏移在0-2.5mm的次数占比
    y2 INTEGER DEFAULT 0,  -- y偏移在2.5-5mm的次数占比
    y3 INTEGER DEFAULT 0,  -- y偏移在5-10mm的次数占比
    y4 INTEGER DEFAULT 0,  -- y偏移>10mm的次数占比
    
    -- 高度偏移统计
    h1 INTEGER DEFAULT 0,  -- 高度偏移在0-2.5mm的次数占比
    h2 INTEGER DEFAULT 0,  -- 高度偏移在2.5-5mm的次数占比
    h3 INTEGER DEFAULT 0,  -- 高度偏移在5-10mm的次数占比
    h4 INTEGER DEFAULT 0,  -- 高度偏移>10mm的次数占比
    
    -- 偏移距离总和
    ystacked_total_offset DECIMAL(15,6),  -- 当日y偏移距离总和
    hstacked_total_offset DECIMAL(15,6),  -- 当日高度偏移距离总和
    xstacked_total_offset DECIMAL(15,6),  -- 当日x偏移距离总和
    
    -- 原始数据信息
    original_id INTEGER,                   -- 原始数据ID
    original_data JSONB,                   -- 原始数据(JSON格式)
    
    -- 数据来源信息
    data_source VARCHAR(50) DEFAULT 'api', -- 数据来源
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 同步时间
    
    -- 审计字段
    create_by VARCHAR(64) DEFAULT 'system',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT 'system',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500),
    
    -- 逻辑删除
    is_deleted SMALLINT DEFAULT 0 CHECK (is_deleted IN (0, 1))
);

-- 创建复合唯一约束（日期 + 基站名称 + 终端编号）
ALTER TABLE surface_monitoring_data 
ADD CONSTRAINT uk_date_station_wgbh 
UNIQUE (date, station_name, wgbh);

-- 创建索引
CREATE INDEX idx_surface_monitoring_date ON surface_monitoring_data(date);
CREATE INDEX idx_surface_monitoring_station ON surface_monitoring_data(station_name);
CREATE INDEX idx_surface_monitoring_wgbh ON surface_monitoring_data(wgbh);
CREATE INDEX idx_surface_monitoring_original_id ON surface_monitoring_data(original_id);
CREATE INDEX idx_surface_monitoring_sync_time ON surface_monitoring_data(sync_time);
CREATE INDEX idx_surface_monitoring_create_time ON surface_monitoring_data(create_time);
CREATE INDEX idx_surface_monitoring_is_deleted ON surface_monitoring_data(is_deleted);

-- 为JSONB字段创建GIN索引
CREATE INDEX idx_surface_monitoring_original_data ON surface_monitoring_data USING GIN (original_data);

-- 添加表注释
COMMENT ON TABLE surface_monitoring_data IS '地表监测数据表';

-- 添加字段注释
COMMENT ON COLUMN surface_monitoring_data.id IS '主键ID';
COMMENT ON COLUMN surface_monitoring_data.date IS '监测日期';
COMMENT ON COLUMN surface_monitoring_data.station_name IS '基站名称';
COMMENT ON COLUMN surface_monitoring_data.wgbh IS '终端编号';
COMMENT ON COLUMN surface_monitoring_data.x1 IS 'x偏移在0-2.5mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.x2 IS 'x偏移在2.5-5mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.x3 IS 'x偏移在5-10mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.x4 IS 'x偏移>10mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.y1 IS 'y偏移在0-2.5mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.y2 IS 'y偏移在2.5-5mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.y3 IS 'y偏移在5-10mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.y4 IS 'y偏移>10mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.h1 IS '高度偏移在0-2.5mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.h2 IS '高度偏移在2.5-5mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.h3 IS '高度偏移在5-10mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.h4 IS '高度偏移>10mm的次数占比';
COMMENT ON COLUMN surface_monitoring_data.ystacked_total_offset IS '当日y偏移距离总和';
COMMENT ON COLUMN surface_monitoring_data.hstacked_total_offset IS '当日高度偏移距离总和';
COMMENT ON COLUMN surface_monitoring_data.xstacked_total_offset IS '当日x偏移距离总和';
COMMENT ON COLUMN surface_monitoring_data.original_id IS '原始数据ID';
COMMENT ON COLUMN surface_monitoring_data.original_data IS '原始数据(JSON格式)';
COMMENT ON COLUMN surface_monitoring_data.data_source IS '数据来源';
COMMENT ON COLUMN surface_monitoring_data.sync_time IS '同步时间';
COMMENT ON COLUMN surface_monitoring_data.create_by IS '创建者';
COMMENT ON COLUMN surface_monitoring_data.create_time IS '创建时间';
COMMENT ON COLUMN surface_monitoring_data.update_by IS '更新者';
COMMENT ON COLUMN surface_monitoring_data.update_time IS '更新时间';
COMMENT ON COLUMN surface_monitoring_data.remark IS '备注';
COMMENT ON COLUMN surface_monitoring_data.is_deleted IS '是否删除(0:否 1:是)';

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_surface_monitoring_data_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_surface_monitoring_data_modtime
    BEFORE UPDATE ON surface_monitoring_data
    FOR EACH ROW
    EXECUTE FUNCTION update_surface_monitoring_data_modified_column();

-- 创建统计视图
CREATE VIEW surface_monitoring_statistics AS
SELECT 
    date,
    COUNT(*) as station_count,
    AVG(x1 + x2 + x3 + x4) as avg_x_total,
    AVG(y1 + y2 + y3 + y4) as avg_y_total,
    AVG(h1 + h2 + h3 + h4) as avg_h_total,
    AVG(ABS(xstacked_total_offset)) as avg_x_offset,
    AVG(ABS(ystacked_total_offset)) as avg_y_offset,
    AVG(ABS(hstacked_total_offset)) as avg_h_offset,
    MAX(ABS(xstacked_total_offset)) as max_x_offset,
    MAX(ABS(ystacked_total_offset)) as max_y_offset,
    MAX(ABS(hstacked_total_offset)) as max_h_offset
FROM surface_monitoring_data 
WHERE is_deleted = 0
GROUP BY date
ORDER BY date DESC;

COMMENT ON VIEW surface_monitoring_statistics IS '地表监测数据统计视图';

-- 查询验证表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'surface_monitoring_data'
ORDER BY ordinal_position;
