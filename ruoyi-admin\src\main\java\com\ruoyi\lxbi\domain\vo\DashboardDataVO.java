package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 仪表板综合数据VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DashboardDataVO {
    
    /**
     * 概览统计
     */
    private HiddenTroubleOverviewVO overview;
    
    /**
     * 部门分布
     */
    private List<DepartmentDistributionVO> departmentDistribution;
    
    /**
     * 位置频率统计
     */
    private List<LocationFrequencyVO> locationFrequency;
    
    /**
     * 趋势数据
     */
    private List<TrendStatisticsVO> trendData;
    
    /**
     * 状态分布
     */
    private List<StatusDistributionVO> statusDistribution;
    
    /**
     * 等级分布
     */
    private List<GradeDistributionVO> gradeDistribution;
}
