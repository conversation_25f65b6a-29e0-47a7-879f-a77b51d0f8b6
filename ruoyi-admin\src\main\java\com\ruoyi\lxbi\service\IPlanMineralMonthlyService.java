package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanMineralMonthly;
import com.ruoyi.lxbi.domain.request.PlanMineralMonthlyBatchDto;

/**
 * 选矿整体月计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface IPlanMineralMonthlyService 
{
    /**
     * 查询选矿整体月计划
     * 
     * @param id 选矿整体月计划主键
     * @return 选矿整体月计划
     */
    public PlanMineralMonthly selectPlanMineralMonthlyById(Long id);

    /**
     * 查询选矿整体月计划列表
     * 
     * @param planMineralMonthly 选矿整体月计划
     * @return 选矿整体月计划集合
     */
    public List<PlanMineralMonthly> selectPlanMineralMonthlyList(PlanMineralMonthly planMineralMonthly);

    /**
     * 新增选矿整体月计划
     * 
     * @param planMineralMonthly 选矿整体月计划
     * @return 结果
     */
    public int insertPlanMineralMonthly(PlanMineralMonthly planMineralMonthly);

    /**
     * 修改选矿整体月计划
     * 
     * @param planMineralMonthly 选矿整体月计划
     * @return 结果
     */
    public int updatePlanMineralMonthly(PlanMineralMonthly planMineralMonthly);

    /**
     * 批量删除选矿整体月计划
     * 
     * @param ids 需要删除的选矿整体月计划主键集合
     * @return 结果
     */
    public int deletePlanMineralMonthlyByIds(Long[] ids);

    /**
     * 删除选矿整体月计划信息
     *
     * @param id 选矿整体月计划主键
     * @return 结果
     */
    public int deletePlanMineralMonthlyById(Long id);

    /**
     * 批量保存选矿整体月计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    public int batchSavePlanMineralMonthly(List<PlanMineralMonthlyBatchDto> batchDataList);
}
