package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.Date;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.DataDrillingMapper;
import com.ruoyi.lxbi.domain.DataDrilling;
import com.ruoyi.lxbi.domain.response.DataDrillingVo;
import com.ruoyi.lxbi.domain.request.DataDrillingBatchDto;
import com.ruoyi.lxbi.service.IDataDrillingService;

/**
 * 钻孔施工数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Service
public class DataDrillingServiceImpl implements IDataDrillingService 
{
    @Autowired
    private DataDrillingMapper dataDrillingMapper;

    /**
     * 查询钻孔施工数据
     * 
     * @param id 钻孔施工数据主键
     * @return 钻孔施工数据
     */
    @Override
    public DataDrilling selectDataDrillingById(Long id)
    {
        return dataDrillingMapper.selectDataDrillingById(id);
    }

    /**
     * 查询钻孔施工数据列表
     *
     * @param dataDrilling 钻孔施工数据
     * @return 钻孔施工数据
     */
    @Override
    public List<DataDrillingVo> selectDataDrillingList(DataDrilling dataDrilling)
    {
        return dataDrillingMapper.selectDataDrillingList(dataDrilling);
    }

    /**
     * 新增钻孔施工数据
     * 
     * @param dataDrilling 钻孔施工数据
     * @return 结果
     */
    @Override
    public int insertDataDrilling(DataDrilling dataDrilling)
    {
        dataDrilling.setCreateTime(DateUtils.getNowDate());
        return dataDrillingMapper.insertDataDrilling(dataDrilling);
    }

    /**
     * 修改钻孔施工数据
     * 
     * @param dataDrilling 钻孔施工数据
     * @return 结果
     */
    @Override
    public int updateDataDrilling(DataDrilling dataDrilling)
    {
        dataDrilling.setUpdateTime(DateUtils.getNowDate());
        return dataDrillingMapper.updateDataDrilling(dataDrilling);
    }

    /**
     * 批量删除钻孔施工数据
     * 
     * @param ids 需要删除的钻孔施工数据主键
     * @return 结果
     */
    @Override
    public int deleteDataDrillingByIds(Long[] ids)
    {
        return dataDrillingMapper.deleteDataDrillingByIds(ids);
    }

    /**
     * 删除钻孔施工数据信息
     *
     * @param id 钻孔施工数据主键
     * @return 结果
     */
    @Override
    public int deleteDataDrillingById(Long id)
    {
        return dataDrillingMapper.deleteDataDrillingById(id);
    }

    /**
     * 根据作业日期查询钻孔施工数据列表
     *
     * @param operationDate 作业日期
     * @return 钻孔施工数据集合
     */
    @Override
    public List<DataDrillingVo> selectDataDrillingByOperationDate(Date operationDate) {
        return dataDrillingMapper.selectDataDrillingByOperationDate(operationDate);
    }

    /**
     * 批量保存钻孔施工数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDataDrilling(List<DataDrillingBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个日期的数据
        Date operationDate = batchDataList.get(0).getOperationDate();
        if (operationDate == null) {
            throw new ServiceException("作业日期不能为空");
        }

        boolean allSameDate = batchDataList.stream()
                .allMatch(data -> operationDate.equals(data.getOperationDate()));
        if (!allSameDate) {
            throw new ServiceException("批量数据必须是同一个作业日期");
        }

        // 查询现有数据
        List<DataDrillingVo> existingDataList = selectDataDrillingByOperationDate(operationDate);
        Map<Long, DataDrillingVo> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(DataDrillingVo::getId, data -> data));

        List<DataDrilling> toInsert = new ArrayList<>();
        List<DataDrilling> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (DataDrillingBatchDto batchData : batchDataList) {
            if (Boolean.TRUE.equals(batchData.getIsNew())) {
                // 新增数据
                DataDrilling newData = new DataDrilling();
                BeanUtils.copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if (batchData.getId() != null && existingDataMap.containsKey(batchData.getId())) {
                // 更新现有数据（根据ID判断）
                DataDrilling updateData = new DataDrilling();
                BeanUtils.copyProperties(batchData, updateData);
                updateData.setId(batchData.getId());
                updateData.setUpdateBy(SecurityUtils.getUsername());
                updateData.setUpdateTime(DateUtils.getNowDate());
                toUpdate.add(updateData);

                // 从现有数据中移除，剩下的就是要删除的
                toDelete.remove(batchData.getId());
            }
        }

        int result = 0;

        // 执行删除
        if (!toDelete.isEmpty()) {
            result += dataDrillingMapper.deleteDataDrillingByIds(toDelete.toArray(new Long[0]));
        }

        // 执行新增
        if (!toInsert.isEmpty()) {
            result += dataDrillingMapper.batchInsertDataDrilling(toInsert);
        }

        // 执行更新
        for (DataDrilling updateData : toUpdate) {
            result += dataDrillingMapper.updateDataDrilling(updateData);
        }

        return result;
    }
}
