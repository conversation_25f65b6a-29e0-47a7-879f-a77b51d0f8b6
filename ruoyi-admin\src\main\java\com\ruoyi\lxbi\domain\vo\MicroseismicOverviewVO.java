package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 微震概览VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MicroseismicOverviewVO {
    
    /**
     * 台网事件数
     */
    private Long networkEventCount;
    
    /**
     * 目标事件数
     */
    private Long targetEventCount;
    
    /**
     * 设备事件总数
     */
    private Long deviceEventTotal;
    
    /**
     * 微震监测设备数量
     */
    private Long microseismicDeviceCount;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 统计周期
     */
    private String period;
}
