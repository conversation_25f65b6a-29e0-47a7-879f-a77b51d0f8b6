package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataDrilling;
import com.ruoyi.lxbi.domain.response.DataDrillingVo;
import com.ruoyi.lxbi.service.IDataDrillingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.lxbi.domain.request.DataDrillingBatchDto;

/**
 * 钻孔施工数据Controller
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@RestController
@RequestMapping("/data/drilling")
public class DataDrillingController extends BaseController {
    @Autowired
    private IDataDrillingService dataDrillingService;

    /**
     * 查询钻孔施工数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:drilling:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataDrilling dataDrilling) {
        startPage();
        List<DataDrillingVo> list = dataDrillingService.selectDataDrillingList(dataDrilling);
        return getDataTable(list);
    }

    /**
     * 导出钻孔施工数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:drilling:export')")
    @Log(title = "钻孔施工数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataDrilling dataDrilling) {
        List<DataDrillingVo> list = dataDrillingService.selectDataDrillingList(dataDrilling);
        ExcelUtil<DataDrillingVo> util = new ExcelUtil<DataDrillingVo>(DataDrillingVo.class);
        util.exportExcel(response, list, "钻孔施工数据数据");
    }

    /**
     * 获取钻孔施工数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:drilling:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataDrillingService.selectDataDrillingById(id));
    }

    /**
     * 新增钻孔施工数据
     */
    @PreAuthorize("@ss.hasPermi('data:drilling:add')")
    @Log(title = "钻孔施工数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataDrilling dataDrilling)
    {
        return toAjax(dataDrillingService.insertDataDrilling(dataDrilling));
    }

    /**
     * 修改钻孔施工数据
     */
    @PreAuthorize("@ss.hasPermi('data:drilling:edit')")
    @Log(title = "钻孔施工数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataDrilling dataDrilling)
    {
        return toAjax(dataDrillingService.updateDataDrilling(dataDrilling));
    }

    /**
     * 删除钻孔施工数据
     */
    @PreAuthorize("@ss.hasPermi('data:drilling:remove')")
    @Log(title = "钻孔施工数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataDrillingService.deleteDataDrillingByIds(ids));
    }

    /**
     * 批量保存钻孔施工数据（增删改查）
     * 传入批量列表，验证是否同一个日期的数据，然后查询这个日期的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('data:drilling:edit')")
    @Log(title = "钻孔施工数据批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<DataDrillingBatchDto> batchDataList)
    {
        try {
            int result = dataDrillingService.batchSaveDataDrilling(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
