package com.ruoyi.lxbi.admin.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 隐患统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Mapper
public interface HiddenTroubleStatMapper {
    
    /**
     * 获取隐患总数
     *
     * @return 隐患总数
     */
    Long getTotalHiddenTroubleCount();

    /**
     * 获取重大隐患数
     *
     * @return 重大隐患数
     */
    Long getMajorHiddenTroubleCount();

    /**
     * 获取危险源数量（用隐患类别数量代替）
     *
     * @return 危险源数量
     */
    Long getRiskSourceCount();

    /**
     * 获取风险管控数量（用已整改隐患数代替）
     *
     * @return 风险管控数量
     */
    Long getRiskControlCount();

    /**
     * 按日期范围获取隐患总数
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 隐患总数
     */
    Long getTotalHiddenTroubleCountByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按日期范围获取重大隐患数
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 重大隐患数
     */
    Long getMajorHiddenTroubleCountByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按日期范围获取危险源数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 危险源数量
     */
    Long getRiskSourceCountByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按日期范围获取风险管控数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 风险管控数量
     */
    Long getRiskControlCountByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 获取部门隐患分布统计
     *
     * @return 部门分布统计数据
     */
    List<Map<String, Object>> getDepartmentDistribution();

    /**
     * 按日期范围获取部门隐患分布统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 部门分布统计数据
     */
    List<Map<String, Object>> getDepartmentDistributionByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 获取高频发隐患位置统计
     *
     * @return 位置频率统计数据
     */
    List<Map<String, Object>> getLocationFrequency();

    /**
     * 按日期范围获取高频发隐患位置统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 位置频率统计数据
     */
    List<Map<String, Object>> getLocationFrequencyByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 按日统计隐患数量
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日统计数据
     */
    List<Map<String, Object>> getDailyStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 按周统计隐患数量
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 周统计数据
     */
    List<Map<String, Object>> getWeeklyStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 按月统计隐患数量
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 月统计数据
     */
    List<Map<String, Object>> getMonthlyStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 获取隐患状态分布统计
     *
     * @return 状态分布统计数据
     */
    List<Map<String, Object>> getStatusDistribution();

    /**
     * 按日期范围获取隐患状态分布统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 状态分布统计数据
     */
    List<Map<String, Object>> getStatusDistributionByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 获取隐患等级分布统计
     *
     * @return 等级分布统计数据
     */
    List<Map<String, Object>> getGradeDistribution();

    /**
     * 按日期范围获取隐患等级分布统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 等级分布统计数据
     */
    List<Map<String, Object>> getGradeDistributionByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 获取隐患类别分布统计
     *
     * @return 类别分布统计数据
     */
    List<Map<String, Object>> getCategoryDistribution();

    /**
     * 按日期范围获取隐患类别分布统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 类别分布统计数据
     */
    List<Map<String, Object>> getCategoryDistributionByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 获取责任人隐患统计
     *
     * @return 责任人统计数据
     */
    List<Map<String, Object>> getResponsiblePersonStatistics();

    /**
     * 按日期范围获取责任人隐患统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 责任人统计数据
     */
    List<Map<String, Object>> getResponsiblePersonStatisticsByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 获取整改完成率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 整改完成率统计数据
     */
    Map<String, Object> getRectificationCompletionRate(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 获取超期隐患统计
     *
     * @return 超期隐患统计数据
     */
    Map<String, Object> getOverdueStatistics();

    /**
     * 按日期范围获取超期隐患统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 超期隐患统计数据
     */
    Map<String, Object> getOverdueStatisticsByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 按部门和等级统计隐患数量
     * 
     * @return 部门等级统计数据
     */
    List<Map<String, Object>> getDepartmentGradeStatistics();
    
    /**
     * 获取隐患趋势对比数据（重大隐患vs一般隐患）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势对比数据
     */
    List<Map<String, Object>> getTrendComparisonData(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 获取检查人员工作量统计
     * 
     * @return 检查人员统计数据
     */
    List<Map<String, Object>> getInspectorWorkloadStatistics();
    
    /**
     * 获取复查人员工作量统计
     * 
     * @return 复查人员统计数据
     */
    List<Map<String, Object>> getReviewerWorkloadStatistics();
    
    /**
     * 获取隐患处理时效统计
     *
     * @return 处理时效统计数据
     */
    List<Map<String, Object>> getProcessingTimeStatistics();

    /**
     * 获取近期隐患趋势（最近30天）
     *
     * @return 近期趋势数据
     */
    List<Map<String, Object>> getRecentTrendData();

    /**
     * 按日期范围获取近期隐患趋势
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 近期趋势数据
     */
    List<Map<String, Object>> getRecentTrendDataByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按日统计超期隐患趋势
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 超期隐患日统计数据
     */
    List<Map<String, Object>> getOverdueDailyStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按周统计超期隐患趋势
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 超期隐患周统计数据
     */
    List<Map<String, Object>> getOverdueWeeklyStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按月统计超期隐患趋势
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 超期隐患月统计数据
     */
    List<Map<String, Object>> getOverdueMonthlyStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 获取隐患热力图数据（按小时分布）
     *
     * @return 热力图数据
     */
    List<Map<String, Object>> getHourlyHeatmapData();

    /**
     * 获取隐患严重程度分析
     *
     * @return 严重程度分析数据
     */
    List<Map<String, Object>> getSeverityAnalysis();

    /**
     * 获取隐患整改效率统计
     *
     * @return 整改效率统计数据
     */
    List<Map<String, Object>> getRectificationEfficiency();

    /**
     * 按日期范围获取隐患整改效率统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 整改效率统计数据
     */
    List<Map<String, Object>> getRectificationEfficiencyByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按日期范围获取安全小结统计
     * 包括：隐患总数、待整改数、超时数等
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 安全小结统计数据
     */
    Map<String, Object> getSafetySummaryByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
