package com.ruoyi.lxbi.admin.service.impl;

import com.ruoyi.lxbi.admin.mapper.HiddenTroubleStatMapper;
import com.ruoyi.lxbi.admin.service.IHiddenTroubleStatService;
import com.ruoyi.lxbi.domain.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 隐患统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Slf4j
@Service
public class HiddenTroubleStatServiceImpl implements IHiddenTroubleStatService {

    @Autowired
    private HiddenTroubleStatMapper hiddenTroubleStatMapper;

    /**
     * 获取隐患分析概览统计
     */
    @Override
    public HiddenTroubleOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate) {
        // 如果需要根据viewType计算日期范围，可以在这里处理
        // 否则直接使用传入的startDate和endDate

        // 获取隐患总数
        Long totalCount = hiddenTroubleStatMapper.getTotalHiddenTroubleCountByDate(startDate, endDate);

        // 获取重大隐患数
        Long majorCount = hiddenTroubleStatMapper.getMajorHiddenTroubleCountByDate(startDate, endDate);

        // 获取危险源数（这里用隐患类别数量代替）
        Long riskSourceCount = hiddenTroubleStatMapper.getRiskSourceCountByDate(startDate, endDate);

        // 获取风险管控数（这里用已整改的隐患数代替）
        Long riskControlCount = hiddenTroubleStatMapper.getRiskControlCountByDate(startDate, endDate);

        HiddenTroubleOverviewVO overview = new HiddenTroubleOverviewVO(
                totalCount != null ? totalCount : 0L,
                majorCount != null ? majorCount : 0L,
                riskSourceCount != null ? riskSourceCount : 0L,
                riskControlCount != null ? riskControlCount : 0L
        );

        log.debug("获取隐患分析概览统计成功，视图类型: {}, 开始日期: {}, 结束日期: {}, 结果: {}", viewType, startDate, endDate, overview);
        return overview;
    }

    /**
     * 获取部门隐患分布统计
     */
    @Override
    public List<DepartmentDistributionVO> getDepartmentDistribution(String viewType, String startDate, String endDate) {
        // 计算日期范围
        // 直接使用传入的startDate和endDate参数

        List<Map<String, Object>> distribution = hiddenTroubleStatMapper.getDepartmentDistributionByDate(startDate, endDate);
        List<DepartmentDistributionVO> result = distribution.stream().map(map -> {
            DepartmentDistributionVO vo = new DepartmentDistributionVO();
            vo.setDepartment((String) map.get("department"));
            vo.setCount(((Number) map.get("count")).longValue());
            vo.setMajorCount(((Number) map.get("major_count")).longValue());
            vo.setGeneralCount(((Number) map.get("general_count")).longValue());
            return vo;
        }).collect(Collectors.toList());

        log.debug("获取部门隐患分布统计成功，视图类型: {}, 统计日期: {}, 数据条数: {}", viewType, startDate, endDate, result.size());
        return result;
    }

    /**
     * 获取高频发隐患位置统计
     */
    @Override
    public List<LocationFrequencyVO> getLocationFrequency(String viewType, String startDate, String endDate) {
        try {
            // 计算日期范围
            // 直接使用传入的startDate和endDate参数

            List<Map<String, Object>> locationStats = hiddenTroubleStatMapper.getLocationFrequencyByDate(startDate, endDate);
            List<LocationFrequencyVO> result = locationStats.stream().map(map -> {
                LocationFrequencyVO vo = new LocationFrequencyVO();
                vo.setLocation((String) map.get("location"));
                vo.setCount(((Number) map.get("count")).longValue());
                vo.setPercentage((BigDecimal) map.get("percentage"));
                return vo;
            }).collect(Collectors.toList());

            log.debug("获取高频发隐患位置统计成功，视图类型: {}, 统计日期: {}, 数据条数: {}", viewType, startDate, endDate, result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取隐患趋势统计
     */
    @Override
    public List<TrendStatisticsVO> getTrendStatistics(String viewType, String startDate, String endDate) {
        try {
            List<TrendStatisticsVO> trendData;

            switch (viewType.toLowerCase()) {
                case "daily":
                    trendData = getDailyStatistics(startDate, endDate);
                    break;
                case "weekly":
                    trendData = getWeeklyStatistics(startDate, endDate);
                    break;
                case "monthly":
                    trendData = getMonthlyStatistics(startDate, endDate);
                    break;
                default:
                    log.warn("不支持的统计视图类型: {}, 使用默认的日统计", viewType);
                    trendData = getDailyStatistics(startDate, endDate);
                    break;
            }

            log.debug("获取隐患趋势统计成功，视图类型: {}, 数据条数: {}", viewType, trendData.size());
            return trendData;

        } catch (Exception e) {


            log.error("方法执行失败", e);


            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);


        }
    }

    /**
     * 获取隐患状态分布统计
     */
    @Override
    public List<StatusDistributionVO> getStatusDistribution(String viewType, String startDate, String endDate) {
        try {
            // 计算日期范围
            // 直接使用传入的startDate和endDate参数

            List<Map<String, Object>> statusStats = hiddenTroubleStatMapper.getStatusDistributionByDate(startDate, endDate);
            List<StatusDistributionVO> result = statusStats.stream().map(map -> {
                StatusDistributionVO vo = new StatusDistributionVO();
                vo.setStatus((Integer) map.get("status"));
                vo.setStatusName((String) map.get("status_name"));
                vo.setCount(((Number) map.get("count")).longValue());
                vo.setPercentage((BigDecimal) map.get("percentage"));
                return vo;
            }).collect(Collectors.toList());

            log.debug("获取隐患状态分布统计成功，视图类型: {}, 统计日期: {}, 数据条数: {}", viewType, startDate, endDate, result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取隐患等级分布统计
     */
    @Override
    public List<GradeDistributionVO> getGradeDistribution(String viewType, String startDate, String endDate) {
        try {
            // 计算日期范围
            // 直接使用传入的startDate和endDate参数

            List<Map<String, Object>> gradeStats = hiddenTroubleStatMapper.getGradeDistributionByDate(startDate, endDate);
            List<GradeDistributionVO> result = gradeStats.stream().map(map -> {
                GradeDistributionVO vo = new GradeDistributionVO();
                vo.setGrade((String) map.get("grade"));
                vo.setGradeName((String) map.get("grade_name"));
                vo.setCount(((Number) map.get("count")).longValue());
                vo.setPercentage((BigDecimal) map.get("percentage"));
                return vo;
            }).collect(Collectors.toList());

            log.debug("获取隐患等级分布统计成功，视图类型: {}, 统计日期: {}, 数据条数: {}", viewType, startDate, endDate, result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取综合统计数据（用于仪表板）
     */
    @Override
    public DashboardDataVO getDashboardData(String viewType, String startDate, String endDate) {
        try {
            DashboardDataVO dashboardData = new DashboardDataVO();

            // 获取概览统计
            HiddenTroubleOverviewVO overview = getOverviewStatistics(viewType, startDate, endDate);
            dashboardData.setOverview(overview);

            // 获取部门分布
            List<DepartmentDistributionVO> departmentDistribution = getDepartmentDistribution(viewType, startDate, endDate);
            dashboardData.setDepartmentDistribution(departmentDistribution);

            // 获取位置频率统计
            List<LocationFrequencyVO> locationFrequency = getLocationFrequency(viewType, startDate, endDate);
            dashboardData.setLocationFrequency(locationFrequency);

            // 获取趋势统计（最近一段时间）
            Map<String, String> dateRange = getDefaultDateRange(viewType);
            List<TrendStatisticsVO> trendData = getTrendStatistics(viewType,
                    dateRange.get("startDate"), dateRange.get("endDate"));
            dashboardData.setTrendData(trendData);

            // 获取状态分布
            List<StatusDistributionVO> statusDistribution = getStatusDistribution(viewType, startDate, endDate);
            dashboardData.setStatusDistribution(statusDistribution);

            // 获取等级分布
            List<GradeDistributionVO> gradeDistribution = getGradeDistribution(viewType, startDate, endDate);
            dashboardData.setGradeDistribution(gradeDistribution);

            log.debug("获取综合统计数据成功，视图类型: {}, 统计日期: {}", viewType, startDate, endDate);
            return dashboardData;

        } catch (Exception e) {


            log.error("方法执行失败", e);


            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);


        }
    }

    /**
     * 按日统计隐患数量
     */
    @Override
    public List<TrendStatisticsVO> getDailyStatistics(String startDate, String endDate) {
        try {
            List<Map<String, Object>> dailyStats = hiddenTroubleStatMapper.getDailyStatistics(startDate, endDate);
            List<TrendStatisticsVO> result = dailyStats.stream().map(this::mapToTrendStatisticsVO).collect(Collectors.toList());
            log.debug("获取日统计数据成功，数据条数: {}", result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 按周统计隐患数量
     */
    @Override
    public List<TrendStatisticsVO> getWeeklyStatistics(String startDate, String endDate) {
        try {
            List<Map<String, Object>> weeklyStats = hiddenTroubleStatMapper.getWeeklyStatistics(startDate, endDate);
            List<TrendStatisticsVO> result = weeklyStats.stream().map(this::mapToTrendStatisticsVO).collect(Collectors.toList());
            log.debug("获取周统计数据成功，数据条数: {}", result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 按月统计隐患数量
     */
    @Override
    public List<TrendStatisticsVO> getMonthlyStatistics(String startDate, String endDate) {
        try {
            List<Map<String, Object>> monthlyStats = hiddenTroubleStatMapper.getMonthlyStatistics(startDate, endDate);
            List<TrendStatisticsVO> result = monthlyStats.stream().map(this::mapToTrendStatisticsVO).collect(Collectors.toList());
            log.debug("获取月统计数据成功，数据条数: {}", result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取隐患类别分布统计
     */
    @Override
    public List<LocationFrequencyVO> getCategoryDistribution(String viewType, String startDate, String endDate) {
        try {
            // 计算日期范围
            // 直接使用传入的startDate和endDate参数

            List<Map<String, Object>> categoryStats = hiddenTroubleStatMapper.getCategoryDistributionByDate(startDate, endDate);
            List<LocationFrequencyVO> result = categoryStats.stream().map(map -> {
                LocationFrequencyVO vo = new LocationFrequencyVO();
                vo.setLocation((String) map.get("category"));
                vo.setCount(((Number) map.get("count")).longValue());
                vo.setPercentage((BigDecimal) map.get("percentage"));
                return vo;
            }).collect(Collectors.toList());

            log.debug("获取隐患类别分布统计成功，视图类型: {}, 统计日期: {}, 数据条数: {}", viewType, startDate, endDate, result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取责任人隐患统计
     */
    @Override
    public List<ResponsiblePersonStatVO> getResponsiblePersonStatistics(String viewType, String startDate, String endDate) {
        try {
            // 计算日期范围
            // 直接使用传入的startDate和endDate参数

            List<Map<String, Object>> personStats = hiddenTroubleStatMapper.getResponsiblePersonStatisticsByDate(startDate, endDate);
            List<ResponsiblePersonStatVO> result = personStats.stream().map(map -> {
                ResponsiblePersonStatVO vo = new ResponsiblePersonStatVO();
                vo.setPerson((String) map.get("person"));
                vo.setTotalCount(((Number) map.get("total_count")).longValue());
                vo.setCompletedCount(((Number) map.get("completed_count")).longValue());
                vo.setOverdueCount(((Number) map.get("overdue_count")).longValue());
                vo.setCompletionRate((BigDecimal) map.get("completion_rate"));
                return vo;
            }).collect(Collectors.toList());

            log.debug("获取责任人隐患统计成功，视图类型: {}, 统计日期: {}, 数据条数: {}", viewType, startDate, endDate, result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取整改完成率统计
     */
    @Override
    public CompletionRateVO getRectificationCompletionRate(String viewType, String startDate, String endDate) {
        try {
            Map<String, Object> rateData = hiddenTroubleStatMapper.getRectificationCompletionRate(startDate, endDate);
            CompletionRateVO completionRate = new CompletionRateVO();
            completionRate.setTotalCount(((Number) rateData.get("total_count")).longValue());
            completionRate.setCompletedCount(((Number) rateData.get("completed_count")).longValue());
            completionRate.setCompletionRate((BigDecimal) rateData.get("completion_rate"));

            log.debug("获取整改完成率统计成功: {}", completionRate);
            return completionRate;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取超期隐患统计
     */
    @Override
    public OverdueStatisticsVO getOverdueStatistics(String viewType, String startDate, String endDate) {
        try {
            // 计算日期范围
            // 直接使用传入的startDate和endDate参数

            Map<String, Object> overdueData = hiddenTroubleStatMapper.getOverdueStatisticsByDate(startDate, endDate);
            OverdueStatisticsVO overdueStats = new OverdueStatisticsVO();
            overdueStats.setTotalCount(((Number) overdueData.get("total_count")).longValue());
            overdueStats.setOverdueCount(((Number) overdueData.get("overdue_count")).longValue());
            overdueStats.setOverdueRate((BigDecimal) overdueData.get("overdue_rate"));

            log.debug("获取超期隐患统计成功，视图类型: {}, 统计日期: {}, 结果: {}", viewType, startDate, endDate, overdueStats);
            return overdueStats;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取近期隐患趋势（最近30天）
     */
    @Override
    public List<TrendStatisticsVO> getRecentTrendData(String viewType, String startDate, String endDate) {
        try {
            // 计算日期范围
            // 直接使用传入的startDate和endDate参数

            List<Map<String, Object>> trendData = hiddenTroubleStatMapper.getRecentTrendDataByDate(startDate, endDate);
            List<TrendStatisticsVO> result = trendData.stream().map(this::mapToTrendStatisticsVO).collect(Collectors.toList());
            log.debug("获取近期隐患趋势数据成功，视图类型: {}, 统计日期: {}, 数据条数: {}", viewType, startDate, endDate, result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取超期隐患趋势统计
     */
    @Override
    public List<TrendStatisticsVO> getOverdueTrendStatistics(String viewType, String startDate, String endDate) {
        try {
            List<TrendStatisticsVO> trendData;

            switch (viewType.toLowerCase()) {
                case "daily":
                    trendData = getOverdueDailyStatistics(startDate, endDate);
                    break;
                case "weekly":
                    trendData = getOverdueWeeklyStatistics(startDate, endDate);
                    break;
                case "monthly":
                    trendData = getOverdueMonthlyStatistics(startDate, endDate);
                    break;
                default:
                    log.warn("不支持的统计视图类型: {}, 使用默认的日统计", viewType);
                    trendData = getOverdueDailyStatistics(startDate, endDate);
                    break;
            }

            log.debug("获取超期隐患趋势统计成功，视图类型: {}, 数据条数: {}", viewType, trendData.size());
            return trendData;

        } catch (Exception e) {
            log.error("获取超期隐患趋势统计失败", e);
            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按日统计超期隐患数量
     */
    private List<TrendStatisticsVO> getOverdueDailyStatistics(String startDate, String endDate) {
        try {
            List<Map<String, Object>> dailyStats = hiddenTroubleStatMapper.getOverdueDailyStatistics(startDate, endDate);
            List<TrendStatisticsVO> result = dailyStats.stream().map(this::mapToOverdueTrendStatisticsVO).collect(Collectors.toList());
            log.debug("获取超期隐患日统计数据成功，数据条数: {}", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取超期隐患日统计数据失败", e);
            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按周统计超期隐患数量
     */
    private List<TrendStatisticsVO> getOverdueWeeklyStatistics(String startDate, String endDate) {
        try {
            List<Map<String, Object>> weeklyStats = hiddenTroubleStatMapper.getOverdueWeeklyStatistics(startDate, endDate);
            List<TrendStatisticsVO> result = weeklyStats.stream().map(this::mapToOverdueTrendStatisticsVO).collect(Collectors.toList());
            log.debug("获取超期隐患周统计数据成功，数据条数: {}", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取超期隐患周统计数据失败", e);
            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按月统计超期隐患数量
     */
    private List<TrendStatisticsVO> getOverdueMonthlyStatistics(String startDate, String endDate) {
        try {
            List<Map<String, Object>> monthlyStats = hiddenTroubleStatMapper.getOverdueMonthlyStatistics(startDate, endDate);
            List<TrendStatisticsVO> result = monthlyStats.stream().map(this::mapToOverdueTrendStatisticsVO).collect(Collectors.toList());
            log.debug("获取超期隐患月统计数据成功，数据条数: {}", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取超期隐患月统计数据失败", e);
            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将超期隐患统计数据映射为TrendStatisticsVO
     */
    private TrendStatisticsVO mapToOverdueTrendStatisticsVO(Map<String, Object> data) {
        TrendStatisticsVO vo = new TrendStatisticsVO();

        // 设置日期相关字段
        if (data.containsKey("date")) {
            vo.setDate((String) data.get("date"));
            vo.setShortDate((String) data.get("short_date"));
        } else if (data.containsKey("week_start")) {
            vo.setDate((String) data.get("week_start"));
            vo.setShortDate((String) data.get("week_label"));
        } else if (data.containsKey("month")) {
            vo.setDate((String) data.get("month"));
            vo.setShortDate((String) data.get("month_label"));
        }

        // 设置超期隐患数量作为主要统计数据
        vo.setCount(((Number) data.getOrDefault("overdue_count", 0)).longValue());
        vo.setTotalCount(((Number) data.getOrDefault("total_count", 0)).longValue());

        // 设置超期率
        if (data.containsKey("overdue_rate")) {
            vo.setOverdueRate((BigDecimal) data.get("overdue_rate"));
        }

        return vo;
    }

    /**
     * 获取隐患整改效率统计
     */
    @Override
    public List<RectificationEfficiencyVO> getRectificationEfficiency(String viewType, String startDate, String endDate) {
        try {
            // 计算日期范围
            // 直接使用传入的startDate和endDate参数

            List<Map<String, Object>> efficiencyData = hiddenTroubleStatMapper.getRectificationEfficiencyByDate(startDate, endDate);
            List<RectificationEfficiencyVO> result = efficiencyData.stream().map(map -> {
                RectificationEfficiencyVO vo = new RectificationEfficiencyVO();
                vo.setDepartment((String) map.get("department"));
                vo.setTotalCount(((Number) map.get("total_count")).longValue());
                vo.setCompletedCount(((Number) map.get("completed_count")).longValue());
                vo.setAvgCompletionDays((BigDecimal) map.get("avg_completion_days"));
                vo.setCompletionRate((BigDecimal) map.get("completion_rate"));
                return vo;
            }).collect(Collectors.toList());

            log.debug("获取隐患整改效率统计数据成功，视图类型: {}, 统计日期: {}, 数据条数: {}", viewType, startDate, endDate, result.size());
            return result;
        } catch (Exception e) {

            log.error("方法执行失败", e);

            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);

        }
    }

    /**
     * 获取默认日期范围
     */
    private Map<String, String> getDefaultDateRange(String viewType) {
        Map<String, String> dateRange = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();

        // 结束日期为今天
        String endDate = sdf.format(cal.getTime());

        // 根据周期设置开始日期
        switch (viewType.toLowerCase()) {
            case "daily":
                cal.add(Calendar.DAY_OF_MONTH, -30); // 最近30天
                break;
            case "weekly":
                cal.add(Calendar.WEEK_OF_YEAR, -12); // 最近12周
                break;
            case "monthly":
                cal.add(Calendar.MONTH, -12); // 最近12个月
                break;
            default:
                cal.add(Calendar.DAY_OF_MONTH, -30);
                break;
        }

        String startDate = sdf.format(cal.getTime());

        dateRange.put("startDate", startDate);
        dateRange.put("endDate", endDate);

        return dateRange;
    }

    /**
     * 转换Map到TrendStatisticsVO
     */
    private TrendStatisticsVO mapToTrendStatisticsVO(Map<String, Object> map) {
        TrendStatisticsVO vo = new TrendStatisticsVO();
        vo.setDate((String) map.get("date"));
        String shortDate = (String) map.get("short_date");
        String weekLabel = (String) map.get("week_label");
        if (shortDate == null) {
            shortDate = weekLabel;
        }
        String monthLabel = (String) map.get("month_label");
        if (shortDate == null) {
            shortDate = monthLabel;
        }
        vo.setShortDate(shortDate);
        vo.setTotalCount(map.get("total_count") != null ? ((Number) map.get("total_count")).longValue() : 0L);
        vo.setMajorCount(map.get("major_count") != null ? ((Number) map.get("major_count")).longValue() : 0L);
        vo.setGeneralCount(map.get("general_count") != null ? ((Number) map.get("general_count")).longValue() : 0L);
        vo.setCompletedCount(map.get("completed_count") != null ? ((Number) map.get("completed_count")).longValue() : 0L);
        vo.setOverdueCount(map.get("overdue_count") != null ? ((Number) map.get("overdue_count")).longValue() : 0L);
        return vo;
    }



    /**
     * 获取安全小结统计
     */
    @Override
    public SafetySummaryVO getSafetySummary(String viewType, String startDate, String endDate) {
        try {
            // 计算日期范围
            // 直接使用传入的startDate和endDate参数

            // 获取安全小结统计数据
            Map<String, Object> summaryData = hiddenTroubleStatMapper.getSafetySummaryByDate(startDate, endDate);

            SafetySummaryVO summary = new SafetySummaryVO();
            summary.setTotalCount(summaryData.get("total_count") != null ? ((Number) summaryData.get("total_count")).longValue() : 0L);
            summary.setMajorCount(summaryData.get("major_count") != null ? ((Number) summaryData.get("major_count")).longValue() : 0L);
            summary.setPendingCount(summaryData.get("pending_count") != null ? ((Number) summaryData.get("pending_count")).longValue() : 0L);
            summary.setOverdueCount(summaryData.get("overdue_count") != null ? ((Number) summaryData.get("overdue_count")).longValue() : 0L);
            summary.setCompletedCount(summaryData.get("completed_count") != null ? ((Number) summaryData.get("completed_count")).longValue() : 0L);
            summary.setReviewPendingCount(summaryData.get("review_pending_count") != null ? ((Number) summaryData.get("review_pending_count")).longValue() : 0L);
            summary.setRejectedCount(summaryData.get("rejected_count") != null ? ((Number) summaryData.get("rejected_count")).longValue() : 0L);
            summary.setStartDate(startDate);
            summary.setEndDate(endDate);
            summary.setPeriod(viewType);

            log.debug("获取安全小结统计成功，视图类型: {}, 统计日期: {}, 结果: {}", viewType, startDate, endDate, summary);
            return summary;

        } catch (Exception e) {
            log.error("方法执行失败", e);
            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成安全小结文本描述
     */
    @Override
    public SafetySummaryTextVO generateSafetySummaryText(String viewType, String startDate, String endDate) {
        try {
            // 获取安全小结统计数据
            SafetySummaryVO summary = getSafetySummary(viewType, startDate, endDate);

            // 生成文本描述
            List<String> summaryTexts = new ArrayList<>();

            // 第一条：隐患总数和重大隐患分析
            String text1 = generateHiddenTroubleAnalysisText(summary, viewType);
            summaryTexts.add(text1);

            // 第二条：当前待整改隐患分析
            String text2 = generatePendingAnalysisText(summary);
            summaryTexts.add(text2);

            // 第三条：存在问题和建议
            String text3 = generateIssuesAndSuggestionsText(summary);
            summaryTexts.add(text3);

            // 第四条：已完成整改分析
            String text4 = generateCompletionAnalysisText(summary);
            summaryTexts.add(text4);

            // 构建返回对象
            SafetySummaryTextVO result = new SafetySummaryTextVO();
            result.setTitle("【一】隐患分析");
            result.setSummaryTexts(summaryTexts);
            result.setStartDate(summary.getStartDate());
            result.setEndDate(summary.getEndDate());
            result.setViewType(viewType);
            result.setGenerateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            log.debug("生成安全小结文本成功，视图类型: {}, 统计日期: {}", viewType, startDate, endDate);
            return result;

        } catch (Exception e) {
            log.error("方法执行失败", e);
            throw new RuntimeException("数据查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成隐患总数和重大隐患分析文本
     */
    private String generateHiddenTroubleAnalysisText(SafetySummaryVO summary, String viewType) {
        String periodText = getPeriodText(viewType);
        long majorCount = summary.getMajorCount();
        long totalCount = summary.getTotalCount();

        return String.format("● %s共识别隐患 %d 项，其中重大隐患 %d 项。",
            periodText, totalCount, majorCount);
    }

    /**
     * 生成当前待整改隐患分析文本
     */
    private String generatePendingAnalysisText(SafetySummaryVO summary) {
        long pendingCount = summary.getPendingCount();
        long totalCount = summary.getTotalCount();
        double pendingRate = totalCount > 0 ? (pendingCount * 100.0 / totalCount) : 0;

        return String.format("● 当前待整改隐患 %d 项，占比下降 %.1f%%。",
            pendingCount, pendingRate);
    }

    /**
     * 生成存在问题和建议文本
     */
    private String generateIssuesAndSuggestionsText(SafetySummaryVO summary) {
        long overdueCount = summary.getOverdueCount();

        if (overdueCount > 0) {
            return String.format("● 存在 %d 项隐患已超期未整改，建议加强督促整改。", overdueCount);
        } else {
            return "● 隐患整改进度良好，建议继续保持。";
        }
    }

    /**
     * 生成已完成整改分析文本
     */
    private String generateCompletionAnalysisText(SafetySummaryVO summary) {
        long completedCount = summary.getCompletedCount();
        long totalCount = summary.getTotalCount();
        double completionRate = totalCount > 0 ? (completedCount * 100.0 / totalCount) : 0;

        return String.format("● 已完成整改隐患 %d 项，整改完成率为 %.1f%%。",
            completedCount, completionRate);
    }

    /**
     * 获取时间周期文本
     */
    private String getPeriodText(String viewType) {
        switch (viewType.toLowerCase()) {
            case "daily":
                return "昨日";
            case "weekly":
                return "本周";
            case "monthly":
                return "本月";
            default:
                return "本期";
        }
    }
}
