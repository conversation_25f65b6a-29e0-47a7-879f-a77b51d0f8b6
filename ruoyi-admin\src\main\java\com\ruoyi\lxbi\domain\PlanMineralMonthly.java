package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 选矿整体月计划对象 plan_mineral_monthly
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanMineralMonthly extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 原矿处理量 */
    @Excel(name = "原矿处理量")
    private BigDecimal rawOreProcessingVolume;

    /** 干选量 */
    @Excel(name = "干选量")
    private BigDecimal drySeparationVolume;

    /** 入磨量 */
    @Excel(name = "入磨量")
    private BigDecimal grindingFeedVolume;

    /** 原矿品位-TFe */
    @Excel(name = "原矿品位-TFe", suffix = "%")
    private BigDecimal rawOreGradeTfe;

    /** 精矿品位-TFe */
    @Excel(name = "精矿品位-TFe", suffix = "%")
    private BigDecimal concentrateGrade;

    /** 尾矿品位-TFe */
    @Excel(name = "尾矿品位-TFe", suffix = "%")
    private BigDecimal tailingGradeTfe;

    /** 精矿量 */
    @Excel(name = "精矿量")
    private BigDecimal concentrateVolume;

    /** 计划月份 */
    @Excel(name = "计划月份" , sort = 1, mergeByValue = true)
    private String planDate;

    @Excel(name = "精矿细度(-500目含量)", suffix = "%")
    private BigDecimal concentrateFineness;

    @Excel(name = "铁精粉水分", suffix = "%")
    private BigDecimal ironConcentrateMoisture;

    @Excel(name = "综合选比")
    private BigDecimal comprehensiveRatio;

    @Excel(name = "入磨选比")
    private BigDecimal grindingRatio;

    @Excel(name = "尾矿搅拌槽-尾矿粒级(-200目)", suffix = "%")
    private BigDecimal tailingsAgitatorTank;

    @Excel(name = "尾矿脱水筛上量")
    private BigDecimal overflowOfTailings;

    @Excel(name = "原矿品位-mFe", suffix = "%")
    private BigDecimal rawOreGradeMfe;

    @Excel(name = "尾矿品位-mFe", suffix = "%")
    private BigDecimal tailingGradeMfe;

}
