package com.ruoyi.lxbi.service.impl;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.PlanDthMonthlyMapper;
import com.ruoyi.lxbi.domain.PlanDthMonthly;
import com.ruoyi.lxbi.domain.request.PlanDthMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanDthMonthlyVo;
import com.ruoyi.lxbi.service.IPlanDthMonthlyService;

/**
 * 潜孔月计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class PlanDthMonthlyServiceImpl implements IPlanDthMonthlyService 
{
    @Autowired
    private PlanDthMonthlyMapper planDthMonthlyMapper;

    /**
     * 查询潜孔月计划
     * 
     * @param id 潜孔月计划主键
     * @return 潜孔月计划
     */
    @Override
    public PlanDthMonthly selectPlanDthMonthlyById(Long id)
    {
        return planDthMonthlyMapper.selectPlanDthMonthlyById(id);
    }

    /**
     * 查询潜孔月计划列表
     *
     * @param planDthMonthly 潜孔月计划
     * @return 潜孔月计划
     */
    @Override
    public List<PlanDthMonthlyVo> selectPlanDthMonthlyList(PlanDthMonthly planDthMonthly)
    {
        return planDthMonthlyMapper.selectPlanDthMonthlyList(planDthMonthly);
    }

    /**
     * 新增潜孔月计划
     * 
     * @param planDthMonthly 潜孔月计划
     * @return 结果
     */
    @Override
    public int insertPlanDthMonthly(PlanDthMonthly planDthMonthly)
    {
        planDthMonthly.setCreateTime(DateUtils.getNowDate());
        return planDthMonthlyMapper.insertPlanDthMonthly(planDthMonthly);
    }

    /**
     * 修改潜孔月计划
     * 
     * @param planDthMonthly 潜孔月计划
     * @return 结果
     */
    @Override
    public int updatePlanDthMonthly(PlanDthMonthly planDthMonthly)
    {
        planDthMonthly.setUpdateTime(DateUtils.getNowDate());
        return planDthMonthlyMapper.updatePlanDthMonthly(planDthMonthly);
    }

    /**
     * 批量删除潜孔月计划
     * 
     * @param ids 需要删除的潜孔月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanDthMonthlyByIds(Long[] ids)
    {
        return planDthMonthlyMapper.deletePlanDthMonthlyByIds(ids);
    }

    /**
     * 删除潜孔月计划信息
     *
     * @param id 潜孔月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanDthMonthlyById(Long id)
    {
        return planDthMonthlyMapper.deletePlanDthMonthlyById(id);
    }

    /**
     * 批量保存潜孔月计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePlanDthMonthly(List<PlanDthMonthlyBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个月份的数据
        String planMonth = batchDataList.get(0).getPlanMonth();
        if (StringUtils.isBlank(planMonth)) {
            throw new ServiceException("计划月份不能为空");
        }
        // 验证是否同一个日期和项目部的数据
        Long projectDepartmentId = batchDataList.get(0).getProjectDepartmentId();
        if (projectDepartmentId == null) {
            throw new ServiceException("项目部门不能为空");
        }
        boolean allSameDateAndProject = batchDataList.stream()
                .allMatch(data -> planMonth.equals(data.getPlanMonth())
                        && projectDepartmentId.equals(data.getProjectDepartmentId()));
        if (!allSameDateAndProject) {
            throw new ServiceException("批量数据必须是同一个作业日期和项目部门");
        }

        // 查询该月份的现有数据
        PlanDthMonthly queryParam = new PlanDthMonthly();
        queryParam.setPlanDate(planMonth);
        queryParam.setProjectDepartmentId(projectDepartmentId);
        List<PlanDthMonthlyVo> existingDataList = planDthMonthlyMapper.selectPlanDthMonthlyList(queryParam);
        Map<Long, PlanDthMonthly> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(PlanDthMonthly::getId, data -> data));

        List<PlanDthMonthly> toInsert = new ArrayList<>();
        List<PlanDthMonthly> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (PlanDthMonthlyBatchDto batchData : batchDataList) {
            if ("add".equals(batchData.getOperationType())) {
                // 新增数据
                PlanDthMonthly newData = new PlanDthMonthly();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if ("edit".equals(batchData.getOperationType()) && batchData.getId() != null) {
                // 更新现有数据
                if (existingDataMap.containsKey(batchData.getId())) {
                    PlanDthMonthly updateData = new PlanDthMonthly();
                    copyProperties(batchData, updateData);
                    updateData.setId(batchData.getId());
                    updateData.setUpdateBy(SecurityUtils.getUsername());
                    updateData.setUpdateTime(DateUtils.getNowDate());
                    toUpdate.add(updateData);
                    toDelete.remove(batchData.getId());
                }
            }
        }

        int totalProcessed = 0;

        // 执行批量操作
        if (!toInsert.isEmpty()) {
            for (PlanDthMonthly data : toInsert) {
                totalProcessed += planDthMonthlyMapper.insertPlanDthMonthly(data);
            }
        }

        if (!toUpdate.isEmpty()) {
            for (PlanDthMonthly data : toUpdate) {
                totalProcessed += planDthMonthlyMapper.updatePlanDthMonthly(data);
            }
        }

        if (!toDelete.isEmpty()) {
            totalProcessed += planDthMonthlyMapper.deletePlanDthMonthlyByIds(toDelete.toArray(new Long[0]));
        }

        return totalProcessed;
    }

    /**
     * 复制属性
     */
    private void copyProperties(PlanDthMonthlyBatchDto source, PlanDthMonthly target) {

    }
}
