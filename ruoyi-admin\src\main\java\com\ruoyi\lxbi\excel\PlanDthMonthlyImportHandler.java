package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.domain.BaseWorkingFace;
import com.ruoyi.lxbi.domain.PlanDthMonthly;
import com.ruoyi.lxbi.domain.excel.PlanDthMonthlyImport;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;
import com.ruoyi.lxbi.service.IBaseStopeService;
import com.ruoyi.lxbi.service.IBaseWorkingFaceService;
import com.ruoyi.lxbi.service.IPlanDthMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 潜孔月计划导入处理器
 *
 * <AUTHOR>
 */
@Component
public class PlanDthMonthlyImportHandler extends ExcelImportHandler<PlanDthMonthlyImport> {

    @Autowired
    private IBaseProjectDepartmentService baseProjectDepartmentService;

    @Autowired
    private IBaseWorkingFaceService baseWorkingFaceService;

    @Autowired
    private IBaseStopeService baseStopeService;

    @Autowired
    private IPlanDthMonthlyService planDthMonthlyService;

    @Override
    protected Class<PlanDthMonthlyImport> getEntityClass() {
        return PlanDthMonthlyImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置项目部门选项
        List<ExcelOptionInfo> projectDepartments = new ArrayList<>();
        BaseProjectDepartment deptQueryParam = new BaseProjectDepartment();
        List<BaseProjectDepartment> deptList = baseProjectDepartmentService.selectBaseProjectDepartmentList(deptQueryParam);

        for (BaseProjectDepartment dept : deptList) {
            projectDepartments.add(new ExcelOptionInfo(
                    dept.getProjectDepartmentId(),
                    dept.getProjectDepartmentName()
            ));
        }
        context.setOptions("projectDepartment", projectDepartments);

        // 设置工作面选项
        List<ExcelOptionInfo> workingFaces = new ArrayList<>();
        BaseWorkingFace faceQueryParam = new BaseWorkingFace();
        List<BaseWorkingFace> faceList = baseWorkingFaceService.selectBaseWorkingFaceList(faceQueryParam);

        for (BaseWorkingFace face : faceList) {
            workingFaces.add(new ExcelOptionInfo(
                    face.getWorkingFaceId(),
                    face.getWorkingFaceName()
            ));
        }
        context.setOptions("workingFace", workingFaces);

        // 设置采场选项
        List<ExcelOptionInfo> stopes = new ArrayList<>();
        BaseStope stopeQueryParam = new BaseStope();
        List<BaseStope> stopeList = baseStopeService.selectBaseStopeListAll(stopeQueryParam);

        for (BaseStope stope : stopeList) {
            stopes.add(new ExcelOptionInfo(
                    stope.getStopeId(),
                    stope.getStopeName()
            ));
        }
        context.setOptions("stope", stopes);
    }

    @Override
    protected void validateData(ExcelDataInfo<PlanDthMonthlyImport> dataInfo, ExcelImportContext context) {
        PlanDthMonthlyImport data = dataInfo.getData();

        // 验证计划月份格式
        if (data.getPlanDate() != null) {
            Pattern pattern = Pattern.compile("^\\d{4}\\d{2}$");
            if (!pattern.matcher(data.getPlanDate()).matches()) {
                dataInfo.addError("planDate", "计划月份格式不正确，应为yyyyMM格式");
            }
        }

        // 验证潜孔钻机米数
        if (data.getDthMeter() != null && data.getDthMeter().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("dthMeter", "潜孔钻机米数不能为负数");
        }
    }

    @Override
    protected void saveData(PlanDthMonthlyImport data, ExcelImportContext context) {
        // 转换为实体对象
        PlanDthMonthly entity = new PlanDthMonthly();
        entity.setProjectDepartmentId(data.getProjectDepartmentId());
        entity.setWorkingFaceId(data.getWorkingFaceId());
        entity.setStopeId(data.getStopeId());
        entity.setDthMeter(data.getDthMeter());
        entity.setPlanDate(data.getPlanDate());
        
        // 保存到数据库
        planDthMonthlyService.insertPlanDthMonthly(entity);
    }

    @Override
    public List<PlanDthMonthlyImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("潜孔月计划验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("潜孔月计划导入完成，总行数: " + ctx.getTotalRows());
    }
}
