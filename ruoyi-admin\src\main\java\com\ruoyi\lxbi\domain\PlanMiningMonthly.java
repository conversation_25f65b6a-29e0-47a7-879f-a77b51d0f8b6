package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 采矿整体月计划对象 plan_mining_monthly
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanMiningMonthly extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 项目ID */
    private Long projectDepartmentId;

    /** 掘进米数 */
    @Excel(name = "掘进米数")
    private BigDecimal driftMeter;

    /** 原矿量 */
    @Excel(name = "原矿量")
    private BigDecimal rawOreVolume;

    /** 支护米数 */
    @Excel(name = "支护米数")
    private BigDecimal supportMeter;

    /** 充填量 */
    @Excel(name = "充填量")
    private BigDecimal fillingVolume;

    /** 潜孔米数 */
    @Excel(name = "潜孔米数")
    private BigDecimal dthMeter;

    /** 中深孔米数 */
    @Excel(name = "中深孔米数")
    private BigDecimal deepHoleMeter;

    /** 出矿量 */
    @Excel(name = "出矿量")
    private BigDecimal oreOutputVolume;

    /** 计划月份 */
    @Excel(name = "计划月份", mergeByValue = true, sort = 1)
    private String planDate;

}
