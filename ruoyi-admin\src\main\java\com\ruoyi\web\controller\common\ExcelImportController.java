package com.ruoyi.web.controller.common;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportResult;
import com.ruoyi.common.core.domain.excel.ExcelTemplateInfo;
import com.ruoyi.common.core.excel.ExcelImportService;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Excel导入统一控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common/excel")
public class ExcelImportController extends BaseController {

    @Autowired
    private ExcelImportService excelImportService;

    /**
     * 获取所有模板信息
     */
    @GetMapping("/templates")
    public R<Map<String, ExcelTemplateInfo>> getTemplates() {
        Map<String, ExcelTemplateInfo> templates = excelImportService.getAllTemplates();
        return R.ok(templates);
    }

    /**
     * 获取指定模板信息
     */
    @GetMapping("/template/{key}")
    public R<ExcelTemplateInfo> getTemplate(@PathVariable String key) {
        if (StringUtils.isEmpty(key)) {
            return R.fail("模板key不能为空");
        }

        ExcelTemplateInfo templateInfo = excelImportService.getTemplateInfo(key);
        if (templateInfo == null) {
            return R.fail("未找到对应的模板：" + key);
        }

        return R.ok(templateInfo);
    }

    /**
     * 下载Excel模板
     */
    @Log(title = "Excel模板下载", businessType = BusinessType.EXPORT)
    @GetMapping("/template/{key}/download")
    public void downloadTemplate(@PathVariable String key, HttpServletResponse response) {
        if (StringUtils.isEmpty(key)) {
            throw new RuntimeException("模板key不能为空");
        }

        if (!excelImportService.hasHandler(key)) {
            throw new RuntimeException("未找到对应的导入处理器：" + key);
        }

        excelImportService.downloadTemplate(key, response);
    }

    /**
     * 验证Excel数据
     */
    @Log(title = "Excel数据验证", businessType = BusinessType.OTHER)
    @PostMapping("/validate/{key}")
    public R<ExcelImportResult<?>> validateExcel(@PathVariable String key, @RequestParam("file") MultipartFile file) {
        if (StringUtils.isEmpty(key)) {
            return R.fail("模板key不能为空");
        }

        if (file == null || file.isEmpty()) {
            return R.fail("请选择要上传的文件");
        }

        // 检查文件类型
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName) || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            return R.fail("请上传Excel文件（.xlsx或.xls格式）");
        }

        try {
            ExcelImportResult<?> result = excelImportService.validateExcel(key, file);
            return R.ok(result);
        } catch (Exception e) {
            logger.error("验证Excel数据失败", e);
            return R.fail("验证Excel数据失败：" + e.getMessage());
        }
    }

    /**
     * 验证单行数据
     */
    @Log(title = "Excel单行数据验证", businessType = BusinessType.OTHER)
    @PostMapping("/validate/{key}/row")
    public R<ExcelDataInfo<?>> validateRow(@PathVariable String key, @RequestBody ExcelDataInfo<?> rowData) {
        if (StringUtils.isEmpty(key)) {
            return R.fail("模板key不能为空");
        }

        if (rowData == null || rowData.getData() == null) {
            return R.fail("行数据不能为空");
        }

        try {
            ExcelDataInfo<?> result = excelImportService.validateRow(key, rowData);
            return R.ok(result);
        } catch (Exception e) {
            logger.error("验证单行数据失败", e);
            return R.fail("验证单行数据失败：" + e.getMessage());
        }
    }

    /**
     * 导入Excel数据（通过缓存ID）
     */
    @Log(title = "Excel数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import/{key}/{cacheId}")
    public <T> R<ExcelImportResult<T>> importExcel(@PathVariable String key, @PathVariable String cacheId, @RequestBody List<ExcelDataInfo<T>> dataList) {
        if (StringUtils.isEmpty(key)) {
            return R.fail("模板key不能为空");
        }

        if (StringUtils.isEmpty(cacheId)) {
            return R.fail("缓存ID不能为空");
        }

        try {
            ExcelImportResult<T> result = excelImportService.importExcel(key, cacheId, dataList);
            return R.ok(result);
        } catch (Exception e) {
            logger.error("导入Excel数据失败", e);
            return R.fail("导入Excel数据失败：" + e.getMessage());
        }
    }

    /**
     * 导入Excel文件（一步完成验证和导入）
     */
    @Log(title = "Excel文件导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import/{key}/file")
    public R<ExcelImportResult<?>> importExcelFile(@PathVariable String key, @RequestParam("file") MultipartFile file) {
        if (StringUtils.isEmpty(key)) {
            return R.fail("模板key不能为空");
        }

        if (file == null || file.isEmpty()) {
            return R.fail("请选择要上传的文件");
        }

        try {
            ExcelImportResult<?> result = excelImportService.importExcelFile(key, file);
            return R.ok(result);
        } catch (Exception e) {
            logger.error("导入Excel文件失败", e);
            return R.fail("导入Excel文件失败：" + e.getMessage());
        }
    }

    /**
     * 下载失败数据
     */
    @Log(title = "Excel失败数据下载", businessType = BusinessType.EXPORT)
    @GetMapping("/failures/{key}/{cacheId}")
    public void downloadFailures(@PathVariable String key, @PathVariable String cacheId, HttpServletResponse response) {
        if (StringUtils.isEmpty(key)) {
            throw new RuntimeException("模板key不能为空");
        }

        if (StringUtils.isEmpty(cacheId)) {
            throw new RuntimeException("缓存ID不能为空");
        }

        excelImportService.downloadFailures(key, cacheId, response);
    }
}
