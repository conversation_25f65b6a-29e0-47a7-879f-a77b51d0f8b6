package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 矿井提升数据班次统计对象 - 统一日/周/月班次统计
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class DataMineHoistingPeriodStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 周开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekStartDate;

    /**
     * 周结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekEndDate;

    /**
     * 班次ID
     */
    @Excel(name = "班次ID")
    private Long workingPeriodId;

    /**
     * 班次名称
     */
    @Excel(name = "班次名称")
    private String workingPeriodName;

    /**
     * 总操作时间 (用于周/月统计)
     */
    @Excel(name = "总操作时间")
    private Double totalOperationTime;

    /**
     * 总故障时间 (用于周/月统计)
     */
    @Excel(name = "总故障时间")
    private Double totalFaultTime;

    /**
     * 总提升斗数 (用于周/月统计)
     */
    @Excel(name = "总提升斗数")
    private Long totalBuckets;

    /**
     * 总提升重量 (用于周/月统计)
     */
    @Excel(name = "总提升重量")
    private Double totalWeight;
}
