package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.ArrayList;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.DataEquipmentMapper;
import com.ruoyi.lxbi.domain.DataEquipment;
import com.ruoyi.lxbi.domain.request.DataEquipmentBatchDto;
import com.ruoyi.lxbi.domain.response.DataEquipmentEfficiencyStats;
import com.ruoyi.lxbi.domain.response.DataEquipmentUtilizationStats;
import com.ruoyi.lxbi.service.IDataEquipmentService;

import java.util.Date;

/**
 * 设备数据管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class DataEquipmentServiceImpl implements IDataEquipmentService 
{
    @Autowired
    private DataEquipmentMapper dataEquipmentMapper;

    /**
     * 查询设备数据管理
     * 
     * @param id 设备数据管理主键
     * @return 设备数据管理
     */
    @Override
    public DataEquipment selectDataEquipmentById(Long id)
    {
        return dataEquipmentMapper.selectDataEquipmentById(id);
    }

    /**
     * 查询设备数据管理列表
     * 
     * @param dataEquipment 设备数据管理
     * @return 设备数据管理
     */
    @Override
    public List<DataEquipment> selectDataEquipmentList(DataEquipment dataEquipment)
    {
        return dataEquipmentMapper.selectDataEquipmentList(dataEquipment);
    }

    /**
     * 新增设备数据管理
     * 
     * @param dataEquipment 设备数据管理
     * @return 结果
     */
    @Override
    public int insertDataEquipment(DataEquipment dataEquipment)
    {
        dataEquipment.setCreateTime(DateUtils.getNowDate());
        return dataEquipmentMapper.insertDataEquipment(dataEquipment);
    }

    /**
     * 修改设备数据管理
     * 
     * @param dataEquipment 设备数据管理
     * @return 结果
     */
    @Override
    public int updateDataEquipment(DataEquipment dataEquipment)
    {
        dataEquipment.setUpdateTime(DateUtils.getNowDate());
        return dataEquipmentMapper.updateDataEquipment(dataEquipment);
    }

    /**
     * 批量删除设备数据管理
     * 
     * @param ids 需要删除的设备数据管理主键
     * @return 结果
     */
    @Override
    public int deleteDataEquipmentByIds(Long[] ids)
    {
        return dataEquipmentMapper.deleteDataEquipmentByIds(ids);
    }

    /**
     * 删除设备数据管理信息
     *
     * @param id 设备数据管理主键
     * @return 结果
     */
    @Override
    public int deleteDataEquipmentById(Long id)
    {
        return dataEquipmentMapper.deleteDataEquipmentById(id);
    }

    /**
     * 批量保存设备数据
     *
     * @param dataList 设备数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDataEquipment(List<DataEquipmentBatchDto> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            throw new RuntimeException("批量数据不能为空");
        }

        List<DataEquipment> insertList = new ArrayList<>();
        List<DataEquipment> updateList = new ArrayList<>();

        for (DataEquipmentBatchDto dto : dataList) {
            DataEquipment entity = new DataEquipment();
            BeanUtils.copyProperties(dto, entity);

            if (dto.getIsNew() != null && dto.getIsNew()) {
                // 新增数据
                entity.setCreateBy(SecurityUtils.getUsername());
                entity.setCreateTime(DateUtils.getNowDate());
                entity.setUpdateBy(SecurityUtils.getUsername());
                entity.setUpdateTime(DateUtils.getNowDate());
                insertList.add(entity);
            } else {
                // 更新数据
                entity.setUpdateBy(SecurityUtils.getUsername());
                entity.setUpdateTime(DateUtils.getNowDate());
                updateList.add(entity);
            }
        }

        int totalAffected = 0;

        // 批量插入
        if (!insertList.isEmpty()) {
            totalAffected += dataEquipmentMapper.batchInsertDataEquipment(insertList);
        }

        // 批量更新
        if (!updateList.isEmpty()) {
            for (DataEquipment entity : updateList) {
                totalAffected += dataEquipmentMapper.updateDataEquipment(entity);
            }
        }

        return totalAffected;
    }

    /**
     * 查询设备台效统计数据
     *
     * @param equipmentType 设备类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param timeType 时间聚合类型（week/month/year）
     * @return 台效统计数据
     */
    @Override
    public List<DataEquipmentEfficiencyStats> getEfficiencyStats(Long equipmentType, Date startTime, Date endTime, String timeType) {
        return dataEquipmentMapper.getEfficiencyStats(equipmentType, startTime, endTime, timeType);
    }

    /**
     * 查询设备作业率统计数据
     *
     * @param equipmentType 设备类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param timeType 时间聚合类型（week/month/year）
     * @return 作业率统计数据
     */
    @Override
    public List<DataEquipmentUtilizationStats> getUtilizationStats(Long equipmentType, Date startTime, Date endTime, String timeType) {
        return dataEquipmentMapper.getUtilizationStats(equipmentType, startTime, endTime, timeType);
    }
}
