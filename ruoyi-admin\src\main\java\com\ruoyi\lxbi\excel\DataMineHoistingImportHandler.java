package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.BaseWorkingPeriod;
import com.ruoyi.lxbi.domain.DataMineHoisting;
import com.ruoyi.lxbi.domain.excel.DataMineHoistingImport;
import com.ruoyi.lxbi.service.IBaseWorkingPeriodService;
import com.ruoyi.lxbi.service.IDataMineHoistingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 矿井提升数据导入处理器
 *
 * <AUTHOR>
 */
@Component
public class DataMineHoistingImportHandler extends ExcelImportHandler<DataMineHoistingImport> {

    @Autowired
    private IBaseWorkingPeriodService baseWorkingPeriodService;

    @Autowired
    private IDataMineHoistingService dataMineHoistingService;

    @Override
    protected Class<DataMineHoistingImport> getEntityClass() {
        return DataMineHoistingImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置作业时段选项
        List<ExcelOptionInfo> workingPeriods = new ArrayList<>();
        BaseWorkingPeriod queryParam = new BaseWorkingPeriod();
        queryParam.setStatus(1L); // 只查询启用状态的作业时段
        List<BaseWorkingPeriod> periodList = baseWorkingPeriodService.selectBaseWorkingPeriodList(queryParam);

        for (BaseWorkingPeriod period : periodList) {
            workingPeriods.add(new ExcelOptionInfo(
                    period.getWorkingPeriodId(),
                    period.getWorkingPeriodName()
            ));
        }
        context.setOptions("workingPeriod", workingPeriods);
    }

    @Override
    protected void validateData(ExcelDataInfo<DataMineHoistingImport> dataInfo, ExcelImportContext context) {
        DataMineHoistingImport data = dataInfo.getData();

        // 验证作业日期不能为未来日期
        if (data.getOperationDate() != null && data.getOperationDate().after(new Date())) {
            dataInfo.addError("operationDate", "作业日期不能是未来日期");
        }

        // 验证运行时间
        if (data.getOperationTime() != null) {
            if (data.getOperationTime() < 0 || data.getOperationTime() > 1440) {
                dataInfo.addError("operationTime", "运行时间必须在0-1440分钟之间");
            }
        }

        // 验证故障时长
        if (data.getFaultTime() != null) {
            if (data.getFaultTime() < 0 || data.getFaultTime() > 1440) {
                dataInfo.addError("faultTime", "故障时长必须在0-1440分钟之间");
            }
        }

        // 验证提升斗数
        if (data.getBuckets() != null && data.getBuckets() < 0) {
            dataInfo.addError("buckets", "提升斗数不能为负数");
        }

        if (data.getWeight() != null && data.getWeight() < 0) {
            dataInfo.addError("weight", "提升量不能为负数");
        }

        if (data.getFaultStartTime() != null && data.getFaultEndTime() == null) {
            dataInfo.addError("faultEndTime", "填写故障开始时间时，故障结束时间不能为空");
        }
        if (data.getFaultStartTime() == null && data.getFaultEndTime() != null) {
            dataInfo.addError("faultStartTime", "填写故障结束时间时，故障开始时间不能为空");
        }

        // 验证故障时间逻辑
        if (data.getFaultStartTime() != null && data.getFaultEndTime() != null) {
            // 如果填写了故障时间，故障时长应该大于0
            if (data.getFaultTime() == null || data.getFaultTime() <= 0) {
                dataInfo.addError("faultTime", "填写故障时间时，故障时长必须大于0");
            }
            if (data.getFaultEndTime().isBefore(data.getFaultStartTime())) {
                dataInfo.addError("faultEndTime", "故障结束时间必须大于故障开始时间");
            }
            if (!data.getFaultStartTime().plusMinutes(data.getFaultTime()).equals(data.getFaultEndTime())) {
                dataInfo.addError("faultTime", "故障时长与故障开始时间、结束时间不匹配");
            }
        }

    }

    @Override
    protected void saveData(DataMineHoistingImport data, ExcelImportContext context) {
        // 转换为实体对象
        DataMineHoisting entity = new DataMineHoisting();
        entity.setWorkingPeriodId(data.getWorkingPeriodId());
        entity.setOperationDate(data.getOperationDate());
        entity.setOperationTime(data.getOperationTime());
        entity.setFaultTime(data.getFaultTime());
        entity.setBuckets(data.getBuckets());
        entity.setWeight(data.getWeight());
        entity.setFaultReason(data.getFaultReason());
        entity.setFaultStartTime(data.getFaultStartTime());
        entity.setFaultEndTime(data.getFaultEndTime());
        
        // 保存到数据库
        dataMineHoistingService.insertDataMineHoisting(entity);
    }

    @Override
    public List<DataMineHoistingImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("矿井提升数据验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("矿井提升数据导入完成，总行数: " + ctx.getTotalRows());
    }
}
