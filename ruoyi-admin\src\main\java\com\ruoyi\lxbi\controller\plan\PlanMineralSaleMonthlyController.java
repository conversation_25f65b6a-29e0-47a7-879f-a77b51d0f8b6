package com.ruoyi.lxbi.controller.plan;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.PlanMineralSaleMonthly;
import com.ruoyi.lxbi.domain.request.PlanMineralSaleMonthlyBatchDto;
import com.ruoyi.lxbi.service.IPlanMineralSaleMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 选矿销售月计划Controller
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@RestController
@RequestMapping("/plan/planMineralSaleMonthly")
public class PlanMineralSaleMonthlyController extends BaseController {
    @Autowired
    private IPlanMineralSaleMonthlyService planMineralSaleMonthlyService;

    /**
     * 查询选矿销售月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralSaleMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanMineralSaleMonthly planMineralSaleMonthly) {
        startPage();
        List<PlanMineralSaleMonthly> list = planMineralSaleMonthlyService.selectPlanMineralSaleMonthlyList(planMineralSaleMonthly);
        return getDataTable(list);
    }

    /**
     * 导出选矿销售月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralSaleMonthly:export')")
    @Log(title = "选矿销售月计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanMineralSaleMonthly planMineralSaleMonthly) {
        List<PlanMineralSaleMonthly> list = planMineralSaleMonthlyService.selectPlanMineralSaleMonthlyList(planMineralSaleMonthly);
        ExcelUtil<PlanMineralSaleMonthly> util = new ExcelUtil<PlanMineralSaleMonthly>(PlanMineralSaleMonthly.class);
        util.exportExcel(response, list, "选矿销售月计划数据");
    }

    /**
     * 获取选矿销售月计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralSaleMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planMineralSaleMonthlyService.selectPlanMineralSaleMonthlyById(id));
    }

    /**
     * 新增选矿销售月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralSaleMonthly:add')")
    @Log(title = "选矿销售月计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanMineralSaleMonthly planMineralSaleMonthly)
    {
        return toAjax(planMineralSaleMonthlyService.insertPlanMineralSaleMonthly(planMineralSaleMonthly));
    }

    /**
     * 修改选矿销售月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralSaleMonthly:edit')")
    @Log(title = "选矿销售月计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanMineralSaleMonthly planMineralSaleMonthly)
    {
        return toAjax(planMineralSaleMonthlyService.updatePlanMineralSaleMonthly(planMineralSaleMonthly));
    }

    /**
     * 删除选矿销售月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralSaleMonthly:remove')")
    @Log(title = "选矿销售月计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(planMineralSaleMonthlyService.deletePlanMineralSaleMonthlyByIds(ids));
    }

    /**
     * 批量保存选矿销售月计划（增删改查）
     * 传入批量列表，验证是否同一个月份的数据，然后查询这个月份的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('plan:planMineralSaleMonthly:edit')")
    @Log(title = "选矿销售月计划批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<PlanMineralSaleMonthlyBatchDto> batchDataList)
    {
        try {
            int result = planMineralSaleMonthlyService.batchSavePlanMineralSaleMonthly(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
