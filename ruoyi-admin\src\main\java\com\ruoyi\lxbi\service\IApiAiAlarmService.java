package com.ruoyi.lxbi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.lxbi.domain.ApiAiAlarm;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * AI报警数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface IApiAiAlarmService extends IService<ApiAiAlarm> {

    /**
     * 同步AI报警数据（手动同步）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    Map<String, Object> syncAiAlarmData(String startDate, String endDate);

    /**
     * 同步前一天的AI报警数据（定时任务使用）
     * 
     * @return 同步结果
     */
    Map<String, Object> syncPreviousDayData();

    /**
     * 根据外部ID查询报警数据
     * 
     * @param externalId 外部ID
     * @return 报警数据
     */
    ApiAiAlarm getByExternalId(String externalId);

    /**
     * 根据时间范围查询报警数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警数据列表
     */
    List<ApiAiAlarm> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据报警类型查询数据
     * 
     * @param alarmType 报警类型
     * @return 报警数据列表
     */
    List<ApiAiAlarm> getByAlarmType(String alarmType);

    /**
     * 根据设备ID查询数据
     * 
     * @param deviceId 设备ID
     * @return 报警数据列表
     */
    List<ApiAiAlarm> getByDeviceId(String deviceId);

    /**
     * 批量保存报警数据
     * 
     * @param alarmList 报警数据列表
     * @param syncSource 同步来源
     * @return 保存结果
     */
    Map<String, Object> batchSaveAlarms(List<Map<String, Object>> alarmList, String syncSource);

    /**
     * 统计指定时间范围内的报警数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警数量
     */
    Long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 清理过期数据
     * 
     * @param retentionDays 保留天数
     * @return 清理结果
     */
    Map<String, Object> cleanExpiredData(int retentionDays);

    /**
     * 获取同步状态统计
     * 
     * @return 同步状态统计
     */
    Map<String, Object> getSyncStatusStatistics();

    /**
     * 获取最新的同步时间
     * 
     * @return 最新同步时间
     */
    LocalDateTime getLatestSyncTime();

    /**
     * 重新同步失败的数据
     * 
     * @return 重新同步结果
     */
    Map<String, Object> resyncFailedData();
}
