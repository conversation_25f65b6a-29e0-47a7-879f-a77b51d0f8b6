-- 人员超时定位数据表 (PostgreSQL)
CREATE TABLE kafka_people_position_time_over (
    id BIGSERIAL PRIMARY KEY,
    mine_code VARCHAR(50),
    mine_name VA<PERSON>HAR(100),
    data_upload_time TIMESTAMP,
    person_card_code VA<PERSON><PERSON>R(50) NOT NULL,
    person_name VA<PERSON>HAR(50),
    enter_well_time TIMESTAMP,
    alarm_start_time TIMESTAMP,
    alarm_end_time TIMESTAMP,
    area_code VARCHAR(50),
    enter_current_area_time TIMESTAMP,
    base_station_code VARCHAR(50),
    enter_current_base_station_time TIMESTAMP,
    status BIGINT DEFAULT 1,
    is_deleted BIGINT DEFAULT 0,
    create_by VARCHAR(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500) DEFAULT NULL
);

-- 添加字段注释 (PostgreSQL语法)
COMMENT ON TABLE kafka_people_position_time_over IS '人员超时定位数据表';
COMMENT ON COLUMN kafka_people_position_time_over.id IS '主键ID';
COMMENT ON COLUMN kafka_people_position_time_over.mine_code IS '煤矿编码';
COMMENT ON COLUMN kafka_people_position_time_over.mine_name IS '矿井名称';
COMMENT ON COLUMN kafka_people_position_time_over.data_upload_time IS '数据上传时间';
COMMENT ON COLUMN kafka_people_position_time_over.person_card_code IS '人员卡编码';
COMMENT ON COLUMN kafka_people_position_time_over.person_name IS '姓名';
COMMENT ON COLUMN kafka_people_position_time_over.enter_well_time IS '入井时刻';
COMMENT ON COLUMN kafka_people_position_time_over.alarm_start_time IS '报警开始时间';
COMMENT ON COLUMN kafka_people_position_time_over.alarm_end_time IS '报警结束时间';
COMMENT ON COLUMN kafka_people_position_time_over.area_code IS '区域编码';
COMMENT ON COLUMN kafka_people_position_time_over.enter_current_area_time IS '进入当前所处区域时间';
COMMENT ON COLUMN kafka_people_position_time_over.base_station_code IS '基站编码';
COMMENT ON COLUMN kafka_people_position_time_over.enter_current_base_station_time IS '进入当前所处基站时刻';
COMMENT ON COLUMN kafka_people_position_time_over.status IS '状态(1:正常 0:异常)';
COMMENT ON COLUMN kafka_people_position_time_over.is_deleted IS '是否删除(0:未删除 1:已删除)';
COMMENT ON COLUMN kafka_people_position_time_over.create_by IS '创建者';
COMMENT ON COLUMN kafka_people_position_time_over.create_time IS '创建时间';
COMMENT ON COLUMN kafka_people_position_time_over.update_by IS '更新者';
COMMENT ON COLUMN kafka_people_position_time_over.update_time IS '更新时间';
COMMENT ON COLUMN kafka_people_position_time_over.remark IS '备注';

-- 创建索引 (PostgreSQL)
CREATE INDEX idx_kppto_person_card_code ON kafka_people_position_time_over(person_card_code);
CREATE INDEX idx_kppto_alarm_start_time ON kafka_people_position_time_over(alarm_start_time);
CREATE INDEX idx_kppto_data_upload_time ON kafka_people_position_time_over(data_upload_time);
CREATE INDEX idx_kppto_area_code ON kafka_people_position_time_over(area_code);
CREATE INDEX idx_kppto_person_name ON kafka_people_position_time_over(person_name);
CREATE INDEX idx_kppto_status_deleted ON kafka_people_position_time_over(status, is_deleted);

-- 创建复合索引用于唯一性检查 (PostgreSQL部分索引)
CREATE UNIQUE INDEX idx_kppto_person_alarm_unique
ON kafka_people_position_time_over(person_card_code, alarm_start_time)
WHERE is_deleted = 0;
