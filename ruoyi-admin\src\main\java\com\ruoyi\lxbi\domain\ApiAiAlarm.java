package com.ruoyi.lxbi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI报警数据对象 api_ai_alarm
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("api_ai_alarm")
public class ApiAiAlarm {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 第三方系统的原始ID */
    @TableField("external_id")
    private String externalId;

    /** 报警类型 */
    @TableField("alarm_type")
    private String alarmType;

    /** 报警等级 */
    @TableField("alarm_level")
    private String alarmLevel;

    /** 报警状态 */
    @TableField("alarm_status")
    private String alarmStatus;

    /** 报警标题 */
    @TableField("alarm_title")
    private String alarmTitle;

    /** 报警描述 */
    @TableField("alarm_description")
    private String alarmDescription;

    /** 报警时间 */
    @TableField("alarm_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime alarmTime;

    /** 创建时间 */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新时间 */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 位置名称 */
    @TableField("location_name")
    private String locationName;

    /** 位置编码 */
    @TableField("location_code")
    private String locationCode;

    /** X坐标 */
    @TableField("coordinate_x")
    private BigDecimal coordinateX;

    /** Y坐标 */
    @TableField("coordinate_y")
    private BigDecimal coordinateY;

    /** Z坐标 */
    @TableField("coordinate_z")
    private BigDecimal coordinateZ;

    /** 设备ID */
    @TableField("device_id")
    private String deviceId;

    /** 设备名称 */
    @TableField("device_name")
    private String deviceName;

    /** 设备类型 */
    @TableField("device_type")
    private String deviceType;

    /** 报警数值 */
    @TableField("alarm_value")
    private BigDecimal alarmValue;

    /** 阈值 */
    @TableField("threshold_value")
    private BigDecimal thresholdValue;

    /** 单位 */
    @TableField("unit")
    private String unit;

    /** 处理人 */
    @TableField("handler_name")
    private String handlerName;

    /** 处理时间 */
    @TableField("handle_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handleTime;

    /** 处理状态 */
    @TableField("handle_status")
    private String handleStatus;

    /** 处理备注 */
    @TableField("handle_remark")
    private String handleRemark;

    /** 报警图片URL */
    @TableField("image_url")
    private String imageUrl;

    /** 报警视频URL */
    @TableField("video_url")
    private String videoUrl;

    /** 扩展数据(JSON格式) */
    @TableField("extra_data")
    private String extraData;

    /** 原始数据(完整的API响应) */
    @TableField("raw_data")
    private String rawData;

    /** 同步时间 */
    @TableField("sync_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /** 同步状态 */
    @TableField("sync_status")
    private String syncStatus;

    /** 同步来源(AUTO/MANUAL) */
    @TableField("sync_source")
    private String syncSource;

    /** 数据版本 */
    @TableField("data_version")
    private String dataVersion;

    /** 是否删除 */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /** 创建人 */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /** 创建时间 */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /** 更新人 */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /** 更新时间 */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
