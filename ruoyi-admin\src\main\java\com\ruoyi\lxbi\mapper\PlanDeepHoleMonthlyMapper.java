package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanDeepHoleMonthly;
import com.ruoyi.lxbi.domain.response.PlanDeepHoleMonthlyVo;

/**
 * 中深孔月计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface PlanDeepHoleMonthlyMapper 
{
    /**
     * 查询中深孔月计划
     * 
     * @param id 中深孔月计划主键
     * @return 中深孔月计划
     */
    public PlanDeepHoleMonthly selectPlanDeepHoleMonthlyById(Long id);

    /**
     * 查询中深孔月计划列表
     * 
     * @param planDeepHoleMonthly 中深孔月计划
     * @return 中深孔月计划集合
     */
    public List<PlanDeepHoleMonthlyVo> selectPlanDeepHoleMonthlyList(PlanDeepHoleMonthly planDeepHoleMonthly);

    /**
     * 新增中深孔月计划
     * 
     * @param planDeepHoleMonthly 中深孔月计划
     * @return 结果
     */
    public int insertPlanDeepHoleMonthly(PlanDeepHoleMonthly planDeepHoleMonthly);

    /**
     * 修改中深孔月计划
     * 
     * @param planDeepHoleMonthly 中深孔月计划
     * @return 结果
     */
    public int updatePlanDeepHoleMonthly(PlanDeepHoleMonthly planDeepHoleMonthly);

    /**
     * 删除中深孔月计划
     * 
     * @param id 中深孔月计划主键
     * @return 结果
     */
    public int deletePlanDeepHoleMonthlyById(Long id);

    /**
     * 批量删除中深孔月计划
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanDeepHoleMonthlyByIds(Long[] ids);
}
