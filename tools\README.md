# 微震事件数据转换工具

这个工具用于将微震事件Excel数据转换为SQL插入脚本，支持雪花ID生成。

## 文件说明

- `create_microseismic_events_table.sql` - 数据库建表脚本
- `excel_to_sql_converter.py` - Excel转SQL的Python转换脚本
- `requirements.txt` - Python依赖包列表
- `microseismic-events.xlsx` - 源Excel数据文件
- `README.md` - 使用说明文档

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 创建数据库表

首先在数据库中执行建表脚本：

```sql
-- 执行建表脚本
source create_microseismic_events_table.sql
```

### 2. 转换Excel数据

运行Python脚本将Excel文件转换为SQL插入脚本：

```bash
python excel_to_sql_converter.py
```

脚本会自动：
- 读取 `microseismic-events.xlsx` 文件
- 为每条记录生成唯一的雪花ID
- 生成 `microseismic_events_insert.sql` 插入脚本

### 3. 执行插入脚本

在数据库中执行生成的插入脚本：

```sql
-- 执行插入脚本
source microseismic_events_insert.sql
```

## 数据表结构

### microseismic_events 表字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键（雪花ID） |
| project_id | INTEGER | 工程ID |
| event_name | VARCHAR(100) | 事件名称 |
| event_date | DATE | 微震事件日期 |
| event_time | TIME | 微震事件时间 |
| location_error | DECIMAL(15,8) | 微震事件定位误差 |
| x_coordinate | DECIMAL(15,8) | 微震事件X轴坐标 |
| y_coordinate | DECIMAL(15,8) | 微震事件Y轴坐标 |
| z_coordinate | DECIMAL(15,8) | 微震事件Z轴坐标 |
| radiated_energy | DECIMAL(15,8) | 微震事件辐射能 |
| p_wave_radiated_energy | DECIMAL(15,8) | 微震事件P波辐射能 |
| s_wave_radiated_energy | DECIMAL(15,8) | 微震事件S波辐射能 |
| moment_magnitude | DECIMAL(10,6) | 矩震级 |
| richter_magnitude | DECIMAL(10,6) | 里氏震级 |
| local_magnitude | DECIMAL(10,6) | 当地震级 |
| seismic_moment | DECIMAL(15,8) | 地震矩 |
| p_wave_seismic_moment | DECIMAL(15,8) | P波地震矩 |
| s_wave_seismic_moment | DECIMAL(15,8) | S波地震矩 |
| volumetric_strain | DECIMAL(15,12) | 体变势 |
| apparent_stress | DECIMAL(15,8) | 视应力 |
| apparent_volume | DECIMAL(15,12) | 视体积 |
| corner_frequency | DECIMAL(10,3) | 转角频率 |
| p_wave_corner_frequency | DECIMAL(10,3) | P波转角频率 |
| s_wave_corner_frequency | DECIMAL(10,3) | S波转角频率 |
| p_wave_low_freq_amplitude | DECIMAL(15,12) | P波低频幅值 |
| s_wave_low_freq_amplitude | DECIMAL(15,12) | S波低频幅值 |
| static_stress_drop | DECIMAL(15,8) | 静态应力降 |
| dynamic_stress_drop | DECIMAL(15,8) | 动态应力降 |
| source_radius | DECIMAL(15,12) | 震源半径 |
| max_slip_velocity | DECIMAL(15,8) | 最大滑移速度 |
| server_id | INTEGER | 微震事件服务器ID |
| triggered_sensor_count | INTEGER | 被触发传感器个数 |
| triggered_sensor_ids | TEXT | 被触发传感器ID |
| location_sensor_ids | TEXT | 参与定位传感器ID |
| signal_type | INTEGER | 信号类型 |
| auto_signal_type | INTEGER | 信号类型_自动 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 雪花ID算法

使用64位雪花ID算法生成唯一主键：
- 1位符号位（固定为0）
- 41位时间戳（毫秒级，可使用69年）
- 5位数据中心ID
- 5位工作机器ID  
- 12位序列号（同一毫秒内可生成4096个ID）

## 自定义配置

可以在 `excel_to_sql_converter.py` 中修改以下配置：

```python
# 修改输入输出文件路径
excel_file = "your_excel_file.xlsx"
output_sql_file = "your_output.sql"

# 修改雪花ID生成器参数
id_generator = SnowflakeIdGenerator(datacenter_id=1, worker_id=1)
```

## 注意事项

1. 确保Excel文件的列名与CSV文件保持一致
2. 数据库需要支持BIGINT类型存储雪花ID
3. 建议在测试环境先验证SQL脚本的正确性
4. 大量数据插入时建议分批执行，避免事务过大

## 错误处理

脚本包含完整的错误处理机制：
- Excel文件读取失败
- 数据格式验证
- SQL生成异常
- 文件写入错误

运行时如遇到问题，请检查：
1. Excel文件是否存在且可读
2. Python依赖是否正确安装
3. 输出目录是否有写入权限
