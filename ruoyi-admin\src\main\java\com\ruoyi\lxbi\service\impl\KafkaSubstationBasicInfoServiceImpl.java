package com.ruoyi.lxbi.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaSubstationBasicInfoMapper;
import com.ruoyi.lxbi.domain.KafkaSubstationBasicInfo;
import com.ruoyi.lxbi.service.IKafkaSubstationBasicInfoService;

/**
 * 分站基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class KafkaSubstationBasicInfoServiceImpl implements IKafkaSubstationBasicInfoService
{
    @Autowired
    private KafkaSubstationBasicInfoMapper kafkaSubstationBasicInfoMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询分站基本信息
     * 
     * @param id 分站基本信息主键
     * @return 分站基本信息
     */
    @Override
    public KafkaSubstationBasicInfo selectKafkaSubstationBasicInfoById(Long id)
    {
        return kafkaSubstationBasicInfoMapper.selectKafkaSubstationBasicInfoById(id);
    }

    /**
     * 查询分站基本信息列表
     * 
     * @param kafkaSubstationBasicInfo 分站基本信息
     * @return 分站基本信息
     */
    @Override
    public List<KafkaSubstationBasicInfo> selectKafkaSubstationBasicInfoList(KafkaSubstationBasicInfo kafkaSubstationBasicInfo)
    {
        return kafkaSubstationBasicInfoMapper.selectKafkaSubstationBasicInfoList(kafkaSubstationBasicInfo);
    }

    /**
     * 新增分站基本信息
     * 
     * @param kafkaSubstationBasicInfo 分站基本信息
     * @return 结果
     */
    @Override
    public int insertKafkaSubstationBasicInfo(KafkaSubstationBasicInfo kafkaSubstationBasicInfo)
    {
        kafkaSubstationBasicInfo.setCreateTime(DateUtils.getNowDate());
        return kafkaSubstationBasicInfoMapper.insertKafkaSubstationBasicInfo(kafkaSubstationBasicInfo);
    }

    /**
     * 修改分站基本信息
     * 
     * @param kafkaSubstationBasicInfo 分站基本信息
     * @return 结果
     */
    @Override
    public int updateKafkaSubstationBasicInfo(KafkaSubstationBasicInfo kafkaSubstationBasicInfo)
    {
        kafkaSubstationBasicInfo.setUpdateTime(DateUtils.getNowDate());
        return kafkaSubstationBasicInfoMapper.updateKafkaSubstationBasicInfo(kafkaSubstationBasicInfo);
    }

    /**
     * 批量删除分站基本信息
     * 
     * @param ids 需要删除的分站基本信息主键
     * @return 结果
     */
    @Override
    public int deleteKafkaSubstationBasicInfoByIds(Long[] ids)
    {
        return kafkaSubstationBasicInfoMapper.deleteKafkaSubstationBasicInfoByIds(ids);
    }

    /**
     * 删除分站基本信息信息
     *
     * @param id 分站基本信息主键
     * @return 结果
     */
    @Override
    public int deleteKafkaSubstationBasicInfoById(Long id)
    {
        return kafkaSubstationBasicInfoMapper.deleteKafkaSubstationBasicInfoById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka分站基本信息消息");

            // 解析Kafka消息
            KafkaSubstationBasicInfo substationInfo = parseKafkaMessage(kafkaMessage);
            if (substationInfo == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(substationInfo.getSubstationCode())) {
                log.warn("分站编码为空，跳过处理");
                return false;
            }

            // 执行插入操作
            int result = insertKafkaSubstationBasicInfo(substationInfo);

            if (result > 0) {
                log.info("成功插入分站基本信息数据，分站编码: {}, 煤矿代码: {}",
                    substationInfo.getSubstationCode(), substationInfo.getMineCode());
                return true;
            } else {
                log.warn("插入分站基本信息数据失败，分站编码: {}",
                    substationInfo.getSubstationCode());
                return false;
            }

        } catch (Exception e) {
            log.error("处理Kafka分站基本信息消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaSubstationBasicInfo parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaSubstationBasicInfo substationInfo = new KafkaSubstationBasicInfo();

            // 基础信息
            substationInfo.setFileEncoding(getStringValue(jsonNode, "文件前缀"));
            substationInfo.setMineCode(getStringValue(jsonNode, "煤矿编码"));
            substationInfo.setMineName(getStringValue(jsonNode, "矿井名称"));
            substationInfo.setDataUploadTime(getDateValue(jsonNode, "数据上传时间"));
            substationInfo.setSubstationCode(getStringValue(jsonNode, "分站编码"));
            substationInfo.setSubstationInstallationLocation(getStringValue(jsonNode, "分站安装位置"));
            substationInfo.setXCoordinate(getBigDecimalValue(jsonNode, "X坐标"));
            substationInfo.setYCoordinate(getBigDecimalValue(jsonNode, "Y坐标"));
            substationInfo.setZCoordinate(getBigDecimalValue(jsonNode, "Z坐标"));

            // 默认值
            substationInfo.setIsDeleted(0L);
            substationInfo.setCreateTime(DateUtils.getNowDate());
            substationInfo.setUpdateTime(DateUtils.getNowDate());

            return substationInfo;

        } catch (Exception e) {
            log.error("解析Kafka分站基本信息消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null && !node.isNull() ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private java.util.Date getDateValue(JsonNode jsonNode, String fieldName) {
        try {
            String dateStr = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            }
        } catch (Exception e) {
            log.warn("解析日期字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }

    /**
     * 从JsonNode中获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(JsonNode jsonNode, String fieldName) {
        try {
            String value = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(value)) {
                return new BigDecimal(value);
            }
        } catch (Exception e) {
            log.warn("解析BigDecimal字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }
}
