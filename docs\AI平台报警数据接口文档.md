# AI平台报警数据接口文档

## 概述

本文档描述了AI平台报警数据对接服务的使用方法和接口说明。系统通过调用AI平台的REST API获取报警数据，支持多种算法类型的报警检测。

## 配置说明

### 1. 配置文件

在 `application.yml` 中配置AI平台API信息：

```yaml
external:
  api:
    ai-alarm:
      base-url: http://10.10.30.51:3001
      login-path: /login
      alarm-path: /alarm
      account: api
      password: 123456
      connect-timeout: 30000
      read-timeout: 60000
      enabled: true
      token-cache-time: 3600
```

### 2. 算法类型说明

| 算法ID | 算法名称 | 说明 |
|--------|----------|------|
| 1 | 火焰烟雾检测 | 检测火焰和烟雾 |
| 2 | 安全帽检测 | 检测是否佩戴安全帽 |
| 10 | 离岗检测 | 检测人员是否离岗 |
| 24 | 自救器检测 | 检测是否佩戴自救器 |

## 接口列表

### 1. 测试API连接

**接口地址：** `GET /ai/alarm/test`

**功能描述：** 测试AI平台API连接是否正常

**权限要求：** 无需认证（匿名访问）

**响应示例：**
```json
{
  "code": 200,
  "msg": "AI平台API连接成功",
  "data": true
}
```

### 2. 获取报警数据列表

**接口地址：** `GET /ai/alarm/list`

**功能描述：** 获取AI平台报警数据，支持分页和筛选

**权限要求：** 无需认证（匿名访问）

**请求参数：**
- `pageNum` (可选): 页数，默认为1
- `pageSize` (可选): 每页数据长度，默认为10
- `filterDateTime` (可选): 时间范围，格式：`2024-10-11 09:26:04--2024-10-12 09:26:04`
- `filterAlgorithmList` (可选): 算法筛选，格式：`1--2--10--24`

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取报警数据成功",
  "data": {
    "data": {
      "alarmDataList": [
        {
          "alarm_time": "2024-10-11 10:30:00",
          "alarm_img": "http://10.10.30.51:3001/images/alarm_001.jpg",
          "stream_name": "camera_001",
          "scene_name": "主井口",
          "algorithm_name": "火焰烟雾检测"
        }
      ],
      "total": 100
    }
  }
}
```

### 3. 获取火焰烟雾检测报警

**接口地址：** `GET /ai/alarm/fire`

**功能描述：** 获取火焰烟雾检测报警数据

**权限要求：** 无需认证（匿名访问）

**请求参数：**
- `pageNum` (可选): 页数，默认为1
- `pageSize` (可选): 每页数据长度，默认为10

### 4. 获取安全帽检测报警

**接口地址：** `GET /ai/alarm/helmet`

**功能描述：** 获取安全帽检测报警数据

**权限要求：** 无需认证（匿名访问）

### 5. 获取离岗检测报警

**接口地址：** `GET /ai/alarm/absence`

**功能描述：** 获取离岗检测报警数据

**权限要求：** 无需认证（匿名访问）

### 6. 获取自救器检测报警

**接口地址：** `GET /ai/alarm/respirator`

**功能描述：** 获取自救器检测报警数据

**权限要求：** 无需认证（匿名访问）

### 7. 根据时间范围获取报警数据

**接口地址：** `GET /ai/alarm/timerange`

**功能描述：** 根据指定时间范围获取报警数据

**权限要求：** 无需认证（匿名访问）

**请求参数：**
- `pageNum` (可选): 页数，默认为1
- `pageSize` (可选): 每页数据长度，默认为10
- `startTime` (必填): 开始时间，格式：`2024-10-11 09:26:04`
- `endTime` (必填): 结束时间，格式：`2024-10-12 09:26:04`

### 8. 清除访问令牌缓存

**接口地址：** `POST /ai/alarm/clearToken`

**功能描述：** 清除缓存的访问令牌，强制重新获取

**权限要求：** 无需认证（匿名访问）

## 数据模型

### AiAlarmDataVO

| 字段名 | 类型 | 说明 |
|--------|------|------|
| alarm_time | String | 报警时间 |
| alarm_img | String | 报警截图的网络路径 |
| stream_name | String | 产生报警数据的摄像头码流名称 |
| scene_name | String | 该摄像头码流所属的场景名称 |
| algorithm_name | String | 产生报警的算法名称 |

## 服务特性

### 1. 自动鉴权

- 系统自动管理访问令牌的获取和刷新
- 令牌缓存在Redis中，避免频繁请求
- 令牌过期时自动重新获取

### 2. 图片URL处理

- 自动为相对路径的图片添加基础URL前缀
- 确保图片链接可以正常访问

### 3. 错误处理

- 完善的异常处理和日志记录
- API调用失败时的重试机制
- 详细的错误信息返回

### 4. 缓存机制

- 访问令牌缓存，减少登录请求
- 可配置的缓存过期时间
- 支持手动清除缓存

## 使用示例

### Java代码示例

```java
@Autowired
private AiAlarmExternalService aiAlarmExternalService;

// 测试连接
boolean isConnected = aiAlarmExternalService.testConnection();

// 获取报警数据
AiAlarmResponseVO response = aiAlarmExternalService.getAlarmData(1, 10, null, null);

// 获取火焰烟雾检测报警
List<AiAlarmDataVO> fireAlarms = aiAlarmExternalService.getAlarmDataByAlgorithm(1, 10, "1");

// 根据时间范围获取报警
List<AiAlarmDataVO> timeRangeAlarms = aiAlarmExternalService.getAlarmDataByTimeRange(
    1, 10, "2024-10-11 09:26:04", "2024-10-12 09:26:04");
```

### HTTP请求示例

```bash
# 测试连接（无需认证）
curl -X GET "http://localhost:8080/ai/alarm/test"

# 获取报警数据（无需认证）
curl -X GET "http://localhost:8080/ai/alarm/list?pageNum=1&pageSize=10"

# 获取火焰烟雾检测报警（无需认证）
curl -X GET "http://localhost:8080/ai/alarm/fire?pageNum=1&pageSize=5"

# 根据时间范围获取报警（无需认证）
curl -X GET "http://localhost:8080/ai/alarm/timerange?pageNum=1&pageSize=10&startTime=2024-10-11%2009:26:04&endTime=2024-10-12%2009:26:04"

# 清除访问令牌缓存（无需认证）
curl -X POST "http://localhost:8080/ai/alarm/clearToken"
```

## 注意事项

1. **网络连接**: 确保服务器能够访问AI平台的API地址
2. **认证信息**: 确保配置的用户名和密码正确
3. **时间格式**: 时间参数必须使用指定格式：`YYYY-MM-DD HH:mm:ss`
4. **匿名访问**: 所有接口都支持匿名访问，无需用户认证
5. **缓存管理**: 如果修改了认证信息，需要清除缓存的令牌

## 故障排除

### 1. 连接失败
- 检查网络连接
- 验证API地址是否正确
- 确认防火墙设置

### 2. 认证失败
- 检查用户名和密码
- 清除缓存的令牌重试
- 确认账户是否被锁定

### 3. 数据获取失败
- 检查请求参数格式
- 验证时间范围是否合理
- 确认算法ID是否正确

## 总结

AI平台报警数据对接服务提供了完整的报警数据获取功能，支持多种筛选条件和算法类型。通过自动鉴权和缓存机制，确保了服务的稳定性和性能。
