package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 设备监测次数VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentMonitoringFrequencyVO {
    
    /**
     * 设备名称
     */
    private String equipmentName;
    
    /**
     * 设备代码
     */
    private String equipmentCode;
    
    /**
     * 监测次数
     */
    private Long monitoringCount;
    
    /**
     * 三处理次数
     */
    private Long threeProcessingCount;
    
    /**
     * 未处理次数
     */
    private Long unprocessedCount;
    
    /**
     * 区域类型 (采场/选厂)
     */
    private String areaType;
    
    /**
     * 区域类型代码
     */
    private String areaTypeCode;
}
