package com.ruoyi.common.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 财务日期工具类
 * 财务月规则：上月29号到本月28号为一个财务月
 * 财务周规则：周四到周三为一个财务周
 */
public class FinancialDateUtils {

    /**
     * 计算财务月（格式：yyyyMM）
     * 财务月规则：上月29号到本月28号为一个财务月
     * 
     * @param operationDate 操作日期
     * @return 财务月份字符串，格式：yyyyMM
     */
    public static String getFinancialMonth(Date operationDate) {
        if (operationDate == null) {
            return null;
        }
        
        Calendar cal = Calendar.getInstance();
        cal.setTime(operationDate);

        int day = cal.get(Calendar.DAY_OF_MONTH);

        // 如果是29号及以后，属于下个月的财务月
        if (day >= 29) {
            cal.add(Calendar.MONTH, 1);
        }

        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyyMM");
        return monthFormat.format(cal.getTime());
    }

    /**
     * 获取财务月的开始日期
     * 财务月规则：上月29号到本月28号为一个财务月
     * 
     * @param operationDate 操作日期
     * @return 财务月开始日期
     */
    public static Date getFinancialMonthStartDate(Date operationDate) {
        if (operationDate == null) {
            return null;
        }
        
        Calendar cal = Calendar.getInstance();
        cal.setTime(operationDate);

        int day = cal.get(Calendar.DAY_OF_MONTH);

        // 如果是29号及以后，财务月开始日期是当月29号
        if (day >= 29) {
            cal.set(Calendar.DAY_OF_MONTH, 29);
        } else {
            // 如果是28号及以前，财务月开始日期是上月29号
            cal.add(Calendar.MONTH, -1);
            cal.set(Calendar.DAY_OF_MONTH, 29);
        }

        // 设置为当天的开始时间
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTime();
    }

    /**
     * 获取财务月的结束日期
     * 财务月规则：上月29号到本月28号为一个财务月
     * 
     * @param operationDate 操作日期
     * @return 财务月结束日期
     */
    public static Date getFinancialMonthEndDate(Date operationDate) {
        if (operationDate == null) {
            return null;
        }
        
        Calendar cal = Calendar.getInstance();
        cal.setTime(operationDate);

        int day = cal.get(Calendar.DAY_OF_MONTH);

        // 如果是29号及以后，财务月结束日期是下月28号
        if (day >= 29) {
            cal.add(Calendar.MONTH, 1);
            cal.set(Calendar.DAY_OF_MONTH, 28);
        } else {
            // 如果是28号及以前，财务月结束日期是当月28号
            cal.set(Calendar.DAY_OF_MONTH, 28);
        }

        // 设置为当天的结束时间
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);

        return cal.getTime();
    }

    /**
     * 判断两个日期是否为同一天
     * 
     * @param date1 日期1
     * @param date2 日期2
     * @return 是否为同一天
     */
    public static boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }

        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * 判断指定日期是否在财务月范围内
     * 
     * @param targetDate 目标日期
     * @param financialMonthDate 财务月参考日期
     * @return 是否在财务月范围内
     */
    public static boolean isInFinancialMonth(Date targetDate, Date financialMonthDate) {
        if (targetDate == null || financialMonthDate == null) {
            return false;
        }

        Date startDate = getFinancialMonthStartDate(financialMonthDate);
        Date endDate = getFinancialMonthEndDate(financialMonthDate);

        return !targetDate.before(startDate) && !targetDate.after(endDate);
    }

    /**
     * 获取财务月的天数
     * 
     * @param operationDate 操作日期
     * @return 财务月天数
     */
    public static int getFinancialMonthDays(Date operationDate) {
        if (operationDate == null) {
            return 0;
        }

        Date startDate = getFinancialMonthStartDate(operationDate);
        Date endDate = getFinancialMonthEndDate(operationDate);

        long diffInMillies = endDate.getTime() - startDate.getTime();
        return (int) (diffInMillies / (1000 * 60 * 60 * 24)) + 1;
    }

    /**
     * 格式化财务月显示
     *
     * @param operationDate 操作日期
     * @return 财务月显示字符串，格式：yyyy年MM月
     */
    public static String formatFinancialMonth(Date operationDate) {
        String financialMonth = getFinancialMonth(operationDate);
        if (financialMonth == null || financialMonth.length() != 6) {
            return null;
        }

        String year = financialMonth.substring(0, 4);
        String month = financialMonth.substring(4, 6);
        return year + "年" + month + "月";
    }

    /**
     * 获取财务周的开始日期（周四）
     * 财务周规则：周四到周三为一个财务周
     *
     * @param operationDate 操作日期
     * @return 财务周开始日期
     */
    public static Date getFinancialWeekStartDate(Date operationDate) {
        if (operationDate == null) {
            return null;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(operationDate);

        // 获取当前是星期几（1=周日, 2=周一, ..., 5=周四, ..., 7=周六）
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);

        // 计算到周四的天数差
        int daysToThursday;
        if (dayOfWeek >= Calendar.THURSDAY) {
            // 如果是周四到周六，周四就是本周的开始
            daysToThursday = Calendar.THURSDAY - dayOfWeek;
        } else {
            // 如果是周日到周三，需要回到上周的周四
            daysToThursday = Calendar.THURSDAY - dayOfWeek - 7;
        }

        cal.add(Calendar.DAY_OF_MONTH, daysToThursday);

        // 设置为当天的开始时间
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTime();
    }

    /**
     * 获取财务周的结束日期（周三）
     * 财务周规则：周四到周三为一个财务周
     *
     * @param operationDate 操作日期
     * @return 财务周结束日期
     */
    public static Date getFinancialWeekEndDate(Date operationDate) {
        if (operationDate == null) {
            return null;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(getFinancialWeekStartDate(operationDate));

        // 从周四开始，加6天到周三
        cal.add(Calendar.DAY_OF_MONTH, 6);

        // 设置为当天的结束时间
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);

        return cal.getTime();
    }

    /**
     * 获取财务周范围（周四到周三）
     *
     * @param operationDate 操作日期
     * @return 财务周范围数组，[0]为开始日期，[1]为结束日期
     */
    public static Date[] getFinancialWeekRange(Date operationDate) {
        if (operationDate == null) {
            return null;
        }

        Date weekStart = getFinancialWeekStartDate(operationDate);
        Date weekEnd = getFinancialWeekEndDate(operationDate);

        return new Date[]{weekStart, weekEnd};
    }

    /**
     * 判断指定日期是否在财务周范围内
     *
     * @param targetDate 目标日期
     * @param financialWeekDate 财务周参考日期
     * @return 是否在财务周范围内
     */
    public static boolean isInFinancialWeek(Date targetDate, Date financialWeekDate) {
        if (targetDate == null || financialWeekDate == null) {
            return false;
        }

        Date startDate = getFinancialWeekStartDate(financialWeekDate);
        Date endDate = getFinancialWeekEndDate(financialWeekDate);

        return !targetDate.before(startDate) && !targetDate.after(endDate);
    }

    /**
     * 获取财务周的天数（固定为7天）
     *
     * @param operationDate 操作日期
     * @return 财务周天数
     */
    public static int getFinancialWeekDays(Date operationDate) {
        if (operationDate == null) {
            return 0;
        }

        // 财务周固定为7天
        return 7;
    }
}
