# 车辆安全统计接口说明

## 概述

本文档说明了车辆安全统计模块的接口设计和实现，该模块提供车辆安全相关的统计分析功能，包括车辆概览、报警部门分布、告警类型分布和告警记录等统计数据。

## 接口列表

### 1. 车辆安全概览统计

**接口地址**: `GET /lxbi/stat/vehicle-safety/overview`

**功能描述**: 获取车辆安全的总体概览数据

**请求参数**:
- `viewType`: 视图类型 (day/week/month)
- `startDate`: 开始日期 (yyyy-MM-dd)
- `endDate`: 结束日期 (yyyy-MM-dd)

**响应数据**:
```json
{
  "totalVehicleCount": 200,
  "vehicleAlarmCount": 40,
  "startDate": "2025-08-20",
  "endDate": "2025-08-25",
  "period": "day"
}
```

### 2. 车辆报警部门分布统计

**接口地址**: `GET /lxbi/stat/vehicle-safety/alarm-department-distribution`

**功能描述**: 获取车辆报警按部门分布数据，用于饼图展示

**响应数据**:
```json
[
  {
    "departmentName": "A部门",
    "alarmCount": 20,
    "percentage": 33.33,
    "departmentCode": "DEPT_A"
  },
  {
    "departmentName": "B部门",
    "alarmCount": 15,
    "percentage": 25.00,
    "departmentCode": "DEPT_B"
  },
  {
    "departmentName": "C部门",
    "alarmCount": 13,
    "percentage": 21.67,
    "departmentCode": "DEPT_C"
  },
  {
    "departmentName": "D部门",
    "alarmCount": 10,
    "percentage": 16.67,
    "departmentCode": "DEPT_D"
  }
]
```

### 3. 车辆告警类型分布统计

**接口地址**: `GET /lxbi/stat/vehicle-safety/alarm-type-distribution`

**功能描述**: 获取车辆告警类型分布数据，用于饼图展示

**响应数据**:
```json
[
  {
    "alarmTypeName": "超速报警",
    "alarmCount": 20,
    "percentage": 54.05,
    "alarmTypeCode": "OVERSPEED"
  },
  {
    "alarmTypeName": "车辆缺电",
    "alarmCount": 10,
    "percentage": 27.03,
    "alarmTypeCode": "LOW_BATTERY"
  },
  {
    "alarmTypeName": "闯红灯",
    "alarmCount": 7,
    "percentage": 18.92,
    "alarmTypeCode": "RED_LIGHT"
  }
]
```

### 4. 车辆告警记录列表

**接口地址**: `GET /lxbi/stat/vehicle-safety/alarm-records`

**功能描述**: 获取车辆告警记录列表

**响应数据**:
```json
[
  {
    "serialNumber": 1,
    "vehiclePlateNumber": "辽E 1234",
    "alarmType": "超速报警",
    "vehicleLocation": "检测站202",
    "alarmTime": "2025/8/8/20:00",
    "resolveTime": "2025/8/8/21:00",
    "vehicleCode": "VH001",
    "alarmStatus": "已处理",
    "handlerName": "张三"
  },
  {
    "serialNumber": 2,
    "vehiclePlateNumber": "辽E 1234",
    "alarmType": "超载报警",
    "vehicleLocation": "检测站202",
    "alarmTime": "2025/8/8/20:00",
    "resolveTime": "2025/8/8/21:00",
    "vehicleCode": "VH001",
    "alarmStatus": "已处理",
    "handlerName": "李四"
  }
]
```

## 数据结构说明

### VO类设计

#### 1. VehicleSafetyOverviewVO
- **用途**: 车辆安全概览数据
- **字段**: 车辆总数、车辆运行报警数

#### 2. VehicleAlarmDepartmentDistributionVO
- **用途**: 车辆报警部门分布数据（饼图）
- **字段**: 部门名称、报警数量、占比百分比、部门代码

#### 3. VehicleAlarmTypeDistributionVO
- **用途**: 车辆告警类型分布数据（饼图）
- **字段**: 告警类型名称、告警数量、占比百分比、告警类型代码

#### 4. VehicleAlarmRecordVO
- **用途**: 车辆告警记录数据
- **字段**: 序号、车牌号、告警类型、车辆位置、告警时间、解除时间、处理人员

## 业务逻辑

### 1. 数据统计维度

**时间维度**:
- 日统计: 按天统计车辆告警
- 周统计: 按周统计车辆告警
- 月统计: 按月统计车辆告警

**车辆维度**:
- 按车辆类型统计
- 按所属部门统计
- 按告警类型统计

### 2. 统计指标

**车辆基础指标**:
- 车辆总数: 系统中所有车辆的总数
- 车辆运行报警数: 产生运行报警的车辆数量

**告警分析指标**:
- 告警类型分布: 不同类型告警的数量和占比
- 部门告警分布: 各部门车辆告警的数量和占比
- 告警处理状态: 已处理/处理中/未处理

### 3. 数据展示

**概览卡片**:
- 车辆总数: 200台
- 车辆运行报警: 40起

**饼图展示**:
- 车辆报警部门分布
- 车辆告警类型分布

**列表展示**:
- 车辆告警记录详情
- 包含车牌号、告警类型、位置、时间等信息

## 模拟数据说明

### 1. 概览数据
- 车辆总数: 200台
- 车辆运行报警数: 40起

### 2. 部门分布
包含以下部门:
- A部门: 20起报警 (33.33%)
- B部门: 15起报警 (25.00%)
- C部门: 13起报警 (21.67%)
- D部门: 10起报警 (16.67%)

### 3. 告警类型分布
- 超速报警: 20起 (54.05%)
- 车辆缺电: 10起 (27.03%)
- 闯红灯: 7起 (18.92%)

### 4. 告警记录
模拟了常见的车辆告警:
- 超速报警、超载报警
- 区域超时、车辆缺电
- 闯红灯等交通违规

## 扩展接入点

### 1. 车辆管理系统接入
```java
// 可接入的数据源
- 车辆基础信息系统
- 车辆GPS定位系统
- 车辆状态监控系统
- 交通违规监控系统
```

### 2. 数据接入方式
- **实时数据**: 通过Kafka队列接收车辆状态变更
- **定时同步**: 定时从车辆管理系统同步数据
- **API接口**: 通过REST API获取车辆信息

### 3. 数据库设计建议
```sql
-- 车辆基础信息表
CREATE TABLE vehicle_info (
    id BIGSERIAL PRIMARY KEY,
    vehicle_code VARCHAR(50) UNIQUE,
    plate_number VARCHAR(20),
    vehicle_type VARCHAR(50),
    department_code VARCHAR(50),
    status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 车辆告警记录表
CREATE TABLE vehicle_alarm_record (
    id BIGSERIAL PRIMARY KEY,
    vehicle_code VARCHAR(50),
    plate_number VARCHAR(20),
    alarm_type VARCHAR(50),
    alarm_location VARCHAR(100),
    alarm_time TIMESTAMP,
    resolve_time TIMESTAMP,
    alarm_status VARCHAR(20),
    handler_name VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 车辆位置记录表
CREATE TABLE vehicle_location_record (
    id BIGSERIAL PRIMARY KEY,
    vehicle_code VARCHAR(50),
    longitude DECIMAL(10,6),
    latitude DECIMAL(10,6),
    location_name VARCHAR(100),
    record_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 测试说明

### 1. 单元测试
- 所有Service方法的功能测试
- 不同视图类型的测试
- 数据完整性和一致性测试

### 2. 接口测试
```bash
# 测试概览统计
curl -X GET "http://localhost:8080/lxbi/stat/vehicle-safety/overview?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试部门分布
curl -X GET "http://localhost:8080/lxbi/stat/vehicle-safety/alarm-department-distribution?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试告警类型分布
curl -X GET "http://localhost:8080/lxbi/stat/vehicle-safety/alarm-type-distribution?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试告警记录
curl -X GET "http://localhost:8080/lxbi/stat/vehicle-safety/alarm-records?viewType=day&startDate=2025-08-20&endDate=2025-08-25"
```

### 3. 数据一致性验证
- 概览数据与分布数据的一致性
- 告警记录与统计数据的一致性
- 百分比计算的准确性

## 后续开发计划

### 1. 数据接入
- 接入真实的车辆管理系统
- 建立车辆GPS定位数据流
- 实现车辆告警自动检测

### 2. 功能增强
- 车辆轨迹分析
- 车辆行为预警
- 车辆维护提醒

### 3. 可视化优化
- 实时车辆位置地图
- 告警热力图
- 移动端车辆监控

## 业务价值

### 1. 安全管理提升
- **实时告警监控**: 及时发现和处理车辆安全问题
- **违规行为分析**: 识别和预防车辆违规行为
- **部门责任管理**: 明确各部门车辆安全责任

### 2. 运营效率优化
- **车辆调度优化**: 基于告警数据优化车辆调度
- **维护计划制定**: 根据告警类型制定维护计划
- **驾驶员培训**: 针对性的安全培训

### 3. 合规性支持
- **告警记录完整**: 完整记录所有车辆告警事件
- **处理流程追踪**: 跟踪告警处理全过程
- **数据可追溯**: 支持事故调查和分析

## 总结

车辆安全统计模块提供了完整的车辆安全监控和分析功能，通过多维度的统计分析，帮助管理人员及时了解车辆运行状况，预防安全事故，提高车辆管理效率。

当前实现使用模拟数据，为后续接入真实车辆数据预留了扩展接口，可以根据实际业务需求进行数据源的接入和功能的扩展。
