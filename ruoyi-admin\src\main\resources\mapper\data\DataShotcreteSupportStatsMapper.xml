<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataShotcreteSupportStatsMapper">

    <!-- 喷浆支护总体统计结果映射（含计划量） -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataSupportTypeTotalWithPlanStats" id="DataSupportTypeTotalWithPlanStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="supportType"          column="support_type"           />
        <result property="totalSupportLength"   column="total_support_length"   />
        <result property="totalSupportVolume"   column="total_support_volume"   />
        <result property="planSupportLength"    column="plan_support_length"    />
        <result property="planSupportVolume"    column="plan_support_volume"    />
    </resultMap>

    <!-- 喷浆支护项目部门统计结果映射（含计划量） -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataSupportTypeDepartmentWithPlanStats" id="DataSupportTypeDepartmentWithPlanStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="supportType"          column="support_type"           />
        <result property="projectDepartmentId"  column="project_department_id"  />
        <result property="projectDepartmentName" column="project_department_name" />
        <result property="totalSupportLength"   column="total_support_length"   />
        <result property="totalSupportVolume"   column="total_support_volume"   />
        <result property="planSupportLength"    column="plan_support_length"    />
        <result property="planSupportVolume"    column="plan_support_volume"    />
    </resultMap>

    <!-- ========== 喷浆支护总体统计查询方法（含计划量） ========== -->

    <select id="selectDailyTotalWithPlanStats" resultMap="DataSupportTypeTotalWithPlanStatsResult">
        SELECT
            da.operation_date,
            da.support_type,
            da.total_support_length,
            da.total_support_volume,
            ROUND(psm.shotcrete_support_meter / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_support_length,
            ROUND(psm.shotcrete_support_volume / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_support_volume
        FROM vdata_support_daily_type_stats da
        CROSS JOIN get_financial_month(da.operation_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(shotcrete_support_meter) as shotcrete_support_meter,
                SUM(shotcrete_support_volume) as shotcrete_support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM'))
        ) psm ON fm.financial_year = psm.year AND fm.financial_month = psm.month
        WHERE da.support_type = '2'
        <if test="startDate != null">
            AND operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND operation_date &lt;= #{endDate}
        </if>
        ORDER BY da.operation_date
    </select>

    <select id="selectWeeklyTotalWithPlanStats" resultMap="DataSupportTypeTotalWithPlanStatsResult">
        SELECT
            da.year,
            da.week_number,
            da.week_start_date,
            da.week_end_date,
            da.support_type,
            da.total_support_length,
            da.total_support_volume,
            ROUND(psm.shotcrete_support_meter / 4, 2) as plan_support_length,
            ROUND(psm.shotcrete_support_volume / 4, 2) as plan_support_volume
        FROM vdata_support_weekly_type_stats da
        CROSS JOIN get_financial_month(da.week_start_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(shotcrete_support_meter) as shotcrete_support_meter,
                SUM(shotcrete_support_volume) as shotcrete_support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM'))
        ) psm ON fm.financial_year = psm.year AND fm.financial_month = psm.month
        WHERE da.support_type = '2'
        <if test="startDate != null">
            AND week_start_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND week_end_date &lt;= #{endDate}
        </if>
        ORDER BY da.year, da.week_number
    </select>

    <select id="selectMonthlyTotalWithPlanStats" resultMap="DataSupportTypeTotalWithPlanStatsResult">
        SELECT
            da.year,
            da.month,
            da.support_type,
            da.total_support_length,
            da.total_support_volume,
            psm.shotcrete_support_meter as plan_support_length,
            psm.shotcrete_support_volume as plan_support_volume
        FROM vdata_support_monthly_type_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(shotcrete_support_meter) as shotcrete_support_meter,
                SUM(shotcrete_support_volume) as shotcrete_support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM'))
        ) psm ON da.year = psm.year AND da.month = psm.month
        WHERE da.support_type = '2'
        <if test="startDate != null">
            AND MAKE_DATE(da.year, da.month, 1) &gt;= DATE_TRUNC('month', #{startDate}::date)
        </if>
        <if test="endDate != null">
            AND MAKE_DATE(da.year, da.month, 1) &lt;= DATE_TRUNC('month', #{endDate}::date)
        </if>
        ORDER BY da.year, da.month
    </select>

    <select id="selectYearlyTotalWithPlanStats" resultMap="DataSupportTypeTotalWithPlanStatsResult">
        SELECT
            da.year,
            da.support_type,
            da.total_support_length,
            da.total_support_volume,
            psm.shotcrete_support_meter as plan_support_length,
            psm.shotcrete_support_volume as plan_support_volume
        FROM vdata_support_yearly_type_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                SUM(shotcrete_support_meter) as shotcrete_support_meter,
                SUM(shotcrete_support_volume) as shotcrete_support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM'))
        ) psm ON da.year = psm.year
        WHERE da.support_type = '2'
        <if test="startDate != null">
            AND da.year &gt;= EXTRACT(YEAR FROM #{startDate})
        </if>
        <if test="endDate != null">
            AND da.year &lt;= EXTRACT(YEAR FROM #{endDate})
        </if>
        ORDER BY da.year
    </select>

    <!-- ========== 喷浆支护项目部门统计查询方法（含计划量） ========== -->

    <select id="selectDailyDepartmentWithPlanStats" resultMap="DataSupportTypeDepartmentWithPlanStatsResult">
        SELECT
            da.operation_date,
            da.support_type,
            da.project_department_id,
            da.project_department_name,
            da.total_support_length,
            da.total_support_volume,
            ROUND(psm.shotcrete_support_meter / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_support_length,
            ROUND(psm.shotcrete_support_volume / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_support_volume
        FROM vdata_support_daily_type_department_stats da
        CROSS JOIN get_financial_month(da.operation_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                project_department_id,
                SUM(shotcrete_support_meter) as shotcrete_support_meter,
                SUM(shotcrete_support_volume) as shotcrete_support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) psm ON fm.financial_year = psm.year AND fm.financial_month = psm.month AND da.project_department_id = psm.project_department_id
        WHERE da.support_type = '2'
        <if test="startDate != null">
            AND operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND operation_date &lt;= #{endDate}
        </if>
        ORDER BY da.operation_date, da.project_department_id
    </select>

    <select id="selectWeeklyDepartmentWithPlanStats" resultMap="DataSupportTypeDepartmentWithPlanStatsResult">
        SELECT
            da.year,
            da.week_number,
            da.week_start_date,
            da.week_end_date,
            da.support_type,
            da.project_department_id,
            da.project_department_name,
            da.total_support_length,
            da.total_support_volume,
            ROUND(psm.shotcrete_support_meter / 4, 2) as plan_support_length,
            ROUND(psm.shotcrete_support_volume / 4, 2) as plan_support_volume
        FROM vdata_support_weekly_type_department_stats da
        CROSS JOIN get_financial_month(da.week_start_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                project_department_id,
                SUM(shotcrete_support_meter) as shotcrete_support_meter,
                SUM(shotcrete_support_volume) as shotcrete_support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) psm ON fm.financial_year = psm.year AND fm.financial_month = psm.month AND da.project_department_id = psm.project_department_id
        WHERE da.support_type = '2'
        <if test="startDate != null">
            AND week_start_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND week_end_date &lt;= #{endDate}
        </if>
        ORDER BY da.year, da.week_number, da.project_department_id
    </select>

    <select id="selectMonthlyDepartmentWithPlanStats" resultMap="DataSupportTypeDepartmentWithPlanStatsResult">
        SELECT
            da.year,
            da.month,
            da.support_type,
            da.project_department_id,
            da.project_department_name,
            da.total_support_length,
            da.total_support_volume,
            psm.shotcrete_support_meter as plan_support_length,
            psm.shotcrete_support_volume as plan_support_volume
        FROM vdata_support_monthly_type_department_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                project_department_id,
                SUM(shotcrete_support_meter) as shotcrete_support_meter,
                SUM(shotcrete_support_volume) as shotcrete_support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) psm ON da.year = psm.year AND da.month = psm.month AND da.project_department_id = psm.project_department_id
        WHERE da.support_type = '2'
        <if test="startDate != null">
            AND MAKE_DATE(da.year, da.month, 1) &gt;= DATE_TRUNC('month', #{startDate}::date)
        </if>
        <if test="endDate != null">
            AND MAKE_DATE(da.year, da.month, 1) &lt;= DATE_TRUNC('month', #{endDate}::date)
        </if>
        ORDER BY da.year, da.month, da.project_department_id
    </select>

    <select id="selectYearlyDepartmentWithPlanStats" resultMap="DataSupportTypeDepartmentWithPlanStatsResult">
        SELECT
            da.year,
            da.support_type,
            da.project_department_id,
            da.project_department_name,
            da.total_support_length,
            da.total_support_volume,
            psm.shotcrete_support_meter as plan_support_length,
            psm.shotcrete_support_volume as plan_support_volume
        FROM vdata_support_yearly_type_department_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                project_department_id,
                SUM(shotcrete_support_meter) as shotcrete_support_meter,
                SUM(shotcrete_support_volume) as shotcrete_support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) psm ON da.year = psm.year AND da.project_department_id = psm.project_department_id
        WHERE da.support_type = '2'
        <if test="startDate != null">
            AND da.year &gt;= EXTRACT(YEAR FROM #{startDate})
        </if>
        <if test="endDate != null">
            AND da.year &lt;= EXTRACT(YEAR FROM #{endDate})
        </if>
        ORDER BY da.year, da.project_department_id
    </select>

</mapper>
