package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 安全小结VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SafetySummaryVO {
    
    /**
     * 隐患总数
     */
    private Long totalCount;

    /**
     * 重大隐患数（等级为1的隐患）
     */
    private Long majorCount;

    /**
     * 待整改数（状态为0的隐患）
     */
    private Long pendingCount;
    
    /**
     * 超时数（状态为4的隐患）
     */
    private Long overdueCount;
    
    /**
     * 已整改数（状态为2的隐患）
     */
    private Long completedCount;
    
    /**
     * 待复查数（状态为3的隐患）
     */
    private Long reviewPendingCount;
    
    /**
     * 已驳回数（状态为1的隐患）
     */
    private Long rejectedCount;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 统计周期
     */
    private String period;
}
