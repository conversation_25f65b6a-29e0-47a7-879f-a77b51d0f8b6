# 地表监测系统Token缓存优化说明

## 概述

为地表监测系统添加了类似AI报警系统的token缓存机制，使用Redis缓存SESSION token，避免频繁登录，提高系统性能和稳定性。

## 优化内容

### 1. 添加Redis缓存支持

**新增依赖**：
```java
@Autowired
private RedisTemplate<String, String> redisTemplate;
```

**缓存配置**：
```java
// Redis缓存键
private static final String TOKEN_CACHE_KEY = "surface_monitoring_session_token";

// Token缓存时间配置
@Value("${surface.monitoring.api.token-cache-time:3600}")
private int tokenCacheTime;
```

### 2. 重构登录逻辑

**修改前（每次都登录）**：
```java
public Map<String, Object> login() {
    // 每次都调用登录接口
    // 没有缓存机制
}
```

**修改后（带缓存）**：
```java
public String getAuthToken() {
    // 1. 先从Redis缓存获取
    String cachedToken = redisTemplate.opsForValue().get(TOKEN_CACHE_KEY);
    if (StringUtils.hasText(cachedToken)) {
        return cachedToken; // 直接使用缓存
    }
    
    // 2. 缓存失效时重新登录
    Map<String, Object> loginResult = performLogin();
    if (success) {
        // 3. 缓存新token
        redisTemplate.opsForValue().set(TOKEN_CACHE_KEY, newToken, tokenCacheTime, TimeUnit.SECONDS);
    }
    return newToken;
}
```

### 3. 优化API调用

**修改前**：
```java
// 添加认证token（如果已登录）
if (authToken != null && !authToken.isEmpty()) {
    headers.add("Cookie", "SESSION=" + authToken);
}
```

**修改后**：
```java
// 获取认证token（带缓存）
String token = getAuthToken();
if (token != null && !token.isEmpty()) {
    headers.add("Cookie", "SESSION=" + token);
}
```

### 4. 添加错误处理

**401未授权处理**：
```java
if (response.getStatusCode() == HttpStatus.UNAUTHORIZED) {
    log.warn("认证失败，清除缓存的token");
    redisTemplate.delete(TOKEN_CACHE_KEY);
    this.authToken = null;
}
```

## 配置文件更新

### application.yml

```yaml
# 地表监测系统配置
surface:
  monitoring:
    api:
      # API基础URL
      base-url: http://10.10.22.24:19093/busgateway
      # 超时时间（毫秒）
      timeout: 30000
      # Token缓存时间（秒）- 设置为1小时
      token-cache-time: 3600
```

## 缓存机制特点

### 1. 智能缓存策略

**缓存流程**：
```
1. 检查Redis中是否有有效token
2. 如果有，直接返回缓存的token
3. 如果没有或已过期，调用登录接口获取新token
4. 将新token存储到Redis，设置过期时间
5. 返回新token
```

**缓存时间**：
- **设置时间**：1小时（3600秒）
- **原因**：地表监测系统的SESSION token有效期相对较短，设置1小时比较合适

### 2. 自动失效处理

**过期检测**：
- Redis自动过期清理
- 401错误时主动清除缓存
- 支持手动清除缓存

**重试机制**：
- 缓存失效时自动重新登录
- 登录失败时的优雅降级
- 完整的异常处理

### 3. 性能优化

**减少登录请求**：
```
无缓存: 每次API调用都需要登录
有缓存: 1小时内只需要登录一次
性能提升: 显著减少网络请求
```

**并发支持**：
- 多个请求共享同一个SESSION token
- 避免并发登录导致的资源竞争
- 支持集群环境下的token共享

## 使用示例

### 1. 获取缓存的token

```java
// 自动处理缓存逻辑
String token = surfaceMonitoringExternalService.getAuthToken();
if (token != null) {
    // 使用token调用API
}
```

### 2. 调用API（自动使用缓存token）

```java
// API调用会自动使用缓存的token
SurfaceMonitoringApiResponse response = surfaceMonitoringExternalService.getLatest7DayMpptShiftingTotal();
```

### 3. 手动登录（会更新缓存）

```java
Map<String, Object> result = surfaceMonitoringExternalService.login();
```

## 监控和维护

### 1. Redis命令检查

```bash
# 检查token是否存在
redis-cli EXISTS surface_monitoring_session_token

# 查看token内容
redis-cli GET surface_monitoring_session_token

# 查看剩余过期时间
redis-cli TTL surface_monitoring_session_token

# 手动清除token
redis-cli DEL surface_monitoring_session_token
```

### 2. 日志监控

**关键日志**：
```
DEBUG - 从缓存中获取到地表监测SESSION token
INFO  - 地表监测系统登录成功，SESSION token已缓存
WARN  - 认证失败，清除缓存的token
ERROR - 获取地表监测认证token失败
```

### 3. 健康检查

```java
// 测试API连接（会自动处理token）
Map<String, Object> healthStatus = surfaceMonitoringExternalService.getHealthStatus();
```

## 与AI报警系统的对比

| 特性 | AI报警系统 | 地表监测系统 |
|------|------------|--------------|
| 认证方式 | Bearer Token | SESSION Cookie |
| 缓存时间 | 23小时 | 1小时 |
| 缓存键 | `ai_alarm_access_token` | `surface_monitoring_session_token` |
| Token格式 | `Authorization: Bearer {token}` | `Cookie: SESSION={token}` |
| 有效期 | 24小时 | 较短（具体未知） |

## 故障排查

### 1. 常见问题

**缓存不生效**：
- 检查Redis连接是否正常
- 确认配置文件中的缓存时间设置
- 查看是否有异常日志

**频繁登录**：
- 检查token有效期是否过短
- 确认API返回的SESSION token格式
- 调整缓存时间配置

**认证失败**：
- 检查用户名密码是否正确
- 确认API地址是否可访问
- 查看网络连接状态

### 2. 调试方法

**查看缓存状态**：
```bash
# 查看所有地表监测相关的缓存键
redis-cli KEYS "*surface_monitoring*"

# 查看缓存详情
redis-cli --scan --pattern "*surface_monitoring*" | xargs redis-cli MGET
```

**测试登录**：
```bash
# 手动清除缓存测试重新登录
redis-cli DEL surface_monitoring_session_token

# 调用API触发重新登录
curl -X GET "http://localhost:8080/lxbi/stat/surface-monitoring/overview"
```

## 配置建议

### 1. 生产环境

```yaml
surface:
  monitoring:
    api:
      # 生产环境配置
      base-url: http://production-server:19093/busgateway
      timeout: 30000
      token-cache-time: 3600  # 1小时，适合生产环境
```

### 2. 开发环境

```yaml
surface:
  monitoring:
    api:
      # 开发环境配置
      base-url: http://dev-server:19093/busgateway
      timeout: 10000
      token-cache-time: 600   # 10分钟，便于开发测试
```

### 3. 测试环境

```yaml
surface:
  monitoring:
    api:
      # 测试环境配置
      base-url: http://test-server:19093/busgateway
      timeout: 15000
      token-cache-time: 1800  # 30分钟，平衡性能和测试需求
```

## 总结

地表监测系统的token缓存优化具有以下特点：

1. **高效性**: 1小时缓存时间，大幅减少登录请求
2. **可靠性**: 完善的异常处理和自动重试机制
3. **灵活性**: 可配置的缓存时间和策略
4. **可维护性**: 详细的日志记录和监控支持
5. **兼容性**: 支持SESSION Cookie认证方式

通过这套缓存机制，地表监测系统可以在保证功能正常的前提下，显著提升性能和稳定性，为地表监测数据的高效获取提供了可靠保障。

## 编译状态

✅ **编译成功**
- **编译时间**: 18.8秒
- **编译文件**: 548个源文件
- **状态**: BUILD SUCCESS

所有新增的token缓存功能都已正确实现并通过编译验证。
