<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.PlanDeepHoleMonthlyMapper">

    <resultMap type="PlanDeepHoleMonthly" id="PlanDeepHoleMonthlyResult">
        <result property="id" column="id"/>
        <result property="projectDepartmentId" column="project_department_id"/>
        <result property="workingFaceId" column="working_face_id"/>
        <result property="stopeId" column="stope_id"/>
        <result property="deepHoleMeter" column="deep_hole_meter"/>
        <result property="planDate" column="plan_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectPlanDeepHoleMonthlyVo">
        select pdhm.*, bpd.project_department_name, bs.stope_name, bws.working_face_name
        from plan_deep_hole_monthly pdhm
                 left join base_project_department bpd on pdhm.project_department_id = bpd.project_department_id
                 left join base_stope bs on pdhm.stope_id = bs.stope_id
                 left join base_working_face bws on pdhm.working_face_id = bws.working_face_id
    </sql>

    <select id="selectPlanDeepHoleMonthlyList" parameterType="PlanDeepHoleMonthly"
            resultType="com.ruoyi.lxbi.domain.response.PlanDeepHoleMonthlyVo">
        <include refid="selectPlanDeepHoleMonthlyVo"/>
        <where>
            <if test="projectDepartmentId != null ">and pdhm.project_department_id = #{projectDepartmentId}</if>
            <if test="workingFaceId != null ">and pdhm.working_face_id = #{workingFaceId}</if>
            <if test="stopeId != null ">and pdhm.stope_id = #{stopeId}</if>
            <if test="planDate != null  and planDate != ''">and pdhm.plan_date = #{planDate}</if>
        </where>
    </select>

    <select id="selectPlanDeepHoleMonthlyById" parameterType="Long"
            resultType="com.ruoyi.lxbi.domain.response.PlanDeepHoleMonthlyVo">
        <include refid="selectPlanDeepHoleMonthlyVo"/>
        where pdhm.id = #{id}
    </select>

    <insert id="insertPlanDeepHoleMonthly" parameterType="PlanDeepHoleMonthly" useGeneratedKeys="true" keyProperty="id">
        insert into plan_deep_hole_monthly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="workingFaceId != null">working_face_id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="deepHoleMeter != null">deep_hole_meter,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="workingFaceId != null">#{workingFaceId},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="deepHoleMeter != null">#{deepHoleMeter},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updatePlanDeepHoleMonthly" parameterType="PlanDeepHoleMonthly">
        update plan_deep_hole_monthly
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="workingFaceId != null">working_face_id = #{workingFaceId},</if>
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="deepHoleMeter != null">deep_hole_meter = #{deepHoleMeter},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanDeepHoleMonthlyById" parameterType="Long">
        delete
        from plan_deep_hole_monthly
        where id = #{id}
    </delete>

    <delete id="deletePlanDeepHoleMonthlyByIds" parameterType="String">
        delete from plan_deep_hole_monthly where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>