package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采矿整体月计划导入对象
 */
@Data
@ExcelImportTemplate(
        key = "plan_mining_monthly",
        name = "采矿整体月计划导入",
        description = "用于导入采矿整体月计划数据",
        sheetName = "采矿月计划"
)
public class PlanMiningMonthlyImport {

    /**
     * 计划月份
     */
    @ExcelProperty(value = "计划月份", index = 0)
    @ExcelRequired(message = "计划月份不能为空")
    @ExcelSelected(prompt = "请输入计划月份，格式：yyyyMM，例如：202501")
    private String planDate;

    /**
     * 项目部门ID（存储值）
     */
    @ExcelSelected(
            optionKey = "projectDepartment",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long projectDepartmentId;

    /**
     * 项目部门名称（显示值）
     */
    @ExcelProperty(value = "项目部门", index = 1)
    @ExcelRequired(message = "项目部门不能为空")
    @ExcelSelected(
            optionKey = "projectDepartment",
            prompt = "请选择项目部门",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String projectDepartmentName;

    /**
     * 掘进米数
     */
    @ExcelProperty(value = "掘进米数", index = 2)
    @ExcelSelected(prompt = "请输入掘进米数")
    private BigDecimal driftMeter;

    /**
     * 原矿量
     */
    @ExcelProperty(value = "原矿量", index = 3)
    @ExcelSelected(prompt = "请输入原矿量")
    private BigDecimal rawOreVolume;

    /**
     * 支护米数
     */
    @ExcelProperty(value = "支护米数", index = 4)
    @ExcelSelected(prompt = "请输入支护米数")
    private BigDecimal supportMeter;

    /**
     * 充填量
     */
    @ExcelProperty(value = "充填量", index = 5)
    @ExcelSelected(prompt = "请输入充填量")
    private BigDecimal fillingVolume;

    /**
     * 潜孔米数
     */
    @ExcelProperty(value = "潜孔米数", index = 6)
    @ExcelSelected(prompt = "请输入潜孔米数")
    private BigDecimal dthMeter;

    /**
     * 中深孔米数
     */
    @ExcelProperty(value = "中深孔米数", index = 7)
    @ExcelSelected(prompt = "请输入中深孔米数")
    private BigDecimal deepHoleMeter;

    /**
     * 出矿量
     */
    @ExcelProperty(value = "出矿量", index = 8)
    @ExcelSelected(prompt = "请输入出矿量")
    private BigDecimal oreOutputVolume;
}
