package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.lxbi.domain.response.DataTunnelingDepartmentWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingStopeStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingWorkingFaceStats;

/**
 * 掘进数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface DataTunnelingStatsMapper 
{
    // ========== 总体统计查询方法（含计划量） ==========
    
    /**
     * 查询总体统计数据列表（含计划量） (日)
     */
    public List<DataTunnelingTotalWithPlanStats> selectDailyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (周)
     */
    public List<DataTunnelingTotalWithPlanStats> selectWeeklyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (月)
     */
    public List<DataTunnelingTotalWithPlanStats> selectMonthlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (年)
     */
    public List<DataTunnelingTotalWithPlanStats> selectYearlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 项目部门统计查询方法（含计划量） ==========
    
    /**
     * 查询项目部门统计数据列表（含计划量） (日)
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectDailyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表（含计划量） (周)
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectWeeklyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表（含计划量） (月)
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectMonthlyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表（含计划量） (年)
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectYearlyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 重点工程总体统计查询方法（含计划量） ==========

    /**
     * 查询重点工程总体统计数据列表（含计划量） (日)
     */
    public List<DataTunnelingTotalWithPlanStats> selectDailyPriorityProjectTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程总体统计数据列表（含计划量） (周)
     */
    public List<DataTunnelingTotalWithPlanStats> selectWeeklyPriorityProjectTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程总体统计数据列表（含计划量） (月)
     */
    public List<DataTunnelingTotalWithPlanStats> selectMonthlyPriorityProjectTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程总体统计数据列表（含计划量） (年)
     */
    public List<DataTunnelingTotalWithPlanStats> selectYearlyPriorityProjectTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 重点工程项目部门统计查询方法（含计划量） ==========

    /**
     * 查询重点工程项目部门统计数据列表（含计划量） (日)
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectDailyPriorityProjectDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程项目部门统计数据列表（含计划量） (周)
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectWeeklyPriorityProjectDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程项目部门统计数据列表（含计划量） (月)
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectMonthlyPriorityProjectDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程项目部门统计数据列表（含计划量） (年)
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectYearlyPriorityProjectDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ==================== 重点工程采场统计方法 ====================

    /**
     * 查询重点工程采场统计数据列表 (日)
     */
    public List<DataTunnelingStopeStats> selectDailyPriorityProjectStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程采场统计数据列表 (周)
     */
    public List<DataTunnelingStopeStats> selectWeeklyPriorityProjectStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程采场统计数据列表 (月)
     */
    public List<DataTunnelingStopeStats> selectMonthlyPriorityProjectStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程采场统计数据列表 (年)
     */
    public List<DataTunnelingStopeStats> selectYearlyPriorityProjectStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ==================== 重点工程工作面统计方法 ====================

    /**
     * 查询重点工程工作面统计数据列表 (日)
     */
    public List<DataTunnelingWorkingFaceStats> selectDailyPriorityProjectWorkingFaceStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程工作面统计数据列表 (周)
     */
    public List<DataTunnelingWorkingFaceStats> selectWeeklyPriorityProjectWorkingFaceStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程工作面统计数据列表 (月)
     */
    public List<DataTunnelingWorkingFaceStats> selectMonthlyPriorityProjectWorkingFaceStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询重点工程工作面统计数据列表 (年)
     */
    public List<DataTunnelingWorkingFaceStats> selectYearlyPriorityProjectWorkingFaceStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
