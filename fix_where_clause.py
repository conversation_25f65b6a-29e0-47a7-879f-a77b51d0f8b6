#!/usr/bin/env python3
"""
修复SQL文件中缺失的WHERE关键字问题
将 "FROM table AND condition" 修复为 "FROM table WHERE condition"
"""

import re
import os

def fix_sql_file(file_path):
    """修复SQL文件中的WHERE子句"""
    print(f"正在修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 记录原始内容长度
    original_length = len(content)
    
    # 修复模式1: FROM api_hidden_trouble_record AND condition
    pattern1 = r'FROM\s+api_hidden_trouble_record\s+AND\s+'
    replacement1 = 'FROM api_hidden_trouble_record WHERE '
    content = re.sub(pattern1, replacement1, content, flags=re.IGNORECASE)
    
    # 修复模式2: 在子查询中的情况
    pattern2 = r'\(\s*SELECT[^)]*FROM\s+api_hidden_trouble_record\s+AND\s+'
    def replace_subquery(match):
        return match.group(0).replace(' AND ', ' WHERE ', 1)
    content = re.sub(pattern2, replace_subquery, content, flags=re.IGNORECASE | re.DOTALL)
    
    # 检查是否有修改
    if len(content) != original_length:
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 文件已修复: {file_path}")
        return True
    else:
        print(f"ℹ️  文件无需修复: {file_path}")
        return False

def main():
    """主函数"""
    # 需要修复的文件
    file_path = 'ruoyi-admin/src/main/resources/mapper/stat/HiddenTroubleStatMapper.xml'
    
    if os.path.exists(file_path):
        if fix_sql_file(file_path):
            print(f"\n🎉 修复完成!")
        else:
            print(f"\n📝 无需修复")
    else:
        print(f"❌ 文件不存在: {file_path}")

if __name__ == "__main__":
    main()
