<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DateCrushingOperationStatsMapper">
    
    <!-- 统一 Stats 结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DateCrushingOperationStats" id="DateCrushingOperationStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="totalOperationTime"   column="total_operation_time"   />
        <result property="totalFaultTime"       column="total_fault_time"       />
        <result property="faultCount"           column="fault_count"            />
        <result property="totalCrushingVolume"  column="total_crushing_volume"  />
    </resultMap>
    
    <!-- 统一 PeriodStats 结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DateCrushingOperationPeriodStats" id="DateCrushingOperationPeriodStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="workingPeriodId"      column="working_period_id"      />
        <result property="workingPeriodName"    column="working_period_name"    />
        <result property="totalOperationTime"   column="total_operation_time"   />
        <result property="totalFaultTime"       column="total_fault_time"       />
        <result property="faultCount"           column="fault_count"            />
        <result property="totalCrushingVolume"  column="total_crushing_volume"  />
    </resultMap>
   
    <!-- 新增统一 Stats 查询方法 -->
    <select id="selectDailyStats" resultMap="DateCrushingOperationStatsResult">
        select operation_date, total_operation_time, total_fault_time, fault_count, total_crushing_volume
        from vdata_crushing_daily_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date
    </select>
    
    <select id="selectWeeklyStats" resultMap="DateCrushingOperationStatsResult">
        select year, week_number, week_start_date, week_end_date, 
               total_operation_time, total_fault_time, fault_count, total_crushing_volume
        from vdata_crushing_weekly_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number
    </select>
    
    <select id="selectMonthlyStats" resultMap="DateCrushingOperationStatsResult">
        select year, month, total_operation_time, total_fault_time, fault_count, total_crushing_volume
        from vdata_crushing_monthly_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month
    </select>
    
    <!-- 新增统一 PeriodStats 查询方法 -->
    <select id="selectDailyPeriodStats" resultMap="DateCrushingOperationPeriodStatsResult">
        select operation_date, working_period_id, working_period_name, 
               total_operation_time, total_fault_time, fault_count, total_crushing_volume
        from vdata_crushing_daily_period_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date, working_period_id
    </select>
    
    <select id="selectWeeklyPeriodStats" resultMap="DateCrushingOperationPeriodStatsResult">
        select year, week_number, week_start_date, week_end_date, working_period_id, working_period_name, 
               total_operation_time, total_fault_time, fault_count, total_crushing_volume
        from vdata_crushing_weekly_period_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number, working_period_id
    </select>
    
    <select id="selectMonthlyPeriodStats" resultMap="DateCrushingOperationPeriodStatsResult">
        select year, month, working_period_id, working_period_name, 
               total_operation_time, total_fault_time, fault_count, total_crushing_volume
        from vdata_crushing_monthly_period_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month, working_period_id
    </select>

    <!-- 年度统计查询方法 -->
    <select id="selectYearlyStats" resultMap="DateCrushingOperationStatsResult">
        select year, total_operation_time, total_fault_time, fault_count, total_crushing_volume
        from vdata_crushing_yearly_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year
    </select>

    <select id="selectYearlyPeriodStats" resultMap="DateCrushingOperationPeriodStatsResult">
        select year, working_period_id, working_period_name,
               total_operation_time, total_fault_time, fault_count, total_crushing_volume
        from vdata_crushing_yearly_period_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year, working_period_id
    </select>
</mapper>
