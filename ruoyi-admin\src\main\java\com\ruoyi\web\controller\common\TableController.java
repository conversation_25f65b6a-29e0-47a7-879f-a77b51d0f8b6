package com.ruoyi.web.controller.common;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.TableConfigInfo;
import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.core.table.TableHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 通用表格控制器
 */
@RestController
@RequestMapping("/common/table")
public class TableController extends BaseController {

    @Autowired
    private TableHandlerRegistry tableHandlerRegistry;

    /**
     * 获取表格配置
     */
    @GetMapping("/config/{code}")
    public AjaxResult getTableConfig(@PathVariable String code) {
        BaseTableHandler<?, ?> handler = tableHandlerRegistry.getHandler(code);
        if (handler == null) {
            return error("表格处理器不存在: " + code);
        }

        TableConfigInfo config = handler.getTableConfig();
        return success(config);
    }

    /**
     * 查询表格数据
     */
    @GetMapping("/data/{code}")
    public R<List<?>> getTableData(@PathVariable String code, @RequestParam Map<String, Object> params) {
        BaseTableHandler<?, ?> handler = tableHandlerRegistry.getHandler(code);
        if (handler == null) {
            return R.ok(List.of());
        }

        return R.ok(handler.queryTableData(params));
    }
}