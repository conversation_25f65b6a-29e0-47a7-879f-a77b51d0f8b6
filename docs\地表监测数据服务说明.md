# 地表监测数据服务说明

## 📋 概述

根据地表监测数据获取接口定义，我已经为您创建了完整的地表监测数据访问第三方服务功能。该服务用于获取七日基站偏移数据统计，并提供数据存储、查询、统计等功能。

## 🎯 功能特性

### 1. **第三方API集成**
- ✅ 调用第三方地表监测API
- ✅ 数据解析和转换
- ✅ 错误处理和重试机制
- ✅ 连接健康检查

### 2. **数据管理**
- ✅ 自动数据同步
- ✅ UPSERT操作（插入或更新）
- ✅ 复合唯一约束（日期+基站+终端）
- ✅ 原始数据完整保存

### 3. **查询功能**
- ✅ 按日期范围查询
- ✅ 按基站名称查询
- ✅ 获取最新数据
- ✅ 偏移异常数据查询
- ✅ 分页查询支持

### 4. **统计分析**
- ✅ 按日期统计
- ✅ 按基站统计
- ✅ 偏移量统计
- ✅ 异常数据统计

## 🏗️ 架构设计

### 1. **数据流程**
```
第三方API → SurfaceMonitoringExternalService → 数据解析 → 数据库存储 → 业务查询
```

### 2. **核心组件**

#### 2.1 实体类
- `SurfaceMonitoringData` - 地表监测数据实体
- `SurfaceMonitoringApiResponse` - API响应实体

#### 2.2 服务层
- `SurfaceMonitoringExternalService` - 第三方API服务
- `ISurfaceMonitoringDataService` - 业务服务接口
- `SurfaceMonitoringDataServiceImpl` - 业务服务实现

#### 2.3 数据层
- `SurfaceMonitoringDataMapper` - 数据访问接口
- `SurfaceMonitoringDataMapper.xml` - SQL映射文件

#### 2.4 控制层
- `SurfaceMonitoringDataController` - REST API控制器

## 📊 数据库设计

### 表结构：`surface_monitoring_data`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | BIGSERIAL | 主键ID |
| `date` | DATE | 监测日期 |
| `station_name` | VARCHAR(100) | 基站名称 |
| `wgbh` | VARCHAR(50) | 终端编号 |
| `x1-x4` | INTEGER | X轴偏移统计 |
| `y1-y4` | INTEGER | Y轴偏移统计 |
| `h1-h4` | INTEGER | 高度偏移统计 |
| `xstacked_total_offset` | DECIMAL(15,6) | X轴偏移距离总和 |
| `ystacked_total_offset` | DECIMAL(15,6) | Y轴偏移距离总和 |
| `hstacked_total_offset` | DECIMAL(15,6) | 高度偏移距离总和 |
| `original_id` | INTEGER | 原始数据ID |
| `original_data` | JSONB | 原始数据(JSON格式) |

### 约束和索引
- **复合唯一约束**: `(date, station_name, wgbh)`
- **索引**: 日期、基站、终端编号、原始ID等
- **GIN索引**: 支持JSON字段查询

## 🔌 API接口

### 1. **第三方API调用**

#### 接口地址
```
POST http://***********:19093/busgateway/operate/selectLatest7DayMpptShiftingTotal
```

#### 响应格式
```json
{
  "result": true,
  "message": "success",
  "data": [
    {
      "date": "2025-08-23",
      "station_name": "基站001",
      "wgbh": "WGBH001",
      "x1": 10, "x2": 5, "x3": 3, "x4": 2,
      "y1": 8, "y2": 6, "y3": 4, "y4": 2,
      "h1": 12, "h2": 8, "h3": 5, "h4": 3,
      "xstackedTotalOffset": 15.5,
      "ystackedTotalOffset": 12.3,
      "hstackedTotalOffset": 18.7,
      "id": 12345
    }
  ]
}
```

### 2. **系统API接口**

#### 2.1 数据同步
```bash
POST /lxbi/surfaceMonitoring/sync
```

#### 2.2 健康检查
```bash
GET /lxbi/surfaceMonitoring/health
```

#### 2.3 连接测试
```bash
POST /lxbi/surfaceMonitoring/testConnection
```

#### 2.4 统计数据
```bash
GET /lxbi/surfaceMonitoring/statistics
```

#### 2.5 查询接口
```bash
# 按日期范围查询
GET /lxbi/surfaceMonitoring/dateRange?startDate=2025-08-20&endDate=2025-08-23

# 按基站查询
GET /lxbi/surfaceMonitoring/station/{stationName}

# 获取最新数据
GET /lxbi/surfaceMonitoring/latest?limit=10

# 获取异常数据
GET /lxbi/surfaceMonitoring/abnormal?threshold=10.0

# 按日期统计
GET /lxbi/surfaceMonitoring/statistics/{date}
```

## 🧪 测试使用

### 1. **执行数据库脚本**
```sql
-- 创建表结构
\i sql/create_surface_monitoring_data_table.sql
```

### 2. **配置第三方API**
```yaml
# application.yml
surface:
  monitoring:
    api:
      base-url: http://***********:19093/busgateway
      timeout: 30000
```

### 3. **运行测试脚本**
```bash
# 执行API测试
bash scripts/test_surface_monitoring_api.sh
```

### 4. **手动测试示例**

#### 测试API连接
```bash
curl -X POST "http://localhost:8080/lxbi/surfaceMonitoring/testConnection"
```

#### 同步数据
```bash
curl -X POST "http://localhost:8080/lxbi/surfaceMonitoring/sync"
```

#### 查看统计
```bash
curl -X GET "http://localhost:8080/lxbi/surfaceMonitoring/statistics"
```

## 📈 数据处理流程

### 1. **数据同步流程**
```
1. 调用第三方API获取数据
2. 解析JSON响应数据
3. 转换为系统实体对象
4. 验证必要字段
5. 执行UPSERT操作
6. 记录处理结果
```

### 2. **字段映射关系**

| 第三方API字段 | 系统字段 | 说明 |
|---------------|----------|------|
| `date` | `date` | 监测日期 |
| `station_name` | `stationName` | 基站名称 |
| `wgbh` | `wgbh` | 终端编号 |
| `x1-x4` | `x1-x4` | X轴偏移统计 |
| `y1-y4` | `y1-y4` | Y轴偏移统计 |
| `h1-h4` | `h1-h4` | 高度偏移统计 |
| `xstackedTotalOffset` | `xstackedTotalOffset` | X轴偏移总和 |
| `ystackedTotalOffset` | `ystackedTotalOffset` | Y轴偏移总和 |
| `hstackedTotalOffset` | `hstackedTotalOffset` | 高度偏移总和 |
| `id` | `originalId` | 原始数据ID |

### 3. **唯一性处理**
- 基于 `(date, station_name, wgbh)` 的复合唯一性
- 相同组合的数据会被更新而不是重复插入
- 保留原始创建时间，更新修改时间

## ⚠️ 注意事项

### 1. **网络配置**
- 确保能访问第三方API地址：`http://***********:19093`
- 配置合适的超时时间
- 考虑网络重试机制

### 2. **数据质量**
- 监控空值数据的处理
- 验证偏移量数据的合理性
- 定期检查数据完整性

### 3. **性能考虑**
- 大量数据时考虑分批处理
- 合理设置查询分页大小
- 监控数据库性能

### 4. **错误处理**
- 记录详细的错误日志
- 实现优雅的降级机制
- 设置合适的告警阈值

## 🚀 扩展功能

### 1. **定时任务**
- 可以添加定时同步任务
- 支持增量数据同步
- 数据质量监控

### 2. **数据可视化**
- 偏移趋势图表
- 基站分布地图
- 异常数据告警

### 3. **数据导出**
- Excel导出功能
- 报表生成
- 数据备份

现在您已经拥有了完整的地表监测数据服务，可以访问第三方API并进行数据管理和分析！
