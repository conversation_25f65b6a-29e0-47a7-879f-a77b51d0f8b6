package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataOrepassOperationStatsRequest;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationPeriodStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationOrepassStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationDepartmentStats;
import com.ruoyi.lxbi.mapper.DataOrepassOperationStatsMapper;
import com.ruoyi.lxbi.service.IDataOrepassOperationStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 溜井放矿数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DataOrepassOperationStatsServiceImpl implements IDataOrepassOperationStatsService {
    @Autowired
    private DataOrepassOperationStatsMapper dataOrepassOperationStatsMapper;
    
    /**
     * 查询统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 统计数据集合
     */
    @Override
    public List<DataOrepassOperationStats> selectStatsList(DataOrepassOperationStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectDailyStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectWeeklyStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectYearlyStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataOrepassOperationStatsMapper.selectMonthlyStats(request.getStartDate(), request.getEndDate());
        }
    }
    
    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    @Override
    public List<DataOrepassOperationPeriodStats> selectPeriodStatsList(DataOrepassOperationStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectDailyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectWeeklyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectYearlyPeriodStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataOrepassOperationStatsMapper.selectMonthlyPeriodStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询溜井统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 溜井统计数据集合
     */
    @Override
    public List<DataOrepassOperationOrepassStats> selectOrepassStatsList(DataOrepassOperationStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectDailyOrepassStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectWeeklyOrepassStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectYearlyOrepassStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataOrepassOperationStatsMapper.selectMonthlyOrepassStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询项目部门统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合
     */
    @Override
    public List<DataOrepassOperationDepartmentStats> selectDepartmentStatsList(DataOrepassOperationStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectDailyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectWeeklyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataOrepassOperationStatsMapper.selectYearlyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataOrepassOperationStatsMapper.selectMonthlyDepartmentStats(request.getStartDate(), request.getEndDate());
        }
    }

}
