package com.ruoyi.lxbi.admin.service;

import java.util.List;
import com.ruoyi.lxbi.admin.domain.KafkaPeoplePositionBasicInfo;

/**
 * KAFKA人员基础信息数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
public interface IKafkaPeoplePositionBasicInfoService 
{
    /**
     * 查询KAFKA人员基础信息数据
     * 
     * @param id KAFKA人员基础信息数据主键
     * @return KAFKA人员基础信息数据
     */
    public KafkaPeoplePositionBasicInfo selectKafkaPeoplePositionBasicInfoById(Long id);

    /**
     * 查询KAFKA人员基础信息数据列表
     * 
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return KAFKA人员基础信息数据集合
     */
    public List<KafkaPeoplePositionBasicInfo> selectKafkaPeoplePositionBasicInfoList(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo);

    /**
     * 新增KAFKA人员基础信息数据
     * 
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    public int insertKafkaPeoplePositionBasicInfo(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo);

    /**
     * 修改KAFKA人员基础信息数据
     * 
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    public int updateKafkaPeoplePositionBasicInfo(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo);

    /**
     * 批量删除KAFKA人员基础信息数据
     * 
     * @param ids 需要删除的KAFKA人员基础信息数据主键集合
     * @return 结果
     */
    public int deleteKafkaPeoplePositionBasicInfoByIds(Long[] ids);

    /**
     * 删除KAFKA人员基础信息数据信息
     *
     * @param id KAFKA人员基础信息数据主键
     * @return 结果
     */
    public int deleteKafkaPeoplePositionBasicInfoById(Long id);

    /**
     * 根据人员卡编码查询
     *
     * @param personCardCode 人员卡编码
     * @return KAFKA人员基础信息数据
     */
    public KafkaPeoplePositionBasicInfo selectByPersonCardCode(String personCardCode);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 批量插入或更新（UPSERT）
     *
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    public int upsertKafkaPeoplePositionBasicInfo(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaPeoplePositionBasicInfo parseKafkaMessage(String kafkaMessage);

    /**
     * 统计指定日期范围内的活跃人员数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 活跃人员数量
     */
    public Long countActivePersonnelByDateRange(String startDate, String endDate);
}
