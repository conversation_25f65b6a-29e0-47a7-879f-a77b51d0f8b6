package com.ruoyi.lxbi.admin.kafka;

import com.ruoyi.lxbi.admin.service.IKafkaPeopleLocationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

/**
 * 区域基本信息Kafka监听器
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class PeopleLocationInfoKafkaListener {

    @Autowired
    private IKafkaPeopleLocationInfoService kafkaPeopleLocationInfoService;

    /**
     * 监听区域基本信息主题
     * 
     * @param record Kafka消息记录
     * @param ack 手动确认
     */
    @KafkaListener(topics = "PeoplePos_Location_Info")
    public void handlePeopleLocationInfo(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try {
            String message = record.value();
            log.info("接收到区域基本信息Kafka消息，主题: {}, 分区: {}, 偏移量: {}", 
                record.topic(), record.partition(), record.offset());
            log.debug("消息内容: {}", message);

            // 处理消息
            boolean success = kafkaPeopleLocationInfoService.processKafkaMessage(message);
            
            if (success) {
                log.info("区域基本信息消息处理成功，偏移量: {}", record.offset());
                // 手动确认消息
                if (ack != null) {
                    ack.acknowledge();
                }
            } else {
                log.error("区域基本信息消息处理失败，偏移量: {}, 消息: {}", record.offset(), message);
                // 根据业务需求决定是否确认消息
                // 这里选择确认消息以避免重复处理
                if (ack != null) {
                    ack.acknowledge();
                }
            }

        } catch (Exception e) {
            log.error("处理区域基本信息Kafka消息异常，偏移量: {}", record.offset(), e);
            // 异常情况下也确认消息，避免重复处理
            if (ack != null) {
                ack.acknowledge();
            }
        }
    }
}
