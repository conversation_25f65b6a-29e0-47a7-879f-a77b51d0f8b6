# 人员安全统计接口文档

## 概述

根据人员安全统计图表需求，创建了以下接口来提供人员安全相关的统计数据。

## 接口列表

### 1. 人员安全概览接口

**接口地址：** `GET /sec/personnelSafetyStat/overview`

**功能描述：** 获取人员安全概览数据，包括下井人次、区域超时、教育培训、违规行为等统计信息。

**请求参数：**
- `viewType` (可选): 视图类型，支持 daily/weekly/monthly，默认为 daily
- `startDate` (可选): 开始日期，格式 yyyy-MM-dd
- `endDate` (可选): 结束日期，格式 yyyy-MM-dd

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "downWellCount": 200,
    "areaTimeoutCount": 40,
    "educationTrainingCount": 2,
    "violationCount": 2,
    "startDate": "2025-08-25",
    "endDate": "2025-08-25",
    "period": "daily"
  }
}
```

### 2. 主要超时组分布接口

**接口地址：** `GET /sec/personnelSafetyStat/timeoutGroupDistribution`

**功能描述：** 获取主要超时组分布统计，用于饼图显示各组别的超时情况分布。

**请求参数：** 同概览接口

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "groupName": "A班组",
      "timeoutCount": 100,
      "percentage": 33.33
    },
    {
      "groupName": "B班组",
      "timeoutCount": 150,
      "percentage": 50.00
    },
    {
      "groupName": "C班组",
      "timeoutCount": 50,
      "percentage": 16.67
    }
  ]
}
```

### 3. 区域超时人员名单接口

**接口地址：** `GET /sec/personnelSafetyStat/areaTimeoutPersonnel`

**功能描述：** 获取区域超时人员名单，显示具体的超时人员信息。

**请求参数：** 同概览接口

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "timeoutCount": 10,
      "personnelName": "张三",
      "currentTimeoutArea": "A区域",
      "averageStayTime": "15小时"
    },
    {
      "timeoutCount": 8,
      "personnelName": "李四",
      "currentTimeoutArea": "A区域",
      "averageStayTime": "15小时"
    }
  ]
}
```

### 4. 来救数量趋势接口

**接口地址：** `GET /sec/personnelSafetyStat/rescueQuantityTrend`

**功能描述：** 获取来救数量趋势统计，用于折线图显示救援和培训数量的变化趋势。

**请求参数：** 同概览接口

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "date": "2025-08-20",
      "shortDate": "08-20",
      "rescueCount": 12,
      "trainingCount": 6,
      "rescuePercentage": 66.67,
      "trainingPercentage": 33.33
    },
    {
      "date": "2025-08-21",
      "shortDate": "08-21",
      "rescueCount": 10,
      "trainingCount": 5,
      "rescuePercentage": 66.67,
      "trainingPercentage": 33.33
    }
  ]
}
```

### 5. 人员安全综合统计接口（仪表板）

**接口地址：** `GET /sec/personnelSafetyStat/dashboard`

**功能描述：** 获取人员安全综合统计数据，包含所有人员安全相关的统计数据，适用于仪表板一次性获取所有数据。

**请求参数：** 同概览接口

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "overview": {
      "downWellCount": 200,
      "areaTimeoutCount": 40,
      "educationTrainingCount": 2,
      "violationCount": 2,
      "startDate": "2025-08-25",
      "endDate": "2025-08-25",
      "period": "daily"
    },
    "timeoutGroupDistribution": [...],
    "areaTimeoutPersonnel": [...],
    "rescueQuantityTrend": [...]
  }
}
```

### 6. 人员安全小结统计接口

**接口地址：** `GET /sec/personnelSafetyStat/safetySummary`

**功能描述：** 获取人员安全小结统计，包括当前井下作业人员、检测到违规行为、人员求救报警、存在超时人员等。

**请求参数：** 同概览接口

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "currentUndergroundPersonnel": 296,
    "detectedViolations": 3,
    "personnelDistressAlarms": 1,
    "timeoutPersonnel": 2,
    "startDate": "2025-08-25",
    "endDate": "2025-08-25",
    "period": "daily"
  }
}
```

### 7. 人员安全小结文本生成接口

**接口地址：** `GET /sec/personnelSafetyStat/safetySummaryText`

**功能描述：** 生成人员安全小结文本描述，根据统计数据生成文字说明。

**请求参数：** 同概览接口

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "title": "人员安全",
    "summaryTexts": [
      "当前井下作业人员 296人。",
      "检测到违规行为 3起（包括未戴安全帽、禁区停留）。",
      "人员求救报警 1起，已于 3 分钟内响应并处理。",
      "存在 2名人员井下滞留超过14小时。"
    ],
    "startDate": "2025-08-25",
    "endDate": "2025-08-25",
    "viewType": "daily",
    "generateTime": "2025-08-25 14:30:00"
  }
}
```

### 8. 人员安全简化概览接口

**接口地址：** `GET /sec/personnelSafetyStat/simpleOverview`

**功能描述：** 获取人员安全简化概览，包括下井人数、违规行为、求救数。

**请求参数：** 同概览接口

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "downWellPersonnel": 296,
    "violationBehavior": 2,
    "distressCalls": 3,
    "startDate": "2025-08-25",
    "endDate": "2025-08-25",
    "period": "daily"
  }
}
```

## 数据源说明

### Kafka队列数据处理

系统已集成 `PeoplePos_People_TimeOver` 队列的数据解析和存储功能：

1. **队列监听**: `PeoplePositionTimeOverKafkaListener` 监听队列消息
2. **数据解析**: 自动解析JSON格式的队列消息，提取以下字段：
   - 煤矿编码、矿井名称、数据上传时间
   - 人员卡编码、姓名、入井时刻
   - 报警开始时间、报警结束时间
   - 区域编码、进入当前所处区域时间
   - 基站编码、进入当前所处基站时刻
3. **数据存储**: 存储到 `kafka_people_position_time_over` 表（PostgreSQL）
4. **去重处理**: 根据人员卡编码和报警开始时间进行唯一性检查

### 数据库支持

系统完全支持 **PostgreSQL** 数据库：

1. **SQL语法**: 所有查询都使用PostgreSQL标准语法
2. **索引优化**: 使用PostgreSQL部分索引提高查询性能
3. **类型安全**: 明确的类型转换（如 `::date`）
4. **正则表达式**: 使用PostgreSQL的 `~` 操作符
5. **时间函数**: 使用 `EXTRACT(EPOCH FROM ...)` 计算时间差

详细配置请参考：`docs/PostgreSQL配置说明.md`

### 真实数据统计

接口已更新为使用真实数据：

- **下井人次**: 从 `kafka_people_position_basic_info` 表统计活跃人员
- **区域超时**: 从 `kafka_people_position_time_over` 表统计超时记录
- **超时组分布**: 基于人员姓名规则进行组别分类统计
- **超时人员名单**: 查询具体的超时人员信息和驻留时间

## 注意事项

1. 所有接口目前使用 `@Anonymous` 注解，无需认证即可访问
2. 核心数据（下井人次、区域超时）已对接真实数据源，教育培训和违规行为暂时使用模拟数据
3. 日期参数如果不传，系统会根据 `viewType` 自动计算默认的日期范围
4. 所有接口都支持日、周、月三种视图类型的统计
5. 系统会自动处理Kafka队列数据，无需手动干预

## 数据模型说明

### PersonnelSafetyOverviewVO
- `downWellCount`: 下井人次
- `areaTimeoutCount`: 区域超时次数
- `educationTrainingCount`: 教育培训次数
- `violationCount`: 违规行为次数

### TimeoutGroupDistributionVO
- `groupName`: 组别名称
- `timeoutCount`: 超时次数
- `percentage`: 占比百分比

### AreaTimeoutPersonnelVO
- `timeoutCount`: 超时次数
- `personnelName`: 人员姓名
- `currentTimeoutArea`: 当前超时区域
- `averageStayTime`: 平均驻留时间

### RescueQuantityTrendVO
- `date`: 完整日期
- `shortDate`: 短日期格式
- `rescueCount`: 救援数量
- `trainingCount`: 培训数量
- `rescuePercentage`: 救援数量百分比
- `trainingPercentage`: 培训数量百分比

### PersonnelSafetySummaryVO
- `currentUndergroundPersonnel`: 当前井下作业人员数
- `detectedViolations`: 检测到违规行为数
- `personnelDistressAlarms`: 人员求救报警数
- `timeoutPersonnel`: 存在超时人员数

### PersonnelSafetySummaryTextVO
- `title`: 人员安全小结标题
- `summaryTexts`: 人员安全小结文本列表
- `generateTime`: 生成时间

### PersonnelSafetySimpleOverviewVO
- `downWellPersonnel`: 下井人数
- `violationBehavior`: 违规行为数
- `distressCalls`: 求救数
