package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.PlanStopeMonthlyMapper;
import com.ruoyi.lxbi.domain.PlanStopeMonthly;
import com.ruoyi.lxbi.domain.request.PlanStopeMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanStopeMonthlyVo;
import com.ruoyi.lxbi.service.IPlanStopeMonthlyService;

/**
 * 采场月度计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Service
public class PlanStopeMonthlyServiceImpl implements IPlanStopeMonthlyService 
{
    @Autowired
    private PlanStopeMonthlyMapper planStopeMonthlyMapper;

    /**
     * 查询采场月度计划
     * 
     * @param id 采场月度计划主键
     * @return 采场月度计划
     */
    @Override
    public PlanStopeMonthly selectPlanStopeMonthlyById(Long id)
    {
        return planStopeMonthlyMapper.selectPlanStopeMonthlyById(id);
    }

    /**
     * 查询采场月度计划列表
     *
     * @param planStopeMonthly 采场月度计划
     * @return 采场月度计划
     */
    @Override
    public List<PlanStopeMonthlyVo> selectPlanStopeMonthlyList(PlanStopeMonthly planStopeMonthly)
    {
        return planStopeMonthlyMapper.selectPlanStopeMonthlyList(planStopeMonthly);
    }

    /**
     * 新增采场月度计划
     * 
     * @param planStopeMonthly 采场月度计划
     * @return 结果
     */
    @Override
    public int insertPlanStopeMonthly(PlanStopeMonthly planStopeMonthly)
    {
        planStopeMonthly.setCreateTime(DateUtils.getNowDate());
        return planStopeMonthlyMapper.insertPlanStopeMonthly(planStopeMonthly);
    }

    /**
     * 修改采场月度计划
     * 
     * @param planStopeMonthly 采场月度计划
     * @return 结果
     */
    @Override
    public int updatePlanStopeMonthly(PlanStopeMonthly planStopeMonthly)
    {
        planStopeMonthly.setUpdateTime(DateUtils.getNowDate());
        return planStopeMonthlyMapper.updatePlanStopeMonthly(planStopeMonthly);
    }

    /**
     * 批量删除采场月度计划
     * 
     * @param ids 需要删除的采场月度计划主键
     * @return 结果
     */
    @Override
    public int deletePlanStopeMonthlyByIds(Long[] ids)
    {
        return planStopeMonthlyMapper.deletePlanStopeMonthlyByIds(ids);
    }

    /**
     * 删除采场月度计划信息
     *
     * @param id 采场月度计划主键
     * @return 结果
     */
    @Override
    public int deletePlanStopeMonthlyById(Long id)
    {
        return planStopeMonthlyMapper.deletePlanStopeMonthlyById(id);
    }

    /**
     * 批量保存采场月度计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePlanStopeMonthly(List<PlanStopeMonthlyBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个月份的数据
        String planMonth = batchDataList.get(0).getPlanMonth();
        if (StringUtils.isBlank(planMonth)) {
            throw new ServiceException("计划月份不能为空");
        }

        boolean allSameMonth = batchDataList.stream()
                .allMatch(data -> planMonth.equals(data.getPlanMonth()));
        if (!allSameMonth) {
            throw new ServiceException("批量数据必须是同一个计划月份");
        }

        // 查询该月份的现有数据
        PlanStopeMonthly queryParam = new PlanStopeMonthly();
        queryParam.setPlanDate(planMonth);
        List<PlanStopeMonthlyVo> existingDataList = planStopeMonthlyMapper.selectPlanStopeMonthlyList(queryParam);
        Map<Long, PlanStopeMonthly> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(PlanStopeMonthly::getId, data -> data));

        List<PlanStopeMonthly> toInsert = new ArrayList<>();
        List<PlanStopeMonthly> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (PlanStopeMonthlyBatchDto batchData : batchDataList) {
            if ("add".equals(batchData.getOperationType())) {
                // 新增数据
                PlanStopeMonthly newData = new PlanStopeMonthly();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if ("edit".equals(batchData.getOperationType()) && batchData.getId() != null) {
                // 更新现有数据
                if (existingDataMap.containsKey(batchData.getId())) {
                    PlanStopeMonthly updateData = new PlanStopeMonthly();
                    copyProperties(batchData, updateData);
                    updateData.setId(batchData.getId());
                    updateData.setUpdateBy(SecurityUtils.getUsername());
                    updateData.setUpdateTime(DateUtils.getNowDate());
                    toUpdate.add(updateData);
                    toDelete.remove(batchData.getId());
                }
            }
        }

        int totalProcessed = 0;

        // 执行批量操作
        if (!toInsert.isEmpty()) {
            for (PlanStopeMonthly data : toInsert) {
                totalProcessed += planStopeMonthlyMapper.insertPlanStopeMonthly(data);
            }
        }

        if (!toUpdate.isEmpty()) {
            for (PlanStopeMonthly data : toUpdate) {
                totalProcessed += planStopeMonthlyMapper.updatePlanStopeMonthly(data);
            }
        }

        if (!toDelete.isEmpty()) {
            totalProcessed += planStopeMonthlyMapper.deletePlanStopeMonthlyByIds(toDelete.toArray(new Long[0]));
        }

        return totalProcessed;
    }

    /**
     * 复制属性
     */
    private void copyProperties(PlanStopeMonthlyBatchDto source, PlanStopeMonthly target) {
        target.setPlanDate(source.getPlanMonth());
        target.setStopeId(source.getStopeId());
        target.setOreOutput(source.getOreOutput());
        if (StringUtils.isNotBlank(source.getRemark())) {
            target.setRemark(source.getRemark());
        }
    }
}
