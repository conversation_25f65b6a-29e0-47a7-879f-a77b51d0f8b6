package com.ruoyi.common.utils;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import com.ruoyi.common.core.domain.TableConfigInfo;
import com.ruoyi.common.core.domain.TableHeaderInfo;
import com.ruoyi.common.core.domain.TableMergeRule;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 表格配置处理器
 */
@Component
public class TableConfigProcessor {

    private static final Map<String, TableConfigInfo> TABLE_CONFIG_CACHE = new HashMap<>();

    /**
     * 处理表格配置
     */
    public TableConfigInfo processTableConfig(Class<?> clazz) {
        TableConfig tableConfig = clazz.getAnnotation(TableConfig.class);
        if (tableConfig == null) {
            throw new RuntimeException("类未标注@TableConfig注解");
        }

        String code = tableConfig.code();
        if (TABLE_CONFIG_CACHE.containsKey(code)) {
            return TABLE_CONFIG_CACHE.get(code);
        }

        TableConfigInfo configInfo = new TableConfigInfo();
        configInfo.setCode(code);
        configInfo.setName(tableConfig.name());
        configInfo.setDescription(tableConfig.description());

        // 处理表头
        List<TableHeaderInfo> headers = processHeaders(clazz);
        configInfo.setHeaders(headers);

        // 处理合并规则
        List<TableMergeRule> mergeRules = processMergeRules(clazz);
        configInfo.setMergeRules(mergeRules);

        TABLE_CONFIG_CACHE.put(code, configInfo);
        return configInfo;
    }

    /**
     * 处理表头配置
     */
    private List<TableHeaderInfo> processHeaders(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<HeaderField> headerFields = new ArrayList<>();

        for (Field field : fields) {
            TableHeader header = field.getAnnotation(TableHeader.class);
            if (header != null) {
                HeaderField headerField = new HeaderField();
                headerField.field = field;
                headerField.header = header;
                headerField.prop = header.prop().isEmpty() ? field.getName() : header.prop();
                headerFields.add(headerField);
            }
        }

        // 按order排序
        headerFields.sort(Comparator.comparingInt(h -> h.header.order()));

        return buildMultiLevelHeaderTree(headerFields);
    }

    /**
     * 构建多层表头树结构
     */
    private List<TableHeaderInfo> buildMultiLevelHeaderTree(List<HeaderField> headerFields) {
        // 用于存储所有节点的映射，key为路径，value为节点
        Map<String, TableHeaderInfo> nodeMap = new HashMap<>();
        List<TableHeaderInfo> roots = new ArrayList<>();

        for (HeaderField headerField : headerFields) {
            String[] parentPath = headerField.header.parentPath();
            String currentLabel = headerField.header.label();

            if (parentPath.length == 0) {
                // 根节点
                TableHeaderInfo headerInfo = createHeaderInfo(currentLabel, headerField.prop, headerField.header.width());
                roots.add(headerInfo);
                nodeMap.put(currentLabel, headerInfo);
            } else {
                // 子节点，需要构建完整路径
                String currentPath = "";
                TableHeaderInfo parent = null;

                // 逐级创建父节点
                for (int i = 0; i < parentPath.length; i++) {
                    String pathPart = parentPath[i];
                    currentPath = currentPath.isEmpty() ? pathPart : currentPath + "/" + pathPart;

                    TableHeaderInfo node = nodeMap.get(currentPath);
                    if (node == null) {
                        // 创建中间节点
                        node = createHeaderInfo(pathPart, null, 0);
                        nodeMap.put(currentPath, node);

                        if (i == 0) {
                            // 第一级，添加到根节点
                            roots.add(node);
                        } else {
                            // 添加到父节点
                            if (parent.getChildren() == null) {
                                parent.setChildren(new ArrayList<>());
                            }
                            parent.getChildren().add(node);
                        }
                    }
                    parent = node;
                }

                // 创建当前字段节点
                TableHeaderInfo currentNode = createHeaderInfo(currentLabel, headerField.prop, headerField.header.width());
                String fullPath = String.join("/", parentPath) + "/" + currentLabel;
                nodeMap.put(fullPath, currentNode);

                // 添加到最后一个父节点
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(currentNode);
            }
        }

        return roots;
    }

    /**
     * 创建表头信息对象
     */
    private TableHeaderInfo createHeaderInfo(String label, String prop, int width) {
        TableHeaderInfo headerInfo = new TableHeaderInfo();
        headerInfo.setLabel(label);
        headerInfo.setProp(prop);
        headerInfo.setWidth(width == 0 ? null : width);
        return headerInfo;
    }

    /**
     * 处理合并规则
     */
    private List<TableMergeRule> processMergeRules(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<TableMergeRule> mergeRules = new ArrayList<>();
        Map<String, List<String>> colMergeGroups = new HashMap<>();

        String prevColumn = null;

        for (Field field : fields) {
            TableHeader header = field.getAnnotation(TableHeader.class);
            if (header != null) {
                String prop = header.prop().isEmpty() ? field.getName() : header.prop();

                // 处理行合并
                if (header.enableRowMerge()) {
                    TableMergeRule rowRule = new TableMergeRule();
                    rowRule.setMergeType("row");
                    rowRule.setColumn(prop);
                    rowRule.setRelateColumn(header.relateColumn().isEmpty() ? prevColumn : header.relateColumn());
                    mergeRules.add(rowRule);
                }

                // 处理列合并分组 - 支持多个分组
                for (String groupName : header.colMergeGroup()) {
                    if (!groupName.isEmpty()) {
                        colMergeGroups.computeIfAbsent(groupName, k -> new ArrayList<>()).add(prop);
                    }
                }

                prevColumn = prop;
            }
        }

        // 添加列合并规则
        for (List<String> columns : colMergeGroups.values()) {
            if (columns.size() > 1) {
                TableMergeRule colRule = new TableMergeRule();
                colRule.setMergeType("col");
                colRule.setColumns(columns);
                mergeRules.add(colRule);
            }
        }

        return mergeRules;
    }

    /**
     * 获取表格配置
     */
    public TableConfigInfo getTableConfig(String code) {
        return TABLE_CONFIG_CACHE.get(code);
    }

    /**
     * 表头字段信息
     */
    private static class HeaderField {
        Field field;
        TableHeader header;
        String prop;
    }
}