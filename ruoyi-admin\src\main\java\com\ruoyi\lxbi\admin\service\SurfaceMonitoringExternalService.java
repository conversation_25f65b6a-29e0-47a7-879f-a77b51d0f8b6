package com.ruoyi.lxbi.admin.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.lxbi.admin.domain.dto.SurfaceMonitoringApiResponse;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 地表监测第三方API服务
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Slf4j
@Service
public class SurfaceMonitoringExternalService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * -- GETTER --
     *  获取API基础URL
     *
     * @return API基础URL
     */
    @Getter
    @Value("${surface.monitoring.api.base-url:http://10.10.22.24:19093/busgateway}")
    private String baseUrl;

    /**
     * -- GETTER --
     *  获取API超时配置
     *
     * @return 超时时间（毫秒）
     */
    @Getter
    @Value("${surface.monitoring.api.timeout:30000}")
    private int timeout;

    @Value("${surface.monitoring.api.token-cache-time:3600}")
    private int tokenCacheTime;

    // Redis缓存键
    private static final String SESSION_CACHE_KEY = "surface_monitoring_session_token";
    private static final String XTOKEN_CACHE_KEY = "surface_monitoring_xtoken";

    // 存储登录后的token（内存备份）
    private String sessionToken;
    private String xToken;

    /**
     * 获取认证信息（SESSION和xtoken）
     *
     * @return 认证信息Map，包含session和xtoken
     */
    public Map<String, String> getAuthInfo() {
        try {
            Map<String, String> authInfo = new HashMap<>();

            // 先从缓存中获取SESSION和xtoken
            String cachedSession = redisTemplate.opsForValue().get(SESSION_CACHE_KEY);
            String cachedXToken = redisTemplate.opsForValue().get(XTOKEN_CACHE_KEY);

            if (StringUtils.hasText(cachedSession)) {
                log.debug("从缓存中获取到地表监测SESSION token");
                this.sessionToken = cachedSession;
                this.xToken = cachedXToken; // xtoken可能为null

                authInfo.put("session", cachedSession);
                if (StringUtils.hasText(cachedXToken)) {
                    authInfo.put("xtoken", cachedXToken);
                }
                return authInfo;
            }

            // 缓存中没有token，重新登录获取
            Map<String, Object> loginResult = performLogin();

            if ((Boolean) loginResult.getOrDefault("success", false)) {
                String newSession = (String) loginResult.get("session");
                String newXToken = (String) loginResult.get("xtoken");

                if (newSession != null) {
                    // 将SESSION缓存到Redis
                    redisTemplate.opsForValue().set(SESSION_CACHE_KEY, newSession, tokenCacheTime, TimeUnit.SECONDS);
                    authInfo.put("session", newSession);

                    // 如果有xtoken也缓存
                    if (StringUtils.hasText(newXToken)) {
                        redisTemplate.opsForValue().set(XTOKEN_CACHE_KEY, newXToken, tokenCacheTime, TimeUnit.SECONDS);
                        authInfo.put("xtoken", newXToken);
                    }

                    log.info("地表监测系统登录成功，认证信息已缓存");
                    return authInfo;
                }
            }

            return authInfo;
        } catch (Exception e) {
            log.error("获取地表监测认证信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取SESSION token（向后兼容）
     *
     * @return SESSION token
     */
    public String getAuthToken() {
        Map<String, String> authInfo = getAuthInfo();
        return authInfo.get("session");
    }

    /**
     * 登录第三方系统获取认证信息
     *
     * @return 登录结果
     */
    public Map<String, Object> login() {
        Map<String, String> authInfo = getAuthInfo();
        Map<String, Object> result = new HashMap<>();

        if (!authInfo.isEmpty() && authInfo.containsKey("session")) {
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("session", authInfo.get("session"));
            if (authInfo.containsKey("xtoken")) {
                result.put("xtoken", authInfo.get("xtoken"));
            }
        } else {
            result.put("success", false);
            result.put("message", "登录失败");
        }

        return result;
    }

    /**
     * 执行实际的登录操作
     *
     * @return 登录结果
     */
    private Map<String, Object> performLogin() {
        Map<String, Object> result = new HashMap<>();

        try {
            String url = baseUrl + "/web/login";

            log.info("开始登录第三方地表监测系统: {}", url);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("User-Agent", "LXBI-System/1.0");

            // 构建登录请求体
            Map<String, String> loginRequest = new HashMap<>();
            loginRequest.put("yhm", "admin");
            loginRequest.put("mm", "Lxky.123");

            String requestBody = objectMapper.writeValueAsString(loginRequest);
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送登录请求
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.info("登录请求成功，响应状态: {}", response.getStatusCode());
                log.debug("登录响应内容: {}", responseBody);

                // 解析Set-Cookie响应头获取SESSION
                String sessionToken = extractSessionFromCookies(response.getHeaders());

                // 解析登录响应，提取xtoken（如果有）
                String xToken = null;
                try {
                    Map<String, Object> loginResponse = objectMapper.readValue(responseBody, Map.class);
                    if (loginResponse.containsKey("data")) {
                        Object data = loginResponse.get("data");
                        if (data instanceof String) {
                            xToken = (String) data;
                        } else if (data instanceof Map) {
                            Map<String, Object> dataMap = (Map<String, Object>) data;
                            if (dataMap.containsKey("xtoken")) {
                                xToken = (String) dataMap.get("xtoken");
                            }
                        }
                    }
                } catch (Exception e) {
                    log.debug("解析响应体中的xtoken失败，可能不包含xtoken: {}", e.getMessage());
                }

                if (sessionToken != null) {
                    // 保存SESSION和xtoken
                    this.sessionToken = sessionToken;
                    this.xToken = xToken;

                    result.put("success", true);
                    result.put("message", "登录成功");
                    result.put("session", sessionToken);
                    if (xToken != null) {
                        result.put("xtoken", xToken);
                    }

                    log.info("第三方系统登录成功，SESSION: {}, xtoken: {}",
                        sessionToken.substring(0, Math.min(10, sessionToken.length())) + "...",
                        xToken != null ? xToken.substring(0, Math.min(10, xToken.length())) + "..." : "无");
                } else {
                    result.put("success", false);
                    result.put("message", "登录响应中未找到SESSION cookie");
                    log.warn("登录响应中未找到SESSION cookie，响应头: {}", response.getHeaders());
                }

            } else {
                log.error("登录请求失败，HTTP状态码: {}", response.getStatusCode());
                result.put("success", false);
                result.put("message", "登录失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("登录第三方系统异常", e);
            result.put("success", false);
            result.put("message", "登录异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 从响应头中提取SESSION cookie
     *
     * @param headers 响应头
     * @return SESSION值，如果没有找到返回null
     */
    private String extractSessionFromCookies(HttpHeaders headers) {
        try {
            List<String> setCookieHeaders = headers.get("Set-Cookie");
            if (setCookieHeaders != null && !setCookieHeaders.isEmpty()) {
                for (String setCookie : setCookieHeaders) {
                    log.debug("处理Set-Cookie: {}", setCookie);

                    // 解析Set-Cookie头，查找SESSION
                    if (setCookie.startsWith("SESSION=")) {
                        // 提取SESSION值，格式: SESSION=xxxxxxxxx; Path=/; HttpOnly
                        String sessionValue = setCookie.substring("SESSION=".length());
                        int semicolonIndex = sessionValue.indexOf(';');
                        if (semicolonIndex > 0) {
                            sessionValue = sessionValue.substring(0, semicolonIndex);
                        }

                        log.info("从Set-Cookie中提取到SESSION: {}...",
                            sessionValue.substring(0, Math.min(10, sessionValue.length())));
                        return sessionValue;
                    }
                }
            }

            log.warn("响应头中未找到SESSION cookie");
            return null;
        } catch (Exception e) {
            log.error("解析Set-Cookie失败", e);
            return null;
        }
    }

    /**
     * 更新缓存的认证信息（当收到新的Set-Cookie时调用）
     *
     * @param headers 响应头
     */
    private void updateAuthInfoFromResponse(HttpHeaders headers) {
        try {
            String newSession = extractSessionFromCookies(headers);
            if (newSession != null && !newSession.equals(this.sessionToken)) {
                log.info("检测到新的SESSION，更新缓存");

                // 更新内存中的SESSION
                this.sessionToken = newSession;

                // 更新Redis缓存
                redisTemplate.opsForValue().set(SESSION_CACHE_KEY, newSession, tokenCacheTime, TimeUnit.SECONDS);

                log.info("SESSION已更新并缓存");
            }
        } catch (Exception e) {
            log.error("更新认证信息失败", e);
        }
    }

    /**
     * 查询七日基站偏移数据统计
     *
     * @return API响应结果
     */
    public SurfaceMonitoringApiResponse getLatest7DayMpptShiftingTotal() {
        try {
            String url = baseUrl + "/operate/selectLatest7DayMpptShiftingTotal";
            
            log.info("开始调用地表监测API: {}", url);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("User-Agent", "LXBI-System/1.0");

            // 获取认证信息（带缓存）
            Map<String, String> authInfo = getAuthInfo();
            if (!authInfo.isEmpty()) {
                // 添加SESSION cookie
                if (authInfo.containsKey("session")) {
                    headers.add("Cookie", "SESSION=" + authInfo.get("session"));
                }
                // 添加xtoken头（如果有）
                if (authInfo.containsKey("xtoken")) {
                    headers.add("xtoken", authInfo.get("xtoken"));
                }
            }
            
            // 创建请求实体（POST请求，但无请求体）
            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.info("地表监测API调用成功，响应状态: {}", response.getStatusCode());
                log.debug("地表监测API响应内容: {}", responseBody);

                // 检查响应头中是否有新的Set-Cookie，如果有则更新缓存
                updateAuthInfoFromResponse(response.getHeaders());

                // 解析响应
                SurfaceMonitoringApiResponse apiResponse = objectMapper.readValue(responseBody, SurfaceMonitoringApiResponse.class);
                
                if (apiResponse.getResult() != null && apiResponse.getResult()) {
                    log.info("地表监测API返回成功，数据条数: {}", 
                        apiResponse.getData() != null ? apiResponse.getData().size() : 0);
                } else {
                    log.warn("地表监测API返回失败，消息: {}", apiResponse.getMessage());
                }
                
                return apiResponse;
            } else {
                log.error("地表监测API调用失败，HTTP状态码: {}", response.getStatusCode());

                // 如果是401未授权，清除缓存的认证信息
                if (response.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    log.warn("认证失败，清除缓存的认证信息");
                    redisTemplate.delete(SESSION_CACHE_KEY);
                    redisTemplate.delete(XTOKEN_CACHE_KEY);
                    this.sessionToken = null;
                    this.xToken = null;
                }

                return createErrorResponse("HTTP请求失败，状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("调用地表监测API异常", e);
            return createErrorResponse("API调用异常: " + e.getMessage());
        }
    }

    /**
     * 测试API连接
     * 
     * @return 连接测试结果
     */
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始测试地表监测API连接");
            
            SurfaceMonitoringApiResponse response = getLatest7DayMpptShiftingTotal();
            
            if (response != null && response.getResult() != null && response.getResult()) {
                result.put("success", true);
                result.put("message", "API连接测试成功");
                result.put("dataCount", response.getData() != null ? response.getData().size() : 0);
                result.put("apiMessage", response.getMessage());
            } else {
                result.put("success", false);
                result.put("message", "API连接测试失败");
                result.put("error", response != null ? response.getMessage() : "响应为空");
            }
            
        } catch (Exception e) {
            log.error("测试地表监测API连接异常", e);
            result.put("success", false);
            result.put("message", "API连接测试异常");
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取API健康状态
     * 
     * @return 健康状态信息
     */
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 调用API测试连接
            SurfaceMonitoringApiResponse response = getLatest7DayMpptShiftingTotal();
            
            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;
            
            boolean isHealthy = response != null && response.getResult() != null && response.getResult();
            
            status.put("healthy", isHealthy);
            status.put("responseTime", responseTime);
            status.put("baseUrl", baseUrl);
            status.put("timeout", timeout);
            status.put("checkTime", new java.util.Date());
            
            if (isHealthy) {
                status.put("message", "地表监测API服务正常");
                status.put("dataCount", response.getData() != null ? response.getData().size() : 0);
            } else {
                status.put("message", "地表监测API服务异常");
                status.put("error", response != null ? response.getMessage() : "响应异常");
            }
            
        } catch (Exception e) {
            log.error("获取地表监测API健康状态异常", e);
            status.put("healthy", false);
            status.put("message", "健康检查异常");
            status.put("error", e.getMessage());
            status.put("checkTime", new java.util.Date());
        }
        
        return status;
    }

    /**
     * 创建错误响应
     * 
     * @param errorMessage 错误消息
     * @return 错误响应对象
     */
    private SurfaceMonitoringApiResponse createErrorResponse(String errorMessage) {
        SurfaceMonitoringApiResponse response = new SurfaceMonitoringApiResponse();
        response.setResult(false);
        response.setMessage(errorMessage);
        response.setData(null);
        return response;
    }

}
