package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 掘进月计划对象 plan_drift_monthly
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanDriftMonthly extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 工作面ID */
    private Long workingFaceId;

    /** 采场ID */
    private Long stopeId;

    /** 掘进米数 */
    @Excel(name = "掘进米数")
    private BigDecimal driftMeter;

    /** 掘进方量 */
    @Excel(name = "掘进方量")
    private BigDecimal driftVolume;

    /** 计划月份 */
    @Excel(name = "计划月份", sort = 1, mergeByValue = true)
    private String planDate;

}
