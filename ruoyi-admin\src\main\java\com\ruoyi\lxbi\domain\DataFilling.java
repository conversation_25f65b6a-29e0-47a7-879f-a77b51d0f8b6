package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 充填数据对象 data_filling
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataFilling extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 充填数据ID */
    private Long id;

    /** 作业日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "作业日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /** 项目部门ID */
    @Excel(name = "项目部门ID")
    private Long projectDepartmentId;

    /** 采场ID */
    @Excel(name = "采场ID")
    private Long stopeId;

    /** 作业时段ID */
    @Excel(name = "作业时段ID")
    private Long workingPeriodId;

    /** 生产料浆量(m³) */
    @Excel(name = "生产料浆量(m³)")
    private BigDecimal slurryVolume;

    /** 胶固粉量(t) */
    @Excel(name = "胶固粉量(t)")
    private BigDecimal cementWeight;

    /** 充填比例 */
    @Excel(name = "充填比例")
    private String fillingRatio;

    /** 充填浓度(%) */
    @Excel(name = "充填浓度(%)")
    private BigDecimal fillingConcentration;

    /** 首冲时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首冲时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstFillingTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endFillingTime;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

}
