package com.ruoyi.common.utils.excel;

import cn.idev.excel.write.handler.RowWriteHandler;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteTableHolder;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

@RequiredArgsConstructor
public class ExcelErrorWriteHandler implements RowWriteHandler {

    private final List<String> messages;

    private int columnIndex = -1;

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        CellStyle cellStyle = writeSheetHolder.getSheet().getWorkbook().createCellStyle();
        Font font = writeSheetHolder.getSheet().getWorkbook().createFont();
        font.setColor(IndexedColors.RED.getIndex());
        cellStyle.setFont(font);
        // 设置为文本格式
        DataFormat dataFormat = writeSheetHolder.getSheet().getWorkbook().createDataFormat();
        cellStyle.setDataFormat(dataFormat.getFormat("@"));

        int currentRowIndex = 0;
        if (isHead && columnIndex == -1) {
            columnIndex = row.getLastCellNum();
            Cell cell = row.createCell(columnIndex);
            cell.setCellValue("错误信息");
            cell.setCellStyle(cellStyle);
        } else if (currentRowIndex < messages.size()) {
            int lastCellNum = row.getLastCellNum() == -1 ? 0 : row.getLastCellNum();
            Cell cell = row.createCell(lastCellNum);
            cell.setCellValue(messages.get(currentRowIndex));
            cell.setCellStyle(cellStyle);
        }
    }
}
