package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 趋势统计响应VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrendResponseVO {
    
    /**
     * 统计周期
     */
    private String period;
    
    /**
     * 开始日期
     */
    private String startDate;
    
    /**
     * 结束日期
     */
    private String endDate;
    
    /**
     * 趋势数据列表
     */
    private List<TrendStatisticsVO> data;
}
