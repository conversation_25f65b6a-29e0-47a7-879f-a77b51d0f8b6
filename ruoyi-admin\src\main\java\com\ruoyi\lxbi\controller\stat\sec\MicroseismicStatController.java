package com.ruoyi.lxbi.controller.stat.sec;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.admin.service.IMicroseismicStatService;
import com.ruoyi.lxbi.domain.vo.*;
import com.ruoyi.lxbi.utils.DateRangeCalculator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 微震统计Controller
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@RestController
@RequestMapping("/sec/stat/microseismic")
public class MicroseismicStatController {

    @Autowired
    private IMicroseismicStatService microseismicStatService;

    /**
     * 获取微震概览统计
     */
    @Anonymous
    @GetMapping("/overview")
    public R<MicroseismicOverviewVO> getOverviewStatistics(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        MicroseismicOverviewVO overview = microseismicStatService.getOverviewStatistics(viewType, startDate, endDate);
        return R.ok(overview);
    }



    /**
     * 获取微震事件分布统计
     */
    @Anonymous
    @GetMapping("/event-distribution")
    public R<List<MicroseismicEventDistributionVO>> getEventDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<MicroseismicEventDistributionVO> distribution = microseismicStatService.getEventDistribution(viewType, startDate, endDate);
        return R.ok(distribution);
    }

    /**
     * 获取微震散点图数据
     */
    @Anonymous
    @GetMapping("/scatter-data")
    public R<List<MicroseismicScatterDataVO>> getScatterData(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<MicroseismicScatterDataVO> scatterData = microseismicStatService.getScatterData(viewType, startDate, endDate);
        return R.ok(scatterData);
    }

    /**
     * 获取微震P波/S波能量占比统计
     */
    @Anonymous
    @GetMapping("/energy-ratio")
    public R<MicroseismicEnergyRatioVO> getEnergyRatio(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        MicroseismicEnergyRatioVO energyRatio = microseismicStatService.getEnergyRatio(viewType, startDate, endDate);
        return R.ok(energyRatio);
    }
}
