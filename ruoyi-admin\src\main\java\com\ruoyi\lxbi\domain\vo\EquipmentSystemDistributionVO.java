package com.ruoyi.lxbi.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备系统分布VO (雷达图数据)
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentSystemDistributionVO {

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 故障设备数量
     */
    private Long faultCount;

    /**
     * 区域类型 (采场/选厂)
     */
    private String areaType;

    /**
     * 区域类型代码
     */
    private String areaTypeCode;


}
