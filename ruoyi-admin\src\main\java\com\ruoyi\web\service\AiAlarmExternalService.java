package com.ruoyi.web.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.framework.config.ExternalApiProperties;
import com.ruoyi.lxbi.domain.vo.AiAlarmDataVO;
import com.ruoyi.lxbi.domain.vo.AiAlarmResponseVO;
import com.ruoyi.lxbi.domain.vo.AiLoginResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * AI平台报警数据对接服务
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class AiAlarmExternalService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ExternalApiProperties externalApiProperties;

    // Token缓存相关字段
    private String cachedAccessToken;
    private LocalDateTime tokenExpireTime;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String TOKEN_CACHE_KEY = "ai_alarm_access_token";

    /**
     * 获取访问令牌
     * 
     * @return 访问令牌
     */
    public String getAccessToken() {
        try {
            // 检查API是否启用
            if (!externalApiProperties.getAiAlarm().getEnabled()) {
                log.warn("AI平台报警系统API未启用");
                return null;
            }

            // 先从缓存中获取token
            String cachedToken = redisTemplate.opsForValue().get(TOKEN_CACHE_KEY);
            if (StringUtils.hasText(cachedToken)) {
                log.debug("从缓存中获取到访问令牌");
                return cachedToken;
            }

            String loginUrl = externalApiProperties.getAiAlarm().getLoginUrl();
            log.info("调用AI平台登录接口: {}", loginUrl);

            // 构建登录请求参数（使用表单格式，不是JSON）
            String account = externalApiProperties.getAiAlarm().getAccount();
            String password = externalApiProperties.getAiAlarm().getPassword();
            String requestBody = "account=" + account + "&password=" + password;

            log.info("登录请求参数: account={}", account);

            // 设置请求头（使用表单格式）
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.add("Accept", MediaType.APPLICATION_JSON_VALUE);
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                loginUrl, 
                HttpMethod.POST, 
                entity, 
                String.class
            );

            log.info("登录API响应状态: {}", response.getStatusCode());
            log.debug("登录API响应内容: {}", response.getBody());

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                AiLoginResponseVO loginResponse = objectMapper.readValue(response.getBody(), AiLoginResponseVO.class);
                
                if (loginResponse.getData() != null && StringUtils.hasText(loginResponse.getData().getAccessToken())) {
                    String accessToken = loginResponse.getData().getAccessToken();
                    
                    // 将token缓存到Redis
                    redisTemplate.opsForValue().set(TOKEN_CACHE_KEY, accessToken, 
                            externalApiProperties.getAiAlarm().getTokenCacheTime(), TimeUnit.SECONDS);
                    
                    log.info("AI平台登录成功，获取到访问令牌");
                    return accessToken;
                } else {
                    log.error("登录响应中未找到访问令牌");
                    return null;
                }
            } else {
                log.error("AI平台登录失败，状态码: {}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("获取AI平台访问令牌失败", e);
            return null;
        }
    }

    /**
     * 登录AI报警系统
     *
     * @return 登录是否成功
     */
    public boolean login() {
        try {
            String accessToken = getAccessToken();
            return accessToken != null && !accessToken.isEmpty();
        } catch (Exception e) {
            log.error("登录AI报警系统失败", e);
            return false;
        }
    }

    /**
     * 获取报警数据
     * 
     * @param pageNum 页数
     * @param pageSize 每页数据长度
     * @param filterDateTime 时间范围（可选）
     * @param filterAlgorithmList 根据算法筛选（可选）
     * @return 报警数据响应
     */
    public AiAlarmResponseVO getAlarmData(Integer pageNum, Integer pageSize, 
                                         String filterDateTime, String filterAlgorithmList) {
        try {
            // 检查API是否启用
            if (!externalApiProperties.getAiAlarm().getEnabled()) {
                log.warn("AI平台报警系统API未启用");
                return null;
            }

            // 获取访问令牌
            String accessToken = getAccessToken();
            if (!StringUtils.hasText(accessToken)) {
                log.error("无法获取访问令牌，无法调用报警数据接口");
                return null;
            }

            String alarmUrl = externalApiProperties.getAiAlarm().getAlarmUrl();
            log.info("调用AI平台报警数据接口: {}", alarmUrl);

            // 构建请求参数
            StringBuilder urlBuilder = new StringBuilder(alarmUrl);
            urlBuilder.append("?pageNum=").append(pageNum != null ? pageNum : 1);
            urlBuilder.append("&pageSize=").append(pageSize != null ? pageSize : 10);
            
            if (StringUtils.hasText(filterDateTime)) {
                urlBuilder.append("&filterDateTime=").append(filterDateTime);
            }
            
            if (StringUtils.hasText(filterAlgorithmList)) {
                urlBuilder.append("&filterAlgorithmList=").append(filterAlgorithmList);
            }

            String finalUrl = urlBuilder.toString();
            log.info("请求URL: {}", finalUrl);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", accessToken);

            HttpEntity<Void> entity = new HttpEntity<>(headers);

            // 发送GET请求
            ResponseEntity<String> response = restTemplate.exchange(
                finalUrl, 
                HttpMethod.GET, 
                entity, 
                String.class
            );

            log.info("报警数据API响应状态: {}", response.getStatusCode());
            log.debug("报警数据API响应内容: {}", response.getBody());

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                AiAlarmResponseVO alarmResponse = objectMapper.readValue(response.getBody(), AiAlarmResponseVO.class);
                
                // 处理图片URL，添加基础地址
                if (alarmResponse.getData() != null && alarmResponse.getData().getAlarmDataList() != null) {
                    String baseUrl = externalApiProperties.getAiAlarm().getBaseUrl();
                    for (AiAlarmDataVO alarmData : alarmResponse.getData().getAlarmDataList()) {
                        if (StringUtils.hasText(alarmData.getAlarmImg()) && !alarmData.getAlarmImg().startsWith("http")) {
                            alarmData.setAlarmImg(alarmData.getFullAlarmImgUrl(baseUrl));
                        }
                    }
                }
                
                log.info("AI平台报警数据获取成功，共{}条记录", 
                        alarmResponse.getData() != null ? alarmResponse.getData().getTotal() : 0);
                return alarmResponse;
            } else {
                log.error("AI平台报警数据API调用失败，状态码: {}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("调用AI平台报警数据接口失败", e);
            return null;
        }
    }

    /**
     * 获取报警数据列表（简化版本）
     * 
     * @param pageNum 页数
     * @param pageSize 每页数据长度
     * @return 报警数据列表
     */
    public List<AiAlarmDataVO> getAlarmDataList(Integer pageNum, Integer pageSize) {
        AiAlarmResponseVO response = getAlarmData(pageNum, pageSize, null, null);
        if (response != null && response.getData() != null) {
            return response.getData().getAlarmDataList();
        }
        return null;
    }

    /**
     * 根据算法类型获取报警数据
     * 
     * @param pageNum 页数
     * @param pageSize 每页数据长度
     * @param algorithmTypes 算法类型列表（1为火焰烟雾检测、2为安全帽检测、10为离岗检测、24为自救器检测）
     * @return 报警数据列表
     */
    public List<AiAlarmDataVO> getAlarmDataByAlgorithm(Integer pageNum, Integer pageSize, String algorithmTypes) {
        AiAlarmResponseVO response = getAlarmData(pageNum, pageSize, null, algorithmTypes);
        if (response != null && response.getData() != null) {
            return response.getData().getAlarmDataList();
        }
        return null;
    }

    /**
     * 根据时间范围获取报警数据
     *
     * @param pageNum 页数
     * @param pageSize 每页数据长度
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报警数据列表
     */
    public List<AiAlarmDataVO> getAlarmDataByTimeRange(Integer pageNum, Integer pageSize,
                                                      String startTime, String endTime) {
        String filterDateTime = startTime + "--" + endTime;
        AiAlarmResponseVO response = getAlarmData(pageNum, pageSize, filterDateTime, null);
        if (response != null && response.getData() != null) {
            return response.getData().getAlarmDataList();
        }
        return null;
    }

    /**
     * 根据日期范围获取报警数据（用于数据同步）
     *
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 报警数据列表（Map格式，便于数据转换）
     */
    public List<Map<String, Object>> getAlarmData(String startDate, String endDate) {
        try {
            log.info("获取AI报警数据，日期范围: {} - {}", startDate, endDate);

            // 转换日期格式为API需要的格式
            String startTime = startDate + " 00:00:00";
            String endTime = endDate + " 23:59:59";

            List<Map<String, Object>> allAlarmData = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 100; // 每页100条数据

            while (true) {
                // 获取分页数据
                List<AiAlarmDataVO> pageData = getAlarmDataByTimeRange(pageNum, pageSize, startTime, endTime);

                if (pageData == null || pageData.isEmpty()) {
                    break; // 没有更多数据
                }

                // 转换为Map格式
                for (AiAlarmDataVO alarmData : pageData) {
                    Map<String, Object> alarmMap = convertAlarmDataToMap(alarmData);
                    allAlarmData.add(alarmMap);
                }

                // 如果返回的数据少于pageSize，说明已经是最后一页
                if (pageData.size() < pageSize) {
                    break;
                }

                pageNum++;
            }

            log.info("获取AI报警数据完成，共 {} 条数据", allAlarmData.size());
            return allAlarmData;

        } catch (Exception e) {
            log.error("获取AI报警数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 将AiAlarmDataVO转换为Map格式
     */
    private Map<String, Object> convertAlarmDataToMap(AiAlarmDataVO alarmData) {
        Map<String, Object> map = new HashMap<>();

        try {
            // 使用现有字段生成ID
            String generatedId = generateAlarmId(alarmData);
            map.put("id", generatedId);

            // 根据算法名称确定报警类型
            String alarmType = determineAlarmTypeFromAlgorithm(alarmData.getAlgorithmName());
            map.put("alarm_type", alarmType);
            map.put("alarm_level", determineAlarmLevel(alarmType));
            map.put("alarm_status", "ACTIVE");
            map.put("alarm_title", generateAlarmTitle(alarmType));
            map.put("alarm_description", "AI检测到" + alarmData.getAlgorithmName() + "异常");

            // 时间信息
            map.put("alarm_time", alarmData.getAlarmTime());
            map.put("create_time", alarmData.getAlarmTime());
            map.put("update_time", alarmData.getAlarmTime());

            // 位置信息
            map.put("location_name", alarmData.getSceneName());
            map.put("location_code", alarmData.getStreamName());

            // 设备信息
            map.put("device_id", alarmData.getStreamName());
            map.put("device_name", alarmData.getStreamName());
            map.put("device_type", "AI_CAMERA");

            // 媒体信息
            map.put("image_url", alarmData.getAlarmImg());
            map.put("video_url", ""); // 当前VO中没有视频字段

            // 添加原始数据
            map.put("raw_alarm_data", alarmData);

        } catch (Exception e) {
            log.warn("转换报警数据失败: {}", alarmData, e);
        }

        return map;
    }

    /**
     * 生成报警ID
     */
    private String generateAlarmId(AiAlarmDataVO alarmData) {
        // 使用时间戳和流名称生成唯一ID
        String timestamp = alarmData.getAlarmTime() != null ? alarmData.getAlarmTime().replaceAll("[^0-9]", "") : "";
        String streamName = alarmData.getStreamName() != null ? alarmData.getStreamName() : "unknown";
        return "AI_" + streamName + "_" + timestamp;
    }

    /**
     * 根据算法名称确定报警类型
     */
    private String determineAlarmTypeFromAlgorithm(String algorithmName) {
        if (algorithmName == null) return "UNKNOWN";

        String name = algorithmName.toUpperCase();
        if (name.contains("火") || name.contains("烟") || name.contains("FIRE") || name.contains("SMOKE")) {
            return "FIRE_SMOKE";
        } else if (name.contains("安全帽") || name.contains("HELMET")) {
            return "HELMET_VIOLATION";
        } else if (name.contains("离岗") || name.contains("ABSENCE")) {
            return "PERSONNEL_ABSENCE";
        } else if (name.contains("自救器") || name.contains("RESCUE")) {
            return "RESCUE_DEVICE";
        } else if (name.contains("人员") || name.contains("PERSONNEL")) {
            return "PERSONNEL_VIOLATION";
        } else {
            return "AI_DETECTION";
        }
    }

    /**
     * 根据报警类型确定报警等级
     */
    private String determineAlarmLevel(String alarmType) {
        if (alarmType == null) return "MEDIUM";

        switch (alarmType.toUpperCase()) {
            case "FIRE_SMOKE": // 火焰烟雾检测
                return "HIGH";
            case "HELMET_VIOLATION": // 安全帽检测
                return "MEDIUM";
            case "PERSONNEL_ABSENCE": // 离岗检测
                return "MEDIUM";
            case "RESCUE_DEVICE": // 自救器检测
                return "HIGH";
            case "PERSONNEL_VIOLATION": // 人员违规
                return "MEDIUM";
            default:
                return "MEDIUM";
        }
    }

    /**
     * 根据报警类型生成报警标题
     */
    private String generateAlarmTitle(String alarmType) {
        if (alarmType == null) return "AI报警";

        switch (alarmType.toUpperCase()) {
            case "FIRE_SMOKE":
                return "火焰烟雾检测报警";
            case "HELMET_VIOLATION":
                return "安全帽检测报警";
            case "PERSONNEL_ABSENCE":
                return "离岗检测报警";
            case "RESCUE_DEVICE":
                return "自救器检测报警";
            case "PERSONNEL_VIOLATION":
                return "人员违规报警";
            default:
                return "AI检测报警";
        }
    }

    /**
     * 测试AI平台API连接
     * 
     * @return 测试结果
     */
    public boolean testConnection() {
        try {
            log.info("测试AI平台报警系统API连接");
            String accessToken = getAccessToken();
            boolean isSuccess = StringUtils.hasText(accessToken);
            log.info("AI平台API连接测试结果: {}", isSuccess ? "成功" : "失败");
            return isSuccess;
        } catch (Exception e) {
            log.error("AI平台API连接测试失败", e);
            return false;
        }
    }

    /**
     * 清除缓存的访问令牌
     */
    public void clearCachedToken() {
        try {
            redisTemplate.delete(TOKEN_CACHE_KEY);
            log.info("已清除缓存的访问令牌");
        } catch (Exception e) {
            log.error("清除缓存的访问令牌失败", e);
        }
    }
}
