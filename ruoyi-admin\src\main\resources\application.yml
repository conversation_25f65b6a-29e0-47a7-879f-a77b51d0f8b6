# 项目相关配置
ruoyi:
  # 名称
  name: LXBI
  # 版本
  version: 0.1.0
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: data
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 第三方API配置
external:
  api:
    # 隐患管理系统API配置
    hidden-trouble:
      # API基础地址
      base-url: http://***************:10001
      # 统计接口路径
      statistics-path: /api/defense/homepagerule/statistics
      # 列表接口路径
      statistics-page-path: /api/defense/homepagerule/statisticsPage
      # 连接超时时间（毫秒）
      connect-timeout: 30000
      # 读取超时时间（毫秒）
      read-timeout: 60000
      # 是否启用
      enabled: true
    # AI平台报警系统API配置
    ai-alarm:
      # API基础地址
      base-url: http://10.10.30.51:3001
      # 登录接口路径
      login-path: /api/login/
      # 报警数据接口路径
      alarm-path: /api/alarm/
      # 用户名
      account: api
      # 密码
      password: 123456
      # 连接超时时间（毫秒）
      connect-timeout: 30000
      # 读取超时时间（毫秒）
      read-timeout: 60000
      # 是否启用
      enabled: true
      # Token缓存时间（秒）
      token-cache-time: 3600

# 地表监测系统配置
surface:
  monitoring:
    api:
      # API基础URL
      base-url: http://10.10.22.24:19093/busgateway
      # 超时时间（毫秒）
      timeout: 30000
      # Token缓存时间（秒）- 设置为1小时
      token-cache-time: 3600

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  data:
    # redis 配置
    redis:
      # 地址
      host: **********
      # 端口，默认为6379
      port: 6789
      # 数据库索引
      database: 0
      # 密码
      password:
      # 连接超时时间
      timeout: 30s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
  kafka:
    bootstrap-servers: ************:9092,************:9092,************:9092
    consumer:
      group-id: lxbi-consumer-test2
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: false  # 禁用自动提交，使用手动提交
      max-poll-records: 100
      session-timeout-ms: 300000  # 会话超时5分钟
      heartbeat-interval-ms: 10000  # 心跳间隔10秒
      request-timeout-ms: 300000  # 请求超时5分钟
      max-poll-interval-ms: 300000  # 最大轮询间隔5分钟

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 10080

# MyBatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.ruoyi.**.domain
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath*:mapper/**/*Mapper.xml
#  # 加载全局的配置文件
#  configLocation: classpath:mybatis/mybatis-config.xml
#

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml


# PageHelper分页插件
pagehelper:
  helperDialect: postgresql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Springdoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
  group-configs:
    - group: 'default'
      display-name: '测试模块'
      paths-to-match: '/**'
      packages-to-scan: com.ruoyi.lxbi.controller

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*


# MQTT配置 - 支持多个broker连接
mqtt:
  # 多个MQTT连接配置
  connections:
    # 监测监控 - 10.10.22.103:1883
    monitoring:
      broker-url: tcp://10.10.22.103:1883
      client-id: lxbi-monitoring-client
      topics:
        - test_ftp
    # 人员定位 - 10.10.22.103:1883
    personnel-location:
      broker-url: tcp://10.10.22.103:1883
      client-id: lxbi-personnel-client
      topics:
        - pp
    # 额外人员定位 - ************:1883
    extra-personnel:
      broker-url: tcp://************:1883
      client-id: lxbi-extra-personnel-client
      topics:
        - extra_pp
    # 斜坡道车辆 - 10.10.22.104:1883
    vehicle:
      broker-url: tcp://10.10.22.104:1883
      client-id: lxbi-vehicle-client
      topics:
        - vp
  # 通用配置
  common:
    connection-timeout: 30
    keep-alive-interval: 60
    automatic-reconnect: true
    clean-session: true
    username:
    password:
