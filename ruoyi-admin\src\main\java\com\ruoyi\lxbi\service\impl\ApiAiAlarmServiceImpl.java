package com.ruoyi.lxbi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.lxbi.domain.ApiAiAlarm;
import com.ruoyi.lxbi.mapper.ApiAiAlarmMapper;
import com.ruoyi.lxbi.service.IApiAiAlarmService;
import com.ruoyi.web.service.AiAlarmExternalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * AI报警数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
@Service
public class ApiAiAlarmServiceImpl extends ServiceImpl<ApiAiAlarmMapper, ApiAiAlarm> implements IApiAiAlarmService {

    @Autowired
    private ApiAiAlarmMapper apiAiAlarmMapper;

    @Autowired
    private AiAlarmExternalService aiAlarmExternalService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 同步AI报警数据（手动同步）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncAiAlarmData(String startDate, String endDate) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始手动同步AI报警数据，时间范围: {} - {}", startDate, endDate);

            // 调用第三方API获取报警数据
            List<Map<String, Object>> alarmData = aiAlarmExternalService.getAlarmData(startDate, endDate);
            
            if (alarmData == null || alarmData.isEmpty()) {
                result.put("success", true);
                result.put("message", "指定时间范围内无报警数据");
                result.put("totalCount", 0);
                return result;
            }
            
            // 批量保存数据
            Map<String, Object> saveResult = batchSaveAlarms(alarmData, "MANUAL");
            
            result.put("success", true);
            result.put("message", "手动同步完成");
            result.put("syncTime", LocalDateTime.now());
            result.putAll(saveResult);
            
            log.info("手动同步AI报警数据完成: {}", saveResult);
            
        } catch (Exception e) {
            log.error("手动同步AI报警数据失败", e);
            result.put("success", false);
            result.put("message", "同步异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 同步前一天的AI报警数据（定时任务使用）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncPreviousDayData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 计算前一天的日期
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            String startDate = yesterday.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endDate = startDate; // 只同步前一天的数据
            
            log.info("开始定时同步前一天AI报警数据: {}", startDate);
            
            // 先登录获取token
            boolean loginSuccess = aiAlarmExternalService.login();
            if (!loginSuccess) {
                result.put("success", false);
                result.put("message", "登录AI报警系统失败");
                return result;
            }
            
            // 调用第三方API获取报警数据
            List<Map<String, Object>> alarmData = aiAlarmExternalService.getAlarmData(startDate, endDate);
            
            if (alarmData == null || alarmData.isEmpty()) {
                result.put("success", true);
                result.put("message", "前一天无报警数据");
                result.put("totalCount", 0);
                return result;
            }
            
            // 批量保存数据
            Map<String, Object> saveResult = batchSaveAlarms(alarmData, "AUTO");
            
            result.put("success", true);
            result.put("message", "定时同步完成");
            result.put("syncDate", startDate);
            result.put("syncTime", LocalDateTime.now());
            result.putAll(saveResult);
            
            log.info("定时同步前一天AI报警数据完成: {}", saveResult);
            
        } catch (Exception e) {
            log.error("定时同步前一天AI报警数据失败", e);
            result.put("success", false);
            result.put("message", "同步异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 根据外部ID查询报警数据
     */
    @Override
    public ApiAiAlarm getByExternalId(String externalId) {
        return apiAiAlarmMapper.selectByExternalId(externalId);
    }

    /**
     * 根据时间范围查询报警数据
     */
    @Override
    public List<ApiAiAlarm> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return apiAiAlarmMapper.selectByTimeRange(startTime, endTime);
    }

    /**
     * 根据报警类型查询数据
     */
    @Override
    public List<ApiAiAlarm> getByAlarmType(String alarmType) {
        return apiAiAlarmMapper.selectByAlarmType(alarmType);
    }

    /**
     * 根据设备ID查询数据
     */
    @Override
    public List<ApiAiAlarm> getByDeviceId(String deviceId) {
        return apiAiAlarmMapper.selectByDeviceId(deviceId);
    }

    /**
     * 批量保存报警数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchSaveAlarms(List<Map<String, Object>> alarmList, String syncSource) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int totalCount = alarmList.size();
            int successCount = 0;
            int updateCount = 0;
            int insertCount = 0;
            List<String> errorMessages = new ArrayList<>();
            
            LocalDateTime syncTime = LocalDateTime.now();
            
            for (Map<String, Object> alarmData : alarmList) {
                try {
                    // 转换数据为实体对象
                    ApiAiAlarm alarm = convertToEntity(alarmData, syncSource, syncTime);
                    
                    // 检查是否已存在
                    ApiAiAlarm existingAlarm = getByExternalId(alarm.getExternalId());
                    
                    if (existingAlarm != null) {
                        // 更新现有数据
                        alarm.setId(existingAlarm.getId());
                        alarm.setUpdatedAt(syncTime);
                        updateById(alarm);
                        updateCount++;
                    } else {
                        // 插入新数据
                        alarm.setCreatedAt(syncTime);
                        alarm.setUpdatedAt(syncTime);
                        save(alarm);
                        insertCount++;
                    }
                    
                    successCount++;
                    
                } catch (Exception e) {
                    log.error("保存单条报警数据失败: {}", alarmData, e);
                    errorMessages.add("数据ID: " + alarmData.get("id") + ", 错误: " + e.getMessage());
                }
            }
            
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("insertCount", insertCount);
            result.put("updateCount", updateCount);
            result.put("failureCount", totalCount - successCount);
            result.put("errorMessages", errorMessages);
            
            log.info("批量保存AI报警数据完成 - 总数: {}, 成功: {}, 新增: {}, 更新: {}, 失败: {}", 
                totalCount, successCount, insertCount, updateCount, totalCount - successCount);
            
        } catch (Exception e) {
            log.error("批量保存AI报警数据异常", e);
            result.put("success", false);
            result.put("message", "批量保存异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 统计指定时间范围内的报警数量
     */
    @Override
    public Long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return apiAiAlarmMapper.countByTimeRange(startTime, endTime);
    }

    /**
     * 清理过期数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> cleanExpiredData(int retentionDays) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);
            int deletedCount = apiAiAlarmMapper.deleteBeforeTime(cutoffTime);
            
            result.put("success", true);
            result.put("deletedCount", deletedCount);
            result.put("cutoffTime", cutoffTime);
            result.put("message", "清理完成，删除 " + deletedCount + " 条过期数据");
            
            log.info("清理过期AI报警数据完成，删除 {} 条数据，截止时间: {}", deletedCount, cutoffTime);
            
        } catch (Exception e) {
            log.error("清理过期AI报警数据失败", e);
            result.put("success", false);
            result.put("message", "清理异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取同步状态统计
     */
    @Override
    public Map<String, Object> getSyncStatusStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 统计各种状态的数量
            LambdaQueryWrapper<ApiAiAlarm> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ApiAiAlarm::getIsDeleted, false);
            
            long totalCount = count(wrapper);
            
            wrapper.eq(ApiAiAlarm::getSyncStatus, "SUCCESS");
            long successCount = count(wrapper);
            
            wrapper.clear();
            wrapper.eq(ApiAiAlarm::getIsDeleted, false);
            wrapper.eq(ApiAiAlarm::getSyncStatus, "FAILED");
            long failedCount = count(wrapper);
            
            wrapper.clear();
            wrapper.eq(ApiAiAlarm::getIsDeleted, false);
            wrapper.eq(ApiAiAlarm::getSyncSource, "AUTO");
            long autoSyncCount = count(wrapper);
            
            wrapper.clear();
            wrapper.eq(ApiAiAlarm::getIsDeleted, false);
            wrapper.eq(ApiAiAlarm::getSyncSource, "MANUAL");
            long manualSyncCount = count(wrapper);
            
            LocalDateTime latestSyncTime = getLatestSyncTime();
            
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("autoSyncCount", autoSyncCount);
            result.put("manualSyncCount", manualSyncCount);
            result.put("latestSyncTime", latestSyncTime);
            
        } catch (Exception e) {
            log.error("获取同步状态统计失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取最新的同步时间
     */
    @Override
    public LocalDateTime getLatestSyncTime() {
        return apiAiAlarmMapper.getLatestSyncTime();
    }

    /**
     * 重新同步失败的数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> resyncFailedData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询失败的数据
            List<ApiAiAlarm> failedAlarms = apiAiAlarmMapper.selectBySyncStatus("FAILED");
            
            if (failedAlarms.isEmpty()) {
                result.put("success", true);
                result.put("message", "无失败数据需要重新同步");
                result.put("totalCount", 0);
                return result;
            }
            
            int successCount = 0;
            LocalDateTime syncTime = LocalDateTime.now();
            
            for (ApiAiAlarm alarm : failedAlarms) {
                try {
                    // 更新同步状态和时间
                    alarm.setSyncStatus("SUCCESS");
                    alarm.setSyncTime(syncTime);
                    alarm.setUpdatedAt(syncTime);
                    updateById(alarm);
                    successCount++;
                } catch (Exception e) {
                    log.error("重新同步单条数据失败: {}", alarm.getId(), e);
                }
            }
            
            result.put("success", true);
            result.put("totalCount", failedAlarms.size());
            result.put("successCount", successCount);
            result.put("failureCount", failedAlarms.size() - successCount);
            result.put("message", "重新同步完成");
            
            log.info("重新同步失败数据完成 - 总数: {}, 成功: {}", failedAlarms.size(), successCount);
            
        } catch (Exception e) {
            log.error("重新同步失败数据异常", e);
            result.put("success", false);
            result.put("message", "重新同步异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 转换API数据为实体对象
     */
    private ApiAiAlarm convertToEntity(Map<String, Object> alarmData, String syncSource, LocalDateTime syncTime) {
        try {
            ApiAiAlarm alarm = new ApiAiAlarm();
            
            // 基本信息
            alarm.setExternalId(getString(alarmData, "id"));
            alarm.setAlarmType(getString(alarmData, "alarm_type"));
            alarm.setAlarmLevel(getString(alarmData, "alarm_level"));
            alarm.setAlarmStatus(getString(alarmData, "alarm_status"));
            alarm.setAlarmTitle(getString(alarmData, "alarm_title"));
            alarm.setAlarmDescription(getString(alarmData, "alarm_description"));
            
            // 时间信息
            alarm.setAlarmTime(parseDateTime(getString(alarmData, "alarm_time")));
            alarm.setCreateTime(parseDateTime(getString(alarmData, "create_time")));
            alarm.setUpdateTime(parseDateTime(getString(alarmData, "update_time")));
            
            // 位置信息
            alarm.setLocationName(getString(alarmData, "location_name"));
            alarm.setLocationCode(getString(alarmData, "location_code"));
            alarm.setCoordinateX(getBigDecimal(alarmData, "coordinate_x"));
            alarm.setCoordinateY(getBigDecimal(alarmData, "coordinate_y"));
            alarm.setCoordinateZ(getBigDecimal(alarmData, "coordinate_z"));
            
            // 设备信息
            alarm.setDeviceId(getString(alarmData, "device_id"));
            alarm.setDeviceName(getString(alarmData, "device_name"));
            alarm.setDeviceType(getString(alarmData, "device_type"));
            
            // 报警数据
            alarm.setAlarmValue(getBigDecimal(alarmData, "alarm_value"));
            alarm.setThresholdValue(getBigDecimal(alarmData, "threshold_value"));
            alarm.setUnit(getString(alarmData, "unit"));
            
            // 处理信息
            alarm.setHandlerName(getString(alarmData, "handler_name"));
            alarm.setHandleTime(parseDateTime(getString(alarmData, "handle_time")));
            alarm.setHandleStatus(getString(alarmData, "handle_status"));
            alarm.setHandleRemark(getString(alarmData, "handle_remark"));
            
            // 媒体信息
            alarm.setImageUrl(getString(alarmData, "image_url"));
            alarm.setVideoUrl(getString(alarmData, "video_url"));
            
            // 扩展数据
            alarm.setExtraData(objectMapper.writeValueAsString(alarmData.get("extra_data")));
            
            // 原始数据
            alarm.setRawData(objectMapper.writeValueAsString(alarmData));
            
            // 同步信息
            alarm.setSyncTime(syncTime);
            alarm.setSyncStatus("SUCCESS");
            alarm.setSyncSource(syncSource);
            alarm.setDataVersion("1.0");
            alarm.setIsDeleted(false);
            alarm.setCreatedBy("system");
            alarm.setUpdatedBy("system");
            
            return alarm;
            
        } catch (Exception e) {
            log.error("转换API数据为实体对象失败: {}", alarmData, e);
            throw new RuntimeException("数据转换失败: " + e.getMessage());
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimal(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            log.warn("转换BigDecimal失败: key={}, value={}", key, value);
            return null;
        }
    }

    /**
     * 解析日期时间
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 尝试多种日期格式
            DateTimeFormatter[] formatters = {
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")
            };
            
            for (DateTimeFormatter formatter : formatters) {
                try {
                    return LocalDateTime.parse(dateTimeStr, formatter);
                } catch (Exception ignored) {
                    // 继续尝试下一个格式
                }
            }
            
            log.warn("无法解析日期时间: {}", dateTimeStr);
            return null;
            
        } catch (Exception e) {
            log.warn("解析日期时间失败: {}", dateTimeStr, e);
            return null;
        }
    }
}
