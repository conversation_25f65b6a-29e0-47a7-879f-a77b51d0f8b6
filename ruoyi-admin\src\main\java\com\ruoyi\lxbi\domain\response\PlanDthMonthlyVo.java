package com.ruoyi.lxbi.domain.response;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.lxbi.domain.PlanDthMonthly;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> Niu
 * @className : PlanDthMonthlyVo
 * @description :
 * @date: 2025/7/10
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanDthMonthlyVo extends PlanDthMonthly {

    @Excel(name = "项目部门名称", sort = 2, mergeByValue = true)
    private String projectDepartmentName;

    @Excel(name = "工作面名称", sort = 3, mergeByValue = true)
    private String workingFaceName;

    @Excel(name = "采场名称", sort = 4)
    private String stopeName;

}
