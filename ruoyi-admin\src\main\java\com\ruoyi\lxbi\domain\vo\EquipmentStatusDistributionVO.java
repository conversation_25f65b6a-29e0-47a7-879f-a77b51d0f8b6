package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 设备状态分布VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentStatusDistributionVO {
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 设备数量
     */
    private Long equipmentCount;
    
    /**
     * 占比百分比
     */
    private BigDecimal percentage;
    
    /**
     * 状态代码
     */
    private String statusCode;
}
