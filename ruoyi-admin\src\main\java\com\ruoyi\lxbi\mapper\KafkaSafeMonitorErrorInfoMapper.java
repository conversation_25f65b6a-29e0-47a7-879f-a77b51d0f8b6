package com.ruoyi.lxbi.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.lxbi.domain.KafkaSafeMonitorErrorInfo;
import com.ruoyi.lxbi.domain.vo.EnvironmentalSafetyLocationDistributionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 安全监测实时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Mapper
public interface KafkaSafeMonitorErrorInfoMapper
{
    /**
     * 查询安全监测实时数据
     *
     * @param id 安全监测实时数据主键
     * @return 安全监测实时数据
     */
    public KafkaSafeMonitorErrorInfo selectKafkaSafeMonitorErrorInfoById(Long id);

    /**
     * 查询安全监测实时数据列表
     *
     * @param kafkaSafeMonitorErrorInfo 安全监测实时数据
     * @return 安全监测实时数据集合
     */
    public List<KafkaSafeMonitorErrorInfo> selectKafkaSafeMonitorErrorInfoList(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo);

    /**
     * 统计环境安全报警位置分布
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 位置分布统计结果
     */
    public List<Map<String, Object>> selectLocationDistributionStats(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按日期统计环境安全报警数量趋势
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按日期统计的报警数量
     */
    public List<Map<String, Object>> selectAlarmTrendByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按小时统计环境安全报警数量趋势
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按小时统计的报警数量
     */
    public List<Map<String, Object>> selectAlarmTrendByHour(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 新增安全监测实时数据
     *
     * @param kafkaSafeMonitorErrorInfo 安全监测实时数据
     * @return 结果
     */
    public int insertKafkaSafeMonitorErrorInfo(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo);

    /**
     * 修改安全监测实时数据
     *
     * @param kafkaSafeMonitorErrorInfo 安全监测实时数据
     * @return 结果
     */
    public int updateKafkaSafeMonitorErrorInfo(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo);

    /**
     * 删除安全监测实时数据
     *
     * @param id 安全监测实时数据主键
     * @return 结果
     */
    public int deleteKafkaSafeMonitorErrorInfoById(Long id);

    /**
     * 批量删除安全监测实时数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaSafeMonitorErrorInfoByIds(Long[] ids);
}
