package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DataDrillingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataDrillingDepartmentWithPlanStats;
import com.ruoyi.lxbi.service.IDataDthStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 潜孔施工数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@RestController
@RequestMapping("/data/stats/dth")
public class DataDthStatsController {
    @Autowired
    private IDataDthStatsService dataDthStatsService;

    /**
     * 查询总体潜孔进尺统计数据（含计划量）
     * 对应图表一：总体潜孔进尺统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:dth:01a')")
    @GetMapping("/01a")
    public R<List<DataDrillingTotalWithPlanStats>> totalProgressWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                          @RequestParam(value = "startDate", required = false) String startDate,
                                                                          @RequestParam(value = "endDate", required = false) String endDate) {
        DataDrillingStatsRequest request = new DataDrillingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataDrillingTotalWithPlanStats> stats = dataDthStatsService.selectTotalWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询按部门分组的潜孔进尺统计数据（含计划量）
     * 对应图表二：按部门分组的潜孔进尺统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:dth:01b')")
    @GetMapping("/01b")
    public R<List<DataDrillingDepartmentWithPlanStats>> departmentProgressWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                                    @RequestParam(value = "startDate", required = false) String startDate,
                                                                                    @RequestParam(value = "endDate", required = false) String endDate) {
        DataDrillingStatsRequest request = new DataDrillingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataDrillingDepartmentWithPlanStats> stats = dataDthStatsService.selectDepartmentWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

}
