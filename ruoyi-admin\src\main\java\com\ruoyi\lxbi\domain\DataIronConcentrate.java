package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 铁精粉生产数据对象 data_iron_concentrate
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataIronConcentrate extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 铁精粉数据ID */
    private Long id;

    /** 作业日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "作业日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /** TFe含量（%） */
    @Excel(name = "TFe含量", readConverterExp = "%=")
    private BigDecimal tfeContent;

    /** 精矿细度-500目含量（%） */
    @Excel(name = "精矿细度-500目含量", readConverterExp = "%=")
    private BigDecimal finenessMinus500;

    /** 产量（吨） */
    @Excel(name = "产量", readConverterExp = "吨=")
    private BigDecimal productionVolume;

    /** 水分（%） */
    @Excel(name = "水分", readConverterExp = "%=")
    private BigDecimal moistureContent;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

}
