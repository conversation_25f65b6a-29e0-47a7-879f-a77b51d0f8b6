package com.ruoyi.lxbi.admin.service.impl;

import com.ruoyi.lxbi.admin.service.IVehicleSafetyStatService;
import com.ruoyi.lxbi.domain.vo.*;
import com.ruoyi.lxbi.service.IKafkaVehiclePropertyService;
import com.ruoyi.lxbi.service.IKafkaVehicleSpeedOverService;
import com.ruoyi.lxbi.domain.KafkaVehicleProperty;
import com.ruoyi.lxbi.domain.KafkaVehicleSpeedOver;
import com.ruoyi.lxbi.mapper.KafkaVehiclePropertyMapper;
import com.ruoyi.lxbi.mapper.KafkaVehicleSpeedOverMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 车辆安全统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class VehicleSafetyStatServiceImpl implements IVehicleSafetyStatService {

    @Autowired
    private IKafkaVehiclePropertyService kafkaVehiclePropertyService;

    @Autowired
    private IKafkaVehicleSpeedOverService kafkaVehicleSpeedOverService;

    @Autowired
    private KafkaVehiclePropertyMapper kafkaVehiclePropertyMapper;

    @Autowired
    private KafkaVehicleSpeedOverMapper kafkaVehicleSpeedOverMapper;

    /**
     * 获取车辆安全概览统计
     */
    @Override
    public VehicleSafetyOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate) {
        try {
            log.info("获取车辆安全概览统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 使用统计SQL查询截止日期前的车辆总数
            Long totalVehicleCount = kafkaVehiclePropertyMapper.countVehiclesByEndDate(endDate);
            if (totalVehicleCount == null) {
                totalVehicleCount = 0L;
            }

            // 使用统计SQL查询指定时间范围内的车辆告警数量
            Long vehicleAlarmCount = kafkaVehicleSpeedOverMapper.countAlarmVehiclesByDateRange(startDate, endDate);
            if (vehicleAlarmCount == null) {
                vehicleAlarmCount = 0L;
            }

            VehicleSafetyOverviewVO overview = new VehicleSafetyOverviewVO();
            overview.setTotalVehicleCount(totalVehicleCount);
            overview.setVehicleAlarmCount(vehicleAlarmCount);
            overview.setStartDate(startDate);
            overview.setEndDate(endDate);
            overview.setPeriod(viewType);

            log.info("车辆安全概览统计完成 - 车辆总数: {}, 告警车辆数: {}", totalVehicleCount, vehicleAlarmCount);
            return overview;
        } catch (Exception e) {
            log.error("获取车辆安全概览统计失败", e);
            throw new RuntimeException("获取车辆安全概览统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取车辆报警部门分布统计
     */
    @Override
    public List<VehicleAlarmDepartmentDistributionVO> getAlarmDepartmentDistribution(String viewType, String startDate, String endDate) {
        try {
            log.info("获取车辆报警部门分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 查询告警数据
            KafkaVehicleSpeedOver speedOverQuery = new KafkaVehicleSpeedOver();
            List<KafkaVehicleSpeedOver> speedOverList = kafkaVehicleSpeedOverService.selectKafkaVehicleSpeedOverList(speedOverQuery);

            // 根据时间范围过滤告警数据
            List<KafkaVehicleSpeedOver> filteredAlarms = filterAlarmsByDateRange(speedOverList, startDate, endDate);

            // 查询车辆属性数据
            KafkaVehicleProperty vehicleQuery = new KafkaVehicleProperty();
            List<KafkaVehicleProperty> allVehicles = kafkaVehiclePropertyService.selectKafkaVehiclePropertyList(vehicleQuery);

            // 创建车辆标识卡到部门的映射
            Map<String, String> vehicleToDepartmentMap = allVehicles.stream()
                .filter(v -> StringUtils.hasText(v.getVehicleLocationCardNumber()) && StringUtils.hasText(v.getDepartment()))
                .collect(Collectors.toMap(
                    KafkaVehicleProperty::getVehicleLocationCardNumber,
                    KafkaVehicleProperty::getDepartment,
                    (existing, replacement) -> existing
                ));

            // 按部门统计告警数量
            Map<String, Long> departmentAlarmCount = filteredAlarms.stream()
                .filter(alarm -> StringUtils.hasText(alarm.getVehicleIdentificationCard()))
                .filter(alarm -> vehicleToDepartmentMap.containsKey(alarm.getVehicleIdentificationCard()))
                .collect(Collectors.groupingBy(
                    alarm -> vehicleToDepartmentMap.get(alarm.getVehicleIdentificationCard()),
                    Collectors.counting()
                ));

            // 计算总告警数
            long totalAlarms = departmentAlarmCount.values().stream().mapToLong(Long::longValue).sum();

            // 构建结果列表
            List<VehicleAlarmDepartmentDistributionVO> distributionList = new ArrayList<>();
            for (Map.Entry<String, Long> entry : departmentAlarmCount.entrySet()) {
                String departmentName = entry.getKey();
                Long alarmCount = entry.getValue();
                BigDecimal percentage = totalAlarms > 0 ?
                    BigDecimal.valueOf(alarmCount * 100.0 / totalAlarms).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;

                distributionList.add(new VehicleAlarmDepartmentDistributionVO(
                    departmentName, alarmCount, percentage, generateDepartmentCode(departmentName)
                ));
            }

            // 按告警数量降序排序
            distributionList.sort((a, b) -> Long.compare(b.getAlarmCount(), a.getAlarmCount()));

            return distributionList;
        } catch (Exception e) {
            log.error("获取车辆报警部门分布统计失败", e);
            throw new RuntimeException("获取车辆报警部门分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取车辆告警类型分布统计
     */
    @Override
    public List<VehicleAlarmTypeDistributionVO> getAlarmTypeDistribution(String viewType, String startDate, String endDate) {
        try {
            log.info("获取车辆告警类型分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 查询告警数据
            KafkaVehicleSpeedOver speedOverQuery = new KafkaVehicleSpeedOver();
            List<KafkaVehicleSpeedOver> speedOverList = kafkaVehicleSpeedOverService.selectKafkaVehicleSpeedOverList(speedOverQuery);

            // 根据时间范围过滤告警数据
            List<KafkaVehicleSpeedOver> filteredAlarms = filterAlarmsByDateRange(speedOverList, startDate, endDate);

            // 目前只有超速告警一种类型
            List<VehicleAlarmTypeDistributionVO> distributionList = new ArrayList<>();

            if (!filteredAlarms.isEmpty()) {
                long speedOverCount = filteredAlarms.size();
                distributionList.add(new VehicleAlarmTypeDistributionVO(
                    "超速告警",
                    speedOverCount,
                    new BigDecimal("100.00"),
                    "SPEED_OVER"
                ));
            }

            return distributionList;
        } catch (Exception e) {
            log.error("获取车辆告警类型分布统计失败", e);
            throw new RuntimeException("获取车辆告警类型分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取车辆告警记录列表
     */
    @Override
    public List<VehicleAlarmRecordVO> getAlarmRecords(String viewType, String startDate, String endDate) {
        try {
            log.info("获取车辆告警记录列表，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 查询告警数据
            KafkaVehicleSpeedOver speedOverQuery = new KafkaVehicleSpeedOver();
            List<KafkaVehicleSpeedOver> speedOverList = kafkaVehicleSpeedOverService.selectKafkaVehicleSpeedOverList(speedOverQuery);

            // 根据时间范围过滤告警数据
            List<KafkaVehicleSpeedOver> filteredAlarms = filterAlarmsByDateRange(speedOverList, startDate, endDate);

            // 查询车辆属性数据
            KafkaVehicleProperty vehicleQuery = new KafkaVehicleProperty();
            List<KafkaVehicleProperty> allVehicles = kafkaVehiclePropertyService.selectKafkaVehiclePropertyList(vehicleQuery);

            // 创建车辆标识卡到车辆信息的映射
            Map<String, KafkaVehicleProperty> vehicleMap = allVehicles.stream()
                .filter(v -> StringUtils.hasText(v.getVehicleLocationCardNumber()))
                .collect(Collectors.toMap(
                    KafkaVehicleProperty::getVehicleLocationCardNumber,
                    v -> v,
                    (existing, replacement) -> existing
                ));

            // 构建告警记录列表
            List<VehicleAlarmRecordVO> recordList = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            for (int i = 0; i < filteredAlarms.size(); i++) {
                KafkaVehicleSpeedOver alarm = filteredAlarms.get(i);
                KafkaVehicleProperty vehicle = vehicleMap.get(alarm.getVehicleIdentificationCard());

                VehicleAlarmRecordVO record = new VehicleAlarmRecordVO();
                record.setSerialNumber(i + 1);
                record.setVehiclePlateNumber(vehicle != null ? vehicle.getVehicleCode() : alarm.getVehicleIdentificationCard());
                record.setAlarmType("超速告警");
                record.setVehicleLocation("矿区内"); // 可以根据实际需求设置位置信息
                record.setAlarmTime(alarm.getAlarmStartTime() != null ? sdf.format(alarm.getAlarmStartTime()) : "");
                record.setResolveTime(alarm.getAlarmEndTime() != null ? sdf.format(alarm.getAlarmEndTime()) : "");
                record.setVehicleCode(vehicle != null ? vehicle.getVehicleCode() : "");
                record.setAlarmStatus(alarm.getAlarmEndTime() != null ? "已解除" : "告警中");
                record.setHandlerName("系统自动"); // 可以根据实际需求设置处理人员

                recordList.add(record);
            }

            // 按告警时间降序排序
            recordList.sort((a, b) -> {
                if (StringUtils.hasText(a.getAlarmTime()) && StringUtils.hasText(b.getAlarmTime())) {
                    return b.getAlarmTime().compareTo(a.getAlarmTime());
                }
                return 0;
            });
            return recordList;
        } catch (Exception e) {
            log.error("获取车辆告警记录列表失败", e);
            throw new RuntimeException("获取车辆告警记录列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取车辆类型分布统计
     */
    @Override
    public List<VehicleTypeDistributionVO> getVehicleTypeDistribution(String viewType, String startDate, String endDate) {
        try {
            log.info("获取车辆类型分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 查询车辆属性数据
            KafkaVehicleProperty vehicleQuery = new KafkaVehicleProperty();
            List<KafkaVehicleProperty> allVehicles = kafkaVehiclePropertyService.selectKafkaVehiclePropertyList(vehicleQuery);

            // 按车辆类型统计数量
            Map<String, Long> typeCount = allVehicles.stream()
                .filter(v -> StringUtils.hasText(v.getVehicleType()))
                .collect(Collectors.groupingBy(
                    KafkaVehicleProperty::getVehicleType,
                    Collectors.counting()
                ));

            // 计算总车辆数
            long totalVehicles = typeCount.values().stream().mapToLong(Long::longValue).sum();

            // 构建结果列表
            List<VehicleTypeDistributionVO> distributionList = new ArrayList<>();
            for (Map.Entry<String, Long> entry : typeCount.entrySet()) {
                String typeName = entry.getKey();
                Long count = entry.getValue();
                BigDecimal percentage = totalVehicles > 0 ?
                    BigDecimal.valueOf(count * 100.0 / totalVehicles).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;

                distributionList.add(new VehicleTypeDistributionVO(
                    typeName, count, percentage, generateVehicleTypeCode(typeName)
                ));
            }

            // 按车辆数量降序排序
            distributionList.sort((a, b) -> Long.compare(b.getVehicleCount(), a.getVehicleCount()));

            return distributionList;
        } catch (Exception e) {
            log.error("获取车辆类型分布统计失败", e);
            throw new RuntimeException("获取车辆类型分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取车辆状态分布统计
     */
    @Override
    public List<VehicleStatusDistributionVO> getVehicleStatusDistribution(String viewType, String startDate, String endDate) {
        try {
            log.info("获取车辆状态分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 查询车辆属性数据
            KafkaVehicleProperty vehicleQuery = new KafkaVehicleProperty();
            List<KafkaVehicleProperty> allVehicles = kafkaVehiclePropertyService.selectKafkaVehiclePropertyList(vehicleQuery);

            // 按车辆状态统计数量
            Map<String, Long> statusCount = allVehicles.stream()
                .filter(v -> StringUtils.hasText(v.getVehicleStatus()))
                .collect(Collectors.groupingBy(
                    KafkaVehicleProperty::getVehicleStatus,
                    Collectors.counting()
                ));

            // 计算总车辆数
            long totalVehicles = statusCount.values().stream().mapToLong(Long::longValue).sum();

            // 构建结果列表
            List<VehicleStatusDistributionVO> distributionList = new ArrayList<>();
            for (Map.Entry<String, Long> entry : statusCount.entrySet()) {
                String statusCode = entry.getKey();
                Long count = entry.getValue();
                BigDecimal percentage = totalVehicles > 0 ?
                    BigDecimal.valueOf(count * 100.0 / totalVehicles).setScale(2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;

                String statusName = convertStatusCodeToName(statusCode);
                distributionList.add(new VehicleStatusDistributionVO(
                    statusName, count, percentage, statusCode
                ));
            }

            // 按车辆数量降序排序
            distributionList.sort((a, b) -> Long.compare(b.getVehicleCount(), a.getVehicleCount()));

            return distributionList;
        } catch (Exception e) {
            log.error("获取车辆状态分布统计失败", e);
            throw new RuntimeException("获取车辆状态分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 根据时间范围过滤告警数据
     */
    private List<KafkaVehicleSpeedOver> filterAlarmsByDateRange(List<KafkaVehicleSpeedOver> alarms, String startDate, String endDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);

            // 设置结束时间为当天的23:59:59
            Calendar cal = Calendar.getInstance();
            cal.setTime(end);
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
            end = cal.getTime();

            Date finalEnd = end;
            return alarms.stream()
                .filter(alarm -> alarm.getAlarmStartTime() != null)
                .filter(alarm -> !alarm.getAlarmStartTime().before(start) && !alarm.getAlarmStartTime().after(finalEnd))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("日期过滤失败，返回所有数据: {}", e.getMessage());
            return alarms;
        }
    }

    /**
     * 生成部门代码
     */
    private String generateDepartmentCode(String departmentName) {
        if (!StringUtils.hasText(departmentName)) {
            return "UNKNOWN";
        }
        return departmentName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "").toUpperCase();
    }

    /**
     * 生成车辆类型代码
     */
    private String generateVehicleTypeCode(String typeName) {
        if (!StringUtils.hasText(typeName)) {
            return "UNKNOWN";
        }
        return typeName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "").toUpperCase();
    }

    /**
     * 将状态代码转换为状态名称
     */
    private String convertStatusCodeToName(String statusCode) {
        if (!StringUtils.hasText(statusCode)) {
            return "未知状态";
        }

        switch (statusCode) {
            case "0":
                return "停用";
            case "1":
                return "正常";
            case "2":
                return "维修";
            case "3":
                return "故障";
            default:
                return "状态" + statusCode;
        }
    }
}
