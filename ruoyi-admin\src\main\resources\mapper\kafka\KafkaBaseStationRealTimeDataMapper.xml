<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaBaseStationRealTimeDataMapper">
    
    <resultMap type="KafkaBaseStationRealTimeData" id="KafkaBaseStationRealTimeDataResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="baseStationCode"    column="base_station_code"    />
        <result property="baseStationRunningStatus"    column="base_station_running_status"    />
        <result property="baseStationPowerSupplyStatus"    column="base_station_power_supply_status"    />
        <result property="dataTime"    column="data_time"    />
    </resultMap>

    <sql id="selectKafkaBaseStationRealTimeDataVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, mine_name, data_upload_time, base_station_code, base_station_running_status, base_station_power_supply_status, data_time from kafka_base_station_real_time_data
    </sql>

    <select id="selectKafkaBaseStationRealTimeDataList" parameterType="KafkaBaseStationRealTimeData" resultMap="KafkaBaseStationRealTimeDataResult">
        <include refid="selectKafkaBaseStationRealTimeDataVo"/>
        <where>  
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="params.beginDataUploadTime != null and params.beginDataUploadTime != '' and params.endDataUploadTime != null and params.endDataUploadTime != ''"> and data_upload_time between #{params.beginDataUploadTime}::date and #{params.endDataUploadTime}::date</if>
            <if test="baseStationCode != null  and baseStationCode != ''"> and base_station_code = #{baseStationCode}</if>
            <if test="baseStationRunningStatus != null  and baseStationRunningStatus != ''"> and base_station_running_status = #{baseStationRunningStatus}</if>
            <if test="baseStationPowerSupplyStatus != null  and baseStationPowerSupplyStatus != ''"> and base_station_power_supply_status = #{baseStationPowerSupplyStatus}</if>
            <if test="params.beginDataTime != null and params.beginDataTime != '' and params.endDataTime != null and params.endDataTime != ''"> and data_time between #{params.beginDataTime}::date and #{params.endDataTime}::date</if>
        </where>
    </select>
    
    <select id="selectKafkaBaseStationRealTimeDataById" parameterType="Long" resultMap="KafkaBaseStationRealTimeDataResult">
        <include refid="selectKafkaBaseStationRealTimeDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertKafkaBaseStationRealTimeData" parameterType="KafkaBaseStationRealTimeData" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_base_station_real_time_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null">file_encoding,</if>
            <if test="mineCode != null">mine_code,</if>
            <if test="mineName != null">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="baseStationCode != null">base_station_code,</if>
            <if test="baseStationRunningStatus != null">base_station_running_status,</if>
            <if test="baseStationPowerSupplyStatus != null">base_station_power_supply_status,</if>
            <if test="dataTime != null">data_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null">#{fileEncoding},</if>
            <if test="mineCode != null">#{mineCode},</if>
            <if test="mineName != null">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="baseStationCode != null">#{baseStationCode},</if>
            <if test="baseStationRunningStatus != null">#{baseStationRunningStatus},</if>
            <if test="baseStationPowerSupplyStatus != null">#{baseStationPowerSupplyStatus},</if>
            <if test="dataTime != null">#{dataTime},</if>
         </trim>
    </insert>

    <update id="updateKafkaBaseStationRealTimeData" parameterType="KafkaBaseStationRealTimeData">
        update kafka_base_station_real_time_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="mineName != null">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="baseStationCode != null">base_station_code = #{baseStationCode},</if>
            <if test="baseStationRunningStatus != null">base_station_running_status = #{baseStationRunningStatus},</if>
            <if test="baseStationPowerSupplyStatus != null">base_station_power_supply_status = #{baseStationPowerSupplyStatus},</if>
            <if test="dataTime != null">data_time = #{dataTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaBaseStationRealTimeDataById" parameterType="Long">
        delete from kafka_base_station_real_time_data where id = #{id}
    </delete>

    <delete id="deleteKafkaBaseStationRealTimeDataByIds" parameterType="String">
        delete from kafka_base_station_real_time_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>