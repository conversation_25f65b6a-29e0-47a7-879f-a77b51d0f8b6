package com.ruoyi.lxbi.controller.base;

import java.util.List;

import com.ruoyi.common.core.domain.R;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.BasePriorityProject;
import com.ruoyi.lxbi.domain.response.BasePriorityProjectVo;
import com.ruoyi.lxbi.service.IBasePriorityProjectService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 重点工程Controller
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/base/basePriorityProject")
public class BasePriorityProjectController extends BaseController {
    @Autowired
    private IBasePriorityProjectService basePriorityProjectService;

    /**
     * 查询重点工程列表
     */
    @PreAuthorize("@ss.hasPermi('base:basePriorityProject:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasePriorityProject basePriorityProject) {
        startPage();
        List<BasePriorityProjectVo> list = basePriorityProjectService.selectBasePriorityProjectList(basePriorityProject);
        return getDataTable(list);
    }

    /**
     * 查询所有重点工程列表（不分页）
     */
    @PreAuthorize("@ss.hasPermi('base:basePriorityProject:list')")
    @GetMapping("/listAll")
    public R<List<BasePriorityProjectVo>> listAll(BasePriorityProject basePriorityProject) {
        List<BasePriorityProjectVo> list = basePriorityProjectService.selectBasePriorityProjectListAll(basePriorityProject);
        return R.ok(list);
    }

    /**
     * 导出重点工程列表
     */
    @PreAuthorize("@ss.hasPermi('base:basePriorityProject:export')")
    @Log(title = "重点工程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasePriorityProject basePriorityProject) {
        List<BasePriorityProjectVo> list = basePriorityProjectService.selectBasePriorityProjectList(basePriorityProject);
        ExcelUtil<BasePriorityProjectVo> util = new ExcelUtil<BasePriorityProjectVo>(BasePriorityProjectVo.class);
        util.exportExcel(response, list, "重点工程数据");
    }

    /**
     * 获取重点工程详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:basePriorityProject:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(basePriorityProjectService.selectBasePriorityProjectById(id));
    }

    /**
     * 新增重点工程
     */
    @PreAuthorize("@ss.hasPermi('base:basePriorityProject:add')")
    @Log(title = "重点工程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasePriorityProject basePriorityProject)
    {
        return toAjax(basePriorityProjectService.insertBasePriorityProject(basePriorityProject));
    }

    /**
     * 修改重点工程
     */
    @PreAuthorize("@ss.hasPermi('base:basePriorityProject:edit')")
    @Log(title = "重点工程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasePriorityProject basePriorityProject)
    {
        return toAjax(basePriorityProjectService.updateBasePriorityProject(basePriorityProject));
    }

    /**
     * 删除重点工程（逻辑删除）
     */
    @PreAuthorize("@ss.hasPermi('base:basePriorityProject:remove')")
    @Log(title = "重点工程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(basePriorityProjectService.logicDeleteBasePriorityProjectByIds(ids));
    }
}
