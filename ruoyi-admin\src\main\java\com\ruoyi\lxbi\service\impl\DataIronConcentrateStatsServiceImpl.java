package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataIronConcentrateStatsRequest;
import com.ruoyi.lxbi.domain.response.DataIronConcentrateTotalWithPlanStats;
import com.ruoyi.lxbi.mapper.DataIronConcentrateStatsMapper;
import com.ruoyi.lxbi.service.IDataIronConcentrateStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 铁精粉生产数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Service
public class DataIronConcentrateStatsServiceImpl implements IDataIronConcentrateStatsService {
    @Autowired
    private DataIronConcentrateStatsMapper dataIronConcentrateStatsMapper;

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    @Override
    public List<DataIronConcentrateTotalWithPlanStats> selectTotalWithPlanStatsList(DataIronConcentrateStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataIronConcentrateStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataIronConcentrateStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataIronConcentrateStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认月统计
            return dataIronConcentrateStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }
}
