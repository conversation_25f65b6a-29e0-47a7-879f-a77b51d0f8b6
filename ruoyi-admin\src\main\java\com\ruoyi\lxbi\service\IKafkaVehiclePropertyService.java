package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaVehicleProperty;

/**
 * 车辆属性数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface IKafkaVehiclePropertyService 
{
    /**
     * 查询车辆属性数据
     * 
     * @param id 车辆属性数据主键
     * @return 车辆属性数据
     */
    public KafkaVehicleProperty selectKafkaVehiclePropertyById(Long id);

    /**
     * 查询车辆属性数据列表
     * 
     * @param kafkaVehicleProperty 车辆属性数据
     * @return 车辆属性数据集合
     */
    public List<KafkaVehicleProperty> selectKafkaVehiclePropertyList(KafkaVehicleProperty kafkaVehicleProperty);

    /**
     * 新增车辆属性数据
     * 
     * @param kafkaVehicleProperty 车辆属性数据
     * @return 结果
     */
    public int insertKafkaVehicleProperty(KafkaVehicleProperty kafkaVehicleProperty);

    /**
     * 修改车辆属性数据
     * 
     * @param kafkaVehicleProperty 车辆属性数据
     * @return 结果
     */
    public int updateKafkaVehicleProperty(KafkaVehicleProperty kafkaVehicleProperty);

    /**
     * 批量删除车辆属性数据
     * 
     * @param ids 需要删除的车辆属性数据主键集合
     * @return 结果
     */
    public int deleteKafkaVehiclePropertyByIds(Long[] ids);

    /**
     * 删除车辆属性数据信息
     *
     * @param id 车辆属性数据主键
     * @return 结果
     */
    public int deleteKafkaVehiclePropertyById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaVehicleProperty parseKafkaMessage(String kafkaMessage);
}
