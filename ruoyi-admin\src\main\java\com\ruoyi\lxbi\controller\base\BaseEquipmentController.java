package com.ruoyi.lxbi.controller.base;

import java.util.List;

import com.ruoyi.common.core.domain.R;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.BaseEquipment;
import com.ruoyi.lxbi.service.IBaseEquipmentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 设备数据Controller
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/base/baseEquipment")
public class BaseEquipmentController extends BaseController {
    @Autowired
    private IBaseEquipmentService baseEquipmentService;

    /**
     * 查询设备数据列表
     */
    @PreAuthorize("@ss.hasPermi('base:baseEquipment:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaseEquipment baseEquipment) {
        startPage();
        List<BaseEquipment> list = baseEquipmentService.selectBaseEquipmentList(baseEquipment);
        return getDataTable(list);
    }

    /**
     * 查询所有设备数据列表（不分页）
     */
    @GetMapping("/listAll")
    public R<List<BaseEquipment>> listAll(BaseEquipment baseEquipment) {
        List<BaseEquipment> list = baseEquipmentService.selectBaseEquipmentListAll(baseEquipment);
        return R.ok(list);
    }

    /**
     * 导出设备数据列表
     */
    @PreAuthorize("@ss.hasPermi('base:baseEquipment:export')")
    @Log(title = "设备数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseEquipment baseEquipment) {
        List<BaseEquipment> list = baseEquipmentService.selectBaseEquipmentList(baseEquipment);
        ExcelUtil<BaseEquipment> util = new ExcelUtil<BaseEquipment>(BaseEquipment.class);
        util.exportExcel(response, list, "设备数据数据");
    }

    /**
     * 获取设备数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:baseEquipment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(baseEquipmentService.selectBaseEquipmentById(id));
    }

    /**
     * 新增设备数据
     */
    @PreAuthorize("@ss.hasPermi('base:baseEquipment:add')")
    @Log(title = "设备数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseEquipment baseEquipment)
    {
        return toAjax(baseEquipmentService.insertBaseEquipment(baseEquipment));
    }

    /**
     * 修改设备数据
     */
    @PreAuthorize("@ss.hasPermi('base:baseEquipment:edit')")
    @Log(title = "设备数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaseEquipment baseEquipment)
    {
        return toAjax(baseEquipmentService.updateBaseEquipment(baseEquipment));
    }

    /**
     * 删除设备数据（逻辑删除）
     */
    @PreAuthorize("@ss.hasPermi('base:baseEquipment:remove')")
    @Log(title = "设备数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(baseEquipmentService.logicDeleteBaseEquipmentByIds(ids));
    }
}
