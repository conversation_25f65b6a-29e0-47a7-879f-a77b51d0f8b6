-- 区域基本信息数据表 (PostgreSQL)
CREATE TABLE kafka_people_location_info (
    id BIGSERIAL PRIMARY KEY,
    mine_code VARCHAR(50),
    mine_name VARCHAR(100),
    data_upload_time TIMESTAMP,
    area_type VARCHAR(50),
    area_code VARCHAR(50) NOT NULL,
    area_approved_personnel BIGINT,
    area_name VARCHAR(100),
    status BIGINT DEFAULT 1,
    is_deleted BIGINT DEFAULT 0,
    create_by VARCHAR(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500) DEFAULT NULL
);

-- 添加字段注释 (PostgreSQL语法)
COMMENT ON TABLE kafka_people_location_info IS '区域基本信息数据表';
COMMENT ON COLUMN kafka_people_location_info.id IS '主键ID';
COMMENT ON COLUMN kafka_people_location_info.mine_code IS '煤矿编码';
COMMENT ON COLUMN kafka_people_location_info.mine_name IS '矿井名称';
COMMENT ON COLUMN kafka_people_location_info.data_upload_time IS '数据上传时间';
COMMENT ON COLUMN kafka_people_location_info.area_type IS '区域类型';
COMMENT ON COLUMN kafka_people_location_info.area_code IS '区域编码';
COMMENT ON COLUMN kafka_people_location_info.area_approved_personnel IS '区域核定人数';
COMMENT ON COLUMN kafka_people_location_info.area_name IS '区域名称';
COMMENT ON COLUMN kafka_people_location_info.status IS '状态(1:正常 0:异常)';
COMMENT ON COLUMN kafka_people_location_info.is_deleted IS '是否删除(0:未删除 1:已删除)';
COMMENT ON COLUMN kafka_people_location_info.create_by IS '创建者';
COMMENT ON COLUMN kafka_people_location_info.create_time IS '创建时间';
COMMENT ON COLUMN kafka_people_location_info.update_by IS '更新者';
COMMENT ON COLUMN kafka_people_location_info.update_time IS '更新时间';
COMMENT ON COLUMN kafka_people_location_info.remark IS '备注';

-- 创建索引 (PostgreSQL)
CREATE INDEX idx_kpli_area_code ON kafka_people_location_info(area_code);
CREATE INDEX idx_kpli_mine_code ON kafka_people_location_info(mine_code);
CREATE INDEX idx_kpli_area_type ON kafka_people_location_info(area_type);
CREATE INDEX idx_kpli_data_upload_time ON kafka_people_location_info(data_upload_time);
CREATE INDEX idx_kpli_status_deleted ON kafka_people_location_info(status, is_deleted);

-- 创建复合索引用于唯一性检查 (PostgreSQL部分索引)
CREATE UNIQUE INDEX idx_kpli_area_mine_unique 
ON kafka_people_location_info(area_code, mine_code) 
WHERE is_deleted = 0;

-- 插入测试数据
INSERT INTO kafka_people_location_info (
    mine_code, mine_name, data_upload_time, area_type, area_code,
    area_approved_personnel, area_name, status, is_deleted, 
    create_by, create_time, update_by, update_time
) VALUES 
-- 主要作业区域
('MINE001', '示例煤矿', '2025-08-25 14:30:00', '作业区', 'AREA001',
 50, '主井口作业区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 14:30:00', '作业区', 'AREA002',
 30, '副井口作业区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 14:30:00', '作业区', 'AREA003',
 40, '运输巷道区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

-- 安全区域
('MINE001', '示例煤矿', '2025-08-25 14:30:00', '安全区', 'SAFE001',
 100, '地面安全区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 14:30:00', '安全区', 'SAFE002',
 20, '井下避难硐室', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

-- 危险区域
('MINE001', '示例煤矿', '2025-08-25 14:30:00', '危险区', 'DANGER001',
 5, '瓦斯监测区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 14:30:00', '危险区', 'DANGER002',
 3, '高压电气区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

-- 休息区域
('MINE001', '示例煤矿', '2025-08-25 14:30:00', '休息区', 'REST001',
 25, '井下休息室', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

('MINE001', '示例煤矿', '2025-08-25 14:30:00', '休息区', 'REST002',
 15, '地面休息区', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00'),

-- 管理区域
('MINE001', '示例煤矿', '2025-08-25 14:30:00', '管理区', 'ADMIN001',
 10, '调度指挥中心', 1, 0, 'system', '2025-08-25 14:30:00', 'system', '2025-08-25 14:30:00');

-- 验证数据插入
SELECT 
    area_code,
    area_name,
    area_type,
    area_approved_personnel,
    mine_name
FROM kafka_people_location_info 
WHERE is_deleted = 0
ORDER BY area_type, area_code;

-- 统计各区域类型的数量
SELECT 
    area_type,
    count(*) as area_count,
    sum(area_approved_personnel) as total_approved_personnel
FROM kafka_people_location_info 
WHERE is_deleted = 0
GROUP BY area_type
ORDER BY area_count DESC;

-- 创建区域统计视图
CREATE OR REPLACE VIEW v_area_statistics AS
SELECT 
    area_type,
    COUNT(*) as area_count,
    SUM(area_approved_personnel) as total_approved_personnel,
    AVG(area_approved_personnel) as avg_approved_personnel,
    MIN(area_approved_personnel) as min_approved_personnel,
    MAX(area_approved_personnel) as max_approved_personnel
FROM kafka_people_location_info 
WHERE is_deleted = 0 AND status = 1
GROUP BY area_type;

COMMENT ON VIEW v_area_statistics IS '区域统计视图';

-- 创建区域详情视图
CREATE OR REPLACE VIEW v_area_details AS
SELECT 
    kpli.area_code,
    kpli.area_name,
    kpli.area_type,
    kpli.area_approved_personnel,
    kpli.mine_code,
    kpli.mine_name,
    kpli.data_upload_time,
    CASE 
        WHEN kpli.area_type = '危险区' THEN '高风险'
        WHEN kpli.area_type = '作业区' THEN '中风险'
        WHEN kpli.area_type = '安全区' THEN '低风险'
        ELSE '一般风险'
    END as risk_level
FROM kafka_people_location_info kpli
WHERE kpli.is_deleted = 0 AND kpli.status = 1;

COMMENT ON VIEW v_area_details IS '区域详情视图';

-- 查看创建的视图
SELECT * FROM v_area_statistics ORDER BY area_count DESC;
SELECT * FROM v_area_details ORDER BY risk_level DESC, area_code;
