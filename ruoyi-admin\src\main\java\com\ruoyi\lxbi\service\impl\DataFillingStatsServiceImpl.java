package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataFillingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataFillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataFillingStopeStats;
import com.ruoyi.lxbi.mapper.DataFillingStatsMapper;
import com.ruoyi.lxbi.service.IDataFillingStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 充填数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class DataFillingStatsServiceImpl implements IDataFillingStatsService {
    @Autowired
    private DataFillingStatsMapper dataFillingStatsMapper;

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    @Override
    public List<DataFillingTotalWithPlanStats> selectTotalWithPlanStatsList(DataFillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataFillingStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataFillingStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataFillingStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认月统计
            return dataFillingStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询采场统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 采场统计数据集合
     */
    @Override
    public List<DataFillingStopeStats> selectStopeStatsList(DataFillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataFillingStatsMapper.selectDailyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataFillingStatsMapper.selectWeeklyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataFillingStatsMapper.selectYearlyStopeStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认月统计
            return dataFillingStatsMapper.selectMonthlyStopeStats(request.getStartDate(), request.getEndDate());
        }
    }
}
