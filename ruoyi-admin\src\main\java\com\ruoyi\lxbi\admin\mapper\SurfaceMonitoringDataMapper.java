package com.ruoyi.lxbi.admin.mapper;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.admin.domain.SurfaceMonitoringData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 地表监测数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Mapper
public interface SurfaceMonitoringDataMapper {
    
    /**
     * 查询地表监测数据
     * 
     * @param id 地表监测数据主键
     * @return 地表监测数据
     */
    public SurfaceMonitoringData selectSurfaceMonitoringDataById(Long id);

    /**
     * 根据日期、基站名称和终端编号查询地表监测数据
     * 
     * @param date 日期
     * @param stationName 基站名称
     * @param wgbh 终端编号
     * @return 地表监测数据
     */
    public SurfaceMonitoringData selectByDateStationWgbh(@Param("date") Date date, 
                                                         @Param("stationName") String stationName, 
                                                         @Param("wgbh") String wgbh);

    /**
     * 根据原始数据ID查询地表监测数据
     * 
     * @param originalId 原始数据ID
     * @return 地表监测数据
     */
    public SurfaceMonitoringData selectByOriginalId(@Param("originalId") Integer originalId);

    /**
     * 查询地表监测数据列表
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 地表监测数据集合
     */
    public List<SurfaceMonitoringData> selectSurfaceMonitoringDataList(SurfaceMonitoringData surfaceMonitoringData);

    /**
     * 按日期范围查询地表监测数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 地表监测数据集合
     */
    public List<SurfaceMonitoringData> selectByDateRange(@Param("startDate") Date startDate, 
                                                        @Param("endDate") Date endDate);

    /**
     * 按基站名称查询地表监测数据
     * 
     * @param stationName 基站名称
     * @return 地表监测数据集合
     */
    public List<SurfaceMonitoringData> selectByStationName(@Param("stationName") String stationName);

    /**
     * 新增地表监测数据
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 结果
     */
    public int insertSurfaceMonitoringData(SurfaceMonitoringData surfaceMonitoringData);

    /**
     * 修改地表监测数据
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 结果
     */
    public int updateSurfaceMonitoringData(SurfaceMonitoringData surfaceMonitoringData);

    /**
     * 删除地表监测数据
     * 
     * @param id 地表监测数据主键
     * @return 结果
     */
    public int deleteSurfaceMonitoringDataById(Long id);

    /**
     * 批量删除地表监测数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSurfaceMonitoringDataByIds(Long[] ids);

    /**
     * UPSERT操作（根据日期、基站名称、终端编号插入或更新）
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 结果
     */
    public int upsertSurfaceMonitoringData(SurfaceMonitoringData surfaceMonitoringData);

    /**
     * 按日期统计地表监测数据
     * 
     * @return 统计结果
     */
    public List<java.util.Map<String, Object>> countByDate();

    /**
     * 按基站统计地表监测数据
     * 
     * @return 统计结果
     */
    public List<java.util.Map<String, Object>> countByStation();

    /**
     * 获取最新的监测数据
     * 
     * @param limit 限制数量
     * @return 最新监测数据
     */
    public List<SurfaceMonitoringData> selectLatestData(@Param("limit") Integer limit);

    /**
     * 获取偏移异常数据（偏移量较大的数据）
     * 
     * @param threshold 偏移阈值
     * @return 异常数据列表
     */
    public List<SurfaceMonitoringData> selectAbnormalOffsetData(@Param("threshold") Double threshold);

    /**
     * 按日期获取统计信息
     * 
     * @param date 日期
     * @return 统计信息
     */
    public java.util.Map<String, Object> getStatisticsByDate(@Param("date") Date date);
}
