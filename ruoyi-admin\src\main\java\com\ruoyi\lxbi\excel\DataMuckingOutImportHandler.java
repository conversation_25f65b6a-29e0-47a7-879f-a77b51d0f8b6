package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.domain.BaseWorkingPeriod;
import com.ruoyi.lxbi.domain.DataMuckingOut;
import com.ruoyi.lxbi.domain.excel.DataMuckingOutImport;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;
import com.ruoyi.lxbi.service.IBaseStopeService;
import com.ruoyi.lxbi.service.IBaseWorkingPeriodService;
import com.ruoyi.lxbi.service.IDataMuckingOutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 出矿数据导入处理器
 *
 * <AUTHOR>
 */
@Component
public class DataMuckingOutImportHandler extends ExcelImportHandler<DataMuckingOutImport> {

    @Autowired
    private IBaseWorkingPeriodService baseWorkingPeriodService;

    @Autowired
    private IBaseProjectDepartmentService baseProjectDepartmentService;

    @Autowired
    private IBaseStopeService baseStopeService;

    @Autowired
    private IDataMuckingOutService dataMuckingOutService;

    @Override
    protected Class<DataMuckingOutImport> getEntityClass() {
        return DataMuckingOutImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置作业时段选项
        List<ExcelOptionInfo> workingPeriods = new ArrayList<>();
        BaseWorkingPeriod queryParam = new BaseWorkingPeriod();
        queryParam.setStatus(1L); // 只查询启用状态的作业时段
        List<BaseWorkingPeriod> periodList = baseWorkingPeriodService.selectBaseWorkingPeriodList(queryParam);

        for (BaseWorkingPeriod period : periodList) {
            workingPeriods.add(new ExcelOptionInfo(
                    period.getWorkingPeriodId(),
                    period.getWorkingPeriodName()
            ));
        }
        context.setOptions("workingPeriod", workingPeriods);

        // 设置项目部门选项
        List<ExcelOptionInfo> projectDepartments = new ArrayList<>();
        BaseProjectDepartment deptQueryParam = new BaseProjectDepartment();
        List<BaseProjectDepartment> deptList = baseProjectDepartmentService.selectBaseProjectDepartmentList(deptQueryParam);

        for (BaseProjectDepartment dept : deptList) {
            projectDepartments.add(new ExcelOptionInfo(
                    dept.getProjectDepartmentId(),
                    dept.getProjectDepartmentName()
            ));
        }
        context.setOptions("projectDepartment", projectDepartments);

        // 设置采场选项
        List<ExcelOptionInfo> stopes = new ArrayList<>();
        BaseStope stopeQueryParam = new BaseStope();
        List<BaseStope> stopeList = baseStopeService.selectBaseStopeListAll(stopeQueryParam);

        for (BaseStope stope : stopeList) {
            stopes.add(new ExcelOptionInfo(
                    stope.getStopeId(),
                    stope.getStopeName()
            ));
        }
        context.setOptions("stope", stopes);
    }

    @Override
    protected void validateData(ExcelDataInfo<DataMuckingOutImport> dataInfo, ExcelImportContext context) {
        DataMuckingOutImport data = dataInfo.getData();

        // 验证作业日期不能为未来日期
        if (data.getOperationDate() != null && data.getOperationDate().after(new Date())) {
            dataInfo.addError("operationDate", "作业日期不能是未来日期");
        }

        // 验证出矿吨数
        if (data.getTons() != null && data.getTons() < 0) {
            dataInfo.addError("tons", "出矿吨数不能为负数");
        }
    }

    @Override
    protected void saveData(DataMuckingOutImport data, ExcelImportContext context) {
        // 转换为实体对象
        DataMuckingOut entity = new DataMuckingOut();
        entity.setOperationDate(data.getOperationDate());
        entity.setProjectDepartmentId(data.getProjectDepartmentId());
        entity.setStopeId(data.getStopeId());
        entity.setWorkingPeriodId(data.getWorkingPeriodId());
        entity.setTons(data.getTons());
        
        // 保存到数据库
        dataMuckingOutService.insertDataMuckingOut(entity);
    }

    @Override
    public List<DataMuckingOutImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("出矿数据验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("出矿数据导入完成，总行数: " + ctx.getTotalRows());
    }
}
