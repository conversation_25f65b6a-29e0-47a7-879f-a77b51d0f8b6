# 环境安全统计接口说明

## 概述

本文档说明了环境安全统计模块的接口设计和实现，该模块提供环境安全相关的统计分析功能，包括环境指标概览、报警位置分布和报警数量趋势等统计数据。

## 接口列表

### 1. 环境安全概览统计

**接口地址**: `GET /lxbi/stat/environmental-safety/overview`

**功能描述**: 获取环境安全的总体概览数据

**请求参数**:
- `viewType`: 视图类型 (day/week/month)
- `startDate`: 开始日期 (yyyy-MM-dd)
- `endDate`: 结束日期 (yyyy-MM-dd)

**响应数据**:
```json
{
  "carbonMonoxideValue": 10,
  "carbonDioxideValue": 10,
  "nitrogenMonoxideValue": 10,
  "oxygenValue": 10,
  "temperatureValue": 10,
  "pressureValue": 10,
  "startDate": "2025-08-20",
  "endDate": "2025-08-25",
  "period": "day"
}
```

### 2. 环境安全报警位置分布统计

**接口地址**: `GET /lxbi/stat/environmental-safety/location-distribution`

**功能描述**: 获取环境安全报警位置分布数据，用于雷达图展示

**响应数据**:
```json
[
  {
    "locationName": "一号回风井井口",
    "depthLevel": "-992m",
    "alarmCount": 208,
    "xCoordinate": 0.0,
    "yCoordinate": 1.0,
    "locationCode": "LOC_001"
  },
  {
    "locationName": "",
    "depthLevel": "-1200",
    "alarmCount": 1200,
    "xCoordinate": 0.2,
    "yCoordinate": 1.0,
    "locationCode": "LOC_002"
  },
  {
    "locationName": "",
    "depthLevel": "-960m",
    "alarmCount": 230,
    "xCoordinate": -0.8,
    "yCoordinate": 0.6,
    "locationCode": "LOC_003"
  }
]
```

### 3. 环境安全报警数量趋势统计

**接口地址**: `GET /lxbi/stat/environmental-safety/data-trend`

**功能描述**: 获取环境安全报警数量趋势数据，用于折线图展示

**响应数据**:
```json
[
  {
    "timePoint": 1,
    "timeLabel": "1",
    "endEventCount": 12,
    "alarmCount": 16
  },
  {
    "timePoint": 2,
    "timeLabel": "2",
    "endEventCount": 10,
    "alarmCount": 15
  },
  {
    "timePoint": 3,
    "timeLabel": "3",
    "endEventCount": 8,
    "alarmCount": 14
  },
  {
    "timePoint": 4,
    "timeLabel": "4",
    "endEventCount": 6,
    "alarmCount": 13
  },
  {
    "timePoint": 5,
    "timeLabel": "5",
    "endEventCount": 5,
    "alarmCount": 12
  },
  {
    "timePoint": 6,
    "timeLabel": "6",
    "endEventCount": 2,
    "alarmCount": 9
  }
]
```

## 数据结构说明

### VO类设计

#### 1. EnvironmentalSafetyOverviewVO
- **用途**: 环境安全概览数据
- **字段**: 一氧化碳、二氧化碳、一氧化氮、氧气、温度、压力数值

#### 2. EnvironmentalSafetyLocationDistributionVO
- **用途**: 环境安全报警位置分布数据（雷达图）
- **字段**: 位置名称、深度标识、报警数量、坐标信息

#### 3. EnvironmentalSafetyDataTrendVO
- **用途**: 环境安全报警数量趋势数据（折线图）
- **字段**: 时间点、结束事件数量、报警数量

## 业务逻辑

### 1. 数据统计维度

**时间维度**:
- 日统计: 按天统计环境安全数据
- 周统计: 按周统计环境安全数据
- 月统计: 按月统计环境安全数据

**环境指标维度**:
- 一氧化碳: 有毒气体监测
- 二氧化碳: 窒息性气体监测
- 一氧化氮: 有害气体监测
- 氧气: 氧气浓度监测
- 温度: 环境温度监测
- 压力: 环境压力监测

### 2. 统计指标

**基础指标**:
- 一氧化碳数值: 一氧化碳浓度监测值
- 二氧化碳数值: 二氧化碳浓度监测值
- 一氧化氮数值: 一氧化氮浓度监测值
- 氧气数值: 氧气浓度监测值
- 温度数值: 环境温度监测值
- 压力数值: 环境压力监测值

**位置分析指标**:
- 深度分布: 不同深度层级的报警分布
- 空间坐标: 雷达图上的位置坐标
- 报警密度: 各位置的报警数量

**趋势分析指标**:
- 结束事件趋势: 已结束事件的数量变化
- 报警数量趋势: 报警事件的数量变化
- 下降趋势: 两个指标都呈现下降趋势

### 3. 数据展示

**概览卡片**:
- 一氧化碳: 10
- 二氧化碳: 10
- 一氧化氮: 10
- 氧气: 10
- 温度: 10
- 压力: 10

**雷达图展示**:
- 显示不同深度层级的报警分布
- 坐标范围: X和Y坐标均在[-1.0, 1.0]范围内
- 报警数量通过数值大小体现

**折线图展示**:
- 结束事件数量趋势（蓝色线）
- 报警数量趋势（红色线）
- 两条线都呈现下降趋势

## 模拟数据说明

### 1. 概览数据
- 所有环境指标数值均为10
- 一氧化碳: 10
- 二氧化碳: 10
- 一氧化氮: 10
- 氧气: 10
- 温度: 10
- 压力: 10

### 2. 位置分布数据
包含以下深度层级:
- **-992m**: 一号回风井井口 (208个报警)
- **-1200**: 1200个报警
- **-960m**: 230个报警
- **-960-840m**: 100个报警
- **-840m**: 119个报警
- **-480m**: 200个报警
- **-1130m**: 110个报警
- **-1020m**: 71个报警
- **-1020-1060m**: 135个报警
- **-1060m**: 200个报警

### 3. 趋势数据特点
**结束事件数据** (蓝色线):
- 时间点1: 12次 → 时间点6: 2次
- 呈现明显的下降趋势

**报警数量数据** (红色线):
- 时间点1: 16次 → 时间点6: 9次
- 呈现稳定的下降趋势

## 扩展接入点

### 1. 环境监测系统接入
```java
// 可接入的数据源
- 气体浓度监测系统
- 温度湿度监测系统
- 压力监测系统
- 空气质量监测系统
```

### 2. 数据接入方式
- **实时数据**: 通过Kafka队列接收传感器数据
- **定时同步**: 定时从监测系统同步数据
- **API接口**: 通过REST API获取监测数据

### 3. 数据库设计建议
```sql
-- 环境监测数据表
CREATE TABLE environmental_monitoring_data (
    id BIGSERIAL PRIMARY KEY,
    monitoring_point_code VARCHAR(50),
    monitoring_time TIMESTAMP,
    carbon_monoxide_value DECIMAL(10,3),
    carbon_dioxide_value DECIMAL(10,3),
    nitrogen_monoxide_value DECIMAL(10,3),
    oxygen_value DECIMAL(10,3),
    temperature_value DECIMAL(8,2),
    pressure_value DECIMAL(10,3),
    alarm_status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 环境监测点基础信息表
CREATE TABLE environmental_monitoring_point (
    id BIGSERIAL PRIMARY KEY,
    point_code VARCHAR(50) UNIQUE,
    point_name VARCHAR(100),
    location_name VARCHAR(100),
    depth_level VARCHAR(20),
    x_coordinate DECIMAL(10,6),
    y_coordinate DECIMAL(10,6),
    point_type VARCHAR(50),
    status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 环境阈值配置表
CREATE TABLE environmental_threshold_config (
    id BIGSERIAL PRIMARY KEY,
    indicator_type VARCHAR(50),
    warning_threshold DECIMAL(10,3),
    alarm_threshold DECIMAL(10,3),
    unit VARCHAR(20),
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 测试说明

### 1. 单元测试
- 所有Service方法的功能测试
- 不同视图类型的测试
- 数据完整性和一致性测试
- 环境指标完整性验证

### 2. 接口测试
```bash
# 测试概览统计
curl -X GET "http://localhost:8080/lxbi/stat/environmental-safety/overview?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试位置分布
curl -X GET "http://localhost:8080/lxbi/stat/environmental-safety/location-distribution?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试数据趋势
curl -X GET "http://localhost:8080/lxbi/stat/environmental-safety/data-trend?viewType=day&startDate=2025-08-20&endDate=2025-08-25"
```

### 3. 数据验证
- 环境指标完整性验证 (6个指标)
- 雷达图坐标范围验证 ([-1.0, 1.0])
- 趋势数据下降验证
- 所有指标数值为10验证

## 后续开发计划

### 1. 数据接入
- 接入真实的环境监测系统
- 建立气体传感器数据流
- 实现环境异常自动检测

### 2. 功能增强
- 环境阈值动态配置
- 预警机制建立
- 历史数据对比分析

### 3. 可视化优化
- 3D环境监测展示
- 实时数据刷新
- 移动端环境监控

## 业务价值

### 1. 安全管理提升
- **实时监控**: 及时发现环境安全问题
- **预警机制**: 提前预警环境异常
- **精确定位**: 准确定位问题发生位置

### 2. 运营效率优化
- **趋势分析**: 了解环境变化趋势
- **资源配置**: 基于数据优化监测点布局
- **维护计划**: 制定针对性的改善措施

### 3. 健康保障支持
- **职业健康**: 保障作业人员健康安全
- **环境质量**: 维护良好的作业环境
- **合规管理**: 满足环保法规要求

## 总结

环境安全统计模块提供了完整的环境监控和分析功能，通过多维度的统计分析，帮助管理人员及时了解环境状况，预防环境安全事故，保障作业人员健康和安全生产。

当前实现使用模拟数据，完美对应了界面图片中的所有功能模块，为后续接入真实监测数据预留了扩展接口，可以根据实际业务需求进行数据源的接入和功能的扩展。
