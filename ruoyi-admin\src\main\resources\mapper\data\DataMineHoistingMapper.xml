<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataMineHoistingMapper">
    
    <resultMap type="DataMineHoisting" id="DataMineHoistingResult">
        <result property="id"    column="id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="faultTime"    column="fault_time"    />
        <result property="buckets"    column="buckets"    />
        <result property="weight"    column="weight"    />
        <result property="faultReason"    column="fault_reason"    />
        <result property="faultStartTime"    column="fault_start_time"    />
        <result property="faultEndTime"    column="fault_end_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataMineHoistingVo">
        select dmh.id,
               dmh.working_period_id,
               dmh.operation_date,
               dmh.operation_time,
               dmh.fault_time,
               dmh.buckets,
               dmh.weight,
               dmh.fault_reason,
               dmh.fault_start_time,
               dmh.fault_end_time,
               dmh.create_by,
               dmh.create_time,
               dmh.update_by,
               dmh.update_time,
               bwp.working_period_name
        from data_mine_hoisting dmh
                 left join base_working_period bwp on bwp.working_period_id = dmh.working_period_id
    </sql>

    <select id="selectDataMineHoistingList" parameterType="DataMineHoisting" resultType="com.ruoyi.lxbi.domain.response.DataMineHoistingVo">
        <include refid="selectDataMineHoistingVo"/>
        <where>
            <if test="operationDate != null"> and dmh.operation_date = #{operationDate}</if>
            <if test="workingPeriodId != null "> and dmh.working_period_id = #{workingPeriodId}</if>
            <if test="params.beginOperationDate != null and params.beginOperationDate != '' and params.endOperationDate != null and params.endOperationDate != ''"> and dmh.operation_date between #{params.beginOperationDate}::date and #{params.endOperationDate}::date</if>
        </where>
    </select>
    
    <select id="selectDataMineHoistingById" parameterType="Long" resultMap="DataMineHoistingResult">
        <include refid="selectDataMineHoistingVo"/>
        where dmh.id = #{id}
    </select>

    <insert id="insertDataMineHoisting" parameterType="DataMineHoisting" useGeneratedKeys="true" keyProperty="id">
        insert into data_mine_hoisting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workingPeriodId != null">working_period_id,</if>
            <if test="operationDate != null">operation_date,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="faultTime != null">fault_time,</if>
            <if test="buckets != null">buckets,</if>
            <if test="weight != null">weight,</if>
            <if test="faultReason != null">fault_reason,</if>
            <if test="faultStartTime != null">fault_start_time,</if>
            <if test="faultEndTime != null">fault_end_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workingPeriodId != null">#{workingPeriodId},</if>
            <if test="operationDate != null">#{operationDate},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="faultTime != null">#{faultTime},</if>
            <if test="buckets != null">#{buckets},</if>
            <if test="weight != null">#{weight},</if>
            <if test="faultReason != null">#{faultReason},</if>
            <if test="faultStartTime != null">#{faultStartTime},</if>
            <if test="faultEndTime != null">#{faultEndTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataMineHoisting" parameterType="DataMineHoisting">
        update data_mine_hoisting
        <trim prefix="SET" suffixOverrides=",">
            <if test="workingPeriodId != null">working_period_id = #{workingPeriodId},</if>
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="faultTime != null">fault_time = #{faultTime},</if>
            <if test="buckets != null">buckets = #{buckets},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="faultReason != null">fault_reason = #{faultReason},</if>
            <if test="faultStartTime != null">fault_start_time = #{faultStartTime},</if>
            <if test="faultEndTime != null">fault_end_time = #{faultEndTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataMineHoistingById" parameterType="Long">
        delete from data_mine_hoisting where id = #{id}
    </delete>

    <delete id="deleteDataMineHoistingByIds" parameterType="String">
        delete from data_mine_hoisting where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDataMineHoistingByOperationDate" parameterType="java.util.Date" resultType="com.ruoyi.lxbi.domain.response.DataMineHoistingVo">
        <include refid="selectDataMineHoistingVo"/>
        where dmh.operation_date = #{operationDate}
        order by dmh.working_period_id asc
    </select>

    <insert id="batchInsertDataMineHoisting" parameterType="java.util.List">
        insert into data_mine_hoisting
        (working_period_id, operation_date, operation_time, fault_time, buckets, weight,
         fault_reason, fault_start_time, fault_end_time, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.workingPeriodId}, #{item.operationDate}, #{item.operationTime}, #{item.faultTime},
             #{item.buckets}, #{item.weight}, #{item.faultReason}, #{item.faultStartTime}, #{item.faultEndTime},
             #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>