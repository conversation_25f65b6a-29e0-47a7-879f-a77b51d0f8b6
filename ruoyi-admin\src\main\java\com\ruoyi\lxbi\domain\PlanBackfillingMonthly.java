package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 充填月计划对象 plan_backfilling_monthly
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanBackfillingMonthly extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 工作面ID */
    private Long workingFaceId;

    /** 采场ID */
    private Long stopeId;

    /** 充填方量 */
    @Excel(name = "充填方量")
    private BigDecimal fillingVolume;

    /** 计划月份 */
    @Excel(name = "计划月份")
    private String planDate;

}
