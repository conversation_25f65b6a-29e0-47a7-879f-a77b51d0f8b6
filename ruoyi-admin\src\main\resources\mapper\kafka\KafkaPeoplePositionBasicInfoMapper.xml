<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.admin.mapper.KafkaPeoplePositionBasicInfoMapper">
    
    <resultMap type="KafkaPeoplePositionBasicInfo" id="KafkaPeoplePositionBasicInfoResult">
        <result property="id"    column="id"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="approvedUndergroundCount"    column="approved_underground_count"    />
        <result property="systemModel"    column="system_model"    />
        <result property="systemName"    column="system_name"    />
        <result property="manufacturerName"    column="manufacturer_name"    />
        <result property="safetyCertificateExpiry"    column="safety_certificate_expiry"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="personCardCode"    column="person_card_code"    />
        <result property="personName"    column="person_name"    />
        <result property="jobType"    column="job_type"    />
        <result property="position"    column="position"    />
        <result property="department"    column="department"    />
        <result property="birthDate"    column="birth_date"    />
        <result property="education"    column="education"    />
        <result property="isMineLeader"    column="is_mine_leader"    />
        <result property="isSpecialPersonnel"    column="is_special_personnel"    />
        <result property="status"    column="status"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKafkaPeoplePositionBasicInfoVo">
        select id, mine_code, mine_name, approved_underground_count, system_model, system_name, manufacturer_name, safety_certificate_expiry, data_upload_time, person_card_code, person_name, job_type, position, department, birth_date, education, is_mine_leader, is_special_personnel, status, is_deleted, create_by, create_time, update_by, update_time, remark from kafka_people_position_basic_info
    </sql>

    <select id="selectKafkaPeoplePositionBasicInfoList" parameterType="KafkaPeoplePositionBasicInfo" resultMap="KafkaPeoplePositionBasicInfoResult">
        <include refid="selectKafkaPeoplePositionBasicInfoVo"/>
        <where>  
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="approvedUndergroundCount != null  and approvedUndergroundCount != ''"> and approved_underground_count = #{approvedUndergroundCount}</if>
            <if test="systemModel != null  and systemModel != ''"> and system_model = #{systemModel}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="manufacturerName != null  and manufacturerName != ''"> and manufacturer_name like concat('%', #{manufacturerName}, '%')</if>
            <if test="safetyCertificateExpiry != null "> and safety_certificate_expiry = #{safetyCertificateExpiry}</if>
            <if test="dataUploadTime != null  and dataUploadTime != ''"> and data_upload_time = #{dataUploadTime}</if>
            <if test="personName != null  and personName != ''"> and person_name like concat('%', #{personName}, '%')</if>
            <if test="jobType != null  and jobType != ''"> and job_type = #{jobType}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="department != null  and department != ''"> and department = #{department}</if>
            <if test="birthDate != null  and birthDate != ''"> and birth_date = #{birthDate}</if>
            <if test="education != null  and education != ''"> and education = #{education}</if>
        </where>
    </select>
    
    <select id="selectKafkaPeoplePositionBasicInfoById" parameterType="Long" resultMap="KafkaPeoplePositionBasicInfoResult">
        <include refid="selectKafkaPeoplePositionBasicInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertKafkaPeoplePositionBasicInfo" parameterType="KafkaPeoplePositionBasicInfo" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_people_position_basic_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mineCode != null and mineCode != ''">mine_code,</if>
            <if test="mineName != null and mineName != ''">mine_name,</if>
            <if test="approvedUndergroundCount != null">approved_underground_count,</if>
            <if test="systemModel != null">system_model,</if>
            <if test="systemName != null">system_name,</if>
            <if test="manufacturerName != null">manufacturer_name,</if>
            <if test="safetyCertificateExpiry != null">safety_certificate_expiry,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="personCardCode != null">person_card_code,</if>
            <if test="personName != null and personName != ''">person_name,</if>
            <if test="jobType != null">job_type,</if>
            <if test="position != null">position,</if>
            <if test="department != null">department,</if>
            <if test="birthDate != null">birth_date,</if>
            <if test="education != null">education,</if>
            <if test="isMineLeader != null">is_mine_leader,</if>
            <if test="isSpecialPersonnel != null">is_special_personnel,</if>
            <if test="status != null">status,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mineCode != null and mineCode != ''">#{mineCode},</if>
            <if test="mineName != null and mineName != ''">#{mineName},</if>
            <if test="approvedUndergroundCount != null">#{approvedUndergroundCount},</if>
            <if test="systemModel != null">#{systemModel},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="manufacturerName != null">#{manufacturerName},</if>
            <if test="safetyCertificateExpiry != null">#{safetyCertificateExpiry},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="personCardCode != null">#{personCardCode},</if>
            <if test="personName != null and personName != ''">#{personName},</if>
            <if test="jobType != null">#{jobType},</if>
            <if test="position != null">#{position},</if>
            <if test="department != null">#{department},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="education != null">#{education},</if>
            <if test="isMineLeader != null">#{isMineLeader},</if>
            <if test="isSpecialPersonnel != null">#{isSpecialPersonnel},</if>
            <if test="status != null">#{status},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKafkaPeoplePositionBasicInfo" parameterType="KafkaPeoplePositionBasicInfo">
        update kafka_people_position_basic_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="mineCode != null and mineCode != ''">mine_code = #{mineCode},</if>
            <if test="mineName != null and mineName != ''">mine_name = #{mineName},</if>
            <if test="approvedUndergroundCount != null">approved_underground_count = #{approvedUndergroundCount},</if>
            <if test="systemModel != null">system_model = #{systemModel},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="manufacturerName != null">manufacturer_name = #{manufacturerName},</if>
            <if test="safetyCertificateExpiry != null">safety_certificate_expiry = #{safetyCertificateExpiry},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="personCardCode != null">person_card_code = #{personCardCode},</if>
            <if test="personName != null and personName != ''">person_name = #{personName},</if>
            <if test="jobType != null">job_type = #{jobType},</if>
            <if test="position != null">position = #{position},</if>
            <if test="department != null">department = #{department},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="education != null">education = #{education},</if>
            <if test="isMineLeader != null">is_mine_leader = #{isMineLeader},</if>
            <if test="isSpecialPersonnel != null">is_special_personnel = #{isSpecialPersonnel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaPeoplePositionBasicInfoById" parameterType="Long">
        delete from kafka_people_position_basic_info where id = #{id}
    </delete>

    <delete id="deleteKafkaPeoplePositionBasicInfoByIds" parameterType="String">
        delete from kafka_people_position_basic_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据人员卡编码查询 -->
    <select id="selectByPersonCardCode" parameterType="String" resultMap="KafkaPeoplePositionBasicInfoResult">
        <include refid="selectKafkaPeoplePositionBasicInfoVo"/>
        where person_card_code = #{personCardCode}
    </select>

    <!-- 根据人员卡编码更新 -->
    <update id="updateByPersonCardCode" parameterType="KafkaPeoplePositionBasicInfo">
        update kafka_people_position_basic_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="mineCode != null and mineCode != ''">mine_code = #{mineCode},</if>
            <if test="mineName != null and mineName != ''">mine_name = #{mineName},</if>
            <if test="approvedUndergroundCount != null">approved_underground_count = #{approvedUndergroundCount},</if>
            <if test="systemModel != null">system_model = #{systemModel},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="manufacturerName != null">manufacturer_name = #{manufacturerName},</if>
            <if test="safetyCertificateExpiry != null">safety_certificate_expiry = #{safetyCertificateExpiry},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="personName != null and personName != ''">person_name = #{personName},</if>
            <if test="jobType != null">job_type = #{jobType},</if>
            <if test="position != null">position = #{position},</if>
            <if test="department != null">department = #{department},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="education != null">education = #{education},</if>
            <if test="isMineLeader != null">is_mine_leader = #{isMineLeader},</if>
            <if test="isSpecialPersonnel != null">is_special_personnel = #{isSpecialPersonnel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where person_card_code = #{personCardCode}
    </update>

    <!-- PostgreSQL UPSERT操作 -->
    <insert id="upsertKafkaPeoplePositionBasicInfo" parameterType="KafkaPeoplePositionBasicInfo">
        INSERT INTO kafka_people_position_basic_info (
            mine_code, mine_name, approved_underground_count, system_model, system_name,
            manufacturer_name, safety_certificate_expiry, data_upload_time, person_card_code,
            person_name, job_type, position, department, birth_date, education,
            is_mine_leader, is_special_personnel, status, is_deleted,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{mineCode}, #{mineName}, #{approvedUndergroundCount}, #{systemModel}, #{systemName},
            #{manufacturerName}, #{safetyCertificateExpiry}, #{dataUploadTime}, #{personCardCode},
            #{personName}, #{jobType}, #{position}, #{department}, #{birthDate}, #{education},
            #{isMineLeader}, #{isSpecialPersonnel}, #{status}, #{isDeleted},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
        ON CONFLICT (person_card_code)
        DO UPDATE SET
            mine_code = EXCLUDED.mine_code,
            mine_name = EXCLUDED.mine_name,
            approved_underground_count = EXCLUDED.approved_underground_count,
            system_model = EXCLUDED.system_model,
            system_name = EXCLUDED.system_name,
            manufacturer_name = EXCLUDED.manufacturer_name,
            safety_certificate_expiry = EXCLUDED.safety_certificate_expiry,
            data_upload_time = EXCLUDED.data_upload_time,
            person_name = EXCLUDED.person_name,
            job_type = EXCLUDED.job_type,
            position = EXCLUDED.position,
            department = EXCLUDED.department,
            birth_date = EXCLUDED.birth_date,
            education = EXCLUDED.education,
            is_mine_leader = EXCLUDED.is_mine_leader,
            is_special_personnel = EXCLUDED.is_special_personnel,
            status = EXCLUDED.status,
            update_by = EXCLUDED.update_by,
            update_time = EXCLUDED.update_time,
            remark = EXCLUDED.remark
            -- 注意：不更新 create_by 和 create_time，保持原有的创建信息
    </insert>

    <!-- 统计指定日期范围内的活跃人员数量 (PostgreSQL) -->
    <select id="countActivePersonnelByDateRange" resultType="Long">
        select count(distinct person_card_code)
        from kafka_people_position_basic_info
        where status = 1 and is_deleted = 0
        and update_time::date between #{startDate}::date and #{endDate}::date
    </select>

</mapper>