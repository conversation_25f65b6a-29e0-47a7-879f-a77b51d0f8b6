<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.BaseWorkingFaceMapper">
    
    <resultMap type="BaseWorkingFace" id="BaseWorkingFaceResult">
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="workingFaceName"    column="working_face_name"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectBaseWorkingFaceVo">
        select working_face_id, working_face_name, status, create_by, create_time, update_by, update_time, start_time, end_time, is_delete from base_working_face
    </sql>

    <select id="selectBaseWorkingFaceList" parameterType="BaseWorkingFace" resultMap="BaseWorkingFaceResult">
        <include refid="selectBaseWorkingFaceVo"/>
        <where>  
            <if test="workingFaceName != null  and workingFaceName != ''"> and working_face_name like concat('%', #{workingFaceName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and start_time between #{params.beginStartTime}::date and #{params.endStartTime}::date</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and end_time between #{params.beginEndTime}::date and #{params.endEndTime}::date</if>
            and is_delete = 0
        </where>
    </select>
    
    <select id="selectBaseWorkingFaceByWorkingFaceId" parameterType="Long" resultMap="BaseWorkingFaceResult">
        <include refid="selectBaseWorkingFaceVo"/>
        where working_face_id = #{workingFaceId}
    </select>
    <select id="getBaseWorkingFaceByWorkingFaceIds" resultType="java.lang.Integer">
        select count(1) as count from base_working_face where working_face_id =
        <foreach item="workingFaceId" collection="array" open="(" separator="," close=")">
            #{workingFaceId}
        </foreach>
        and is_delete = 0 and status = 1;
    </select>

    <insert id="insertBaseWorkingFace" parameterType="BaseWorkingFace" useGeneratedKeys="true" keyProperty="workingFaceId">
        insert into base_working_face
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workingFaceName != null">working_face_name,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workingFaceName != null">#{workingFaceName},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updateBaseWorkingFace" parameterType="BaseWorkingFace">
        update base_working_face
        <trim prefix="SET" suffixOverrides=",">
            <if test="workingFaceName != null">working_face_name = #{workingFaceName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where working_face_id = #{workingFaceId}
    </update>

    <delete id="deleteBaseWorkingFaceByWorkingFaceId" parameterType="Long">
        update base_working_face set is_delete=1 where working_face_id = #{workingFaceId}
    </delete>

    <delete id="deleteBaseWorkingFaceByWorkingFaceIds" parameterType="String">
        update base_working_face set is_delete=1 where working_face_id in
        <foreach item="workingFaceId" collection="array" open="(" separator="," close=")">
            #{workingFaceId}
        </foreach>
    </delete>
</mapper>