<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.admin.mapper.KafkaPeoplePositionTimeOverMapper">
    
    <resultMap type="KafkaPeoplePositionTimeOver" id="KafkaPeoplePositionTimeOverResult">
        <result property="id"    column="id"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="personCardCode"    column="person_card_code"    />
        <result property="personName"    column="person_name"    />
        <result property="enterWellTime"    column="enter_well_time"    />
        <result property="alarmStartTime"    column="alarm_start_time"    />
        <result property="alarmEndTime"    column="alarm_end_time"    />
        <result property="areaCode"    column="area_code"    />
        <result property="enterCurrentAreaTime"    column="enter_current_area_time"    />
        <result property="baseStationCode"    column="base_station_code"    />
        <result property="enterCurrentBaseStationTime"    column="enter_current_base_station_time"    />
        <result property="status"    column="status"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKafkaPeoplePositionTimeOverVo">
        select id, mine_code, mine_name, data_upload_time, person_card_code, person_name, enter_well_time, alarm_start_time, alarm_end_time, area_code, enter_current_area_time, base_station_code, enter_current_base_station_time, status, is_deleted, create_by, create_time, update_by, update_time, remark from kafka_people_position_time_over
    </sql>

    <select id="selectKafkaPeoplePositionTimeOverList" parameterType="KafkaPeoplePositionTimeOver" resultMap="KafkaPeoplePositionTimeOverResult">
        <include refid="selectKafkaPeoplePositionTimeOverVo"/>
        <where>  
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="dataUploadTime != null "> and data_upload_time = #{dataUploadTime}</if>
            <if test="personCardCode != null  and personCardCode != ''"> and person_card_code = #{personCardCode}</if>
            <if test="personName != null  and personName != ''"> and person_name like concat('%', #{personName}, '%')</if>
            <if test="enterWellTime != null "> and enter_well_time = #{enterWellTime}</if>
            <if test="alarmStartTime != null "> and alarm_start_time = #{alarmStartTime}</if>
            <if test="alarmEndTime != null "> and alarm_end_time = #{alarmEndTime}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="enterCurrentAreaTime != null "> and enter_current_area_time = #{enterCurrentAreaTime}</if>
            <if test="baseStationCode != null  and baseStationCode != ''"> and base_station_code = #{baseStationCode}</if>
            <if test="enterCurrentBaseStationTime != null "> and enter_current_base_station_time = #{enterCurrentBaseStationTime}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
        </where>
        order by data_upload_time desc
    </select>
    
    <select id="selectKafkaPeoplePositionTimeOverById" parameterType="Long" resultMap="KafkaPeoplePositionTimeOverResult">
        <include refid="selectKafkaPeoplePositionTimeOverVo"/>
        where id = #{id}
    </select>

    <select id="selectLatestByPersonCardCode" parameterType="String" resultMap="KafkaPeoplePositionTimeOverResult">
        <include refid="selectKafkaPeoplePositionTimeOverVo"/>
        where person_card_code = #{personCardCode} and is_deleted = 0
        order by data_upload_time desc
        limit 1
    </select>

    <select id="selectByPersonCardCodeAndAlarmStartTime" resultMap="KafkaPeoplePositionTimeOverResult">
        <include refid="selectKafkaPeoplePositionTimeOverVo"/>
        where person_card_code = #{personCardCode}
        and TO_CHAR(alarm_start_time, 'YYYY-MM-DD HH24:MI:SS') = #{alarmStartTime}
        and is_deleted = 0
    </select>

    <select id="countTimeoutPersonnelByDateRange" resultType="Long">
        select count(distinct person_card_code)
        from kafka_people_position_time_over
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
    </select>

    <select id="countAreaTimeoutByDateRange" resultType="Long">
        select count(*)
        from kafka_people_position_time_over
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
    </select>

    <select id="selectTimeoutPersonnelByDateRange" resultMap="KafkaPeoplePositionTimeOverResult">
        <include refid="selectKafkaPeoplePositionTimeOverVo"/>
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
        order by data_upload_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <!-- 查询指定日期范围内的超时人员聚合统计 -->
    <select id="selectTimeoutPersonnelAggregateByDateRange" resultType="com.ruoyi.lxbi.domain.vo.TimeoutPersonnelAggregateVO">
        select person_card_code as personCardCode,
               person_name as personName,
               area_code as areaCode,
               count(*) as timeoutCount,
               EXTRACT(EPOCH FROM (max(data_upload_time) - min(enter_current_area_time)))/3600 as stayHours
        from kafka_people_position_time_over
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
        group by person_card_code, person_name, area_code
        order by timeoutCount desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="selectTimeoutGroupDistributionByDateRange" resultType="java.util.Map">
        select
            CASE
                WHEN person_name ~ '^[A-C]' THEN 'A班组'
                WHEN person_name ~ '^[D-F]' THEN 'B班组'
                ELSE 'C班组'
            END as group_name,
            count(*) as timeout_count
        from kafka_people_position_time_over
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
        group by group_name
        order by timeout_count desc
    </select>

    <select id="selectTimeoutCountByDate" resultType="java.util.Map">
        select
            data_upload_time::date as date,
            count(*) as timeout_count,
            count(distinct person_card_code) as timeout_personnel_count
        from kafka_people_position_time_over
        where is_deleted = 0
        and data_upload_time::date between #{startDate}::date and #{endDate}::date
        group by data_upload_time::date
        order by date
    </select>

    <select id="selectRecentTimeoutRecords" resultMap="KafkaPeoplePositionTimeOverResult">
        <include refid="selectKafkaPeoplePositionTimeOverVo"/>
        where is_deleted = 0
        order by data_upload_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
        
    <insert id="insertKafkaPeoplePositionTimeOver" parameterType="KafkaPeoplePositionTimeOver" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_people_position_time_over
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mineCode != null">mine_code,</if>
            <if test="mineName != null">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="personCardCode != null">person_card_code,</if>
            <if test="personName != null">person_name,</if>
            <if test="enterWellTime != null">enter_well_time,</if>
            <if test="alarmStartTime != null">alarm_start_time,</if>
            <if test="alarmEndTime != null">alarm_end_time,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="enterCurrentAreaTime != null">enter_current_area_time,</if>
            <if test="baseStationCode != null">base_station_code,</if>
            <if test="enterCurrentBaseStationTime != null">enter_current_base_station_time,</if>
            <if test="status != null">status,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mineCode != null">#{mineCode},</if>
            <if test="mineName != null">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="personCardCode != null">#{personCardCode},</if>
            <if test="personName != null">#{personName},</if>
            <if test="enterWellTime != null">#{enterWellTime},</if>
            <if test="alarmStartTime != null">#{alarmStartTime},</if>
            <if test="alarmEndTime != null">#{alarmEndTime},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="enterCurrentAreaTime != null">#{enterCurrentAreaTime},</if>
            <if test="baseStationCode != null">#{baseStationCode},</if>
            <if test="enterCurrentBaseStationTime != null">#{enterCurrentBaseStationTime},</if>
            <if test="status != null">#{status},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKafkaPeoplePositionTimeOver" parameterType="KafkaPeoplePositionTimeOver">
        update kafka_people_position_time_over
        <trim prefix="SET" suffixOverrides=",">
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="mineName != null">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="personCardCode != null">person_card_code = #{personCardCode},</if>
            <if test="personName != null">person_name = #{personName},</if>
            <if test="enterWellTime != null">enter_well_time = #{enterWellTime},</if>
            <if test="alarmStartTime != null">alarm_start_time = #{alarmStartTime},</if>
            <if test="alarmEndTime != null">alarm_end_time = #{alarmEndTime},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="enterCurrentAreaTime != null">enter_current_area_time = #{enterCurrentAreaTime},</if>
            <if test="baseStationCode != null">base_station_code = #{baseStationCode},</if>
            <if test="enterCurrentBaseStationTime != null">enter_current_base_station_time = #{enterCurrentBaseStationTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaPeoplePositionTimeOverById" parameterType="Long">
        delete from kafka_people_position_time_over where id = #{id}
    </delete>

    <delete id="deleteKafkaPeoplePositionTimeOverByIds" parameterType="String">
        delete from kafka_people_position_time_over where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
