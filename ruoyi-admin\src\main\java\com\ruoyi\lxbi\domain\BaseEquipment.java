package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 设备数据对象 base_equipment
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseEquipment extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 设备数据ID */
    private Long id;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String equipmentNo;

    /** 矿车数量 */
    @Excel(name = "矿车数量")
    private Long mineCarsNumber;

    /** 单位效率 */
    @Excel(name = "单位效率")
    private BigDecimal unitEfficiency;


    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 是否删除 */
    private Long isDelete;

    /**设备类型*/
    private Integer equipmentType;

}
