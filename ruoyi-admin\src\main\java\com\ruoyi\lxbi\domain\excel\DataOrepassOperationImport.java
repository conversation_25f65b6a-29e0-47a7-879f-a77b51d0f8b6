package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import lombok.Data;

import java.util.Date;

/**
 * 溜井运行数据导入对象
 */
@Data
@ExcelImportTemplate(
        key = "data_orepass_operation",
        name = "溜井运行数据导入",
        description = "用于导入溜井运行数据",
        sheetName = "溜井数据"
)
public class DataOrepassOperationImport {

    /**
     * 作业日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "作业日期", index = 0)
    @ExcelRequired(message = "作业日期不能为空")
    @ExcelSelected(prompt = "请输入作业日期，格式：yyyy-MM-dd，例如：2025-01-15")
    private Date operationDate;

    /**
     * 项目部门ID（存储值）
     */
    @ExcelSelected(
            optionKey = "projectDepartment",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long projectDepartmentId;

    /**
     * 项目部门名称（显示值）
     */
    @ExcelProperty(value = "项目部门", index = 1)
    @ExcelRequired(message = "项目部门不能为空")
    @ExcelSelected(
            optionKey = "projectDepartment",
            prompt = "请选择项目部门",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String projectDepartmentName;

    /**
     * 作业时段ID（存储值）
     */
    @ExcelSelected(
            optionKey = "workingPeriod",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long workingPeriodId;

    /**
     * 作业时段名称（显示值）
     */
    @ExcelProperty(value = "作业时段", index = 2)
    @ExcelRequired(message = "作业时段不能为空")
    @ExcelSelected(
            optionKey = "workingPeriod",
            prompt = "请选择作业时段",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String workingPeriodName;

    /**
     * 溜井ID（存储值）
     */
    @ExcelSelected(
            optionKey = "orePass",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long orePassId;

    /**
     * 溜井名称（显示值）
     */
    @ExcelProperty(value = "溜井", index = 3)
    @ExcelRequired(message = "溜井不能为空")
    @ExcelSelected(
            optionKey = "orePass",
            prompt = "请选择溜井",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String orePassName;

    /**
     * 溜放趟数
     */
    @ExcelProperty(value = "溜放趟数", index = 4)
    @ExcelSelected(prompt = "请输入溜放趟数")
    private Long trips;

    /**
     * 溜放矿石量（吨）
     */
    @ExcelProperty(value = "溜放矿石量(吨)", index = 5)
    @ExcelSelected(prompt = "请输入溜放矿石量，单位：吨")
    private Double oreTons;
}
