package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaSubstationRealTimeData;

/**
 * 分站实时数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface IKafkaSubstationRealTimeDataService 
{
    /**
     * 查询分站实时数据
     * 
     * @param id 分站实时数据主键
     * @return 分站实时数据
     */
    public KafkaSubstationRealTimeData selectKafkaSubstationRealTimeDataById(Long id);

    /**
     * 查询分站实时数据列表
     * 
     * @param kafkaSubstationRealTimeData 分站实时数据
     * @return 分站实时数据集合
     */
    public List<KafkaSubstationRealTimeData> selectKafkaSubstationRealTimeDataList(KafkaSubstationRealTimeData kafkaSubstationRealTimeData);

    /**
     * 新增分站实时数据
     * 
     * @param kafkaSubstationRealTimeData 分站实时数据
     * @return 结果
     */
    public int insertKafkaSubstationRealTimeData(KafkaSubstationRealTimeData kafkaSubstationRealTimeData);

    /**
     * 修改分站实时数据
     * 
     * @param kafkaSubstationRealTimeData 分站实时数据
     * @return 结果
     */
    public int updateKafkaSubstationRealTimeData(KafkaSubstationRealTimeData kafkaSubstationRealTimeData);

    /**
     * 批量删除分站实时数据
     * 
     * @param ids 需要删除的分站实时数据主键集合
     * @return 结果
     */
    public int deleteKafkaSubstationRealTimeDataByIds(Long[] ids);

    /**
     * 删除分站实时数据信息
     *
     * @param id 分站实时数据主键
     * @return 结果
     */
    public int deleteKafkaSubstationRealTimeDataById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaSubstationRealTimeData parseKafkaMessage(String kafkaMessage);
}
