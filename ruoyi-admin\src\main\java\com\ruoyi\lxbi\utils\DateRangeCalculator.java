package com.ruoyi.lxbi.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
 * 日期范围计算工具类
 * 支持财务月份和特殊周计算
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Slf4j
public class DateRangeCalculator {
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    
    /**
     * 根据视图类型计算日期范围
     * @param viewType 视图类型：daily/weekly/monthly
     * @param inputStartDate 输入的开始日期
     * @param inputEndDate 输入的结束日期
     * @return 计算后的日期范围
     */
    public static Map<String, String> calculateDateRangeByViewType(String viewType, String inputStartDate, String inputEndDate) {
        Map<String, String> dateRange = new HashMap<>();
        
        try {
            // 如果两个日期都没有提供，使用默认范围
            if (inputStartDate == null && inputEndDate == null) {
                return getDefaultDateRange();
            }
            
            // 确定基准日期
            String baseDate = inputStartDate != null ? inputStartDate : inputEndDate;
            Calendar cal = Calendar.getInstance();
            cal.setTime(DATE_FORMAT.parse(baseDate));
            
            switch (viewType.toLowerCase()) {
                case "daily":
                    // 按日：如果只有一个日期，另一个使用相同日期
                    String startDate = inputStartDate != null ? inputStartDate : inputEndDate;
                    String endDate = inputEndDate != null ? inputEndDate : inputStartDate;
                    dateRange.put("startDate", startDate);
                    dateRange.put("endDate", endDate);
                    break;
                    
                case "weekly":
                    // 按周：周四到周三为一周
                    Map<String, String> weekRange = calculateWeekRange(cal);
                    dateRange.put("startDate", weekRange.get("startDate"));
                    dateRange.put("endDate", weekRange.get("endDate"));
                    break;
                    
                case "monthly":
                    // 按月：财务月份计算（29号及以后算下个月）
                    Map<String, String> monthRange = calculateFinancialMonthRange(cal);
                    dateRange.put("startDate", monthRange.get("startDate"));
                    dateRange.put("endDate", monthRange.get("endDate"));
                    break;
                    
                default:
                    // 默认按日处理
                    dateRange.put("startDate", baseDate);
                    dateRange.put("endDate", baseDate);
                    break;
            }
            
            log.debug("根据视图类型计算日期范围 - 类型: {}, 基准日期: {}, 开始日期: {}, 结束日期: {}", 
                    viewType, baseDate, dateRange.get("startDate"), dateRange.get("endDate"));
                    
        } catch (Exception e) {
            log.error("计算日期范围失败", e);
            return getDefaultDateRange();
        }
        
        return dateRange;
    }
    
    /**
     * 计算周范围（周四到周三为一周）
     * 参考数据库函数：get_week_thu_to_wed
     * @param cal 基准日期的Calendar对象
     * @return 周的开始和结束日期
     */
    public static Map<String, String> calculateWeekRange(Calendar cal) {
        Map<String, String> weekRange = new HashMap<>();
        
        // 获取星期几 (1=Sunday, 2=Monday, ..., 7=Saturday)
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        
        // 计算距离本周周四的天数
        // 周四是5，如果当前是周四到周三(5,6,7,1,2,3,4)，则计算到本周周四的距离
        int daysToThursday;
        if (dayOfWeek >= Calendar.THURSDAY) {
            // 周四、周五、周六：距离本周周四的天数
            daysToThursday = dayOfWeek - Calendar.THURSDAY;
        } else {
            // 周日、周一、周二、周三：距离上周周四的天数
            daysToThursday = dayOfWeek + 3;
        }
        
        // 计算本周的周四日期（周的开始日期）
        Calendar weekStart = (Calendar) cal.clone();
        weekStart.add(Calendar.DAY_OF_MONTH, -daysToThursday);
        
        // 计算本周的周三日期（周的结束日期）
        Calendar weekEnd = (Calendar) weekStart.clone();
        weekEnd.add(Calendar.DAY_OF_MONTH, 6);
        
        weekRange.put("startDate", DATE_FORMAT.format(weekStart.getTime()));
        weekRange.put("endDate", DATE_FORMAT.format(weekEnd.getTime()));
        
        log.debug("计算周范围 - 基准日期: {}, 周开始: {}, 周结束: {}", 
                DATE_FORMAT.format(cal.getTime()), weekRange.get("startDate"), weekRange.get("endDate"));
        
        return weekRange;
    }
    
    /**
     * 计算财务月份范围（29号及以后算下个月）
     * 参考数据库函数：get_financial_month
     * @param cal 基准日期的Calendar对象
     * @return 财务月份的开始和结束日期
     */
    public static Map<String, String> calculateFinancialMonthRange(Calendar cal) {
        Map<String, String> monthRange = new HashMap<>();
        
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int month = cal.get(Calendar.MONTH);
        int year = cal.get(Calendar.YEAR);
        
        // 财务月份计算：如果是29号及以后，则算下个月
        if (day >= 29) {
            month += 1;
            if (month == 12) { // Calendar.MONTH是0-11，所以12表示下一年的1月
                month = 0;
                year += 1;
            }
        }
        
        // 计算财务月份的第一天
        Calendar monthStart = Calendar.getInstance();
        monthStart.set(year, month, 1);
        
        // 计算财务月份的最后一天
        Calendar monthEnd = Calendar.getInstance();
        monthEnd.set(year, month, monthStart.getActualMaximum(Calendar.DAY_OF_MONTH));
        
        monthRange.put("startDate", DATE_FORMAT.format(monthStart.getTime()));
        monthRange.put("endDate", DATE_FORMAT.format(monthEnd.getTime()));
        
        log.debug("计算财务月份范围 - 基准日期: {}, 财务月开始: {}, 财务月结束: {}", 
                DATE_FORMAT.format(cal.getTime()), monthRange.get("startDate"), monthRange.get("endDate"));
        
        return monthRange;
    }
    
    /**
     * 获取默认日期范围（最近7天）
     * @return 默认日期范围
     */
    public static Map<String, String> getDefaultDateRange() {
        Map<String, String> dateRange = new HashMap<>();
        Calendar cal = Calendar.getInstance();
        
        // 结束日期为昨日
        cal.add(Calendar.DAY_OF_MONTH, -1);
        String endDate = DATE_FORMAT.format(cal.getTime());
        
        // 开始日期为7天前
        cal.add(Calendar.DAY_OF_MONTH, -6);
        String startDate = DATE_FORMAT.format(cal.getTime());
        
        dateRange.put("startDate", startDate);
        dateRange.put("endDate", endDate);
        
        log.debug("默认日期范围 - 开始日期: {}, 结束日期: {}", startDate, endDate);
        
        return dateRange;
    }
    
    /**
     * 获取昨日日期
     * @return 昨日日期字符串
     */
    public static String getYesterdayDate() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return DATE_FORMAT.format(cal.getTime());
    }

    /**
     * 获取趋势统计的默认日期范围（从昨天往前推一个月）
     * @return 趋势统计默认日期范围
     */
    public static Map<String, String> getTrendDefaultDateRange() {
        Map<String, String> dateRange = new HashMap<>();
        Calendar cal = Calendar.getInstance();

        // 结束日期为昨日
        cal.add(Calendar.DAY_OF_MONTH, -1);
        String endDate = DATE_FORMAT.format(cal.getTime());

        // 开始日期为昨日往前推一个月（30天）
        cal.add(Calendar.DAY_OF_MONTH, -30);
        String startDate = DATE_FORMAT.format(cal.getTime());

        dateRange.put("startDate", startDate);
        dateRange.put("endDate", endDate);

        log.debug("趋势统计默认日期范围 - 开始日期: {}, 结束日期: {}", startDate, endDate);

        return dateRange;
    }
    
    /**
     * 验证视图类型参数
     * @param viewType 视图类型
     * @return 是否有效
     */
    public static boolean isValidViewType(String viewType) {
        return "daily".equals(viewType) || "weekly".equals(viewType) || "monthly".equals(viewType);
    }

    /**
     * 计算日期范围的天数
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期范围的天数（包含开始和结束日期）
     */
    public static int calculateDaysBetween(String startDate, String endDate) {
        try {
            Calendar startCal = Calendar.getInstance();
            Calendar endCal = Calendar.getInstance();

            startCal.setTime(DATE_FORMAT.parse(startDate));
            endCal.setTime(DATE_FORMAT.parse(endDate));

            // 计算当前日期范围的天数（包含开始和结束日期）
            long diffInMillis = endCal.getTimeInMillis() - startCal.getTimeInMillis();
            int days = (int) (diffInMillis / (1000 * 60 * 60 * 24)) + 1;

            log.debug("计算日期范围天数 - 开始日期: {}, 结束日期: {}, 天数: {}", startDate, endDate, days);
            return days;
        } catch (Exception e) {
            log.error("计算日期范围天数失败", e);
            return 1; // 出错时返回1天
        }
    }

    /**
     * 判断日期范围是否为一天
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 是否为一天
     */
    public static boolean isSingleDay(String startDate, String endDate) {
        return calculateDaysBetween(startDate, endDate) == 1;
    }

    /**
     * 确保按日查询的最小天数
     * 如果当前日期范围的天数少于指定的最小天数，则向前推开始日期
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param minDays 最小天数
     * @return 调整后的开始日期
     */
    public static String ensureMinimumDaysForDaily(String startDate, String endDate, int minDays) {
        try {
            int currentDays = calculateDaysBetween(startDate, endDate);

            log.debug("当前日期范围天数: {}, 最小要求天数: {}", currentDays, minDays);

            // 如果当前天数少于最小天数，向前推开始日期
            if (currentDays < minDays) {
                Calendar startCal = Calendar.getInstance();
                startCal.setTime(DATE_FORMAT.parse(startDate));

                int daysToAdd = minDays - currentDays;
                startCal.add(Calendar.DAY_OF_MONTH, -daysToAdd);
                String adjustedStartDate = DATE_FORMAT.format(startCal.getTime());

                log.info("按日查询天数不足{}天，开始日期从 {} 调整为 {}", minDays, startDate, adjustedStartDate);
                return adjustedStartDate;
            }

            return startDate;
        } catch (Exception e) {
            log.error("调整按日查询最小天数失败", e);
            return startDate; // 出错时返回原始开始日期
        }
    }
}
