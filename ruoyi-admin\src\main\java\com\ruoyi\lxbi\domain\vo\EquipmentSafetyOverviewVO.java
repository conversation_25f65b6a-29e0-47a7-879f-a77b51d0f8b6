package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 设备安全概览VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentSafetyOverviewVO {
    
    /**
     * 总设备数
     */
    private Long totalEquipmentCount;
    
    /**
     * 运行设备数
     */
    private Long runningEquipmentCount;
    
    /**
     * 故障设备数
     */
    private Long faultEquipmentCount;
    
    /**
     * 设备报警数
     */
    private Long alarmEquipmentCount;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 统计周期
     */
    private String period;
}
