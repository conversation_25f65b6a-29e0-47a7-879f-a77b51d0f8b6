package com.ruoyi.lxbi.admin.service;

import com.ruoyi.lxbi.domain.vo.*;

import java.util.List;

/**
 * 微震统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IMicroseismicStatService {

    /**
     * 获取微震概览统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 微震概览统计
     */
    MicroseismicOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取微震P波完成率统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 微震P波完成率统计
     */
    MicroseismicCompletionRateVO getCompletionRate(String viewType, String startDate, String endDate);

    /**
     * 获取微震事件分布统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 微震事件分布统计列表
     */
    List<MicroseismicEventDistributionVO> getEventDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取微震散点图数据
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 微震散点图数据列表
     */
    List<MicroseismicScatterDataVO> getScatterData(String viewType, String startDate, String endDate);
}
