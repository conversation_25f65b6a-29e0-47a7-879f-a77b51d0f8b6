<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.BaseEquipmentMapper">
    
    <resultMap type="BaseEquipment" id="BaseEquipmentResult">
        <result property="id"    column="id"    />
        <result property="equipmentNo"    column="equipment_no"    />
        <result property="mineCarsNumber"    column="mine_cars_number"    />
        <result property="unitEfficiency"    column="unit_efficiency"    />
        <result property="unit"    column="unit"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="equipmentType"    column="equipment_type"    />
    </resultMap>

    <sql id="selectBaseEquipmentVo">
        select id, equipment_no, mine_cars_number, unit_efficiency, unit, status, create_by, create_time, update_by, update_time, start_time, end_time, is_delete, equipment_type from base_equipment
    </sql>

    <select id="selectBaseEquipmentList" parameterType="BaseEquipment" resultMap="BaseEquipmentResult">
        <include refid="selectBaseEquipmentVo"/>
        <where>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="isDelete == null "> and (is_delete = 0 or is_delete is null)</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and start_time between #{params.beginStartTime}::date and #{params.endStartTime}::date</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and end_time between #{params.beginEndTime}::date and #{params.endEndTime}::date</if>
            <if test="equipmentType != null "> and equipment_type = #{equipmentType}</if>
        </where>
    </select>
    
    <select id="selectBaseEquipmentById" parameterType="Long" resultMap="BaseEquipmentResult">
        <include refid="selectBaseEquipmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseEquipment" parameterType="BaseEquipment" useGeneratedKeys="true" keyProperty="id">
        insert into base_equipment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipmentNo != null">equipment_no,</if>
            <if test="mineCarsNumber != null">mine_cars_number,</if>
            <if test="unitEfficiency != null">unit_efficiency,</if>
            <if test="unit != null">unit,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="equipmentType != null">equipment_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipmentNo != null">#{equipmentNo},</if>
            <if test="mineCarsNumber != null">#{mineCarsNumber},</if>
            <if test="unitEfficiency != null">#{unitEfficiency},</if>
            <if test="unit != null">#{unit},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="equipmentType != null">#{equipmentType},</if>
         </trim>
    </insert>

    <update id="updateBaseEquipment" parameterType="BaseEquipment">
        update base_equipment
        <trim prefix="SET" suffixOverrides=",">
            <if test="equipmentNo != null">equipment_no = #{equipmentNo},</if>
            <if test="mineCarsNumber != null">mine_cars_number = #{mineCarsNumber},</if>
            <if test="unitEfficiency != null">unit_efficiency = #{unitEfficiency},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="equipmentType != null">equipment_type = #{equipmentType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseEquipmentById" parameterType="Long">
        delete from base_equipment where id = #{id}
    </delete>

    <delete id="deleteBaseEquipmentByIds" parameterType="String">
        delete from base_equipment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="logicDeleteBaseEquipmentByIds" parameterType="String">
        update base_equipment set is_delete = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>