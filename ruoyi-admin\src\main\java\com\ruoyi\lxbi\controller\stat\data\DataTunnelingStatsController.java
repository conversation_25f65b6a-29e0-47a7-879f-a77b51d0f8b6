package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DataTunnelingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataTunnelingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingDepartmentWithPlanStats;
import com.ruoyi.lxbi.service.IDataTunnelingStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 掘进数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/data/stats/tunneling")
public class DataTunnelingStatsController {
    @Autowired
    private IDataTunnelingStatsService dataTunnelingStatsService;

    /**
     * 查询总体掘进统计数据（含计划量）
     * 对应图表一：总体掘进统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:tunneling:01a')")
    @GetMapping("/01a")
    public R<List<DataTunnelingTotalWithPlanStats>> totalTunnelingWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                            @RequestParam(value = "startDate", required = false) String startDate,
                                                                            @RequestParam(value = "endDate", required = false) String endDate) {
        DataTunnelingStatsRequest request = new DataTunnelingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataTunnelingTotalWithPlanStats> stats = dataTunnelingStatsService.selectTotalWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询按项目部门分组的掘进统计数据（含计划量）
     * 对应图表二：按项目部门分组的掘进统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:tunneling:01b')")
    @GetMapping("/01b")
    public R<List<DataTunnelingDepartmentWithPlanStats>> departmentTunnelingWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                                      @RequestParam(value = "startDate", required = false) String startDate,
                                                                                      @RequestParam(value = "endDate", required = false) String endDate) {
        DataTunnelingStatsRequest request = new DataTunnelingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataTunnelingDepartmentWithPlanStats> stats = dataTunnelingStatsService.selectDepartmentWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询重点工程总体掘进统计数据（含计划量）
     * 对应图表三：重点工程总体掘进统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:tunneling:02a')")
    @GetMapping("/02a")
    public R<List<DataTunnelingTotalWithPlanStats>> priorityProjectTotalTunnelingWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                                           @RequestParam(value = "startDate", required = false) String startDate,
                                                                                           @RequestParam(value = "endDate", required = false) String endDate) {
        DataTunnelingStatsRequest request = new DataTunnelingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataTunnelingTotalWithPlanStats> stats = dataTunnelingStatsService.selectPriorityProjectTotalWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询重点工程按项目部门分组的掘进统计数据（含计划量）
     * 对应图表四：重点工程按项目部门分组的掘进统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:tunneling:02b')")
    @GetMapping("/02b")
    public R<List<DataTunnelingDepartmentWithPlanStats>> priorityProjectDepartmentTunnelingWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                                                      @RequestParam(value = "startDate", required = false) String startDate,
                                                                                                      @RequestParam(value = "endDate", required = false) String endDate) {
        DataTunnelingStatsRequest request = new DataTunnelingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataTunnelingDepartmentWithPlanStats> stats = dataTunnelingStatsService.selectPriorityProjectDepartmentWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

}
