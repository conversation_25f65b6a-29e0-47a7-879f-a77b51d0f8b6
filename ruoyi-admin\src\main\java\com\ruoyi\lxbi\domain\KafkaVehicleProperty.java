package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 车辆属性数据对象 kafka_vehicle_property
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KafkaVehicleProperty extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long isDeleted;

    /** 文件编码 */
    @Excel(name = "文件编码")
    private String fileEncoding;

    /** 煤矿代码 */
    @Excel(name = "煤矿代码")
    private String mineCode;

    /** 数据生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据生成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataGenerationTime;

    /** 车辆定位卡号 */
    @Excel(name = "车辆定位卡号")
    private String vehicleLocationCardNumber;

    /** 车辆编码 */
    @Excel(name = "车辆编码")
    private String vehicleCode;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String vehicleType;

    /** 部门 */
    @Excel(name = "部门")
    private String department;

    /** 载重 */
    @Excel(name = "载重")
    private BigDecimal loadWeight;

    /** 载员 */
    @Excel(name = "载员")
    private BigDecimal loadPersonnel;

    /** 车辆状态 */
    @Excel(name = "车辆状态")
    private String vehicleStatus;

    /** 出厂日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出厂日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date factoryDate;

    /** 初始日期 (第一次数据生成时间) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initDate;

}
