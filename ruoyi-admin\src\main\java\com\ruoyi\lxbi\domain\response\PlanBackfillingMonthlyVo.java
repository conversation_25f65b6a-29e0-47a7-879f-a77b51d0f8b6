package com.ruoyi.lxbi.domain.response;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.lxbi.domain.PlanBackfillingMonthly;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 充填月计划VO对象
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanBackfillingMonthlyVo extends PlanBackfillingMonthly {

    /** 项目部门名称 */
    @Excel(name = "项目部门名称", sort = 1, mergeByValue = true)
    private String projectDepartmentName;

    /** 工作面名称 */
    @Excel(name = "工作面名称", sort = 2, mergeByValue = true)
    private String workingFaceName;

    /** 采场名称 */
    @Excel(name = "采场名称", sort = 3, mergeByValue = true)
    private String stopeName;

}
