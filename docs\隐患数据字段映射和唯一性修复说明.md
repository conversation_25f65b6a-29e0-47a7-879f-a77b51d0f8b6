# 隐患数据字段映射和唯一性修复说明

## 🔧 修复内容

根据您的要求，我已经完成了以下两个重要修复：

### 1. 修复隐患位置字段映射
**问题**: 原来使用 `riskPart` 字段作为隐患位置
**修复**: 改为使用 `hiddenTroubleLocationDescribe` 字段

### 2. 修复唯一性判断逻辑
**问题**: 原来只使用 `notice_number` 作为唯一标识
**修复**: 改为使用 `notice_number` + `trouble_date` 作为复合唯一标识

## 📋 详细修复内容

### 1. 字段映射修复

#### 修复前
```java
record.setTroubleLocation(getStringValueWithDefault(apiData, "riskPart", "未指定位置"));
```

#### 修复后
```java
record.setTroubleLocation(getStringValueWithDefault(apiData, "hiddenTroubleLocationDescribe", "未指定位置"));
```

### 2. 数据库约束修复

#### 2.1 删除原有单字段唯一约束
```sql
ALTER TABLE api_hidden_trouble_record 
DROP CONSTRAINT IF EXISTS api_hidden_trouble_record_notice_number_key;
```

#### 2.2 添加复合唯一约束
```sql
ALTER TABLE api_hidden_trouble_record 
ADD CONSTRAINT uk_notice_number_trouble_date 
UNIQUE (notice_number, trouble_date);
```

#### 2.3 创建复合索引
```sql
CREATE INDEX IF NOT EXISTS idx_notice_number_trouble_date 
ON api_hidden_trouble_record(notice_number, trouble_date);
```

### 3. 代码逻辑修复

#### 3.1 新增查询方法
```java
/**
 * 根据通知编号和隐患日期查询隐患数据记录
 */
public ApiHiddenTroubleRecord selectByNoticeNumberAndDate(String noticeNumber, Date troubleDate);
```

#### 3.2 修复UPSERT逻辑
```java
// 修复前：只检查通知编号
ApiHiddenTroubleRecord existingRecord = selectByNoticeNumber(apiHiddenTroubleRecord.getNoticeNumber());

// 修复后：检查通知编号和隐患日期
ApiHiddenTroubleRecord existingRecord = selectByNoticeNumberAndDate(
    apiHiddenTroubleRecord.getNoticeNumber(), 
    apiHiddenTroubleRecord.getTroubleDate()
);
```

#### 3.3 修复数据验证
```java
// 新增隐患日期验证
if (record.getTroubleDate() == null) {
    log.warn("隐患日期为空，跳过处理，通知编号: {}", record.getNoticeNumber());
    return false;
}
```

#### 3.4 修复UPSERT SQL
```sql
-- 修复前
ON CONFLICT (notice_number)

-- 修复后  
ON CONFLICT (notice_number, trouble_date)
```

## 🎯 修复效果

### 1. 字段映射修复效果

#### 修复前
```json
{
  "riskPart": "生产车间A区",
  "hiddenTroubleLocationDescribe": "生产车间A区东侧设备间"
}
```
**结果**: 隐患位置显示为 "生产车间A区"（不够详细）

#### 修复后
```json
{
  "riskPart": "生产车间A区", 
  "hiddenTroubleLocationDescribe": "生产车间A区东侧设备间"
}
```
**结果**: 隐患位置显示为 "生产车间A区东侧设备间"（更详细准确）

### 2. 唯一性判断修复效果

#### 修复前
```
通知编号: HD20250219001
隐患日期: 2025-02-19
状态: 待整改

通知编号: HD20250219001  
隐患日期: 2025-02-20
状态: 已整改
```
**问题**: 第二条记录会覆盖第一条记录（因为通知编号相同）

#### 修复后
```
通知编号: HD20250219001
隐患日期: 2025-02-19  
状态: 待整改

通知编号: HD20250219001
隐患日期: 2025-02-20
状态: 已整改
```
**结果**: 两条记录都能正确保存（因为通知编号+日期的组合不同）

## 📊 数据库变更

### 1. 约束变更
```sql
-- 查看当前约束
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'api_hidden_trouble_record'::regclass 
  AND contype = 'u';
```

### 2. 检查重复数据
```sql
-- 查询可能的重复数据
SELECT 
    notice_number,
    trouble_date,
    COUNT(*) as count
FROM api_hidden_trouble_record 
WHERE notice_number IS NOT NULL 
  AND trouble_date IS NOT NULL
GROUP BY notice_number, trouble_date
HAVING COUNT(*) > 1
ORDER BY count DESC;
```

## 🧪 测试验证

### 1. 测试字段映射
```bash
curl -X POST "http://localhost:8080/lxbi/hiddenTrouble/test/processApiData" \
-H "Content-Type: application/json" \
-d '{
  "hiddenTroubleNumber": "TEST001",
  "riskPart": "生产车间A区",
  "hiddenTroubleLocationDescribe": "生产车间A区东侧设备间第3号设备",
  "checkTime": "2025-08-23"
}'
```

**验证**: 数据库中 `trouble_location` 字段应该保存 "生产车间A区东侧设备间第3号设备"

### 2. 测试唯一性约束
```bash
# 第一次插入
curl -X POST "http://localhost:8080/lxbi/hiddenTrouble/test/processApiData" \
-H "Content-Type: application/json" \
-d '{
  "hiddenTroubleNumber": "TEST002",
  "checkTime": "2025-08-23",
  "hiddenTroubleLocationDescribe": "位置1"
}'

# 第二次插入（相同通知编号，相同日期）
curl -X POST "http://localhost:8080/lxbi/hiddenTrouble/test/processApiData" \
-H "Content-Type: application/json" \
-d '{
  "hiddenTroubleNumber": "TEST002", 
  "checkTime": "2025-08-23",
  "hiddenTroubleLocationDescribe": "位置1更新"
}'

# 第三次插入（相同通知编号，不同日期）
curl -X POST "http://localhost:8080/lxbi/hiddenTrouble/test/processApiData" \
-H "Content-Type: application/json" \
-d '{
  "hiddenTroubleNumber": "TEST002",
  "checkTime": "2025-08-24", 
  "hiddenTroubleLocationDescribe": "位置2"
}'
```

**验证结果**:
- 第一次：新增记录
- 第二次：更新第一条记录（相同通知编号+日期）
- 第三次：新增记录（相同通知编号，不同日期）

### 3. 验证数据库数据
```sql
-- 查看测试数据
SELECT 
    id,
    notice_number,
    trouble_date,
    trouble_location,
    create_time,
    update_time
FROM api_hidden_trouble_record 
WHERE notice_number LIKE 'TEST%'
ORDER BY notice_number, trouble_date;
```

## 📝 日志输出示例

### 修复后的日志
```
2025-08-23 21:50:00 INFO - 成功处理隐患数据，通知编号: HD20250219001, 隐患日期: 2025-02-19, 责任人: 张三, 部门: 安全部
2025-08-23 21:50:01 INFO - 成功处理隐患数据，通知编号: HD20250219001, 隐患日期: 2025-02-20, 责任人: 李四, 部门: 生产部
2025-08-23 21:50:02 DEBUG - 新增隐患数据成功，通知编号: HD20250219002, 隐患日期: 2025-02-19
2025-08-23 21:50:03 DEBUG - 更新隐患数据成功，通知编号: HD20250219001, 隐患日期: 2025-02-19
```

## ⚠️ 注意事项

### 1. 数据迁移
- 执行数据库约束变更前，请备份数据
- 检查是否存在重复的 `notice_number + trouble_date` 组合
- 如有重复数据，需要先清理

### 2. 业务影响
- 修复后，相同通知编号的不同日期隐患会被视为不同记录
- 这更符合实际业务场景（一个通知编号可能对应多天的隐患记录）

### 3. 性能考虑
- 复合唯一约束可能略微影响插入性能
- 已创建相应索引来优化查询性能

## ✅ 修复完成

现在系统已经正确实现：
- ✅ **隐患位置字段映射**: 使用 `hiddenTroubleLocationDescribe` 字段
- ✅ **复合唯一性约束**: 基于 `notice_number + trouble_date` 判断唯一性
- ✅ **数据完整性**: 确保隐患日期不为空
- ✅ **详细日志**: 显示通知编号和隐患日期信息
- ✅ **向后兼容**: 保持原有API接口不变

修复后的系统能够更准确地处理隐患数据，避免数据覆盖问题，并提供更详细的位置信息。
