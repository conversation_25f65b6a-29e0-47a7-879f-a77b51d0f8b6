package com.ruoyi.lxbi.controller.plan;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.PlanSupportMonthly;
import com.ruoyi.lxbi.domain.request.PlanSupportMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanSupportMonthlyVo;
import com.ruoyi.lxbi.service.IPlanSupportMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 支护月计划Controller
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/plan/planSupportMonthly")
public class PlanSupportMonthlyController extends BaseController {
    @Autowired
    private IPlanSupportMonthlyService planSupportMonthlyService;

    /**
     * 查询支护月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planSupportMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanSupportMonthly planSupportMonthly) {
        startPage();
        List<PlanSupportMonthlyVo> list = planSupportMonthlyService.selectPlanSupportMonthlyList(planSupportMonthly);
        return getDataTable(list);
    }

    /**
     * 导出支护月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planSupportMonthly:export')")
    @Log(title = "支护月计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanSupportMonthly planSupportMonthly) {
        List<PlanSupportMonthlyVo> list = planSupportMonthlyService.selectPlanSupportMonthlyList(planSupportMonthly);
        ExcelUtil<PlanSupportMonthlyVo> util = new ExcelUtil<PlanSupportMonthlyVo>(PlanSupportMonthlyVo.class);
        util.exportExcel(response, list, "支护月计划数据");
    }

    /**
     * 获取支护月计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('plan:planSupportMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planSupportMonthlyService.selectPlanSupportMonthlyById(id));
    }

    /**
     * 新增支护月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planSupportMonthly:add')")
    @Log(title = "支护月计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanSupportMonthly planSupportMonthly)
    {
        return toAjax(planSupportMonthlyService.insertPlanSupportMonthly(planSupportMonthly));
    }

    /**
     * 修改支护月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planSupportMonthly:edit')")
    @Log(title = "支护月计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanSupportMonthly planSupportMonthly)
    {
        return toAjax(planSupportMonthlyService.updatePlanSupportMonthly(planSupportMonthly));
    }

    /**
     * 删除支护月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planSupportMonthly:remove')")
    @Log(title = "支护月计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(planSupportMonthlyService.deletePlanSupportMonthlyByIds(ids));
    }

    /**
     * 批量保存支护月计划（增删改查）
     * 传入批量列表，验证是否同一个月份的数据，然后查询这个月份的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('plan:planSupportMonthly:edit')")
    @Log(title = "支护月计划批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<PlanSupportMonthlyBatchDto> batchDataList)
    {
        try {
            int result = planSupportMonthlyService.batchSavePlanSupportMonthly(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
