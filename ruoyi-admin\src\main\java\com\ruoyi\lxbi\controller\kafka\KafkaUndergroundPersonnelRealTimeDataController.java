package com.ruoyi.lxbi.controller.kafka;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.KafkaUndergroundPersonnelRealTimeData;
import com.ruoyi.lxbi.service.IKafkaUndergroundPersonnelRealTimeDataService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 井下作业人员实时数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@RestController
@RequestMapping("/kafka/PersonnelRealTimeData")
public class KafkaUndergroundPersonnelRealTimeDataController extends BaseController {
    @Autowired
    private IKafkaUndergroundPersonnelRealTimeDataService kafkaUndergroundPersonnelRealTimeDataService;

    /**
     * 查询井下作业人员实时数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:PersonnelRealTimeData:list')")
    @GetMapping("/list")
    public TableDataInfo list(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData) {
        startPage();
        List<KafkaUndergroundPersonnelRealTimeData> list = kafkaUndergroundPersonnelRealTimeDataService.selectKafkaUndergroundPersonnelRealTimeDataList(kafkaUndergroundPersonnelRealTimeData);
        return getDataTable(list);
    }

    /**
     * 导出井下作业人员实时数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:PersonnelRealTimeData:export')")
    @Log(title = "井下作业人员实时数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData) {
        List<KafkaUndergroundPersonnelRealTimeData> list = kafkaUndergroundPersonnelRealTimeDataService.selectKafkaUndergroundPersonnelRealTimeDataList(kafkaUndergroundPersonnelRealTimeData);
        ExcelUtil<KafkaUndergroundPersonnelRealTimeData> util = new ExcelUtil<KafkaUndergroundPersonnelRealTimeData>(KafkaUndergroundPersonnelRealTimeData.class);
        util.exportExcel(response, list, "井下作业人员实时数据数据");
    }

    /**
     * 获取井下作业人员实时数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:PersonnelRealTimeData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kafkaUndergroundPersonnelRealTimeDataService.selectKafkaUndergroundPersonnelRealTimeDataById(id));
    }

    /**
     * 新增井下作业人员实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:PersonnelRealTimeData:add')")
    @Log(title = "井下作业人员实时数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData)
    {
        return toAjax(kafkaUndergroundPersonnelRealTimeDataService.insertKafkaUndergroundPersonnelRealTimeData(kafkaUndergroundPersonnelRealTimeData));
    }

    /**
     * 修改井下作业人员实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:PersonnelRealTimeData:edit')")
    @Log(title = "井下作业人员实时数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData)
    {
        return toAjax(kafkaUndergroundPersonnelRealTimeDataService.updateKafkaUndergroundPersonnelRealTimeData(kafkaUndergroundPersonnelRealTimeData));
    }

    /**
     * 删除井下作业人员实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:PersonnelRealTimeData:remove')")
    @Log(title = "井下作业人员实时数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kafkaUndergroundPersonnelRealTimeDataService.deleteKafkaUndergroundPersonnelRealTimeDataByIds(ids));
    }
}
