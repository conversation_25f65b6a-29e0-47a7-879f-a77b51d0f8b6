package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.PlanMineralSaleMonthly;
import com.ruoyi.lxbi.domain.excel.PlanMineralSaleMonthlyImport;
import com.ruoyi.lxbi.service.IPlanMineralSaleMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 选矿销售月计划导入处理器
 *
 * <AUTHOR>
 */
@Component
public class PlanMineralSaleMonthlyImportHandler extends ExcelImportHandler<PlanMineralSaleMonthlyImport> {

    @Autowired
    private IPlanMineralSaleMonthlyService planMineralSaleMonthlyService;

    @Override
    protected Class<PlanMineralSaleMonthlyImport> getEntityClass() {
        return PlanMineralSaleMonthlyImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 选矿销售月计划不需要额外的下拉选项
    }

    @Override
    protected void validateData(ExcelDataInfo<PlanMineralSaleMonthlyImport> dataInfo, ExcelImportContext context) {
        PlanMineralSaleMonthlyImport data = dataInfo.getData();

        // 验证计划月份格式
        if (data.getPlanDate() != null) {
            Pattern pattern = Pattern.compile("^\\d{4}\\d{2}$");
            if (!pattern.matcher(data.getPlanDate()).matches()) {
                dataInfo.addError("planDate", "计划月份格式不正确，应为yyyyMM格式");
            }
        }

        // 验证铁精粉量
        if (data.getIronConcentrateVolume() != null && data.getIronConcentrateVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("ironConcentrateVolume", "铁精粉量不能为负数");
        }

        // 验证选矿厂矿仓存矿销售
        if (data.getConcentratorBinsStockVolume() != null && data.getConcentratorBinsStockVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("concentratorBinsStockVolume", "选矿厂矿仓存矿销售量不能为负数");
        }

        // 验证入措施井地表存矿销售
        if (data.getServiceShaftSurfaceStockVolume() != null && data.getServiceShaftSurfaceStockVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("serviceShaftSurfaceStockVolume", "入措施井地表存矿销售量不能为负数");
        }

        // 验证原矿品位
        if (data.getRawOreGrade() != null) {
            if (data.getRawOreGrade().compareTo(BigDecimal.ZERO) < 0 || data.getRawOreGrade().compareTo(new BigDecimal("100")) > 0) {
                dataInfo.addError("rawOreGradeTfe", "原矿品位必须在0-100%之间");
            }
        }

        // 验证库存
        if (data.getStockVolume() != null && data.getStockVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("stockVolume", "库存量不能为负数");
        }
    }

    @Override
    protected void saveData(PlanMineralSaleMonthlyImport data, ExcelImportContext context) {
        // 转换为实体对象
        PlanMineralSaleMonthly entity = new PlanMineralSaleMonthly();
        entity.setIronConcentrateVolume(data.getIronConcentrateVolume());
        entity.setConcentratorBinsStockVolume(data.getConcentratorBinsStockVolume());
        entity.setServiceShaftSurfaceStockVolume(data.getServiceShaftSurfaceStockVolume());
        entity.setRawOreGrade(data.getRawOreGrade());
        entity.setStockVolume(data.getStockVolume());
        entity.setPlanDate(data.getPlanDate());
        
        // 保存到数据库
        planMineralSaleMonthlyService.insertPlanMineralSaleMonthly(entity);
    }

    @Override
    public List<PlanMineralSaleMonthlyImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("选矿销售月计划验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("选矿销售月计划导入完成，总行数: " + ctx.getTotalRows());
    }
}
