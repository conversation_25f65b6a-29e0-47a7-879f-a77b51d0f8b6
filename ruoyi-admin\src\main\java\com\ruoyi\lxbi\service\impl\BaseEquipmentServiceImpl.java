package com.ruoyi.lxbi.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.lxbi.mapper.BaseEquipmentMapper;
import com.ruoyi.lxbi.domain.BaseEquipment;
import com.ruoyi.lxbi.service.IBaseEquipmentService;

/**
 * 设备数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
public class BaseEquipmentServiceImpl implements IBaseEquipmentService 
{
    @Autowired
    private BaseEquipmentMapper baseEquipmentMapper;

    /**
     * 查询设备数据
     * 
     * @param id 设备数据主键
     * @return 设备数据
     */
    @Override
    public BaseEquipment selectBaseEquipmentById(Long id)
    {
        return baseEquipmentMapper.selectBaseEquipmentById(id);
    }

    /**
     * 查询设备数据列表
     *
     * @param baseEquipment 设备数据
     * @return 设备数据
     */
    @Override
    public List<BaseEquipment> selectBaseEquipmentList(BaseEquipment baseEquipment)
    {
        return baseEquipmentMapper.selectBaseEquipmentList(baseEquipment);
    }

    /**
     * 查询所有设备数据列表（不分页）
     *
     * @return 设备数据
     */
    @Override
    public List<BaseEquipment> selectBaseEquipmentListAll(BaseEquipment baseEquipment)
    {
        return baseEquipmentMapper.selectBaseEquipmentList(baseEquipment);
    }

    /**
     * 新增设备数据
     * 
     * @param baseEquipment 设备数据
     * @return 结果
     */
    @Override
    public int insertBaseEquipment(BaseEquipment baseEquipment)
    {
        baseEquipment.setCreateTime(DateUtils.getNowDate());
        return baseEquipmentMapper.insertBaseEquipment(baseEquipment);
    }

    /**
     * 修改设备数据
     * 
     * @param baseEquipment 设备数据
     * @return 结果
     */
    @Override
    public int updateBaseEquipment(BaseEquipment baseEquipment)
    {
        baseEquipment.setUpdateTime(DateUtils.getNowDate());
        return baseEquipmentMapper.updateBaseEquipment(baseEquipment);
    }

    /**
     * 批量删除设备数据
     * 
     * @param ids 需要删除的设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBaseEquipmentByIds(Long[] ids)
    {
        return baseEquipmentMapper.deleteBaseEquipmentByIds(ids);
    }

    /**
     * 删除设备数据信息
     *
     * @param id 设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBaseEquipmentById(Long id)
    {
        return baseEquipmentMapper.deleteBaseEquipmentById(id);
    }

    /**
     * 批量逻辑删除设备数据
     *
     * @param ids 需要删除的设备数据主键
     * @return 结果
     */
    @Override
    public int logicDeleteBaseEquipmentByIds(Long[] ids)
    {
        return baseEquipmentMapper.logicDeleteBaseEquipmentByIds(ids);
    }
}
