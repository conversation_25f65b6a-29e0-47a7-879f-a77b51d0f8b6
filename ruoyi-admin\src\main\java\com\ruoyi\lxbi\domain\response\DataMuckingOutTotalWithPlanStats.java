package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 出矿数据总体统计对象（含计划量） - 统一日/周/月/年统计
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
public class DataMuckingOutTotalWithPlanStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 周开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekStartDate;

    /**
     * 周结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekEndDate;

    /**
     * 总出矿量（实际）
     */
    @Excel(name = "总出矿量")
    private Double totalTons;

    /**
     * 计划出矿量（根据查询类型计算：日-月计划/天数，周-月计划/4，月-月计划，年-年计划）
     */
    @Excel(name = "计划出矿量")
    private Double planTons;
}
