package com.ruoyi.lxbi.mapper;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.domain.DataMuckingOut;
import com.ruoyi.lxbi.domain.response.DataMuckingOutVo;
import org.apache.ibatis.annotations.Param;

/**
 * 出矿数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface DataMuckingOutMapper 
{
    /**
     * 查询出矿数据
     * 
     * @param id 出矿数据主键
     * @return 出矿数据
     */
    public DataMuckingOut selectDataMuckingOutById(Long id);

    /**
     * 查询出矿数据列表
     *
     * @param dataMuckingOut 出矿数据
     * @return 出矿数据集合
     */
    public List<DataMuckingOutVo> selectDataMuckingOutList(DataMuckingOut dataMuckingOut);

    /**
     * 新增出矿数据
     * 
     * @param dataMuckingOut 出矿数据
     * @return 结果
     */
    public int insertDataMuckingOut(DataMuckingOut dataMuckingOut);

    /**
     * 修改出矿数据
     * 
     * @param dataMuckingOut 出矿数据
     * @return 结果
     */
    public int updateDataMuckingOut(DataMuckingOut dataMuckingOut);

    /**
     * 删除出矿数据
     * 
     * @param id 出矿数据主键
     * @return 结果
     */
    public int deleteDataMuckingOutById(Long id);

    /**
     * 批量删除出矿数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataMuckingOutByIds(Long[] ids);

    /**
     * 根据作业日期和项目部门查询出矿数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 出矿数据集合
     */
    public List<DataMuckingOutVo> selectDataMuckingOutByOperationDateAndProject(@Param("operationDate") Date operationDate, @Param("projectDepartmentId") Long projectDepartmentId);

    /**
     * 批量新增出矿数据
     *
     * @param dataMuckingOutList 出矿数据列表
     * @return 结果
     */
    public int batchInsertDataMuckingOut(List<DataMuckingOut> dataMuckingOutList);
}
