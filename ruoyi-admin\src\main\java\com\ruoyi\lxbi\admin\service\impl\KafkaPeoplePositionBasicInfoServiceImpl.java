package com.ruoyi.lxbi.admin.service.impl;

import java.util.List;
import java.util.Date;
import java.text.SimpleDateFormat;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.admin.mapper.KafkaPeoplePositionBasicInfoMapper;
import com.ruoyi.lxbi.admin.domain.KafkaPeoplePositionBasicInfo;
import com.ruoyi.lxbi.admin.service.IKafkaPeoplePositionBasicInfoService;
import lombok.extern.slf4j.Slf4j;

/**
 * KAFKA人员基础信息数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Slf4j
@Service
public class KafkaPeoplePositionBasicInfoServiceImpl implements IKafkaPeoplePositionBasicInfoService
{
    @Autowired
    private KafkaPeoplePositionBasicInfoMapper kafkaPeoplePositionBasicInfoMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询KAFKA人员基础信息数据
     * 
     * @param id KAFKA人员基础信息数据主键
     * @return KAFKA人员基础信息数据
     */
    @Override
    public KafkaPeoplePositionBasicInfo selectKafkaPeoplePositionBasicInfoById(Long id)
    {
        return kafkaPeoplePositionBasicInfoMapper.selectKafkaPeoplePositionBasicInfoById(id);
    }

    /**
     * 查询KAFKA人员基础信息数据列表
     * 
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return KAFKA人员基础信息数据
     */
    @Override
    public List<KafkaPeoplePositionBasicInfo> selectKafkaPeoplePositionBasicInfoList(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo)
    {
        return kafkaPeoplePositionBasicInfoMapper.selectKafkaPeoplePositionBasicInfoList(kafkaPeoplePositionBasicInfo);
    }

    /**
     * 新增KAFKA人员基础信息数据
     * 
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    @Override
    public int insertKafkaPeoplePositionBasicInfo(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo)
    {
        kafkaPeoplePositionBasicInfo.setCreateTime(DateUtils.getNowDate());
        return kafkaPeoplePositionBasicInfoMapper.insertKafkaPeoplePositionBasicInfo(kafkaPeoplePositionBasicInfo);
    }

    /**
     * 修改KAFKA人员基础信息数据
     * 
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    @Override
    public int updateKafkaPeoplePositionBasicInfo(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo)
    {
        kafkaPeoplePositionBasicInfo.setUpdateTime(DateUtils.getNowDate());
        return kafkaPeoplePositionBasicInfoMapper.updateKafkaPeoplePositionBasicInfo(kafkaPeoplePositionBasicInfo);
    }

    /**
     * 批量删除KAFKA人员基础信息数据
     * 
     * @param ids 需要删除的KAFKA人员基础信息数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeoplePositionBasicInfoByIds(Long[] ids)
    {
        return kafkaPeoplePositionBasicInfoMapper.deleteKafkaPeoplePositionBasicInfoByIds(ids);
    }

    /**
     * 删除KAFKA人员基础信息数据信息
     *
     * @param id KAFKA人员基础信息数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeoplePositionBasicInfoById(Long id)
    {
        return kafkaPeoplePositionBasicInfoMapper.deleteKafkaPeoplePositionBasicInfoById(id);
    }

    /**
     * 根据人员卡编码查询
     *
     * @param personCardCode 人员卡编码
     * @return KAFKA人员基础信息数据
     */
    @Override
    public KafkaPeoplePositionBasicInfo selectByPersonCardCode(String personCardCode)
    {
        return kafkaPeoplePositionBasicInfoMapper.selectByPersonCardCode(personCardCode);
    }

    /**
     * 批量插入或更新（UPSERT）
     * 根据人员卡编码唯一性，自动判断是插入还是更新
     *
     * @param kafkaPeoplePositionBasicInfo KAFKA人员基础信息数据
     * @return 结果
     */
    @Override
    public int upsertKafkaPeoplePositionBasicInfo(KafkaPeoplePositionBasicInfo kafkaPeoplePositionBasicInfo)
    {
        // 检查是否已存在
        KafkaPeoplePositionBasicInfo existingInfo = selectByPersonCardCode(kafkaPeoplePositionBasicInfo.getPersonCardCode());
        boolean isUpdate = existingInfo != null;

        // 设置时间字段
        Date now = DateUtils.getNowDate();
        if (isUpdate) {
            // 更新操作：保留原创建时间，更新修改时间
            kafkaPeoplePositionBasicInfo.setCreateTime(existingInfo.getCreateTime());
            kafkaPeoplePositionBasicInfo.setCreateBy(existingInfo.getCreateBy());
        } else {
            // 新增操作：设置创建时间
            kafkaPeoplePositionBasicInfo.setCreateTime(now);
            if (!StringUtils.hasText(kafkaPeoplePositionBasicInfo.getCreateBy())) {
                kafkaPeoplePositionBasicInfo.setCreateBy("kafka-system");
            }
        }

        // 设置更新时间
        kafkaPeoplePositionBasicInfo.setUpdateTime(now);
        if (!StringUtils.hasText(kafkaPeoplePositionBasicInfo.getUpdateBy())) {
            kafkaPeoplePositionBasicInfo.setUpdateBy("kafka-system");
        }

        // 设置默认值
        if (kafkaPeoplePositionBasicInfo.getStatus() == null) {
            kafkaPeoplePositionBasicInfo.setStatus(1L); // 默认启用
        }
        if (kafkaPeoplePositionBasicInfo.getIsDeleted() == null) {
            kafkaPeoplePositionBasicInfo.setIsDeleted(0L); // 默认未删除
        }

        // 执行UPSERT操作
        int result = kafkaPeoplePositionBasicInfoMapper.upsertKafkaPeoplePositionBasicInfo(kafkaPeoplePositionBasicInfo);

        if (result > 0) {
            String operation = isUpdate ? "更新" : "新增";
            log.debug("{}人员基础信息成功，人员卡编码: {}", operation, kafkaPeoplePositionBasicInfo.getPersonCardCode());
        }

        return result;
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     * 根据人员卡编码唯一性，如果存在则更新，不存在则新增
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka人员基础信息消息");

            // 解析Kafka消息
            KafkaPeoplePositionBasicInfo info = parseKafkaMessage(kafkaMessage);
            if (info == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(info.getPersonCardCode())) {
                log.warn("人员卡编码为空，跳过处理");
                return false;
            }

            if (!StringUtils.hasText(info.getPersonName())) {
                log.warn("人员姓名为空，人员卡编码: {}", info.getPersonCardCode());
                return false;
            }

            // 检查是否已存在该人员
            KafkaPeoplePositionBasicInfo existingInfo = selectByPersonCardCode(info.getPersonCardCode());
            boolean isUpdate = existingInfo != null;

            // 执行UPSERT操作（PostgreSQL会自动处理插入或更新）
            int result = upsertKafkaPeoplePositionBasicInfo(info);

            if (result > 0) {
                String operation = isUpdate ? "更新" : "新增";
                log.info("成功{}人员基础信息，人员卡编码: {}, 姓名: {}, 部门: {}",
                    operation, info.getPersonCardCode(), info.getPersonName(), info.getDepartment());
                return true;
            } else {
                log.warn("处理人员基础信息失败，人员卡编码: {}, 姓名: {}",
                    info.getPersonCardCode(), info.getPersonName());
                return false;
            }

        } catch (Exception e) {
            log.error("处理Kafka人员基础信息消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaPeoplePositionBasicInfo parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaPeoplePositionBasicInfo info = new KafkaPeoplePositionBasicInfo();

            // 系统基础信息
            info.setMineCode(getStringValue(jsonNode, "煤矿编码"));
            info.setMineName(getStringValue(jsonNode, "矿井名称"));
            info.setApprovedUndergroundCount(getStringValue(jsonNode, "核定下井人数"));
            info.setSystemModel(getStringValue(jsonNode, "系统型号"));
            info.setSystemName(getStringValue(jsonNode, "系统名称"));
            info.setManufacturerName(getStringValue(jsonNode, "生产厂家名称"));

            // 日期字段处理
            String expiryDateStr = getStringValue(jsonNode, "安标有效期");
            if (StringUtils.hasText(expiryDateStr)) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    info.setSafetyCertificateExpiry(dateFormat.parse(expiryDateStr));
                } catch (Exception e) {
                    log.warn("解析安标有效期失败: {}", expiryDateStr);
                }
            }

            String uploadTimeStr = getStringValue(jsonNode, "数据上传时间");
            if (StringUtils.hasText(uploadTimeStr)) {
                try {
                    SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    info.setDataUploadTime(dateTimeFormat.parse(uploadTimeStr));
                } catch (Exception e) {
                    log.warn("解析数据上传时间失败: {}", uploadTimeStr);
                }
            }

            // 人员基础信息
            info.setPersonCardCode(getStringValue(jsonNode, "人员卡编码"));
            info.setPersonName(getStringValue(jsonNode, "姓名"));
            info.setJobType(getStringValue(jsonNode, "工种"));
            info.setPosition(getStringValue(jsonNode, "职务"));
            info.setDepartment(getStringValue(jsonNode, "队组班组/部门"));
            info.setBirthDate(getStringValue(jsonNode, "出生年月"));
            info.setEducation(getStringValue(jsonNode, "学历"));

            // 布尔字段处理
            info.setIsMineLeader(getLongValue(jsonNode, "是否矿领导"));
            info.setIsSpecialPersonnel(getLongValue(jsonNode, "是否特种人员"));

            // 默认值
            info.setStatus(1L);
            info.setIsDeleted(0L);
            info.setCreateTime(DateUtils.getNowDate());
            info.setUpdateTime(DateUtils.getNowDate());

            return info;

        } catch (Exception e) {
            log.error("解析Kafka消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null && !fieldNode.isNull() ? fieldNode.asText() : null;
    }

    /**
     * 获取Long值
     */
    private Long getLongValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode != null && !fieldNode.isNull()) {
            String value = fieldNode.asText();
            try {
                return Long.parseLong(value);
            } catch (NumberFormatException e) {
                return 0L;
            }
        }
        return 0L;
    }

    /**
     * 统计指定日期范围内的活跃人员数量
     */
    @Override
    public Long countActivePersonnelByDateRange(String startDate, String endDate) {
        try {
            // 这里简化处理，统计所有状态为正常的人员
            // 实际应用中可能需要根据具体业务逻辑来定义"活跃人员"
            return kafkaPeoplePositionBasicInfoMapper.countActivePersonnelByDateRange(startDate, endDate);
        } catch (Exception e) {
            log.error("统计活跃人员数量失败", e);
            return 0L;
        }
    }
}
