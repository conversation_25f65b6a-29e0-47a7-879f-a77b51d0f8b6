package com.ruoyi.lxbi.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.DataFillingMapper;
import com.ruoyi.lxbi.domain.DataFilling;
import com.ruoyi.lxbi.domain.request.DataFillingBatchDto;
import com.ruoyi.lxbi.domain.response.DataFillingVo;
import com.ruoyi.lxbi.service.IDataFillingService;

/**
 * 充填数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class DataFillingServiceImpl implements IDataFillingService 
{
    @Autowired
    private DataFillingMapper dataFillingMapper;

    /**
     * 查询充填数据
     * 
     * @param id 充填数据主键
     * @return 充填数据
     */
    @Override
    public DataFilling selectDataFillingById(Long id)
    {
        return dataFillingMapper.selectDataFillingById(id);
    }

    /**
     * 查询充填数据列表
     *
     * @param dataFilling 充填数据
     * @return 充填数据
     */
    @Override
    public List<DataFillingVo> selectDataFillingList(DataFilling dataFilling)
    {
        return dataFillingMapper.selectDataFillingList(dataFilling);
    }

    /**
     * 新增充填数据
     * 
     * @param dataFilling 充填数据
     * @return 结果
     */
    @Override
    public int insertDataFilling(DataFilling dataFilling)
    {
        dataFilling.setCreateTime(DateUtils.getNowDate());
        return dataFillingMapper.insertDataFilling(dataFilling);
    }

    /**
     * 修改充填数据
     * 
     * @param dataFilling 充填数据
     * @return 结果
     */
    @Override
    public int updateDataFilling(DataFilling dataFilling)
    {
        dataFilling.setUpdateTime(DateUtils.getNowDate());
        return dataFillingMapper.updateDataFilling(dataFilling);
    }

    /**
     * 批量删除充填数据
     * 
     * @param ids 需要删除的充填数据主键
     * @return 结果
     */
    @Override
    public int deleteDataFillingByIds(Long[] ids)
    {
        return dataFillingMapper.deleteDataFillingByIds(ids);
    }

    /**
     * 删除充填数据信息
     *
     * @param id 充填数据主键
     * @return 结果
     */
    @Override
    public int deleteDataFillingById(Long id)
    {
        return dataFillingMapper.deleteDataFillingById(id);
    }

    /**
     * 根据作业日期和项目部门查询充填数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 充填数据集合
     */
    @Override
    public List<DataFillingVo> selectDataFillingByOperationDateAndProject(Date operationDate, Long projectDepartmentId) {
        return dataFillingMapper.selectDataFillingByOperationDateAndProject(operationDate, projectDepartmentId);
    }

    /**
     * 批量保存充填数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDataFilling(List<DataFillingBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个日期和项目部的数据
        Date operationDate = batchDataList.get(0).getOperationDate();
        Long projectDepartmentId = batchDataList.get(0).getProjectDepartmentId();
        if (operationDate == null) {
            throw new ServiceException("作业日期不能为空");
        }
        if (projectDepartmentId == null) {
            throw new ServiceException("项目部门不能为空");
        }

        boolean allSameDate = batchDataList.stream()
                .allMatch(data -> operationDate.equals(data.getOperationDate())
                    && projectDepartmentId.equals(data.getProjectDepartmentId()));
        if (!allSameDate) {
            throw new ServiceException("批量数据必须是同一个作业日期和项目部门");
        }

        // 查询该日期和项目部的所有现有数据
        List<DataFillingVo> existingDataList = selectDataFillingByOperationDateAndProject(operationDate, projectDepartmentId);
        Map<Long, DataFillingVo> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(
                        DataFillingVo::getId,
                        data -> data
                ));

        // 分类处理数据
        List<DataFilling> toInsert = new ArrayList<>();
        List<DataFilling> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>();

        // 处理传入的数据
        for (DataFillingBatchDto batchData : batchDataList) {
            if (Boolean.TRUE.equals(batchData.getIsNew())) {
                // 新增数据
                DataFilling newData = new DataFilling();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if (batchData.getId() != null && existingDataMap.containsKey(batchData.getId())) {
                // 更新现有数据（根据ID判断）
                DataFilling updateData = new DataFilling();
                copyProperties(batchData, updateData);
                updateData.setId(batchData.getId());
                updateData.setUpdateBy(SecurityUtils.getUsername());
                updateData.setUpdateTime(DateUtils.getNowDate());
                toUpdate.add(updateData);

                // 从现有数据中移除，剩下的就是要删除的
                existingDataMap.remove(batchData.getId());
            }
        }

        // 剩余的现有数据需要删除
        toDelete.addAll(existingDataMap.keySet());

        int result = 0;

        // 执行删除
        if (!toDelete.isEmpty()) {
            result += dataFillingMapper.deleteDataFillingByIds(toDelete.toArray(new Long[0]));
        }

        // 执行新增
        if (!toInsert.isEmpty()) {
            result += dataFillingMapper.batchInsertDataFilling(toInsert);
        }

        // 执行更新
        for (DataFilling updateData : toUpdate) {
            result += dataFillingMapper.updateDataFilling(updateData);
        }

        return result;
    }

    /**
     * 复制属性
     */
    private void copyProperties(DataFillingBatchDto source, DataFilling target) {
        target.setOperationDate(source.getOperationDate());
        target.setProjectDepartmentId(source.getProjectDepartmentId());
        target.setStopeId(source.getStopeId());
        target.setWorkingPeriodId(source.getWorkingPeriodId());
        target.setSlurryVolume(source.getSlurryVolume());
        target.setCementWeight(source.getCementWeight());
        target.setFillingRatio(source.getFillingRatio());
        target.setFillingConcentration(source.getFillingConcentration());
        target.setFirstFillingTime(source.getFirstFillingTime());
        target.setEndFillingTime(source.getEndFillingTime());
        target.setRemarks(source.getRemarks());
    }
}
