package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 人员安全仪表板综合数据VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonnelSafetyDashboardVO {
    
    /**
     * 概览统计
     */
    private PersonnelSafetyOverviewVO overview;
    
    /**
     * 主要超时组分布
     */
    private List<TimeoutGroupDistributionVO> timeoutGroupDistribution;
    
    /**
     * 区域超时人员名单
     */
    private List<AreaTimeoutPersonnelVO> areaTimeoutPersonnel;
    
    /**
     * 来救数量趋势数据
     */
    private List<RescueQuantityTrendVO> rescueQuantityTrend;
}
