<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaSubstationRealTimeDataMapper">
    
    <resultMap type="KafkaSubstationRealTimeData" id="KafkaSubstationRealTimeDataResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="substationCode"    column="substation_code"    />
        <result property="substationRunningStatus"    column="substation_running_status"    />
        <result property="substationPowerSupplyStatus"    column="substation_power_supply_status"    />
        <result property="dataTime"    column="data_time"    />
    </resultMap>

    <sql id="selectKafkaSubstationRealTimeDataVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, mine_name, data_upload_time, substation_code, substation_running_status, substation_power_supply_status, data_time from kafka_substation_real_time_data
    </sql>

    <select id="selectKafkaSubstationRealTimeDataList" parameterType="KafkaSubstationRealTimeData" resultMap="KafkaSubstationRealTimeDataResult">
        <include refid="selectKafkaSubstationRealTimeDataVo"/>
        <where>  
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="params.beginDataUploadTime != null and params.beginDataUploadTime != '' and params.endDataUploadTime != null and params.endDataUploadTime != ''"> and data_upload_time between #{params.beginDataUploadTime}::date and #{params.endDataUploadTime}::date</if>
            <if test="substationCode != null  and substationCode != ''"> and substation_code = #{substationCode}</if>
            <if test="substationRunningStatus != null  and substationRunningStatus != ''"> and substation_running_status = #{substationRunningStatus}</if>
            <if test="substationPowerSupplyStatus != null  and substationPowerSupplyStatus != ''"> and substation_power_supply_status = #{substationPowerSupplyStatus}</if>
            <if test="params.beginDataTime != null and params.beginDataTime != '' and params.endDataTime != null and params.endDataTime != ''"> and data_time between #{params.beginDataTime}::date and #{params.endDataTime}::date</if>
        </where>
    </select>
    
    <select id="selectKafkaSubstationRealTimeDataById" parameterType="Long" resultMap="KafkaSubstationRealTimeDataResult">
        <include refid="selectKafkaSubstationRealTimeDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertKafkaSubstationRealTimeData" parameterType="KafkaSubstationRealTimeData" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_substation_real_time_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null">file_encoding,</if>
            <if test="mineCode != null">mine_code,</if>
            <if test="mineName != null">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="substationCode != null">substation_code,</if>
            <if test="substationRunningStatus != null">substation_running_status,</if>
            <if test="substationPowerSupplyStatus != null">substation_power_supply_status,</if>
            <if test="dataTime != null">data_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null">#{fileEncoding},</if>
            <if test="mineCode != null">#{mineCode},</if>
            <if test="mineName != null">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="substationCode != null">#{substationCode},</if>
            <if test="substationRunningStatus != null">#{substationRunningStatus},</if>
            <if test="substationPowerSupplyStatus != null">#{substationPowerSupplyStatus},</if>
            <if test="dataTime != null">#{dataTime},</if>
         </trim>
    </insert>

    <update id="updateKafkaSubstationRealTimeData" parameterType="KafkaSubstationRealTimeData">
        update kafka_substation_real_time_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="mineName != null">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="substationCode != null">substation_code = #{substationCode},</if>
            <if test="substationRunningStatus != null">substation_running_status = #{substationRunningStatus},</if>
            <if test="substationPowerSupplyStatus != null">substation_power_supply_status = #{substationPowerSupplyStatus},</if>
            <if test="dataTime != null">data_time = #{dataTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaSubstationRealTimeDataById" parameterType="Long">
        delete from kafka_substation_real_time_data where id = #{id}
    </delete>

    <delete id="deleteKafkaSubstationRealTimeDataByIds" parameterType="String">
        delete from kafka_substation_real_time_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>