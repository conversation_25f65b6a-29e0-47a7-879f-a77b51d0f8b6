package com.ruoyi.lxbi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 溜井运行数据对象 data_orepass_operation
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataOrepassOperation extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 溜井数据ID
     */
    private Long id;

    /**
     * 项目部门ID
     */
    private Long projectDepartmentId;

    /**
     * 溜井ID
     */
    private Long orePassId;

    /**
     * 作业时段ID
     */
    private Long workingPeriodId;

    /**
     * 作业日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "作业日期", sort = 1, mergeByValue = true, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 溜放趟数
     */
    @Excel(name = "溜放趟数")
    private Long trips;

    /**
     * 溜放矿石量（吨）
     */
    @Excel(name = "溜放矿石量")
    private Double oreTons;

}
