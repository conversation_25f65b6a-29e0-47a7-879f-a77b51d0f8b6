package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import com.ruoyi.common.core.table.TableColumnDataBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 原矿量数据表格VO（日报）
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableConfig(code = "raw_ore_volume_daily", name = "原矿量日报", description = "原矿量生产数据日报统计表格")
public class RawOreVolumeTableVo extends TableColumnDataBase {

    @TableHeader(label = "序号", order = 1, width = 5)
    private String serialNumber;

    @TableHeader(label = "名称", order = 2, parentPath = {"名称"}, colMergeGroup = {"name"})
    private String name;

    @TableHeader(label = "名称", order = 3, parentPath = {"名称"}, colMergeGroup = {"name"})
    private String subName;

    @TableHeader(label = "单位", order = 4, width = 5)
    private String unit;

    @TableHeader(label = "月计划", order = 5)
    private BigDecimal monthlyPlan;

    @TableHeader(label = "日产量", order = 6)
    private BigDecimal dailyOutput;

    @TableHeader(label = "月累计", order = 7)
    private BigDecimal monthlyAccumulated;

    @TableHeader(label = "完成率", order = 8)
    private String completionRate;

    @TableHeader(label = "月累计超欠", order = 9)
    private BigDecimal monthlyOverUnder;
}
