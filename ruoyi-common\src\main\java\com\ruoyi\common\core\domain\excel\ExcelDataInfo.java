package com.ruoyi.common.core.domain.excel;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ExcelDataInfo<T> {
    private int row;
    private T data;
    private RemindType type;
    private List<Message> messages = new ArrayList<>();

    public enum RemindType {
        WARNING, ERROR
    }

    @Data
    @AllArgsConstructor
    public static class Message {
        private RemindType type;
        private String column;
        private String message;
    }

    public synchronized void addError(String column, String message) {
        messages.add(new Message(RemindType.ERROR, column, message));
        type = RemindType.ERROR;
    }

    public synchronized void addWarning(String field, String message) {
        messages.add(new Message(RemindType.WARNING, field, message));
        if (type == null) {
            type = RemindType.WARNING;
        }
    }

}
