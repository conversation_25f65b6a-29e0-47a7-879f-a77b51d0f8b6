package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DateCrushingOperationStatsRequest;
import com.ruoyi.lxbi.domain.response.DateCrushingOperationPeriodStats;
import com.ruoyi.lxbi.domain.response.DateCrushingOperationStats;
import com.ruoyi.lxbi.service.IDateCrushingOperationStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 破碎操作数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/data/stats/crushing")
public class DateCrushingOperationStatsController {
    @Autowired
    private IDateCrushingOperationStatsService dateCrushingOperationStatsService;

//    @PreAuthorize("@ss.hasPermi('data:stats:crushing:01a')")
    @GetMapping("/01a")
    public R<List<DateCrushingOperationStats>> volumeAll(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                          @RequestParam(value = "startDate", required = false) String startDate,
                                                          @RequestParam(value = "endDate", required = false) String endDate) {
        DateCrushingOperationStatsRequest request = new DateCrushingOperationStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DateCrushingOperationStats> stats = dateCrushingOperationStatsService.selectStatsList(request, viewType);
        return R.ok(stats);
    }

//    @PreAuthorize("@ss.hasPermi('data:stats:crushing:01b')")
    @GetMapping("/01b")
    public R<List<DateCrushingOperationPeriodStats>> volumePeriod(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                   @RequestParam(value = "startDate", required = false) String startDate,
                                                                   @RequestParam(value = "endDate", required = false) String endDate) {
        DateCrushingOperationStatsRequest request = new DateCrushingOperationStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DateCrushingOperationPeriodStats> stats = dateCrushingOperationStatsService.selectPeriodStatsList(request, viewType);
        return R.ok(stats);
    }

//    @PreAuthorize("@ss.hasPermi('data:stats:crushing:02a')")
    @GetMapping("/02a")
    public R<List<DateCrushingOperationStats>> timeAll(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                        @RequestParam(value = "startDate", required = false) String startDate,
                                                        @RequestParam(value = "endDate", required = false) String endDate) {
        DateCrushingOperationStatsRequest request = new DateCrushingOperationStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DateCrushingOperationStats> stats = dateCrushingOperationStatsService.selectStatsList(request, viewType);
        return R.ok(stats);
    }

//    @PreAuthorize("@ss.hasPermi('data:stats:crushing:02b')")
    @GetMapping("/02b")
    public R<List<DateCrushingOperationPeriodStats>> dailyPeriodVolumeChart(
            @RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate) {
        DateCrushingOperationStatsRequest request = new DateCrushingOperationStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DateCrushingOperationPeriodStats> stats = dateCrushingOperationStatsService.selectPeriodStatsList(request, viewType);
        return R.ok(stats);
    }

//    @PreAuthorize("@ss.hasPermi('data:stats:crushing:03a')")
    @GetMapping("/03a")
    public R<List<DateCrushingOperationPeriodStats>> faultAll(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                         @RequestParam(value = "startDate", required = false) String startDate,
                                                         @RequestParam(value = "endDate", required = false) String endDate) {
        DateCrushingOperationStatsRequest request = new DateCrushingOperationStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);
        List<DateCrushingOperationPeriodStats> stats = dateCrushingOperationStatsService.selectPeriodStatsList(request, viewType);
        return R.ok(stats);
    }

}
