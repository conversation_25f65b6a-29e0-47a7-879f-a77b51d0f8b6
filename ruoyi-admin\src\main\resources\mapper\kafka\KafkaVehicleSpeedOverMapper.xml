<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaVehicleSpeedOverMapper">
    
    <resultMap type="KafkaVehicleSpeedOver" id="KafkaVehicleSpeedOverResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="dataGenerateTime"    column="data_generate_time"    />
        <result property="vehicleIdentificationCard"    column="vehicle_identification_card"    />
        <result property="inMineTime"    column="in_mine_time"    />
        <result property="alarmStartTime"    column="alarm_start_time"    />
        <result property="alarmEndTime"    column="alarm_end_time"    />
    </resultMap>

    <sql id="selectKafkaVehicleSpeedOverVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, data_generate_time, vehicle_identification_card, in_mine_time, alarm_start_time, alarm_end_time from kafka_vehicle_speed_over
    </sql>

    <select id="selectKafkaVehicleSpeedOverList" parameterType="KafkaVehicleSpeedOver" resultMap="KafkaVehicleSpeedOverResult">
        <include refid="selectKafkaVehicleSpeedOverVo"/>
        <where>  
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="dataGenerateTime != null "> and data_generate_time like concat('%', #{dataGenerateTime}, '%')</if>
            <if test="vehicleIdentificationCard != null  and vehicleIdentificationCard != ''"> and vehicle_identification_card = #{vehicleIdentificationCard}</if>
            <if test="params.beginInMineTime != null and params.beginInMineTime != '' and params.endInMineTime != null and params.endInMineTime != ''"> and in_mine_time between #{params.beginInMineTime}::date and #{params.endInMineTime}::date</if>
            <if test="params.beginAlarmStartTime != null and params.beginAlarmStartTime != '' and params.endAlarmStartTime != null and params.endAlarmStartTime != ''"> and alarm_start_time between #{params.beginAlarmStartTime}::date and #{params.endAlarmStartTime}::date</if>
            <if test="params.beginAlarmEndTime != null and params.beginAlarmEndTime != '' and params.endAlarmEndTime != null and params.endAlarmEndTime != ''"> and alarm_end_time between #{params.beginAlarmEndTime}::date and #{params.endAlarmEndTime}::date</if>
        </where>
    </select>
    
    <select id="selectKafkaVehicleSpeedOverById" parameterType="Long" resultMap="KafkaVehicleSpeedOverResult">
        <include refid="selectKafkaVehicleSpeedOverVo"/>
        where id = #{id}
    </select>

    <!-- 统计指定时间范围内的车辆告警数量 -->
    <select id="countAlarmVehiclesByDateRange" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT vehicle_identification_card)
        FROM kafka_vehicle_speed_over
        WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND alarm_start_time >= #{startDate}::date
            </if>
            <if test="endDate != null and endDate != ''">
                AND alarm_start_time &lt;= #{endDate}::date + INTERVAL '1 day' - INTERVAL '1 second'
            </if>
            AND vehicle_identification_card IS NOT NULL
            AND vehicle_identification_card != ''
    </select>

    <insert id="insertKafkaVehicleSpeedOver" parameterType="KafkaVehicleSpeedOver" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_vehicle_speed_over
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null and fileEncoding != ''">file_encoding,</if>
            <if test="mineCode != null and mineCode != ''">mine_code,</if>
            <if test="dataGenerateTime != null">data_generate_time,</if>
            <if test="vehicleIdentificationCard != null and vehicleIdentificationCard != ''">vehicle_identification_card,</if>
            <if test="inMineTime != null">in_mine_time,</if>
            <if test="alarmStartTime != null">alarm_start_time,</if>
            <if test="alarmEndTime != null">alarm_end_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null and fileEncoding != ''">#{fileEncoding},</if>
            <if test="mineCode != null and mineCode != ''">#{mineCode},</if>
            <if test="dataGenerateTime != null">#{dataGenerateTime},</if>
            <if test="vehicleIdentificationCard != null and vehicleIdentificationCard != ''">#{vehicleIdentificationCard},</if>
            <if test="inMineTime != null">#{inMineTime},</if>
            <if test="alarmStartTime != null">#{alarmStartTime},</if>
            <if test="alarmEndTime != null">#{alarmEndTime},</if>
         </trim>
    </insert>

    <update id="updateKafkaVehicleSpeedOver" parameterType="KafkaVehicleSpeedOver">
        update kafka_vehicle_speed_over
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null and fileEncoding != ''">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null and mineCode != ''">mine_code = #{mineCode},</if>
            <if test="dataGenerateTime != null">data_generate_time = #{dataGenerateTime},</if>
            <if test="vehicleIdentificationCard != null and vehicleIdentificationCard != ''">vehicle_identification_card = #{vehicleIdentificationCard},</if>
            <if test="inMineTime != null">in_mine_time = #{inMineTime},</if>
            <if test="alarmStartTime != null">alarm_start_time = #{alarmStartTime},</if>
            <if test="alarmEndTime != null">alarm_end_time = #{alarmEndTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaVehicleSpeedOverById" parameterType="Long">
        delete from kafka_vehicle_speed_over where id = #{id}
    </delete>

    <delete id="deleteKafkaVehicleSpeedOverByIds" parameterType="String">
        delete from kafka_vehicle_speed_over where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>