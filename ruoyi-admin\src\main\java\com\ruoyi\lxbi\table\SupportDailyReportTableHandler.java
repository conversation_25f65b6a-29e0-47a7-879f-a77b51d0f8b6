package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.domain.PlanSupportMonthly;
import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.response.DataSupportTypeDepartmentWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportTypeTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.PlanSupportMonthlyVo;
import com.ruoyi.lxbi.domain.table.SupportDailyReportTableVo;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;
import com.ruoyi.lxbi.service.IDataShotcreteSupportStatsService;
import com.ruoyi.lxbi.service.IPlanSupportMonthlyService;
import com.ruoyi.lxbi.table.params.SupportDailyReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支护数据日报表格处理器
 */
@Component
public class SupportDailyReportTableHandler extends BaseTableHandler<SupportDailyReportTableVo, SupportDailyReportQueryParams> {

    @Autowired
    private IDataShotcreteSupportStatsService dataShotcreteSupportStatsService;

    @Autowired
    private IPlanSupportMonthlyService planSupportMonthlyService;

    @Autowired
    private IBaseProjectDepartmentService baseProjectDepartmentService;

    @Override
    public List<SupportDailyReportTableVo> queryTableData(SupportDailyReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date operationDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取支护数据
        SupportResult supportResult = getSupportData(operationDate);

        // 构建表格数据
        return buildTableData(supportResult);
    }

    /**
     * 获取支护数据
     */
    private SupportResult getSupportData(Date operationDate) {
        // 获取财务月的开始日期到查询日期的累计数据
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(operationDate);

        DataSupportStatsRequest request = new DataSupportStatsRequest();
        request.setStartDate(financialMonthStart);
        request.setEndDate(operationDate);

        // 获取喷浆支护总体统计数据
        List<DataSupportTypeTotalWithPlanStats> supportTotalStats = dataShotcreteSupportStatsService.selectTotalWithPlanStatsList(request, "daily");
        List<DataSupportTypeTotalWithPlanStats> supportDailyStats = supportTotalStats.stream()
                .filter(stats -> FinancialDateUtils.isSameDay(stats.getOperationDate(), operationDate))
                .toList();

        // 获取喷浆支护项目部门统计数据
        List<DataSupportTypeDepartmentWithPlanStats> supportDepartmentStats = dataShotcreteSupportStatsService.selectDepartmentWithPlanStatsList(request, "daily");
        List<DataSupportTypeDepartmentWithPlanStats> supportDepartmentDailyStats = supportDepartmentStats.stream()
                .filter(stats -> FinancialDateUtils.isSameDay(stats.getOperationDate(), operationDate))
                .collect(Collectors.toList());

        // 计算支护总体月累计
        BigDecimal supportTotalMonthlyAccumulated = supportTotalStats.stream()
                .filter(stats -> stats.getTotalSupportLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSupportLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算支护总体日产量
        BigDecimal supportTotalDailyOutput = supportDailyStats.stream()
                .filter(stats -> stats.getTotalSupportLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSupportLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 获取支护月计划
        BigDecimal supportTotalMonthlyPlan = getSupportMonthlyPlan(operationDate);

        // 按项目部门ID汇总月累计数据
        Map<Long, BigDecimal> monthlyAccumulatedByDepartment = supportDepartmentStats.stream()
                .collect(Collectors.groupingBy(
                        DataSupportTypeDepartmentWithPlanStats::getProjectDepartmentId,
                        Collectors.summingDouble(stats -> stats.getTotalSupportLength() != null ? stats.getTotalSupportLength() : 0.0)
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> BigDecimal.valueOf(entry.getValue())
                ));

        // 获取支护月计划按项目部门分组
        Map<Long, BigDecimal> monthlyPlansByDepartment = getSupportMonthlyPlansByDepartment(operationDate);

        return new SupportResult(supportTotalDailyOutput, supportTotalMonthlyAccumulated, supportTotalMonthlyPlan,
                supportDepartmentDailyStats, monthlyAccumulatedByDepartment, monthlyPlansByDepartment);
    }

    /**
     * 获取支护月计划
     */
    private BigDecimal getSupportMonthlyPlan(Date operationDate) {
        // 计算财务月
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        // 获取支护月度计划
        PlanSupportMonthly planQuery = new PlanSupportMonthly();
        planQuery.setPlanDate(financialMonth);
        List<PlanSupportMonthlyVo> supportPlans = planSupportMonthlyService.selectPlanSupportMonthlyList(planQuery);

        // 汇总喷浆支护计划
        return supportPlans.stream()
                .filter(plan -> plan.getShotcreteSupportMeter() != null)
                .map(PlanSupportMonthly::getShotcreteSupportMeter)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取支护月计划按项目部门分组
     */
    private Map<Long, BigDecimal> getSupportMonthlyPlansByDepartment(Date operationDate) {
        // 计算财务月
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        // 获取支护月度计划
        PlanSupportMonthly planQuery = new PlanSupportMonthly();
        planQuery.setPlanDate(financialMonth);
        List<PlanSupportMonthlyVo> supportPlans = planSupportMonthlyService.selectPlanSupportMonthlyList(planQuery);

        // 按项目部门ID汇总喷浆支护计划
        return supportPlans.stream()
                .filter(plan -> plan.getShotcreteSupportMeter() != null)
                .collect(Collectors.groupingBy(
                        PlanSupportMonthly::getProjectDepartmentId,
                        Collectors.reducing(BigDecimal.ZERO,
                                plan -> plan.getShotcreteSupportMeter(),
                                BigDecimal::add)
                ));
    }

    /**
     * 构建表格数据
     */
    private List<SupportDailyReportTableVo> buildTableData(SupportResult supportResult) {
        List<SupportDailyReportTableVo> result = new ArrayList<>();

        // 添加支护总计行
        SupportDailyReportTableVo summary = new SupportDailyReportTableVo();
        summary.setBold(List.of("serialNumber", "name", "monthlyPlan", "dailyOutput", "monthlyAccumulated", "completionRate", "monthlyOverUnder"));
        summary.setSerialNumber("总计");
        summary.setName("支护");
        summary.setSubName("支护");
        summary.setUnit("m");
        summary.setMonthlyPlan(supportResult.getTotalMonthlyPlan());
        summary.setDailyOutput(supportResult.getTotalDailyOutput());
        summary.setMonthlyAccumulated(supportResult.getTotalMonthlyAccumulated());
        
        // 计算总完成率
        if (supportResult.getTotalMonthlyPlan().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal rate = supportResult.getTotalMonthlyAccumulated()
                    .divide(supportResult.getTotalMonthlyPlan(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            summary.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");

            // 计算月累计超欠：月累计 - 月计划
            BigDecimal overUnder = supportResult.getTotalMonthlyAccumulated()
                    .subtract(supportResult.getTotalMonthlyPlan());
            summary.setMonthlyOverUnder(overUnder);
        } else {
            summary.setCompletionRate("　");
            summary.setMonthlyOverUnder(null);
        }
        
        result.add(summary);

        // 添加支护明细项目（按项目部门）
        buildSupportDepartmentData(result, supportResult);

        return result;
    }

    /**
     * 构建支护项目部门明细数据
     */
    private void buildSupportDepartmentData(List<SupportDailyReportTableVo> result, SupportResult supportResult) {
        // 获取所有涉及的项目部门ID
        Set<Long> allDepartmentIds = new HashSet<>();
        allDepartmentIds.addAll(supportResult.getDepartmentDailyStats().stream()
                .map(DataSupportTypeDepartmentWithPlanStats::getProjectDepartmentId)
                .collect(Collectors.toSet()));
        allDepartmentIds.addAll(supportResult.getMonthlyPlansByDepartment().keySet());
        allDepartmentIds.addAll(supportResult.getMonthlyAccumulatedByDepartment().keySet());

        // 批量获取项目部门名称
        Map<Long, String> departmentNameMap = getDepartmentNameMap(allDepartmentIds);

        // 按项目部门ID分组，避免重复
        Map<Long, DataSupportTypeDepartmentWithPlanStats> departmentStatsMap = supportResult.getDepartmentDailyStats().stream()
                .collect(Collectors.toMap(
                        DataSupportTypeDepartmentWithPlanStats::getProjectDepartmentId,
                        stats -> stats,
                        (existing, replacement) -> {
                            // 如果有重复，累加支护长度
                            existing.setTotalSupportLength((existing.getTotalSupportLength() != null ? existing.getTotalSupportLength() : 0.0) +
                                    (replacement.getTotalSupportLength() != null ? replacement.getTotalSupportLength() : 0.0));
                            return existing;
                        }
                ));

        int subSerialNumber = 1;
        for (Long departmentId : allDepartmentIds) {
            DataSupportTypeDepartmentWithPlanStats stats = departmentStatsMap.get(departmentId);
            BigDecimal dailyOutput = stats != null && stats.getTotalSupportLength() != null ?
                    BigDecimal.valueOf(stats.getTotalSupportLength()) : BigDecimal.ZERO;
            BigDecimal monthlyPlan = supportResult.getMonthlyPlansByDepartment().getOrDefault(departmentId, BigDecimal.ZERO);
            BigDecimal accumulated = supportResult.getMonthlyAccumulatedByDepartment().getOrDefault(departmentId, BigDecimal.ZERO);

            SupportDailyReportTableVo vo = new SupportDailyReportTableVo();
            vo.setSerialNumber(String.valueOf(subSerialNumber++));
            vo.setName(getDepartmentNameFromMap(departmentId, stats, departmentNameMap));
            vo.setSubName("喷浆");
            vo.setUnit("m");
            vo.setMonthlyPlan(monthlyPlan);
            vo.setDailyOutput(dailyOutput);
            vo.setMonthlyAccumulated(accumulated);

            // 计算完成率
            if (monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = accumulated.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                vo.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
            } else {
                vo.setCompletionRate("　");
            }

            // 计算月累计超欠：月累计 - 月计划
            BigDecimal overUnder = accumulated.subtract(monthlyPlan);
            vo.setMonthlyOverUnder(overUnder);

            result.add(vo);
        }
    }

    /**
     * 批量获取项目部门名称映射
     */
    private Map<Long, String> getDepartmentNameMap(Set<Long> departmentIds) {
        Map<Long, String> departmentNameMap = new HashMap<>();

        if (departmentIds == null || departmentIds.isEmpty()) {
            return departmentNameMap;
        }

        // 批量查询项目部门信息
        for (Long departmentId : departmentIds) {
            try {
                BaseProjectDepartment department = baseProjectDepartmentService.selectBaseProjectDepartmentByProjectDepartmentId(departmentId);
                if (department != null && department.getProjectDepartmentName() != null) {
                    departmentNameMap.put(departmentId, department.getProjectDepartmentName());
                }
            } catch (Exception e) {
                // 查询失败时记录日志，但不影响整体流程
                System.err.println("获取项目部门名称失败，项目部门ID: " + departmentId + ", 错误: " + e.getMessage());
            }
        }

        return departmentNameMap;
    }

    /**
     * 从映射中获取项目部门名称
     */
    private String getDepartmentNameFromMap(Long departmentId, DataSupportTypeDepartmentWithPlanStats stats, Map<Long, String> departmentNameMap) {
        // 优先使用统计数据中的项目部门名称
        if (stats != null && stats.getProjectDepartmentName() != null && !stats.getProjectDepartmentName().trim().isEmpty()) {
            return stats.getProjectDepartmentName();
        }

        // 其次使用基础数据表中的项目部门名称
        String departmentNameFromBase = departmentNameMap.get(departmentId);
        if (departmentNameFromBase != null && !departmentNameFromBase.trim().isEmpty()) {
            return departmentNameFromBase;
        }

        // 最后使用默认格式（但这种情况应该很少出现）
        return "未知项目部门-" + departmentId;
    }

    /**
     * 支护结果包装类
     */
    private static class SupportResult {
        private final BigDecimal totalDailyOutput;
        private final BigDecimal totalMonthlyAccumulated;
        private final BigDecimal totalMonthlyPlan;
        private final List<DataSupportTypeDepartmentWithPlanStats> departmentDailyStats;
        private final Map<Long, BigDecimal> monthlyAccumulatedByDepartment;
        private final Map<Long, BigDecimal> monthlyPlansByDepartment;

        public SupportResult(BigDecimal totalDailyOutput,
                           BigDecimal totalMonthlyAccumulated,
                           BigDecimal totalMonthlyPlan,
                           List<DataSupportTypeDepartmentWithPlanStats> departmentDailyStats,
                           Map<Long, BigDecimal> monthlyAccumulatedByDepartment,
                           Map<Long, BigDecimal> monthlyPlansByDepartment) {
            this.totalDailyOutput = totalDailyOutput;
            this.totalMonthlyAccumulated = totalMonthlyAccumulated;
            this.totalMonthlyPlan = totalMonthlyPlan;
            this.departmentDailyStats = departmentDailyStats;
            this.monthlyAccumulatedByDepartment = monthlyAccumulatedByDepartment;
            this.monthlyPlansByDepartment = monthlyPlansByDepartment;
        }

        public BigDecimal getTotalDailyOutput() {
            return totalDailyOutput;
        }

        public BigDecimal getTotalMonthlyAccumulated() {
            return totalMonthlyAccumulated;
        }

        public BigDecimal getTotalMonthlyPlan() {
            return totalMonthlyPlan;
        }

        public List<DataSupportTypeDepartmentWithPlanStats> getDepartmentDailyStats() {
            return departmentDailyStats;
        }

        public Map<Long, BigDecimal> getMonthlyAccumulatedByDepartment() {
            return monthlyAccumulatedByDepartment;
        }

        public Map<Long, BigDecimal> getMonthlyPlansByDepartment() {
            return monthlyPlansByDepartment;
        }
    }
}
