package com.ruoyi.lxbi.controller.plan;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.PlanDeepHoleMonthly;
import com.ruoyi.lxbi.domain.request.PlanDeepHoleMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanDeepHoleMonthlyVo;
import com.ruoyi.lxbi.service.IPlanDeepHoleMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 中深孔月计划Controller
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/plan/planDeepHoleMonthly")
public class PlanDeepHoleMonthlyController extends BaseController {
    @Autowired
    private IPlanDeepHoleMonthlyService planDeepHoleMonthlyService;

    /**
     * 查询中深孔月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planDeepHoleMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanDeepHoleMonthly planDeepHoleMonthly) {
        startPage();
        List<PlanDeepHoleMonthlyVo> list = planDeepHoleMonthlyService.selectPlanDeepHoleMonthlyList(planDeepHoleMonthly);
        return getDataTable(list);
    }

    /**
     * 导出中深孔月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planDeepHoleMonthly:export')")
    @Log(title = "中深孔月计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanDeepHoleMonthly planDeepHoleMonthly) {
        List<PlanDeepHoleMonthlyVo> list = planDeepHoleMonthlyService.selectPlanDeepHoleMonthlyList(planDeepHoleMonthly);
        ExcelUtil<PlanDeepHoleMonthlyVo> util = new ExcelUtil<PlanDeepHoleMonthlyVo>(PlanDeepHoleMonthlyVo.class);
        util.exportExcel(response, list, "中深孔月计划数据");
    }

    /**
     * 获取中深孔月计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('plan:planDeepHoleMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planDeepHoleMonthlyService.selectPlanDeepHoleMonthlyById(id));
    }

    /**
     * 新增中深孔月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planDeepHoleMonthly:add')")
    @Log(title = "中深孔月计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanDeepHoleMonthly planDeepHoleMonthly)
    {
        return toAjax(planDeepHoleMonthlyService.insertPlanDeepHoleMonthly(planDeepHoleMonthly));
    }

    /**
     * 修改中深孔月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planDeepHoleMonthly:edit')")
    @Log(title = "中深孔月计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanDeepHoleMonthly planDeepHoleMonthly)
    {
        return toAjax(planDeepHoleMonthlyService.updatePlanDeepHoleMonthly(planDeepHoleMonthly));
    }

    /**
     * 删除中深孔月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planDeepHoleMonthly:remove')")
    @Log(title = "中深孔月计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(planDeepHoleMonthlyService.deletePlanDeepHoleMonthlyByIds(ids));
    }

    /**
     * 批量保存中深孔月计划（增删改查）
     * 传入批量列表，验证是否同一个月份的数据，然后查询这个月份的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('plan:planDeepHoleMonthly:edit')")
    @Log(title = "中深孔月计划批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<PlanDeepHoleMonthlyBatchDto> batchDataList)
    {
        try {
            int result = planDeepHoleMonthlyService.batchSavePlanDeepHoleMonthly(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
