package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaUndergroundPersonnelRealTimeData;

/**
 * 井下作业人员实时数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface IKafkaUndergroundPersonnelRealTimeDataService 
{
    /**
     * 查询井下作业人员实时数据
     * 
     * @param id 井下作业人员实时数据主键
     * @return 井下作业人员实时数据
     */
    public KafkaUndergroundPersonnelRealTimeData selectKafkaUndergroundPersonnelRealTimeDataById(Long id);

    /**
     * 查询井下作业人员实时数据列表
     * 
     * @param kafkaUndergroundPersonnelRealTimeData 井下作业人员实时数据
     * @return 井下作业人员实时数据集合
     */
    public List<KafkaUndergroundPersonnelRealTimeData> selectKafkaUndergroundPersonnelRealTimeDataList(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData);

    /**
     * 新增井下作业人员实时数据
     * 
     * @param kafkaUndergroundPersonnelRealTimeData 井下作业人员实时数据
     * @return 结果
     */
    public int insertKafkaUndergroundPersonnelRealTimeData(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData);

    /**
     * 修改井下作业人员实时数据
     * 
     * @param kafkaUndergroundPersonnelRealTimeData 井下作业人员实时数据
     * @return 结果
     */
    public int updateKafkaUndergroundPersonnelRealTimeData(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData);

    /**
     * 批量删除井下作业人员实时数据
     * 
     * @param ids 需要删除的井下作业人员实时数据主键集合
     * @return 结果
     */
    public int deleteKafkaUndergroundPersonnelRealTimeDataByIds(Long[] ids);

    /**
     * 删除井下作业人员实时数据信息
     *
     * @param id 井下作业人员实时数据主键
     * @return 结果
     */
    public int deleteKafkaUndergroundPersonnelRealTimeDataById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaUndergroundPersonnelRealTimeData parseKafkaMessage(String kafkaMessage);
}
