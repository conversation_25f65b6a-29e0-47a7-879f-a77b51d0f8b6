package com.ruoyi.common.core.table;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 表格处理器注册中心
 */
@Component
public class TableHandlerRegistry {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private final Map<String, BaseTableHandler<?,?>> handlerMap = new HashMap<>();

    @PostConstruct
    public void init() {
        // 自动注册所有表格处理器
        Map<String, BaseTableHandler> handlers = applicationContext.getBeansOfType(BaseTableHandler.class);
        for (BaseTableHandler<?,?> handler : handlers.values()) {
            String tableCode = handler.getTableCode();
            handlerMap.put(tableCode, handler);
        }
    }
    
    /**
     * 获取表格处理器
     */
    public BaseTableHandler<?,?> getHandler(String tableCode) {
        return handlerMap.get(tableCode);
    }
    
    /**
     * 注册表格处理器
     */
    public void registerHandler(String tableCode, BaseTableHandler<?,?> handler) {
        handlerMap.put(tableCode, handler);
    }
}