package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 车辆定位数据对象 kafka_vehicle_position
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KafkaVehiclePosition extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long isDeleted;

    /** 文件编码 */
    private String fileEncoding;

    /** 煤矿代码 */
    private String mineCode;

    /** 数据生成时间 */
    private Date dataGenerateTime;

    /** 车辆标识卡 */
    private String vehicleIdentificationCard;

    /** 进出矿标志 */
    private String inOrOutMineFlag;

    /** 进矿时间 */
    private Date inMineTime;

    /** 出矿时间 */
    private Date outMineTime;

    /** 区域代码 */
    private String areaCode;

    /** 进入区域时间 */
    private Date enterAreaTime;

    /** 分站编码 */
    private String stationCode;

    /** 进入分站时间 */
    private Date enterStationTime;

    /** 分站X坐标 */
    private BigDecimal stationXCoordinate;

    /** 分站Y坐标 */
    private BigDecimal stationYCoordinate;

    /** 分站Z坐标 */
    private BigDecimal stationZCoordinate;

}
