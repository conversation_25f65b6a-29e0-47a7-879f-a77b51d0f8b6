#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微震事件Excel数据转换为SQL插入脚本工具
支持雪花ID生成
"""

import pandas as pd
import re
import time
import random
from datetime import datetime
from typing import List, Any, Optional


class SnowflakeIdGenerator:
    """雪花ID生成器"""
    
    def __init__(self, datacenter_id: int = 1, worker_id: int = 1):
        """
        初始化雪花ID生成器
        
        Args:
            datacenter_id: 数据中心ID (0-31)
            worker_id: 工作机器ID (0-31)
        """
        # 时间戳起始点 (2020-01-01 00:00:00 UTC)
        self.epoch = 1577836800000
        
        # 各部分位数
        self.datacenter_id_bits = 5
        self.worker_id_bits = 5
        self.sequence_bits = 12
        
        # 最大值
        self.max_datacenter_id = (1 << self.datacenter_id_bits) - 1
        self.max_worker_id = (1 << self.worker_id_bits) - 1
        self.max_sequence = (1 << self.sequence_bits) - 1
        
        # 位移
        self.worker_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits
        self.timestamp_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits
        
        # 验证参数
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"datacenter_id must be between 0 and {self.max_datacenter_id}")
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError(f"worker_id must be between 0 and {self.max_worker_id}")
            
        self.datacenter_id = datacenter_id
        self.worker_id = worker_id
        self.sequence = 0
        self.last_timestamp = -1
    
    def _current_timestamp(self) -> int:
        """获取当前时间戳（毫秒）"""
        return int(time.time() * 1000)
    
    def _wait_next_millis(self, last_timestamp: int) -> int:
        """等待下一毫秒"""
        timestamp = self._current_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._current_timestamp()
        return timestamp
    
    def generate_id(self) -> int:
        """生成雪花ID"""
        timestamp = self._current_timestamp()
        
        if timestamp < self.last_timestamp:
            raise Exception("Clock moved backwards. Refusing to generate id")
        
        if timestamp == self.last_timestamp:
            self.sequence = (self.sequence + 1) & self.max_sequence
            if self.sequence == 0:
                timestamp = self._wait_next_millis(self.last_timestamp)
        else:
            self.sequence = 0
        
        self.last_timestamp = timestamp
        
        # 组合各部分生成最终ID
        snowflake_id = ((timestamp - self.epoch) << self.timestamp_shift) | \
                      (self.datacenter_id << self.datacenter_id_shift) | \
                      (self.worker_id << self.worker_id_shift) | \
                      self.sequence
        
        return snowflake_id


class MicroseismicDataConverter:
    """微震数据转换器"""
    
    def __init__(self):
        self.id_generator = SnowflakeIdGenerator()
        self.column_mapping = {
            '工程ID': 'project_id',
            '事件名称': 'event_name',
            '微震事件日期': 'event_date',
            '微震事件时间': 'event_time',
            '微震事件定位误差': 'location_error',
            '微震事件X轴坐标': 'x_coordinate',
            '微震事件Y轴坐标': 'y_coordinate',
            '微震事件Z轴坐标': 'z_coordinate',
            '微震事件辐射能': 'radiated_energy',
            '微震事件P波辐射能': 'p_wave_radiated_energy',
            '微震事件S波辐射能': 's_wave_radiated_energy',
            '矩震级': 'moment_magnitude',
            '里氏震级': 'richter_magnitude',
            '当地震级': 'local_magnitude',
            '地震矩': 'seismic_moment',
            'P波地震矩': 'p_wave_seismic_moment',
            'S波地震矩': 's_wave_seismic_moment',
            '体变势': 'volumetric_strain',
            '视应力': 'apparent_stress',
            '视体积': 'apparent_volume',
            '转角频率': 'corner_frequency',
            'P波转角频率': 'p_wave_corner_frequency',
            'S波转角频率': 's_wave_corner_frequency',
            'P波低频幅值': 'p_wave_low_freq_amplitude',
            'S波低频幅值': 's_wave_low_freq_amplitude',
            '静态应力降': 'static_stress_drop',
            '动态应力降': 'dynamic_stress_drop',
            '震源半径': 'source_radius',
            '最大滑移速度': 'max_slip_velocity',
            '微震事件服务器ID': 'server_id',
            '被触发传感器个数': 'triggered_sensor_count',
            '被触发传感器ID': 'triggered_sensor_ids',
            '参与定位传感器ID': 'location_sensor_ids',
            '信号类型': 'signal_type',
            '信号类型_自动': 'auto_signal_type'
        }
    
    def read_excel_file(self, file_path: str) -> pd.DataFrame:
        """读取Excel文件"""
        try:
            # 尝试读取Excel文件
            df = pd.read_excel(file_path)
            print(f"成功读取Excel文件: {file_path}")
            print(f"数据行数: {len(df)}")
            print(f"列数: {len(df.columns)}")

            # 标准化列名：去除所有空白字符（包含前后和中间的空格、制表符、全角空格等）
            original_columns = list(df.columns)
            normalized_columns = [col.strip().replace('\u3000','') for col in df.columns]
            # normalized_columns = [re.sub(r"\s+", "", str(col)) for col in df.columns]
            df.columns = normalized_columns

            # 如果有变化，打印一次提示，便于排查
            if original_columns != normalized_columns:
                print("已标准化列名:")
                for o, n in zip(original_columns, normalized_columns):
                    if str(o) != str(n):
                        print(f"  '{o}' -> '{n}'")

            return df
        except Exception as e:
            raise Exception(f"读取Excel文件失败: {str(e)}")
    
    def format_value(self, value: Any, column_name: str) -> str:
        """格式化字段值"""
        if pd.isna(value) or value == '' or value is None:
            return 'NULL'
        
        # 字符串类型字段
        string_fields = ['event_name', 'triggered_sensor_ids', 'location_sensor_ids']
        if column_name in string_fields:
            # 转义单引号
            escaped_value = str(value).strip().replace("'", "''")
            return f"'{escaped_value}'"
        
        # 日期字段
        if column_name == 'event_date':
            if isinstance(value, str):
                return f"'{value.strip()}'"
            elif hasattr(value, 'strftime'):
                return f"'{value.strftime('%Y-%m-%d')}'"
            else:
                return f"'{str(value)}'"
        
        # 时间字段
        if column_name == 'event_time':
            if isinstance(value, str):
                return f"'{value.strip()}'"
            elif hasattr(value, 'strftime'):
                return f"'{value.strftime('%H:%M:%S')}'"
            else:
                return f"'{str(value)}'"
        
        # 数值字段
        return str(value)
    
    def generate_insert_sql(self, df: pd.DataFrame, table_name: str = 'excel_microseismic_events') -> List[str]:
        """生成INSERT SQL语句"""
        sql_statements = []
        
        # 生成表头注释
        sql_statements.append(f"-- 微震事件数据插入脚本")
        sql_statements.append(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sql_statements.append(f"-- 数据行数: {len(df)}")
        sql_statements.append("")
        
        # 获取所有列名（英文）
        columns = ['id'] + [self.column_mapping.get(col, col) for col in df.columns if col in self.column_mapping]
        
        for index, row in df.iterrows():
            # 生成雪花ID
            snowflake_id = self.id_generator.generate_id()
            
            # 构建值列表
            values = [str(snowflake_id)]  # 添加雪花ID
            
            for col in df.columns:
                if col in self.column_mapping:
                    english_col = self.column_mapping[col]
                    formatted_value = self.format_value(row[col], english_col)
                    values.append(formatted_value)
            
            # 生成INSERT语句
            columns_str = ', '.join(columns)
            values_str = ', '.join(values)
            
            sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({values_str});"
            sql_statements.append(sql)
            
            # 每100条记录添加一个空行，便于阅读
            if (index + 1) % 100 == 0:
                sql_statements.append("")
        
        return sql_statements
    
    def convert_excel_to_sql(self, excel_file_path: str, output_sql_path: str, table_name: str = 'excel_microseismic_events'):
        """将Excel文件转换为SQL插入脚本"""
        try:
            print("开始转换Excel文件到SQL脚本...")
            
            # 读取Excel文件
            df = self.read_excel_file(excel_file_path)
            
            # 验证列名
            missing_columns = []
            for col in df.columns:
                if col not in self.column_mapping:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"警告: 以下列名未在映射中找到: {missing_columns}")
            
            # 生成SQL语句
            print("生成SQL插入语句...")
            sql_statements = self.generate_insert_sql(df, table_name)
            
            # 写入SQL文件
            with open(output_sql_path, 'w', encoding='utf-8') as f:
                for statement in sql_statements:
                    f.write(statement + '\n')
            
            print(f"转换完成!")
            print(f"输出文件: {output_sql_path}")
            print(f"生成了 {len(df)} 条INSERT语句")
            
        except Exception as e:
            print(f"转换失败: {str(e)}")
            raise


def main():
    """主函数"""
    # 配置文件路径
    excel_file = "microseismic-events.xlsx"
    output_sql_file = "excel_microseismic_events_insert.sql"
    
    # 创建转换器实例
    converter = MicroseismicDataConverter()
    
    try:
        # 执行转换
        converter.convert_excel_to_sql(excel_file, output_sql_file)
        
        print("\n" + "="*50)
        print("转换成功完成!")
        print(f"请检查输出文件: {output_sql_file}")
        print("="*50)
        
    except Exception as e:
        print(f"\n转换失败: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
