package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.PlanMineralSaleMonthlyMapper;
import com.ruoyi.lxbi.domain.PlanMineralSaleMonthly;
import com.ruoyi.lxbi.domain.request.PlanMineralSaleMonthlyBatchDto;
import com.ruoyi.lxbi.service.IPlanMineralSaleMonthlyService;

/**
 * 选矿销售月计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@Service
public class PlanMineralSaleMonthlyServiceImpl implements IPlanMineralSaleMonthlyService 
{
    @Autowired
    private PlanMineralSaleMonthlyMapper planMineralSaleMonthlyMapper;

    /**
     * 查询选矿销售月计划
     * 
     * @param id 选矿销售月计划主键
     * @return 选矿销售月计划
     */
    @Override
    public PlanMineralSaleMonthly selectPlanMineralSaleMonthlyById(Long id)
    {
        return planMineralSaleMonthlyMapper.selectPlanMineralSaleMonthlyById(id);
    }

    /**
     * 查询选矿销售月计划列表
     * 
     * @param planMineralSaleMonthly 选矿销售月计划
     * @return 选矿销售月计划
     */
    @Override
    public List<PlanMineralSaleMonthly> selectPlanMineralSaleMonthlyList(PlanMineralSaleMonthly planMineralSaleMonthly)
    {
        return planMineralSaleMonthlyMapper.selectPlanMineralSaleMonthlyList(planMineralSaleMonthly);
    }

    /**
     * 新增选矿销售月计划
     * 
     * @param planMineralSaleMonthly 选矿销售月计划
     * @return 结果
     */
    @Override
    public int insertPlanMineralSaleMonthly(PlanMineralSaleMonthly planMineralSaleMonthly)
    {
        planMineralSaleMonthly.setCreateTime(DateUtils.getNowDate());
        return planMineralSaleMonthlyMapper.insertPlanMineralSaleMonthly(planMineralSaleMonthly);
    }

    /**
     * 修改选矿销售月计划
     * 
     * @param planMineralSaleMonthly 选矿销售月计划
     * @return 结果
     */
    @Override
    public int updatePlanMineralSaleMonthly(PlanMineralSaleMonthly planMineralSaleMonthly)
    {
        planMineralSaleMonthly.setUpdateTime(DateUtils.getNowDate());
        return planMineralSaleMonthlyMapper.updatePlanMineralSaleMonthly(planMineralSaleMonthly);
    }

    /**
     * 批量删除选矿销售月计划
     * 
     * @param ids 需要删除的选矿销售月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanMineralSaleMonthlyByIds(Long[] ids)
    {
        return planMineralSaleMonthlyMapper.deletePlanMineralSaleMonthlyByIds(ids);
    }

    /**
     * 删除选矿销售月计划信息
     *
     * @param id 选矿销售月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanMineralSaleMonthlyById(Long id)
    {
        return planMineralSaleMonthlyMapper.deletePlanMineralSaleMonthlyById(id);
    }

    /**
     * 批量保存选矿销售月计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePlanMineralSaleMonthly(List<PlanMineralSaleMonthlyBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个月份的数据
        String planMonth = batchDataList.get(0).getPlanMonth();
        if (StringUtils.isBlank(planMonth)) {
            throw new ServiceException("计划月份不能为空");
        }

        boolean allSameMonth = batchDataList.stream()
                .allMatch(data -> planMonth.equals(data.getPlanMonth()));
        if (!allSameMonth) {
            throw new ServiceException("批量数据必须是同一个计划月份");
        }

        // 查询该月份的现有数据
        PlanMineralSaleMonthly queryParam = new PlanMineralSaleMonthly();
        queryParam.setPlanDate(planMonth);
        List<PlanMineralSaleMonthly> existingDataList = planMineralSaleMonthlyMapper.selectPlanMineralSaleMonthlyList(queryParam);
        Map<Long, PlanMineralSaleMonthly> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(PlanMineralSaleMonthly::getId, data -> data));

        List<PlanMineralSaleMonthly> toInsert = new ArrayList<>();
        List<PlanMineralSaleMonthly> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (PlanMineralSaleMonthlyBatchDto batchData : batchDataList) {
            if ("add".equals(batchData.getOperationType())) {
                // 新增数据
                PlanMineralSaleMonthly newData = new PlanMineralSaleMonthly();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if ("edit".equals(batchData.getOperationType()) && batchData.getId() != null) {
                // 更新现有数据
                if (existingDataMap.containsKey(batchData.getId())) {
                    PlanMineralSaleMonthly updateData = new PlanMineralSaleMonthly();
                    copyProperties(batchData, updateData);
                    updateData.setId(batchData.getId());
                    updateData.setUpdateBy(SecurityUtils.getUsername());
                    updateData.setUpdateTime(DateUtils.getNowDate());
                    toUpdate.add(updateData);
                    toDelete.remove(batchData.getId());
                }
            }
        }

        int totalProcessed = 0;

        // 执行批量操作
        if (!toInsert.isEmpty()) {
            for (PlanMineralSaleMonthly data : toInsert) {
                totalProcessed += planMineralSaleMonthlyMapper.insertPlanMineralSaleMonthly(data);
            }
        }

        if (!toUpdate.isEmpty()) {
            for (PlanMineralSaleMonthly data : toUpdate) {
                totalProcessed += planMineralSaleMonthlyMapper.updatePlanMineralSaleMonthly(data);
            }
        }

        if (!toDelete.isEmpty()) {
            totalProcessed += planMineralSaleMonthlyMapper.deletePlanMineralSaleMonthlyByIds(toDelete.toArray(new Long[0]));
        }

        return totalProcessed;
    }

    /**
     * 复制属性
     */
    private void copyProperties(PlanMineralSaleMonthlyBatchDto source, PlanMineralSaleMonthly target) {
        target.setPlanDate(source.getPlanMonth());
        target.setIronConcentrateVolume(source.getIronConcentrateVolume());
        target.setConcentratorBinsStockVolume(source.getConcentratorBinsStockVolume());
        target.setServiceShaftSurfaceStockVolume(source.getServiceShaftSurfaceStockVolume());
        target.setRawOreGrade(source.getRawOreGrade());
        target.setStockVolume(source.getStockVolume());
        if (StringUtils.isNotBlank(source.getRemark())) {
            target.setRemark(source.getRemark());
        }
    }
}
