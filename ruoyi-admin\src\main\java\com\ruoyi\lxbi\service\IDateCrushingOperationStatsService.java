package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DateCrushingOperationStatsRequest;
import com.ruoyi.lxbi.domain.response.DateCrushingOperationStats;
import com.ruoyi.lxbi.domain.response.DateCrushingOperationPeriodStats;

import java.util.List;

/**
 * 破碎操作数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IDateCrushingOperationStatsService {

    /**
     * 查询统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 统计数据集合
     */
    public List<DateCrushingOperationStats> selectStatsList(DateCrushingOperationStatsRequest request, String viewType);

    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    public List<DateCrushingOperationPeriodStats> selectPeriodStatsList(DateCrushingOperationStatsRequest request, String viewType);

}
