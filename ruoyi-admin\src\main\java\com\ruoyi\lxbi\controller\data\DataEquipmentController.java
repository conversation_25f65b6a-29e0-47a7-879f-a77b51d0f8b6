package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataEquipment;
import com.ruoyi.lxbi.domain.request.DataEquipmentBatchDto;
import com.ruoyi.lxbi.domain.response.DataEquipmentEfficiencyStats;
import com.ruoyi.lxbi.domain.response.DataEquipmentUtilizationStats;
import com.ruoyi.lxbi.service.IDataEquipmentService;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 设备数据管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/data/dataEquipment")
public class DataEquipmentController extends BaseController {

    @Autowired
    private IDataEquipmentService dataEquipmentService;

    /**
     * 查询设备数据管理列表
     */
    @PreAuthorize("@ss.hasPermi('data:dataEquipment:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataEquipment dataEquipment) {
        startPage();
        List<DataEquipment> list = dataEquipmentService.selectDataEquipmentList(dataEquipment);
        return getDataTable(list);
    }

    /**
     * 导出设备数据管理列表
     */
    @PreAuthorize("@ss.hasPermi('data:dataEquipment:export')")
    @Log(title = "设备数据管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataEquipment dataEquipment) {
        List<DataEquipment> list = dataEquipmentService.selectDataEquipmentList(dataEquipment);
        ExcelUtil<DataEquipment> util = new ExcelUtil<DataEquipment>(DataEquipment.class);
        util.exportExcel(response, list, "设备数据管理数据");
    }

    /**
     * 获取设备数据管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:dataEquipment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataEquipmentService.selectDataEquipmentById(id));
    }

    /**
     * 新增设备数据管理
     */
    @PreAuthorize("@ss.hasPermi('data:dataEquipment:add')")
    @Log(title = "设备数据管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataEquipment dataEquipment)
    {
        return toAjax(dataEquipmentService.insertDataEquipment(dataEquipment));
    }

    /**
     * 修改设备数据管理
     */
    @PreAuthorize("@ss.hasPermi('data:dataEquipment:edit')")
    @Log(title = "设备数据管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataEquipment dataEquipment)
    {
        return toAjax(dataEquipmentService.updateDataEquipment(dataEquipment));
    }

    /**
     * 删除设备数据管理
     */
    @PreAuthorize("@ss.hasPermi('data:dataEquipment:remove')")
    @Log(title = "设备数据管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataEquipmentService.deleteDataEquipmentByIds(ids));
    }

    /**
     * 批量保存设备数据
     */
    @PreAuthorize("@ss.hasPermi('data:dataEquipment:edit')")
    @Log(title = "设备数据批量保存", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSave")
    public R<String> batchSave(@RequestBody List<DataEquipmentBatchDto> dataList) {
        try {
            int result = dataEquipmentService.batchSaveDataEquipment(dataList);
            return R.ok("批量保存成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量保存失败：" + e.getMessage());
        }
    }

    /**
     * 查询设备台效统计数据
     */
    @PreAuthorize("@ss.hasPermi('data:dataEquipment:list')")
    @GetMapping("/efficiency")
    public R<List<DataEquipmentEfficiencyStats>> getEfficiencyStats(
            @RequestParam() Long equipmentType,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
            @RequestParam(defaultValue = "month") String timeType) {
        startPage();
        List<DataEquipmentEfficiencyStats> list = dataEquipmentService.getEfficiencyStats(equipmentType, startTime, endTime, timeType);
        return R.ok(list);
    }

    /**
     * 查询设备作业率统计数据
     */
    @PreAuthorize("@ss.hasPermi('data:dataEquipment:list')")
    @GetMapping("/utilization")
    public R<List<DataEquipmentUtilizationStats>> getUtilizationStats(
            @RequestParam() Long equipmentType,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
            @RequestParam(defaultValue = "month") String timeType) {
        startPage();
        List<DataEquipmentUtilizationStats> list = dataEquipmentService.getUtilizationStats(equipmentType, startTime, endTime, timeType);
        return R.ok(list);
    }
}
