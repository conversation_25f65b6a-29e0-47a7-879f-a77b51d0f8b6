package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 井下作业人员实时数据对象 kafka_underground_personnel_real_time_data
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KafkaUndergroundPersonnelRealTimeData extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long isDeleted;

    /** 文件编码 */
    @Excel(name = "文件编码")
    private String fileEncoding;

    /** 煤矿编码 */
    @Excel(name = "煤矿编码")
    private String mineCode;

    /** 矿井名称 */
    @Excel(name = "矿井名称")
    private String mineName;

    /** 数据上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataUploadTime;

    /** 人员卡编码 */
    @Excel(name = "人员卡编码")
    private String personnelCardCode;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 出入井标志位 */
    @Excel(name = "出入井标志位")
    private String entryAndExitFlag;

    /** 入井时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入井时刻", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entryTime;

    /** 出井时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出井时刻", width = 30, dateFormat = "yyyy-MM-dd")
    private Date exitTime;

    /** 区域编码 */
    @Excel(name = "区域编码")
    private String areaCode;

    /** 进入当前区域时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "进入当前区域时刻", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entryCurrentAreaTime;

    /** 基站编码 */
    @Excel(name = "基站编码")
    private String baseStationCode;

    /** 进入当前所处基站时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "进入当前所处基站时刻", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entryCurrentBaseStationTime;

    /** 劳动组织方式 */
    @Excel(name = "劳动组织方式")
    private String laborOrganizationMethod;

    /** 距离基站距离 */
    @Excel(name = "距离基站距离")
    private BigDecimal distanceFromBaseStation;

    /** 人员工作状态 */
    @Excel(name = "人员工作状态")
    private String personnelWorkStatus;

    /** 是否矿领导 */
    @Excel(name = "是否矿领导")
    private String isMineLeader;

    /** 是否特种人员 */
    @Excel(name = "是否特种人员")
    private String isSpecialPersonnel;

    /** 行进轨迹基站，时间集合 */
    @Excel(name = "行进轨迹基站，时间集合")
    private String trajectoryBaseStationTimeCollection;

    /** 数据时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataTime;

}
