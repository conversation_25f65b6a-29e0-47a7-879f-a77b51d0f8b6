package com.ruoyi.lxbi.controller.plan;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.PlanBackfillingMonthly;
import com.ruoyi.lxbi.domain.request.PlanBackfillingMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanBackfillingMonthlyVo;
import com.ruoyi.lxbi.service.IPlanBackfillingMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 充填月计划Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/plan/planBackfillingMonthly")
public class PlanBackfillingMonthlyController extends BaseController {
    @Autowired
    private IPlanBackfillingMonthlyService planBackfillingMonthlyService;

    /**
     * 查询充填月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planBackfillingMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanBackfillingMonthly planBackfillingMonthly) {
        startPage();
        List<PlanBackfillingMonthlyVo> list = planBackfillingMonthlyService.selectPlanBackfillingMonthlyList(planBackfillingMonthly);
        return getDataTable(list);
    }

    /**
     * 导出充填月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planBackfillingMonthly:export')")
    @Log(title = "充填月计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanBackfillingMonthly planBackfillingMonthly) {
        List<PlanBackfillingMonthlyVo> list = planBackfillingMonthlyService.selectPlanBackfillingMonthlyList(planBackfillingMonthly);
        ExcelUtil<PlanBackfillingMonthlyVo> util = new ExcelUtil<PlanBackfillingMonthlyVo>(PlanBackfillingMonthlyVo.class);
        util.exportExcel(response, list, "充填月计划数据");
    }

    /**
     * 获取充填月计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('plan:planBackfillingMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planBackfillingMonthlyService.selectPlanBackfillingMonthlyById(id));
    }

    /**
     * 新增充填月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planBackfillingMonthly:add')")
    @Log(title = "充填月计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanBackfillingMonthly planBackfillingMonthly)
    {
        return toAjax(planBackfillingMonthlyService.insertPlanBackfillingMonthly(planBackfillingMonthly));
    }

    /**
     * 修改充填月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planBackfillingMonthly:edit')")
    @Log(title = "充填月计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanBackfillingMonthly planBackfillingMonthly)
    {
        return toAjax(planBackfillingMonthlyService.updatePlanBackfillingMonthly(planBackfillingMonthly));
    }

    /**
     * 删除充填月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planBackfillingMonthly:remove')")
    @Log(title = "充填月计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(planBackfillingMonthlyService.deletePlanBackfillingMonthlyByIds(ids));
    }

    /**
     * 批量保存充填月计划（增删改查）
     * 传入批量列表，验证是否同一个月份的数据，然后查询这个月份的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('plan:planBackfillingMonthly:edit')")
    @Log(title = "充填月计划批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<PlanBackfillingMonthlyBatchDto> batchDataList)
    {
        try {
            int result = planBackfillingMonthlyService.batchSavePlanBackfillingMonthly(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
