package com.ruoyi.lxbi.admin.service;

import com.ruoyi.lxbi.domain.vo.*;

import java.util.List;

/**
 * 车辆安全统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IVehicleSafetyStatService {

    /**
     * 获取车辆安全概览统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 车辆安全概览统计
     */
    VehicleSafetyOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取车辆报警部门分布统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 车辆报警部门分布统计列表
     */
    List<VehicleAlarmDepartmentDistributionVO> getAlarmDepartmentDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取车辆告警类型分布统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 车辆告警类型分布统计列表
     */
    List<VehicleAlarmTypeDistributionVO> getAlarmTypeDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取车辆告警记录列表
     *
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 车辆告警记录列表
     */
    List<VehicleAlarmRecordVO> getAlarmRecords(String viewType, String startDate, String endDate);

    /**
     * 获取车辆类型分布统计
     *
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 车辆类型分布统计列表
     */
    List<VehicleTypeDistributionVO> getVehicleTypeDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取车辆状态分布统计
     *
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 车辆状态分布统计列表
     */
    List<VehicleStatusDistributionVO> getVehicleStatusDistribution(String viewType, String startDate, String endDate);
}
