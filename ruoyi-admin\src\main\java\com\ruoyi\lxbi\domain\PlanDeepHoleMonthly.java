package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 中深孔月计划对象 plan_deep_hole_monthly
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanDeepHoleMonthly extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 工作面ID */
    private Long workingFaceId;

    /** 采场ID */
    private Long stopeId;

    /** 中深孔钻机米数 */
    @Excel(name = "中深孔钻机米数")
    private BigDecimal deepHoleMeter;

    /** 计划月份 */
    @Excel(name = "计划月份", sort = 1, mergeByValue = true)
    private String planDate;

}
