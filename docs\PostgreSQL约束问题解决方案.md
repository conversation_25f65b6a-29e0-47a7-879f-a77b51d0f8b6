# PostgreSQL约束问题解决方案

## 问题描述

在使用PostgreSQL的ON CONFLICT功能时，出现以下错误：

```
ERROR: there is no unique or exclusion constraint matching the ON CONFLICT specification
```

这个错误表明数据库表中缺少与ON CONFLICT子句匹配的唯一约束。

## 错误原因

1. **缺少唯一约束**：表中没有创建对应的唯一约束或唯一索引
2. **约束不匹配**：ON CONFLICT指定的字段与实际的唯一约束字段不匹配
3. **部分索引问题**：使用了WHERE条件的部分索引，但ON CONFLICT语法不支持

## 解决方案

### 方案1：立即修复（推荐）

执行快速修复脚本：

```sql
-- 执行此脚本立即解决问题
\i sql/quick_fix_constraint.sql
```

或者手动执行以下SQL：

```sql
-- 1. 清理重复数据
WITH ranked_data AS (
    SELECT 
        id,
        area_code,
        mine_code,
        ROW_NUMBER() OVER (
            PARTITION BY area_code, mine_code 
            ORDER BY data_upload_time DESC NULLS LAST, id DESC
        ) as rn
    FROM kafka_people_location_info 
    WHERE is_deleted = 0
)
DELETE FROM kafka_people_location_info 
WHERE id IN (
    SELECT id FROM ranked_data WHERE rn > 1
);

-- 2. 删除旧约束
ALTER TABLE kafka_people_location_info DROP CONSTRAINT IF EXISTS uk_area_mine_code;
DROP INDEX IF EXISTS idx_kpli_area_mine_unique;

-- 3. 创建表级唯一约束
ALTER TABLE kafka_people_location_info 
ADD CONSTRAINT uk_area_mine_code UNIQUE (area_code, mine_code);
```

### 方案2：完整部署

如果是新环境，执行完整的部署脚本：

```sql
\i sql/postgresql_deployment.sql
```

### 方案3：检查和修复

使用详细的检查和修复脚本：

```sql
\i sql/create_simple_constraint.sql
```

## 验证修复结果

### 1. 检查约束是否创建成功

```sql
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'kafka_people_location_info'::regclass
AND contype = 'u';
```

期望输出：
```
constraint_name    | constraint_type | constraint_definition
uk_area_mine_code  | u              | UNIQUE (area_code, mine_code)
```

### 2. 测试UPSERT操作

```sql
INSERT INTO kafka_people_location_info (
    mine_code, mine_name, area_code, area_name, area_type,
    area_approved_personnel, status, is_deleted,
    create_by, create_time, update_by, update_time
) VALUES (
    'TEST001', '测试煤矿', 'TESTAREA001', '测试区域', '测试区',
    10, 1, 0,
    'test_user', CURRENT_TIMESTAMP, 'test_user', CURRENT_TIMESTAMP
)
ON CONFLICT (area_code, mine_code) 
DO UPDATE SET
    mine_name = EXCLUDED.mine_name,
    area_name = EXCLUDED.area_name,
    update_by = EXCLUDED.update_by,
    update_time = EXCLUDED.update_time;
```

如果执行成功且没有错误，说明修复完成。

### 3. 清理测试数据

```sql
DELETE FROM kafka_people_location_info 
WHERE area_code = 'TESTAREA001' AND mine_code = 'TEST001';
```

## 代码修改说明

### 1. Mapper XML修改

原来的UPSERT语句：
```xml
ON CONFLICT (area_code, mine_code) WHERE is_deleted = 0
```

修改为：
```xml
ON CONFLICT (area_code, mine_code)
```

### 2. Service层增强

添加了备用处理逻辑，当UPSERT失败时自动切换到传统的查询-插入/更新方式：

```java
try {
    result = kafkaPeopleLocationInfoMapper.upsertKafkaPeopleLocationInfo(locationInfo);
} catch (Exception e) {
    // 备用方案：先查询是否存在，然后决定插入或更新
    KafkaPeopleLocationInfo existing = kafkaPeopleLocationInfoMapper
            .selectByAreaCodeAndMineCode(locationInfo.getAreaCode(), locationInfo.getMineCode());
    
    if (existing != null) {
        // 更新现有记录
        locationInfo.setId(existing.getId());
        result = kafkaPeopleLocationInfoMapper.updateKafkaPeopleLocationInfo(locationInfo);
    } else {
        // 插入新记录
        result = kafkaPeopleLocationInfoMapper.insertKafkaPeopleLocationInfo(locationInfo);
    }
}
```

## 预防措施

### 1. 数据库初始化

在创建表时就应该创建必要的约束：

```sql
CREATE TABLE kafka_people_location_info (
    -- 字段定义...
    CONSTRAINT uk_area_mine_code UNIQUE (area_code, mine_code)
);
```

### 2. 部署检查清单

- [ ] 检查表是否存在
- [ ] 检查唯一约束是否创建
- [ ] 测试UPSERT操作
- [ ] 验证数据完整性

### 3. 监控和告警

添加数据库约束监控：

```sql
-- 检查约束状态
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE tablename = 'kafka_people_location_info'
AND attname IN ('area_code', 'mine_code');
```

## 常见问题

### Q1: 为什么不能使用部分索引？

A: PostgreSQL的ON CONFLICT语法不支持部分索引的WHERE条件。必须使用完整的唯一约束。

### Q2: 如何处理现有的重复数据？

A: 在创建唯一约束之前，必须先清理重复数据。脚本中使用ROW_NUMBER()窗口函数保留最新的记录。

### Q3: UPSERT失败后如何恢复？

A: 代码中已经实现了备用方案，会自动切换到传统的查询-插入/更新方式。

### Q4: 如何验证修复是否成功？

A: 执行测试UPSERT操作，如果没有错误且数据正确插入/更新，说明修复成功。

## 总结

这个问题的根本原因是PostgreSQL数据库中缺少必要的唯一约束。通过创建表级唯一约束并修改相关代码，可以完全解决这个问题。建议在生产环境部署前，先在测试环境验证所有的数据库约束和UPSERT操作。
