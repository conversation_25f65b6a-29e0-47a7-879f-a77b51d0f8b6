# 统计接口日期参数优化总结

## 概述

本文档总结了对所有统计接口的日期参数优化工作，参考EnvironmentalSafetyStatController的模式，将startDate和endDate改为非必填参数，并增加了默认日期范围自动计算功能。

## 修改范围

### ✅ 已完成修改的接口

1. **设备安全统计接口** (`EquipmentSafetyStatController.java`) - ✅ 已完成
2. **环境安全统计接口** (`EnvironmentalSafetyStatController.java`) - ✅ 参考模板
3. **微震统计接口** (`MicroseismicStatController.java`) - ✅ 已完成
4. **地表监测统计接口** (`SurfaceMonitoringStatController.java`) - ✅ 已完成
5. **充填漏浆检测统计接口** (`FillingLeakageStatController.java`) - ✅ 已完成
6. **车辆安全统计接口** (`VehicleSafetyStatController.java`) - ✅ 已完成

## 修改内容

### 1. 导入语句添加

所有接口都添加了必要的导入：
```java
import com.ruoyi.lxbi.utils.DateRangeCalculator;
import java.util.Map;
```

### 2. 参数修改

**修改前**:
```java
@RequestParam String startDate,
@RequestParam String endDate
```

**修改后**:
```java
@RequestParam(required = false) String startDate,
@RequestParam(required = false) String endDate
```

### 3. 方法体增强

每个方法都添加了统一的验证和日期计算逻辑：

```java
// 验证视图类型参数
if (!DateRangeCalculator.isValidViewType(viewType)) {
    return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
}

// 设置默认日期范围或根据viewType计算日期范围
if (startDate == null || endDate == null) {
    Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
    startDate = calculatedDates.get("startDate");
    endDate = calculatedDates.get("endDate");
}
```

## 接口列表

### 设备安全统计接口
- `/lxbi/stat/equipment-safety/overview` - 概览统计
- `/lxbi/stat/equipment-safety/status-distribution` - 状态分布
- `/lxbi/stat/equipment-safety/alarm-trend` - 报警趋势
- `/lxbi/stat/equipment-safety/fault-records` - 故障记录
- `/lxbi/stat/equipment-safety/system-distribution` - 系统分布
- `/lxbi/stat/equipment-safety/monitoring-frequency` - 监测次数

### 环境安全统计接口
- `/lxbi/stat/environmental-safety/overview` - 概览统计
- `/lxbi/stat/environmental-safety/location-distribution` - 位置分布
- `/lxbi/stat/environmental-safety/data-trend` - 数据趋势

### 微震统计接口
- `/lxbi/stat/microseismic/overview` - 概览统计
- `/lxbi/stat/microseismic/completion-rate` - P波完成率
- `/lxbi/stat/microseismic/event-distribution` - 事件分布
- `/lxbi/stat/microseismic/scatter-data` - 散点图数据

### 地表监测统计接口
- `/lxbi/stat/surface-monitoring/overview` - 概览统计
- `/lxbi/stat/surface-monitoring/level-distribution` - 等级分布
- `/lxbi/stat/surface-monitoring/seven-day-trend` - 七日趋势

### 充填漏浆检测统计接口
- `/lxbi/stat/filling-leakage/overview` - 概览统计
- `/lxbi/stat/filling-leakage/alarm-trend` - 报警趋势
- `/lxbi/stat/filling-leakage/location-distribution` - 位置分布

### 车辆安全统计接口
- `/lxbi/stat/vehicle-safety/overview` - 概览统计
- `/lxbi/stat/vehicle-safety/alarm-department-distribution` - 部门分布
- `/lxbi/stat/vehicle-safety/alarm-type-distribution` - 类型分布
- `/lxbi/stat/vehicle-safety/alarm-records` - 告警记录

## 使用示例

### 1. 使用默认日期（推荐）

```bash
# 使用daily默认日期范围
curl -X GET "http://localhost:8080/lxbi/stat/equipment-safety/overview"

# 指定viewType，使用对应的默认日期范围
curl -X GET "http://localhost:8080/lxbi/stat/equipment-safety/overview?viewType=weekly"
curl -X GET "http://localhost:8080/lxbi/stat/equipment-safety/overview?viewType=monthly"
```

### 2. 手动指定日期

```bash
# 手动指定完整日期范围
curl -X GET "http://localhost:8080/lxbi/stat/equipment-safety/overview?viewType=daily&startDate=2025-08-20&endDate=2025-08-25"
```

### 3. 结合其他参数

```bash
# 设备安全 - 按区域筛选
curl -X GET "http://localhost:8080/lxbi/stat/equipment-safety/system-distribution?viewType=weekly&areaType=采场"

# 车辆安全 - 部门分布
curl -X GET "http://localhost:8080/lxbi/stat/vehicle-safety/alarm-department-distribution?viewType=monthly"
```

## 自动日期计算规则

### Daily（日统计）
- 默认：当天00:00:00 到 23:59:59
- 示例：2025-08-25 到 2025-08-25

### Weekly（周统计）
- 默认：本周一00:00:00 到 本周日23:59:59
- 示例：2025-08-25 到 2025-08-31

### Monthly（月统计）
- 默认：本月1号00:00:00 到 本月最后一天23:59:59
- 示例：2025-08-01 到 2025-08-31

## 参数验证

### 1. viewType验证
- 有效值：`daily`, `weekly`, `monthly`
- 无效时返回：`"无效的视图类型，支持的类型: daily, weekly, monthly"`

### 2. 日期参数处理
- 当startDate或endDate为null时，自动计算默认日期范围
- 当两个参数都提供时，使用用户指定的日期范围
- 支持部分指定（只提供startDate或endDate）

## 优势

### 1. 用户体验提升
- **简化调用**：不需要强制提供日期参数
- **智能默认**：根据视图类型自动选择合适的日期范围
- **灵活性**：仍然支持手动指定日期

### 2. 开发效率提升
- **统一标准**：所有接口使用相同的参数处理逻辑
- **减少错误**：自动日期计算减少了日期格式错误
- **易于维护**：统一的验证和处理逻辑

### 3. 系统稳定性
- **参数验证**：统一的参数验证机制
- **错误处理**：清晰的错误信息返回
- **容错性**：支持多种参数组合

## 兼容性

### 向后兼容
- 原有的完整参数调用方式仍然支持
- 新增的可选参数不影响现有调用
- 返回数据格式保持不变

### 前端适配建议
```javascript
// 推荐的前端调用方式
const fetchData = async (viewType = 'daily', customDates = null) => {
  let url = `/lxbi/stat/equipment-safety/overview?viewType=${viewType}`;
  
  // 只有在需要自定义日期时才添加日期参数
  if (customDates) {
    url += `&startDate=${customDates.startDate}&endDate=${customDates.endDate}`;
  }
  
  const response = await fetch(url);
  return response.json();
};
```

## 总结

通过这次优化，所有统计接口都具备了以下特性：

1. **日期参数非必填**：提升了接口的易用性
2. **智能默认计算**：根据viewType自动计算合适的日期范围
3. **统一的参数验证**：保证了接口的稳定性和一致性
4. **向后兼容**：不影响现有的调用方式
5. **灵活配置**：支持多种参数组合

这些改进使得统计接口更加用户友好，同时保持了高度的灵活性和可靠性，为前端开发和API调用提供了更好的体验。
