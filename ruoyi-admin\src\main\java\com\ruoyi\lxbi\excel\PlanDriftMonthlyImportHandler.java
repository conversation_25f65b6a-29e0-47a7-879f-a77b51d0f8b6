package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.domain.BaseWorkingFace;
import com.ruoyi.lxbi.domain.PlanDriftMonthly;
import com.ruoyi.lxbi.domain.excel.PlanDriftMonthlyImport;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;
import com.ruoyi.lxbi.service.IBaseStopeService;
import com.ruoyi.lxbi.service.IBaseWorkingFaceService;
import com.ruoyi.lxbi.service.IPlanDriftMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 掘进月计划导入处理器
 *
 * <AUTHOR>
 */
@Component
public class PlanDriftMonthlyImportHandler extends ExcelImportHandler<PlanDriftMonthlyImport> {

    @Autowired
    private IBaseProjectDepartmentService baseProjectDepartmentService;

    @Autowired
    private IBaseWorkingFaceService baseWorkingFaceService;

    @Autowired
    private IBaseStopeService baseStopeService;

    @Autowired
    private IPlanDriftMonthlyService planDriftMonthlyService;

    @Override
    protected Class<PlanDriftMonthlyImport> getEntityClass() {
        return PlanDriftMonthlyImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置项目部门选项
        List<ExcelOptionInfo> projectDepartments = new ArrayList<>();
        BaseProjectDepartment deptQueryParam = new BaseProjectDepartment();
        List<BaseProjectDepartment> deptList = baseProjectDepartmentService.selectBaseProjectDepartmentList(deptQueryParam);

        for (BaseProjectDepartment dept : deptList) {
            projectDepartments.add(new ExcelOptionInfo(
                    dept.getProjectDepartmentId(),
                    dept.getProjectDepartmentName()
            ));
        }
        context.setOptions("projectDepartment", projectDepartments);

        // 设置工作面选项
        List<ExcelOptionInfo> workingFaces = new ArrayList<>();
        BaseWorkingFace faceQueryParam = new BaseWorkingFace();
        List<BaseWorkingFace> faceList = baseWorkingFaceService.selectBaseWorkingFaceList(faceQueryParam);

        for (BaseWorkingFace face : faceList) {
            workingFaces.add(new ExcelOptionInfo(
                    face.getWorkingFaceId(),
                    face.getWorkingFaceName()
            ));
        }
        context.setOptions("workingFace", workingFaces);

        // 设置采场选项
        List<ExcelOptionInfo> stopes = new ArrayList<>();
        BaseStope stopeQueryParam = new BaseStope();
        List<BaseStope> stopeList = baseStopeService.selectBaseStopeListAll(stopeQueryParam);

        for (BaseStope stope : stopeList) {
            stopes.add(new ExcelOptionInfo(
                    stope.getStopeId(),
                    stope.getStopeName()
            ));
        }
        context.setOptions("stope", stopes);

        // 设置是否重点选项（字典数据会自动加载）
        List<ExcelOptionInfo> priorityOptions = new ArrayList<>();
        priorityOptions.add(new ExcelOptionInfo(0, "否"));
        priorityOptions.add(new ExcelOptionInfo(1, "是"));
        context.setOptions("priority", priorityOptions);
    }

    @Override
    protected void validateData(ExcelDataInfo<PlanDriftMonthlyImport> dataInfo, ExcelImportContext context) {
        PlanDriftMonthlyImport data = dataInfo.getData();

        // 验证计划月份格式
        if (data.getPlanDate() != null) {
            Pattern pattern = Pattern.compile("^\\d{4}\\d{2}$");
            if (!pattern.matcher(data.getPlanDate()).matches()) {
                dataInfo.addError("planDate", "计划月份格式不正确，应为yyyyMM格式");
            }
        }

        // 验证掘进米数
        if (data.getDriftMeter() != null && data.getDriftMeter().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("driftMeter", "掘进米数不能为负数");
        }

        // 验证掘进方量
        if (data.getDriftVolume() != null && data.getDriftVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("driftVolume", "掘进方量不能为负数");
        }
    }

    @Override
    protected void saveData(PlanDriftMonthlyImport data, ExcelImportContext context) {
        // 转换为实体对象
        PlanDriftMonthly entity = new PlanDriftMonthly();
        entity.setProjectDepartmentId(data.getProjectDepartmentId());
        entity.setWorkingFaceId(data.getWorkingFaceId());
        entity.setStopeId(data.getStopeId());
        entity.setDriftMeter(data.getDriftMeter());
        entity.setDriftVolume(data.getDriftVolume());
        entity.setPlanDate(data.getPlanDate());
        
        // 保存到数据库
        planDriftMonthlyService.insertPlanDriftMonthly(entity);
    }

    @Override
    public List<PlanDriftMonthlyImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("掘进月计划验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("掘进月计划导入完成，总行数: " + ctx.getTotalRows());
    }
}
