package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.lxbi.domain.BaseEquipment;
import com.ruoyi.lxbi.domain.DataEquipment;
import com.ruoyi.lxbi.domain.excel.DataEquipmentImport;
import com.ruoyi.lxbi.service.IBaseEquipmentService;
import com.ruoyi.lxbi.service.IDataEquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 设备数据Excel导入处理器
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Component
public class DataEquipmentImportHandler extends ExcelImportHandler<DataEquipmentImport> {

    @Autowired
    private IDataEquipmentService dataEquipmentService;

    @Autowired
    private IBaseEquipmentService baseEquipmentService;

    @Override
    protected Class<DataEquipmentImport> getEntityClass() {
        return DataEquipmentImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置设备类型选项
        List<ExcelOptionInfo> equipmentTypes = new ArrayList<>();
        List<BaseEquipment> equipmentList = baseEquipmentService.selectBaseEquipmentListAll(new BaseEquipment());

        for (BaseEquipment equipment : equipmentList) {
            equipmentTypes.add(new ExcelOptionInfo(
                    equipment.getId(),
                    equipment.getEquipmentNo()
            ));
        }
        context.setOptions("baseEquipment", equipmentTypes);
    }

    @Override
    protected void validateData(ExcelDataInfo<DataEquipmentImport> dataInfo, ExcelImportContext context) {
        DataEquipmentImport data = dataInfo.getData();

        // 验证运行日期不能为未来日期
        if (data.getOperationDate() != null && data.getOperationDate().after(new Date())) {
            dataInfo.addError("operationDate", "运行日期不能是未来日期");
        }

        // 验证设备编号
        if (StringUtils.isEmpty(data.getEquipmentNo())) {
            dataInfo.addError("equipmentNo", "设备编号不能为空");
        }

        // 验证编组数量
        if (data.getMineCarsNumber() != null && data.getMineCarsNumber() < 0) {
            dataInfo.addError("mineCarsNumber", "编组数量不能为负数");
        }

        // 验证运行列次
        if (data.getNumberOfRunningTrains() != null && data.getNumberOfRunningTrains().compareTo(java.math.BigDecimal.ZERO) < 0) {
            dataInfo.addError("numberOfRunningTrains", "运行列次不能为负数");
        }

        // 验证运行时长
        if (data.getOperationTime() != null) {
            if (data.getOperationTime() < 0 || data.getOperationTime() > 1440) {
                dataInfo.addError("operationTime", "运行时长必须在0-1440分钟之间");
            }
        }

        // 验证单次处理量
        if (data.getSingleProcessingVolume() != null && data.getSingleProcessingVolume().compareTo(java.math.BigDecimal.ZERO) < 0) {
            dataInfo.addError("singleProcessingVolume", "单次处理量不能为负数");
        }

        // 验证总处理量
        if (data.getTotalProcessingVolume() != null && data.getTotalProcessingVolume().compareTo(java.math.BigDecimal.ZERO) < 0) {
            dataInfo.addError("totalProcessingVolume", "总处理量不能为负数");
        }

        // 验证时间范围
        if (data.getStartTime() != null && data.getEndTime() != null) {
            if (data.getStartTime().isAfter(data.getEndTime())) {
                dataInfo.addError("endTime", "运行结束时间不能早于开始时间");
            }
        }
    }

    @Override
    protected void saveData(DataEquipmentImport data, ExcelImportContext context) {
        // 转换为实体对象
        DataEquipment entity = new DataEquipment();
        entity.setOperationDate(data.getOperationDate());
        entity.setEquipmentNo(data.getEquipmentNo());
        entity.setMineCarsNumber(data.getMineCarsNumber());
        entity.setNumberOfRunningTrains(data.getNumberOfRunningTrains());
        entity.setOperationTime(data.getOperationTime());
        entity.setSingleProcessingVolume(data.getSingleProcessingVolume());
        entity.setTotalProcessingVolume(data.getTotalProcessingVolume());
        entity.setStartTime(data.getStartTime());
        entity.setEndTime(data.getEndTime());
        entity.setEquipmentType(data.getEquipmentType());

        // 保存到数据库
        dataEquipmentService.insertDataEquipment(entity);
    }

    @Override
    public List<DataEquipmentImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("设备数据验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("设备数据导入完成，总行数: " + ctx.getTotalRows());
    }
}
