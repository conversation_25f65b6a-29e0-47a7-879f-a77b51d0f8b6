package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 充填漏浆检测概览VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FillingLeakageOverviewVO {
    
    /**
     * 报警事件总数
     */
    private Long totalAlarmCount;
    
    /**
     * 区域入侵数量
     */
    private Long areaIntrusionCount;
    
    /**
     * 温差报警数量
     */
    private Long temperatureDifferenceAlarmCount;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 统计周期
     */
    private String period;
}
