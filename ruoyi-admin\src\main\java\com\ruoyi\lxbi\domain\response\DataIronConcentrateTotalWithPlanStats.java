package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 铁精粉生产数据总体统计对象（含计划量） - 统一日/周/月/年统计
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
public class DataIronConcentrateTotalWithPlanStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 周开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekStartDate;

    /**
     * 周结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekEndDate;

    /**
     * 记录数量
     */
    @Excel(name = "记录数量")
    private Integer recordCount;

    /**
     * 总生产量（吨）
     */
    @Excel(name = "总生产量")
    private Double totalProductionVolume;

    /**
     * 平均TFe含量（%）
     */
    @Excel(name = "平均TFe含量")
    private Double avgTfeContent;

    /**
     * 平均-500目细度（%）
     */
    @Excel(name = "平均-500目细度")
    private Double avgFinenessMinus500;

    /**
     * 平均水分含量（%）
     */
    @Excel(name = "平均水分含量")
    private Double avgMoistureContent;

    /**
     * 计划生产量（吨）
     */
    @Excel(name = "计划生产量")
    private Double planProductionVolume;
}
