package com.ruoyi.lxbi.controller.stat.sec;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.admin.service.IEnvironmentalSafetyStatService;
import com.ruoyi.lxbi.domain.vo.EnvironmentalSafetyDataTrendVO;
import com.ruoyi.lxbi.domain.vo.EnvironmentalSafetyLocationDistributionVO;
import com.ruoyi.lxbi.domain.vo.EnvironmentalSafetyOverviewVO;
import com.ruoyi.lxbi.utils.DateRangeCalculator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 环境安全统计Controller
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@RestController
@RequestMapping("/sec/stat/environmental-safety")
public class EnvironmentalSafetyStatController {

    @Autowired
    private IEnvironmentalSafetyStatService environmentalSafetyStatService;

    /**
     * 获取环境安全概览统计
     */
    @Anonymous
    @GetMapping("/overview")
    public R<EnvironmentalSafetyOverviewVO> getOverviewStatistics(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        EnvironmentalSafetyOverviewVO overview = environmentalSafetyStatService.getOverviewStatistics(viewType, startDate, endDate);
        return R.ok(overview);
    }

    /**
     * 获取环境安全报警位置分布统计 (雷达图数据)
     */
    @Anonymous
    @GetMapping("/location-distribution")
    public R<List<EnvironmentalSafetyLocationDistributionVO>> getLocationDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<EnvironmentalSafetyLocationDistributionVO> distribution = environmentalSafetyStatService.getLocationDistribution(viewType, startDate, endDate);
        return R.ok(distribution);
    }

    /**
     * 获取环境安全报警数量趋势统计
     * 如果只有一天按照小时区分，超过一天的范围按照日区分
     */
    @Anonymous
    @GetMapping("/data-trend")
    public R<List<EnvironmentalSafetyDataTrendVO>> getDataTrend(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        // 根据日期范围自动选择统计粒度
        String actualViewType = viewType;
        if ("daily".equals(viewType)) {
            // 如果是按日查询，判断日期范围
            if (DateRangeCalculator.isSingleDay(startDate, endDate)) {
                // 只有一天，按小时统计
                actualViewType = "hourly";
            } else {
                // 超过一天，按日统计
                actualViewType = "daily";
            }
        }

        List<EnvironmentalSafetyDataTrendVO> trend = environmentalSafetyStatService.getDataTrend(actualViewType, startDate, endDate);
        return R.ok(trend);
    }
}
