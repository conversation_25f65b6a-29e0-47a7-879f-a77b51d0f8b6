package com.ruoyi.lxbi.admin.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 隐患数据记录对象 api_hidden_trouble_record
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApiHiddenTroubleRecord extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 状态(0:待整改 1:已驳回 2:已整改 3:待复查 4:已超期) */
    private Long status;

    /** 责任人 */
    @Excel(name = "责任人")
    private String responsiblePerson;

    /** 隐患地点 */
    @Excel(name = "隐患地点")
    private String troubleLocation;

    /** 责任部门 */
    @Excel(name = "责任部门")
    private String responsibleDepartment;

    /** 隐患等级(0:一般隐患 1:重大隐患) */
    private String troubleGrade;

    /** 隐患类别 */
    @Excel(name = "隐患类别")
    private String troubleCategory;

    /** 隐患日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "隐患日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date troubleDate;

    /** 检查人 */
    @Excel(name = "检查人")
    private String inspector;

    /** 复查人 */
    @Excel(name = "复查人")
    private String reviewer;

    /** 通知编号 */
    private String noticeNumber;

    /** 原始数据(JSON格式) */
    @Excel(name = "原始数据(JSON格式)")
    private String originalData;

    /** 隐患标题 */
    @Excel(name = "隐患标题")
    private String troubleTitle;

    /** 隐患描述 */
    @Excel(name = "隐患描述")
    private String troubleDescription;

    /** 整改期限 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "整改期限", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rectificationDeadline;

    /** 整改完成日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "整改完成日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rectificationCompletionDate;

    /** 复查日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "复查日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reviewDate;

}
