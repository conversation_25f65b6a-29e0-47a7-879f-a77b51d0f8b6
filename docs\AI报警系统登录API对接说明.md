# AI报警系统登录API对接说明

## 概述

本文档说明了AI报警系统登录API的对接实现，基于实际的curl调用示例和API响应格式进行了代码调整。

## 实际API调用信息

### 1. 登录接口

**请求信息**:
```bash
curl 'http://***********:3001/api/login/' \
  -X POST \
  -H 'Content-Type: application/json;charset=utf-8' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/******** Firefox/142.0' \
  -H 'Accept: application/json, text/plain, */*' \
  --data-raw 'account=api&password=123456'
```

**响应格式**:
```json
{
  "msg": "ok",
  "code": "000000", 
  "data": {
    "accessToken": "79eb92c715098e216a4d982444c8582c",
    "userDataDict": {
      "id": 21,
      "name": "api",
      "account": "api", 
      "password": "123456",
      "phone": null,
      "e_mail": null,
      "face_img": "",
      "role_id": "25",
      "status": 0,
      "creation_time": "2025-08-17 19:56:32",
      "revision_time": "2025-08-17 19:56:32",
      "role_name": "API接口",
      "status_name": "启用"
    }
  }
}
```

## 代码修改内容

### 1. AiAlarmExternalService 更新

**请求格式调整**:
```java
// 原来的JSON格式
Map<String, Object> loginRequest = new HashMap<>();
loginRequest.put("account", account);
loginRequest.put("password", password);

// 修改为字符串格式（匹配实际API）
String requestBody = "account=" + account + "&password=" + password;
```

**请求头调整**:
```java
HttpHeaders headers = new HttpHeaders();
headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
headers.add("User-Agent", "LXBI-System/1.0");
headers.add("Accept", "application/json, text/plain, */*");
headers.add("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
```

**响应解析调整**:
```java
// 检查成功条件：code="000000" 且 msg="ok"
if (responseBody != null && "000000".equals(responseBody.get("code")) && "ok".equals(responseBody.get("msg"))) {
    Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
    if (data != null) {
        String token = (String) data.get("accessToken");
        if (token != null) {
            this.accessToken = token;
            log.info("AI报警系统登录成功，accessToken已保存: {}", token);
            return true;
        }
    }
}
```

### 2. AiLoginResponseVO 更新

**完整的响应结构**:
```java
@Data
public class AiLoginResponseVO {
    private String msg;           // 响应消息
    private String code;          // 响应代码
    private AiLoginDataVO data;   // 响应数据
    
    @Data
    public static class AiLoginDataVO {
        private String accessToken;              // 访问令牌
        private UserDataDict userDataDict;       // 用户数据
        
        @Data
        public static class UserDataDict {
            private Integer id;
            private String name;
            private String account;
            private String password;
            private String phone;
            private String e_mail;
            private String face_img;
            private String role_id;
            private Integer status;
            private String creation_time;
            private String revision_time;
            private String role_name;
            private String status_name;
        }
    }
}
```

### 3. 配置文件

**application.yml**:
```yaml
external:
  api:
    ai-alarm:
      base-url: http://***********:3001
      login-path: /api/login
      alarm-path: /alarm
      account: api
      password: 123456
      connect-timeout: 30000
      read-timeout: 60000
      enabled: true
```

## 登录流程

### 1. 完整登录过程

```java
public boolean login() {
    try {
        String loginUrl = baseUrl + loginPath;
        
        // 1. 构建请求参数（字符串格式）
        String requestBody = "account=" + account + "&password=" + password;
        
        // 2. 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("User-Agent", "LXBI-System/1.0");
        
        // 3. 发送登录请求
        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<Map> response = restTemplate.exchange(
            loginUrl, HttpMethod.POST, entity, Map.class);
        
        // 4. 解析响应
        Map<String, Object> responseBody = response.getBody();
        
        // 5. 检查登录结果
        if ("000000".equals(responseBody.get("code")) && "ok".equals(responseBody.get("msg"))) {
            Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
            String token = (String) data.get("accessToken");
            this.accessToken = token;
            return true;
        }
        
        return false;
    } catch (Exception e) {
        log.error("登录失败", e);
        return false;
    }
}
```

### 2. Token使用

登录成功后，后续API调用会自动携带token：

```java
public void callApiWithToken() {
    HttpHeaders headers = new HttpHeaders();
    headers.add("Authorization", "Bearer " + accessToken);
    // 或者根据实际API要求使用其他格式
    headers.add("accessToken", accessToken);
}
```

## 测试验证

### 1. 单元测试

```java
@Test
public void testLogin() {
    AiAlarmExternalService service = new AiAlarmExternalService();
    boolean result = service.login();
    
    assertTrue(result);
    assertNotNull(service.getAccessToken());
    assertEquals("79eb92c715098e216a4d982444c8582c", service.getAccessToken());
}
```

### 2. 集成测试

```bash
# 测试登录接口
curl -X POST "http://localhost:8080/lxbi/ai-alarm/test-login" \
  -H "Content-Type: application/json"

# 预期响应
{
  "success": true,
  "message": "登录成功",
  "accessToken": "79eb92c715098e216a4d982444c8582c"
}
```

### 3. 日志验证

```
2025-08-25 10:30:00 INFO  - 开始登录AI报警系统: http://***********:3001/api/login
2025-08-25 10:30:00 INFO  - 登录请求参数: account=api
2025-08-25 10:30:01 INFO  - 登录响应: {msg=ok, code=000000, data={accessToken=79eb92c715098e216a4d982444c8582c, ...}}
2025-08-25 10:30:01 INFO  - AI报警系统登录成功，accessToken已保存: 79eb92c715098e216a4d982444c8582c
```

## 错误处理

### 1. 常见错误码

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 000000 | 成功 | 正常处理 |
| 000001 | 账号或密码错误 | 检查配置 |
| 000002 | 账号被锁定 | 联系管理员 |
| 000003 | 系统异常 | 重试或联系技术支持 |

### 2. 异常处理

```java
try {
    boolean loginResult = aiAlarmExternalService.login();
    if (!loginResult) {
        log.error("AI报警系统登录失败");
        // 处理登录失败逻辑
    }
} catch (Exception e) {
    log.error("登录过程中发生异常", e);
    // 处理异常情况
}
```

## 安全建议

### 1. 密码安全

```yaml
# 建议使用加密配置
external:
  api:
    ai-alarm:
      account: api
      password: ${AI_ALARM_PASSWORD:123456}  # 使用环境变量
```

### 2. Token管理

```java
// 建议添加token过期检查
public boolean isTokenValid() {
    return accessToken != null && !accessToken.isEmpty() && !isTokenExpired();
}

// 自动刷新token
public void refreshTokenIfNeeded() {
    if (!isTokenValid()) {
        login();
    }
}
```

### 3. 请求重试

```java
@Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
public boolean loginWithRetry() {
    return login();
}
```

## 总结

通过对接实际的AI报警系统API，我们完成了以下调整：

1. **请求格式**：从JSON格式改为字符串格式 `account=api&password=123456`
2. **响应解析**：匹配实际的响应结构 `{msg, code, data}`
3. **成功判断**：使用 `code="000000"` 和 `msg="ok"` 作为成功标识
4. **Token提取**：从 `data.accessToken` 字段获取认证令牌
5. **VO结构**：完善了响应VO类，包含完整的用户信息

这确保了系统能够正确地与AI报警系统进行认证和数据交互。
