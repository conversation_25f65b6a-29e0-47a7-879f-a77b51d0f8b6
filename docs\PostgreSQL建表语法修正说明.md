# PostgreSQL建表语法修正说明

## 问题说明

在之前的建表脚本中，我错误地使用了MySQL的COMMENT语法，这在PostgreSQL中是不支持的。PostgreSQL有自己的注释语法。

## MySQL vs PostgreSQL 语法差异

### 1. 字段注释语法

**MySQL语法（错误）**:
```sql
CREATE TABLE test (
    id BIGINT PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(100) COMMENT '名称'
);
```

**PostgreSQL语法（正确）**:
```sql
CREATE TABLE test (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100)
);

-- 添加注释
COMMENT ON TABLE test IS '测试表';
COMMENT ON COLUMN test.id IS '主键ID';
COMMENT ON COLUMN test.name IS '名称';
```

### 2. 自增主键语法

**MySQL语法**:
```sql
id BIGINT AUTO_INCREMENT PRIMARY KEY
```

**PostgreSQL语法**:
```sql
id BIGSERIAL PRIMARY KEY
-- 或者
id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY
```

### 3. 布尔类型

**MySQL语法**:
```sql
is_deleted TINYINT(1) DEFAULT 0
```

**PostgreSQL语法**:
```sql
is_deleted BOOLEAN DEFAULT FALSE
```

### 4. JSON类型

**MySQL语法**:
```sql
data JSON
```

**PostgreSQL语法**:
```sql
data JSONB  -- 推荐使用JSONB，性能更好
-- 或者
data JSON
```

## 修正后的PostgreSQL建表脚本

### 1. 完整的建表语句

```sql
-- 创建表
CREATE TABLE api_ai_alarm (
    id BIGSERIAL PRIMARY KEY,
    external_id VARCHAR(100),
    alarm_type VARCHAR(50),
    alarm_level VARCHAR(20),
    alarm_status VARCHAR(20),
    alarm_title VARCHAR(200),
    alarm_description TEXT,
    alarm_time TIMESTAMP,
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    location_name VARCHAR(100),
    location_code VARCHAR(50),
    coordinate_x DECIMAL(10,6),
    coordinate_y DECIMAL(10,6),
    coordinate_z DECIMAL(10,6),
    device_id VARCHAR(100),
    device_name VARCHAR(100),
    device_type VARCHAR(50),
    alarm_value DECIMAL(10,4),
    threshold_value DECIMAL(10,4),
    unit VARCHAR(20),
    handler_name VARCHAR(100),
    handle_time TIMESTAMP,
    handle_status VARCHAR(20),
    handle_remark TEXT,
    image_url TEXT,
    video_url TEXT,
    extra_data JSONB,
    raw_data JSONB,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status VARCHAR(20) DEFAULT 'SUCCESS',
    sync_source VARCHAR(50) DEFAULT 'AUTO',
    data_version VARCHAR(20),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 添加注释

```sql
-- 表注释
COMMENT ON TABLE api_ai_alarm IS 'AI报警数据表，存储从第三方AI报警系统同步的数据';

-- 字段注释
COMMENT ON COLUMN api_ai_alarm.id IS '主键ID';
COMMENT ON COLUMN api_ai_alarm.external_id IS '第三方系统的原始ID';
COMMENT ON COLUMN api_ai_alarm.alarm_type IS '报警类型';
COMMENT ON COLUMN api_ai_alarm.alarm_level IS '报警等级';
COMMENT ON COLUMN api_ai_alarm.alarm_status IS '报警状态';
COMMENT ON COLUMN api_ai_alarm.raw_data IS '原始数据(完整的API响应)';
COMMENT ON COLUMN api_ai_alarm.sync_time IS '数据同步时间';
COMMENT ON COLUMN api_ai_alarm.sync_source IS '同步来源(AUTO-自动同步/MANUAL-手动同步)';
```

### 3. 创建索引

```sql
-- 普通索引
CREATE INDEX idx_api_ai_alarm_external_id ON api_ai_alarm(external_id);
CREATE INDEX idx_api_ai_alarm_alarm_time ON api_ai_alarm(alarm_time);
CREATE INDEX idx_api_ai_alarm_alarm_type ON api_ai_alarm(alarm_type);

-- 复合索引
CREATE INDEX idx_api_ai_alarm_time_type ON api_ai_alarm(alarm_time, alarm_type);

-- 唯一索引（条件索引）
CREATE UNIQUE INDEX idx_api_ai_alarm_external_id_unique 
ON api_ai_alarm(external_id) 
WHERE is_deleted = FALSE;
```

### 4. 创建触发器（自动更新updated_at）

```sql
-- 创建触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_api_ai_alarm_updated_at 
    BEFORE UPDATE ON api_ai_alarm 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
```

## PostgreSQL特有功能

### 1. JSONB类型优势

```sql
-- JSONB支持索引和高效查询
CREATE INDEX idx_api_ai_alarm_raw_data_gin ON api_ai_alarm USING GIN (raw_data);

-- 查询JSON字段
SELECT * FROM api_ai_alarm 
WHERE raw_data->>'alarm_type' = 'FIRE';

-- 查询嵌套JSON
SELECT * FROM api_ai_alarm 
WHERE raw_data->'device'->>'name' = '烟感器001';
```

### 2. 数组类型

```sql
-- 如果需要存储数组数据
ALTER TABLE api_ai_alarm ADD COLUMN tags TEXT[];

-- 查询数组
SELECT * FROM api_ai_alarm 
WHERE 'urgent' = ANY(tags);
```

### 3. 枚举类型

```sql
-- 创建枚举类型
CREATE TYPE alarm_level_enum AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- 使用枚举类型
ALTER TABLE api_ai_alarm 
ALTER COLUMN alarm_level TYPE alarm_level_enum 
USING alarm_level::alarm_level_enum;
```

## 执行脚本

### 1. 连接PostgreSQL

```bash
# 使用psql连接
psql -h localhost -U postgres -d your_database

# 或者使用环境变量
export PGHOST=localhost
export PGUSER=postgres
export PGDATABASE=your_database
psql
```

### 2. 执行建表脚本

```bash
# 在psql中执行
\i sql/postgres/create_api_ai_alarm_table_corrected.sql

# 或者在命令行执行
psql -h localhost -U postgres -d your_database -f sql/postgres/create_api_ai_alarm_table_corrected.sql
```

### 3. 验证表结构

```sql
-- 查看表结构
\d api_ai_alarm

-- 查看表注释
\d+ api_ai_alarm

-- 查看索引
\di api_ai_alarm*

-- 查看触发器
\dS api_ai_alarm
```

## 常用PostgreSQL命令

### 1. 表操作

```sql
-- 查看所有表
\dt

-- 查看表结构
\d table_name

-- 查看表详细信息（包括注释）
\d+ table_name

-- 查看表大小
SELECT pg_size_pretty(pg_total_relation_size('api_ai_alarm'));
```

### 2. 索引操作

```sql
-- 查看表的所有索引
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'api_ai_alarm';

-- 查看索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'api_ai_alarm';
```

### 3. 性能分析

```sql
-- 分析查询计划
EXPLAIN ANALYZE SELECT * FROM api_ai_alarm WHERE alarm_type = 'FIRE';

-- 查看表统计信息
SELECT * FROM pg_stat_user_tables WHERE relname = 'api_ai_alarm';
```

## 总结

修正后的PostgreSQL建表脚本具有以下特点：

1. **正确的语法**: 使用PostgreSQL标准语法
2. **完整的注释**: 使用COMMENT ON语句添加注释
3. **高效的索引**: 包括普通索引、复合索引和唯一索引
4. **自动触发器**: 自动更新updated_at字段
5. **JSONB支持**: 高效的JSON数据存储和查询
6. **数据完整性**: 唯一约束和默认值设置

这个脚本可以直接在PostgreSQL数据库中执行，创建完整的AI报警数据表。
