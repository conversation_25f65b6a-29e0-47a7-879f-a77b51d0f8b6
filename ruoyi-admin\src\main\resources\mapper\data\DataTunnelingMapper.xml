<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataTunnelingMapper">
    
    <resultMap type="DataTunneling" id="DataTunnelingResult">
        <result property="id"    column="id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="tunnelingLength"    column="tunneling_length"    />
        <result property="tunnelingVolume"    column="tunneling_volume"    />
        <result property="workContent"    column="work_content"    />
        <result property="remarks"    column="remarks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataTunnelingVo">
        select dt.id, dt.operation_date, dt.project_department_id, dt.working_face_id, dt.stope_id, dt.working_period_id,
               dt.tunneling_length, dt.tunneling_volume, dt.work_content, dt.remarks,
               dt.create_by, dt.create_time, dt.update_by, dt.update_time,
               bpd.project_department_name,
               bs.stope_name,
               bwf.working_face_name,
               bwp.working_period_name
        from data_tunneling dt
        left join base_project_department bpd on dt.project_department_id = bpd.project_department_id
        left join base_stope bs on dt.stope_id = bs.stope_id
        left join base_working_face bwf on dt.working_face_id = bwf.working_face_id
        left join base_working_period bwp on dt.working_period_id = bwp.working_period_id
    </sql>

    <select id="selectDataTunnelingList" parameterType="DataTunneling" resultMap="DataTunnelingVoResult">
        <include refid="selectDataTunnelingVo"/>
        <where>
            <if test="operationDate != null "> and dt.operation_date = #{operationDate}</if>
            <if test="projectDepartmentId != null "> and dt.project_department_id = #{projectDepartmentId}</if>
            <if test="workingFaceId != null "> and dt.working_face_id = #{workingFaceId}</if>
            <if test="stopeId != null "> and dt.stope_id = #{stopeId}</if>
            <if test="workingPeriodId != null "> and dt.working_period_id = #{workingPeriodId}</if>
            <if test="tunnelingLength != null "> and dt.tunneling_length = #{tunnelingLength}</if>
            <if test="tunnelingVolume != null "> and dt.tunneling_volume = #{tunnelingVolume}</if>
            <if test="workContent != null  and workContent != ''"> and dt.work_content = #{workContent}</if>
            <if test="remarks != null  and remarks != ''"> and dt.remarks = #{remarks}</if>
        </where>
    </select>
    
    <select id="selectDataTunnelingById" parameterType="Long" resultMap="DataTunnelingResult">
        <include refid="selectDataTunnelingVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataTunneling" parameterType="DataTunneling" useGeneratedKeys="true" keyProperty="id">
        insert into data_tunneling
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">operation_date,</if>
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="workingFaceId != null">working_face_id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="workingPeriodId != null">working_period_id,</if>
            <if test="tunnelingLength != null">tunneling_length,</if>
            <if test="tunnelingVolume != null">tunneling_volume,</if>
            <if test="workContent != null">work_content,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">#{operationDate},</if>
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="workingFaceId != null">#{workingFaceId},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="workingPeriodId != null">#{workingPeriodId},</if>
            <if test="tunnelingLength != null">#{tunnelingLength},</if>
            <if test="tunnelingVolume != null">#{tunnelingVolume},</if>
            <if test="workContent != null">#{workContent},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataTunneling" parameterType="DataTunneling">
        update data_tunneling
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="workingFaceId != null">working_face_id = #{workingFaceId},</if>
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="workingPeriodId != null">working_period_id = #{workingPeriodId},</if>
            <if test="tunnelingLength != null">tunneling_length = #{tunnelingLength},</if>
            <if test="tunnelingVolume != null">tunneling_volume = #{tunnelingVolume},</if>
            <if test="workContent != null">work_content = #{workContent},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataTunnelingById" parameterType="Long">
        delete from data_tunneling where id = #{id}
    </delete>

    <delete id="deleteDataTunnelingByIds" parameterType="String">
        delete from data_tunneling where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据作业日期和项目部门查询掘进数据列表 -->
    <resultMap type="DataTunnelingVo" id="DataTunnelingVoResult">
        <result property="id"    column="id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="tunnelingLength"    column="tunneling_length"    />
        <result property="tunnelingVolume"    column="tunneling_volume"    />
        <result property="workContent"    column="work_content"    />
        <result property="remarks"    column="remarks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="projectDepartmentName"    column="project_department_name"    />
        <result property="stopeName"    column="stope_name"    />
        <result property="workingFaceName"    column="working_face_name"    />
        <result property="workingPeriodName"    column="working_period_name"    />
    </resultMap>

    <select id="selectDataTunnelingByOperationDateAndProject" resultMap="DataTunnelingVoResult">
        SELECT
            dt.id, dt.operation_date, dt.project_department_id, dt.working_face_id, dt.stope_id, dt.working_period_id,
            dt.tunneling_length, dt.tunneling_volume, dt.work_content, dt.remarks,
            dt.create_by, dt.create_time, dt.update_by, dt.update_time,
            bpd.project_department_name,
            bs.stope_name,
            bwf.working_face_name,
            bwp.working_period_name
        FROM data_tunneling dt
        LEFT JOIN base_project_department bpd ON dt.project_department_id = bpd.project_department_id
        LEFT JOIN base_stope bs ON dt.stope_id = bs.stope_id
        LEFT JOIN base_working_face bwf ON dt.working_face_id = bwf.working_face_id
        LEFT JOIN base_working_period bwp ON dt.working_period_id = bwp.working_period_id
        WHERE dt.operation_date = #{operationDate}
        AND dt.project_department_id = #{projectDepartmentId}
        ORDER BY dt.working_period_id, dt.stope_id, dt.working_face_id
    </select>

    <!-- 批量新增掘进数据 -->
    <insert id="batchInsertDataTunneling" parameterType="java.util.List">
        INSERT INTO data_tunneling (
            operation_date, project_department_id, working_face_id, stope_id, working_period_id,
            tunneling_length, tunneling_volume, work_content, remarks,
            create_by, create_time, update_by, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.operationDate}, #{item.projectDepartmentId}, #{item.workingFaceId}, #{item.stopeId}, #{item.workingPeriodId},
                #{item.tunnelingLength}, #{item.tunnelingVolume}, #{item.workContent}, #{item.remarks},
                #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}
            )
        </foreach>
    </insert>
</mapper>