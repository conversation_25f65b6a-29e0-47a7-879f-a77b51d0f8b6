package com.ruoyi.lxbi.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.lxbi.mapper.BaseProjectDepartmentMapper;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;

/**
 * 项目部门配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Service
public class BaseProjectDepartmentServiceImpl implements IBaseProjectDepartmentService 
{
    @Autowired
    private BaseProjectDepartmentMapper baseProjectDepartmentMapper;

    /**
     * 查询项目部门配置
     * 
     * @param projectDepartmentId 项目部门配置主键
     * @return 项目部门配置
     */
    @Override
    public BaseProjectDepartment selectBaseProjectDepartmentByProjectDepartmentId(Long projectDepartmentId)
    {
        return baseProjectDepartmentMapper.selectBaseProjectDepartmentByProjectDepartmentId(projectDepartmentId);
    }

    /**
     * 查询项目部门配置列表
     * 
     * @param baseProjectDepartment 项目部门配置
     * @return 项目部门配置
     */
    @Override
    public List<BaseProjectDepartment> selectBaseProjectDepartmentList(BaseProjectDepartment baseProjectDepartment)
    {
        return baseProjectDepartmentMapper.selectBaseProjectDepartmentList(baseProjectDepartment);
    }

    /**
     * 新增项目部门配置
     * 
     * @param baseProjectDepartment 项目部门配置
     * @return 结果
     */
    @Override
    public int insertBaseProjectDepartment(BaseProjectDepartment baseProjectDepartment)
    {
        baseProjectDepartment.setCreateTime(DateUtils.getNowDate());
        return baseProjectDepartmentMapper.insertBaseProjectDepartment(baseProjectDepartment);
    }

    /**
     * 修改项目部门配置
     * 
     * @param baseProjectDepartment 项目部门配置
     * @return 结果
     */
    @Override
    public int updateBaseProjectDepartment(BaseProjectDepartment baseProjectDepartment)
    {
        baseProjectDepartment.setUpdateTime(DateUtils.getNowDate());
        return baseProjectDepartmentMapper.updateBaseProjectDepartment(baseProjectDepartment);
    }

    /**
     * 批量删除项目部门配置
     * 
     * @param projectDepartmentIds 需要删除的项目部门配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseProjectDepartmentByProjectDepartmentIds(Long[] projectDepartmentIds)
    {
        if (projectDepartmentIds == null || projectDepartmentIds.length == 0)
        {
            throw new ServiceException("删除项目部门配置失败，未选择数据");
        }
        if (baseProjectDepartmentMapper.getBaseProjectDepartmentByProjectDepartmentIds(projectDepartmentIds) > 0)
        {
            throw new ServiceException("存在使用中的项目部门配置，无法删除");
        }
        return baseProjectDepartmentMapper.deleteBaseProjectDepartmentByProjectDepartmentIds(projectDepartmentIds);
    }

    /**
     * 删除项目部门配置信息
     * 
     * @param projectDepartmentId 项目部门配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseProjectDepartmentByProjectDepartmentId(Long projectDepartmentId)
    {
        return baseProjectDepartmentMapper.deleteBaseProjectDepartmentByProjectDepartmentId(projectDepartmentId);
    }
}
