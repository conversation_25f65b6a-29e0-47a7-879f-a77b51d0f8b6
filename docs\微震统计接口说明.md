# 微震统计接口说明

## 概述

本文档说明了微震统计模块的接口设计和实现，该模块提供微震监测相关的统计分析功能，包括微震概览、P波完成率、事件分布和散点图数据等统计信息。

## 接口列表

### 1. 微震概览统计

**接口地址**: `GET /lxbi/stat/microseismic/overview`

**功能描述**: 获取微震监测的总体概览数据

**请求参数**:
- `viewType`: 视图类型 (day/week/month)
- `startDate`: 开始日期 (yyyy-MM-dd)
- `endDate`: 结束日期 (yyyy-MM-dd)

**响应数据**:
```json
{
  "networkEventCount": 200,
  "targetEventCount": 40,
  "deviceEventTotal": 2,
  "microseismicDeviceCount": 2,
  "startDate": "2025-08-20",
  "endDate": "2025-08-25",
  "period": "day"
}
```

### 2. 微震P波完成率统计

**接口地址**: `GET /lxbi/stat/microseismic/completion-rate`

**功能描述**: 获取微震P波完成率数据，用于环形进度图展示

**响应数据**:
```json
{
  "completionRate": 77,
  "completedCount": 77,
  "totalCount": 100,
  "rateType": "P波完成率"
}
```

### 3. 微震事件分布统计

**接口地址**: `GET /lxbi/stat/microseismic/event-distribution`

**功能描述**: 获取微震事件分布数据，用于柱状图展示

**响应数据**:
```json
[
  {
    "timePeriod": "1号",
    "eventCount": 100,
    "periodCode": "PERIOD_1"
  },
  {
    "timePeriod": "2号",
    "eventCount": 140,
    "periodCode": "PERIOD_2"
  },
  {
    "timePeriod": "3号",
    "eventCount": 250,
    "periodCode": "PERIOD_3"
  },
  {
    "timePeriod": "4号",
    "eventCount": 100,
    "periodCode": "PERIOD_4"
  },
  {
    "timePeriod": "5号",
    "eventCount": 125,
    "periodCode": "PERIOD_5"
  }
]
```

### 4. 微震散点图数据

**接口地址**: `GET /lxbi/stat/microseismic/scatter-data`

**功能描述**: 获取微震散点图数据，用于散点图展示

**响应数据**:
```json
[
  {
    "xCoordinate": 3.2,
    "yCoordinate": 4.5,
    "magnitude": 2.8,
    "eventType": "微震事件",
    "eventId": "MS0001",
    "occurTime": "2025-08-25 14:30:15"
  },
  {
    "xCoordinate": 5.1,
    "yCoordinate": 2.3,
    "magnitude": 3.5,
    "eventType": "爆破事件",
    "eventId": "MS0002",
    "occurTime": "2025-08-25 13:45:22"
  }
]
```

## 数据结构说明

### VO类设计

#### 1. MicroseismicOverviewVO
- **用途**: 微震概览数据
- **字段**: 台网事件数、目标事件数、设备事件总数、微震监测设备数量

#### 2. MicroseismicCompletionRateVO
- **用途**: 微震P波完成率数据（环形进度图）
- **字段**: 完成率百分比、已完成数量、总数量、完成率类型

#### 3. MicroseismicEventDistributionVO
- **用途**: 微震事件分布数据（柱状图）
- **字段**: 时间段标识、事件数量、时间段代码

#### 4. MicroseismicScatterDataVO
- **用途**: 微震散点图数据
- **字段**: X/Y坐标、震级大小、事件类型、事件ID、发生时间

## 业务逻辑

### 1. 数据统计维度

**时间维度**:
- 日统计: 按天统计微震数据
- 周统计: 按周统计微震数据
- 月统计: 按月统计微震数据

**事件维度**:
- 台网事件: 监测网络检测到的事件
- 目标事件: 符合条件的目标事件
- 设备事件: 设备相关的事件
- 监测设备: 微震监测设备数量

### 2. 统计指标

**基础指标**:
- 台网事件数: 监测网络检测到的微震事件总数
- 目标事件数: 符合分析条件的目标事件数量
- 设备事件总数: 设备相关事件的总数
- 微震监测设备数量: 部署的监测设备数量

**完成率指标**:
- P波完成率: P波检测的完成百分比
- 已完成数量: 已完成P波检测的事件数
- 总数量: 需要进行P波检测的事件总数

**分布分析指标**:
- 时间段分布: 不同时间段的事件数量分布
- 空间分布: 事件在空间上的分布情况
- 震级分布: 不同震级事件的分布

### 3. 数据展示

**概览卡片**:
- 台网事件数: 200
- 目标事件数: 40
- 设备事件总数: 2
- 微震监测设备数量: 2

**环形进度图展示**:
- P波完成率: 77%
- 显示完成进度和剩余部分

**柱状图展示**:
- 事件分布统计
- 5个时间段的事件数量对比

**散点图展示**:
- 微震事件空间分布
- 不同类型事件的位置和震级

## 模拟数据说明

### 1. 概览数据
- 台网事件数: 200
- 目标事件数: 40
- 设备事件总数: 2
- 微震监测设备数量: 2

### 2. P波完成率
- 完成率: 77%
- 已完成数量: 77个
- 总数量: 100个

### 3. 事件分布数据
- **1号时间段**: 100个事件
- **2号时间段**: 140个事件
- **3号时间段**: 250个事件（最高）
- **4号时间段**: 100个事件
- **5号时间段**: 125个事件

### 4. 散点图数据特点
- **坐标范围**: X和Y坐标均在0-7范围内
- **震级范围**: 1.0-5.0震级范围
- **事件类型**: 微震事件、爆破事件、噪声事件
- **数据量**: 20个散点数据

## 扩展接入点

### 1. 微震监测系统接入
```java
// 可接入的数据源
- 微震监测网络系统
- 地震波检测系统
- P波/S波分析系统
- 震级计算系统
```

### 2. 数据接入方式
- **实时数据**: 通过Kafka队列接收监测设备数据
- **定时同步**: 定时从监测系统同步数据
- **API接口**: 通过REST API获取监测数据

### 3. 数据库设计建议
```sql
-- 微震事件记录表
CREATE TABLE microseismic_event (
    id BIGSERIAL PRIMARY KEY,
    event_id VARCHAR(50) UNIQUE,
    event_type VARCHAR(50),
    occur_time TIMESTAMP,
    x_coordinate DECIMAL(10,3),
    y_coordinate DECIMAL(10,3),
    z_coordinate DECIMAL(10,3),
    magnitude DECIMAL(4,2),
    p_wave_detected BOOLEAN,
    s_wave_detected BOOLEAN,
    network_id VARCHAR(50),
    device_id VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 微震监测设备表
CREATE TABLE microseismic_device (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(50) UNIQUE,
    device_name VARCHAR(100),
    device_type VARCHAR(50),
    x_coordinate DECIMAL(10,3),
    y_coordinate DECIMAL(10,3),
    z_coordinate DECIMAL(10,3),
    status VARCHAR(20),
    install_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- P波检测记录表
CREATE TABLE p_wave_detection (
    id BIGSERIAL PRIMARY KEY,
    event_id VARCHAR(50),
    device_id VARCHAR(50),
    detection_time TIMESTAMP,
    wave_amplitude DECIMAL(10,6),
    frequency DECIMAL(8,3),
    detection_status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 测试说明

### 1. 单元测试
- 所有Service方法的功能测试
- 不同视图类型的测试
- 数据完整性和一致性测试
- P波完成率77%验证

### 2. 接口测试
```bash
# 测试概览统计
curl -X GET "http://localhost:8080/lxbi/stat/microseismic/overview?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试P波完成率
curl -X GET "http://localhost:8080/lxbi/stat/microseismic/completion-rate?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试事件分布
curl -X GET "http://localhost:8080/lxbi/stat/microseismic/event-distribution?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试散点图数据
curl -X GET "http://localhost:8080/lxbi/stat/microseismic/scatter-data?viewType=day&startDate=2025-08-20&endDate=2025-08-25"
```

### 3. 数据验证
- 事件分布时间段数量验证 (5个时间段)
- 散点图坐标范围验证 (0-7范围)
- P波完成率77%验证
- 事件类型分布验证

## 后续开发计划

### 1. 数据接入
- 接入真实的微震监测系统
- 建立地震波检测数据流
- 实现震级自动计算

### 2. 功能增强
- 震级阈值动态配置
- 预警机制建立
- 历史数据对比分析

### 3. 可视化优化
- 3D微震事件展示
- 实时数据刷新
- 移动端微震监控

## 业务价值

### 1. 安全管理提升
- **实时监控**: 及时发现微震活动
- **预警机制**: 提前预警地震风险
- **精确定位**: 准确定位震源位置

### 2. 运营效率优化
- **数据分析**: 了解微震活动规律
- **设备管理**: 优化监测设备布局
- **应急响应**: 快速响应异常事件

### 3. 科学研究支持
- **数据积累**: 积累微震监测数据
- **规律分析**: 分析微震活动规律
- **预测模型**: 建立地震预测模型

## 总结

微震统计模块提供了完整的微震监测和分析功能，通过多维度的统计分析，帮助管理人员及时了解微震活动状况，预防地震灾害，保障矿山安全生产。

当前实现使用模拟数据，完美对应了界面图片中的所有功能模块，为后续接入真实监测数据预留了扩展接口，可以根据实际业务需求进行数据源的接入和功能的扩展。
