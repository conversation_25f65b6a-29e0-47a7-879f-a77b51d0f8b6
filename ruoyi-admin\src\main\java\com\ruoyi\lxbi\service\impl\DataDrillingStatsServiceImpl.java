package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataDrillingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalStats;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataDrillingPeriodStats;
import com.ruoyi.lxbi.domain.response.DataDrillingDepartmentStats;
import com.ruoyi.lxbi.domain.response.DataDrillingStopeStats;
import com.ruoyi.lxbi.domain.response.DataDrillingWorkingFaceStats;
import com.ruoyi.lxbi.domain.response.DataDrillingTypeStats;
import com.ruoyi.lxbi.mapper.DataDrillingStatsMapper;
import com.ruoyi.lxbi.service.IDataDrillingStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 钻孔施工数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Service
public class DataDrillingStatsServiceImpl implements IDataDrillingStatsService {
    @Autowired
    private DataDrillingStatsMapper dataDrillingStatsMapper;
    
    /**
     * 查询总体统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合
     */
    @Override
    public List<DataDrillingTotalStats> selectTotalStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDrillingStatsMapper.selectDailyTotalStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDrillingStatsMapper.selectWeeklyTotalStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDrillingStatsMapper.selectYearlyTotalStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDrillingStatsMapper.selectMonthlyTotalStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    @Override
    public List<DataDrillingTotalWithPlanStats> selectTotalWithPlanStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDrillingStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDrillingStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDrillingStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDrillingStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }
    
    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    @Override
    public List<DataDrillingPeriodStats> selectPeriodStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDrillingStatsMapper.selectDailyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDrillingStatsMapper.selectWeeklyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDrillingStatsMapper.selectYearlyPeriodStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDrillingStatsMapper.selectMonthlyPeriodStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询项目部门统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合
     */
    @Override
    public List<DataDrillingDepartmentStats> selectDepartmentStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDrillingStatsMapper.selectDailyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDrillingStatsMapper.selectWeeklyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDrillingStatsMapper.selectYearlyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDrillingStatsMapper.selectMonthlyDepartmentStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询采场统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 采场统计数据集合
     */
    @Override
    public List<DataDrillingStopeStats> selectStopeStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDrillingStatsMapper.selectDailyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDrillingStatsMapper.selectWeeklyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDrillingStatsMapper.selectYearlyStopeStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDrillingStatsMapper.selectMonthlyStopeStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询工作面统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 工作面统计数据集合
     */
    @Override
    public List<DataDrillingWorkingFaceStats> selectWorkingFaceStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDrillingStatsMapper.selectDailyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDrillingStatsMapper.selectWeeklyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDrillingStatsMapper.selectYearlyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDrillingStatsMapper.selectMonthlyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询钻孔类型统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 钻孔类型统计数据集合
     */
    @Override
    public List<DataDrillingTypeStats> selectTypeStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDrillingStatsMapper.selectDailyTypeStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDrillingStatsMapper.selectWeeklyTypeStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDrillingStatsMapper.selectYearlyTypeStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDrillingStatsMapper.selectMonthlyTypeStats(request.getStartDate(), request.getEndDate());
        }
    }

}
