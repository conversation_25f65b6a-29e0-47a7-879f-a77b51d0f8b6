package com.ruoyi.common.core.domain.excel;

import lombok.Data;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Excel导入结果
 *
 * <AUTHOR>
 */
@Data
public class ExcelImportResult<T> {

    private String id;

    /**
     * 总行数
     */
    private int total;

    /**
     * 成功行数
     */
    private int success;

    /**
     * 失败行数
     */
    private int error;

    private int warning;

    /**
     * 验证错误列表
     */
    private List<ExcelDataInfo<T>> dataList;

    /**
     * 消息
     */
    private String message;

    public ExcelImportResult() {
        this.total = 0;
        this.success = 0;
        this.error = 0;
        this.id = UUID.randomUUID().toString();
        this.dataList = new CopyOnWriteArrayList<>();
    }

    public synchronized void addFail(ExcelDataInfo<T> data) {
        addCount(data.getType());
        if (data.getType() != null) {
            this.dataList.add(data);
        }
    }

    public synchronized void addData(ExcelDataInfo<T> data) {
        this.dataList.add(data);
        addCount(data.getType());
    }

    private void addCount(ExcelDataInfo.RemindType type) {
        this.total++;
        switch (type) {
            case WARNING:
                this.warning++;
                break;
            case ERROR:
                this.error++;
                break;
            case null:
                success++;
                break;
        }
    }

}
