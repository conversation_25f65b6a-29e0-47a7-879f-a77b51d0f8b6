package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 设备故障记录VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentFaultRecordVO {
    
    /**
     * 序号
     */
    private Integer serialNumber;
    
    /**
     * 设备名称
     */
    private String equipmentName;
    
    /**
     * 故障时长
     */
    private String faultDuration;
    
    /**
     * 故障原因
     */
    private String faultReason;
    
    /**
     * 设备编码
     */
    private String equipmentCode;
    
    /**
     * 故障开始时间
     */
    private String faultStartTime;
    
    /**
     * 故障结束时间
     */
    private String faultEndTime;
    
    /**
     * 故障状态
     */
    private String faultStatus;
}
