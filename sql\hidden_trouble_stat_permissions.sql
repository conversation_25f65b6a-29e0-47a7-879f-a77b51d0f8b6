-- 隐患统计模块权限配置SQL
-- 执行此SQL为隐患统计功能添加菜单和权限

-- 1. 添加隐患统计主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('隐患统计', 0, 6, 'hiddenTroubleStat', NULL, 1, 0, 'M', '0', '0', NULL, 'chart', 'admin', NOW(), '', NULL, '隐患统计分析菜单');

-- 获取刚插入的主菜单ID（假设为2000，实际使用时需要查询获取）
-- SELECT menu_id FROM sys_menu WHERE menu_name = '隐患统计' AND parent_id = 0;

-- 2. 添加隐患统计子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('隐患分析概览', 2000, 1, 'overview', 'lxbi/hiddenTroubleStat/overview/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:overview', 'dashboard', 'admin', NOW(), '', NULL, '隐患分析概览'),
('部门分布统计', 2000, 2, 'department', 'lxbi/hiddenTroubleStat/department/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:department', 'tree', 'admin', NOW(), '', NULL, '部门隐患分布统计'),
('位置频率统计', 2000, 3, 'location', 'lxbi/hiddenTroubleStat/location/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:location', 'location', 'admin', NOW(), '', NULL, '高频发隐患位置统计'),
('趋势分析', 2000, 4, 'trend', 'lxbi/hiddenTroubleStat/trend/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:trend', 'line-chart', 'admin', NOW(), '', NULL, '隐患趋势统计'),
('状态分布', 2000, 5, 'status', 'lxbi/hiddenTroubleStat/status/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:status', 'circle-check', 'admin', NOW(), '', NULL, '隐患状态分布统计'),
('等级分布', 2000, 6, 'grade', 'lxbi/hiddenTroubleStat/grade/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:grade', 'star', 'admin', NOW(), '', NULL, '隐患等级分布统计'),
('类别分布', 2000, 7, 'category', 'lxbi/hiddenTroubleStat/category/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:category', 'component', 'admin', NOW(), '', NULL, '隐患类别分布统计'),
('责任人统计', 2000, 8, 'person', 'lxbi/hiddenTroubleStat/person/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:person', 'peoples', 'admin', NOW(), '', NULL, '责任人隐患统计'),
('完成率统计', 2000, 9, 'completion', 'lxbi/hiddenTroubleStat/completion/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:completion', 'rate', 'admin', NOW(), '', NULL, '整改完成率统计'),
('超期统计', 2000, 10, 'overdue', 'lxbi/hiddenTroubleStat/overdue/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:overdue', 'time-range', 'admin', NOW(), '', NULL, '超期隐患统计'),
('整改效率', 2000, 11, 'efficiency', 'lxbi/hiddenTroubleStat/efficiency/index', 1, 0, 'C', '0', '0', 'lxbi:hiddenTroubleStat:efficiency', 'speed', 'admin', NOW(), '', NULL, '隐患整改效率统计');

-- 3. 为管理员角色分配权限（假设管理员角色ID为1）
-- 首先获取新添加的菜单ID
-- 这里需要根据实际插入后的menu_id进行调整

-- 示例：为角色ID为1的管理员分配所有隐患统计权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_name IN (
    '隐患统计',
    '隐患分析概览',
    '部门分布统计', 
    '位置频率统计',
    '趋势分析',
    '状态分布',
    '等级分布',
    '类别分布',
    '责任人统计',
    '完成率统计',
    '超期统计',
    '整改效率'
) AND parent_id IN (0, 2000);

-- 4. 创建隐患统计相关的字典数据
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES 
('隐患统计周期', 'hidden_trouble_stat_period', '0', 'admin', NOW(), '', NULL, '隐患统计周期字典'),
('隐患状态', 'hidden_trouble_status', '0', 'admin', NOW(), '', NULL, '隐患状态字典'),
('隐患等级', 'hidden_trouble_grade', '0', 'admin', NOW(), '', NULL, '隐患等级字典');

-- 5. 添加字典数据项
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
-- 统计周期
(1, '按日统计', 'day', 'hidden_trouble_stat_period', '', 'primary', 'Y', '0', 'admin', NOW(), '', NULL, '按日统计'),
(2, '按周统计', 'week', 'hidden_trouble_stat_period', '', 'info', 'N', '0', 'admin', NOW(), '', NULL, '按周统计'),
(3, '按月统计', 'month', 'hidden_trouble_stat_period', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '按月统计'),

-- 隐患状态
(1, '待整改', '0', 'hidden_trouble_status', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '待整改'),
(2, '已驳回', '1', 'hidden_trouble_status', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '已驳回'),
(3, '已整改', '2', 'hidden_trouble_status', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '已整改'),
(4, '待复查', '3', 'hidden_trouble_status', '', 'info', 'N', '0', 'admin', NOW(), '', NULL, '待复查'),
(5, '已超期', '4', 'hidden_trouble_status', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '已超期'),

-- 隐患等级
(1, '一般隐患', '0', 'hidden_trouble_grade', '', 'info', 'Y', '0', 'admin', NOW(), '', NULL, '一般隐患'),
(2, '重大隐患', '1', 'hidden_trouble_grade', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '重大隐患');

-- 6. 查询验证SQL（可选执行）
/*
-- 查看新添加的菜单
SELECT menu_id, menu_name, parent_id, path, perms, icon 
FROM sys_menu 
WHERE menu_name LIKE '%隐患统计%' OR parent_id IN (
    SELECT menu_id FROM sys_menu WHERE menu_name = '隐患统计'
)
ORDER BY parent_id, order_num;

-- 查看角色权限分配
SELECT rm.role_id, r.role_name, m.menu_name, m.perms
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_name LIKE '%隐患统计%'
ORDER BY rm.role_id, m.order_num;

-- 查看字典数据
SELECT dt.dict_name, dd.dict_label, dd.dict_value, dd.list_class
FROM sys_dict_data dd
JOIN sys_dict_type dt ON dd.dict_type = dt.dict_type
WHERE dt.dict_type LIKE '%hidden_trouble%'
ORDER BY dt.dict_type, dd.dict_sort;
*/

-- 注意事项：
-- 1. 执行前请确认parent_id为2000的菜单ID是否正确
-- 2. 根据实际的角色ID调整sys_role_menu中的role_id
-- 3. 如果菜单ID冲突，请调整为合适的ID
-- 4. 图标名称可能需要根据实际的图标库进行调整
