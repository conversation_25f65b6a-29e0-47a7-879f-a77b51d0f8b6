package com.ruoyi.lxbi.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.lxbi.admin.domain.ApiHiddenTroubleRecord;
import com.ruoyi.lxbi.admin.service.IApiHiddenTroubleRecordService;
import com.ruoyi.lxbi.admin.task.HiddenTroubleSyncTask;
import lombok.extern.slf4j.Slf4j;

/**
 * 隐患数据记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Slf4j
@RestController
@RequestMapping("/sec/hiddenTrouble")
public class ApiHiddenTroubleRecordController extends BaseController {
    
    @Autowired
    private IApiHiddenTroubleRecordService apiHiddenTroubleRecordService;

    @Autowired
    private HiddenTroubleSyncTask hiddenTroubleSyncTask;

    /**
     * 查询隐患数据记录列表
     */
    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTrouble:list')")
    @GetMapping("/list")
    public TableDataInfo list(ApiHiddenTroubleRecord apiHiddenTroubleRecord) {
        startPage();
        List<ApiHiddenTroubleRecord> list = apiHiddenTroubleRecordService.selectApiHiddenTroubleRecordList(apiHiddenTroubleRecord);
        return getDataTable(list);
    }

    /**
     * 导出隐患数据记录列表
     */
    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTrouble:export')")
    @Log(title = "隐患数据记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ApiHiddenTroubleRecord apiHiddenTroubleRecord) {
        List<ApiHiddenTroubleRecord> list = apiHiddenTroubleRecordService.selectApiHiddenTroubleRecordList(apiHiddenTroubleRecord);
        ExcelUtil<ApiHiddenTroubleRecord> util = new ExcelUtil<ApiHiddenTroubleRecord>(ApiHiddenTroubleRecord.class);
        util.exportExcel(list, "隐患数据记录数据", "隐患数据");
    }

    /**
     * 获取隐患数据记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTrouble:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(apiHiddenTroubleRecordService.selectApiHiddenTroubleRecordById(id));
    }

    /**
     * 新增隐患数据记录
     */
    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTrouble:add')")
    @Log(title = "隐患数据记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApiHiddenTroubleRecord apiHiddenTroubleRecord) {
        return toAjax(apiHiddenTroubleRecordService.insertApiHiddenTroubleRecord(apiHiddenTroubleRecord));
    }

    /**
     * 修改隐患数据记录
     */
    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTrouble:edit')")
    @Log(title = "隐患数据记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApiHiddenTroubleRecord apiHiddenTroubleRecord) {
        return toAjax(apiHiddenTroubleRecordService.updateApiHiddenTroubleRecord(apiHiddenTroubleRecord));
    }

    /**
     * 删除隐患数据记录
     */
    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTrouble:remove')")
    @Log(title = "隐患数据记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(apiHiddenTroubleRecordService.deleteApiHiddenTroubleRecordByIds(ids));
    }

    /**
     * 获取隐患统计数据
     */
    @Anonymous
    @GetMapping("/statistics")
    @Log(title = "隐患统计", businessType = BusinessType.OTHER)
    public AjaxResult getStatistics() {
        try {
            log.info("获取隐患统计数据");
            Map<String, Object> statistics = apiHiddenTroubleRecordService.getStatistics();
            return AjaxResult.success("获取统计数据成功").put("data", statistics);
        } catch (Exception e) {
            log.error("获取隐患统计数据失败", e);
            return error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 同步第三方隐患数据（全量同步）
     */
    @Anonymous
    @PostMapping("/sync")
    @Log(title = "隐患数据同步", businessType = BusinessType.OTHER)
    public AjaxResult syncData() {
        try {
            log.info("开始同步第三方隐患数据");
            Map<String, Object> result = apiHiddenTroubleRecordService.syncHiddenTroubleData();
            
            if ((Boolean) result.get("success")) {
                return AjaxResult.success("同步成功").put("data", result);
            } else {
                return error("同步失败: " + result.get("message"));
            }
        } catch (Exception e) {
            log.error("同步第三方隐患数据失败", e);
            return error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 按日期范围同步第三方隐患数据
     * 
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 同步结果
     */
    @Anonymous
    @PostMapping("/syncByDateRange")
    @Log(title = "按日期范围同步隐患数据", businessType = BusinessType.OTHER)
    public AjaxResult syncDataByDateRange(@RequestParam String startDate, @RequestParam String endDate) {
        try {
            log.info("开始按日期范围同步第三方隐患数据，日期范围: {} 到 {}", startDate, endDate);
            Map<String, Object> result = apiHiddenTroubleRecordService.syncHiddenTroubleDataByDateRange(startDate, endDate);
            
            if ((Boolean) result.get("success")) {
                return AjaxResult.success("按日期范围同步成功").put("data", result);
            } else {
                return error("按日期范围同步失败: " + result.get("message"));
            }
        } catch (Exception e) {
            log.error("按日期范围同步第三方隐患数据失败", e);
            return error("按日期范围同步失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发同步前一天的隐患数据
     */
    @Anonymous
    @PostMapping("/syncYesterday")
    @Log(title = "同步前一天隐患数据", businessType = BusinessType.OTHER)
    public AjaxResult syncYesterdayData() {
        try {
            log.info("手动触发同步前一天隐患数据");
            hiddenTroubleSyncTask.syncYesterdayData();
            return success("同步前一天隐患数据任务已触发");
        } catch (Exception e) {
            log.error("手动触发同步前一天隐患数据失败", e);
            return error("触发失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发同步指定日期的隐患数据
     * 
     * @param date 指定日期（格式：yyyy-MM-dd）
     * @return 同步结果
     */
    @Anonymous
    @PostMapping("/syncSpecificDate")
    @Log(title = "同步指定日期隐患数据", businessType = BusinessType.OTHER)
    public AjaxResult syncSpecificDate(@RequestParam String date) {
        try {
            log.info("手动触发同步指定日期({})隐患数据", date);
            hiddenTroubleSyncTask.syncSpecificDate(date);
            return success("同步指定日期隐患数据任务已触发");
        } catch (Exception e) {
            log.error("手动触发同步指定日期隐患数据失败", e);
            return error("触发失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发同步指定日期范围的隐患数据
     * 
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 同步结果
     */
    @Anonymous
    @PostMapping("/syncDateRange")
    @Log(title = "同步指定日期范围隐患数据", businessType = BusinessType.OTHER)
    public AjaxResult syncDateRange(@RequestParam String startDate, @RequestParam String endDate) {
        try {
            log.info("手动触发同步指定日期范围({} 到 {})隐患数据", startDate, endDate);
            hiddenTroubleSyncTask.syncDateRange(startDate, endDate);
            return success("同步指定日期范围隐患数据任务已触发");
        } catch (Exception e) {
            log.error("手动触发同步指定日期范围隐患数据失败", e);
            return error("触发失败: " + e.getMessage());
        }
    }

    /**
     * 根据通知编号查询隐患数据
     */
    @Anonymous
    @GetMapping("/notice/{noticeNumber}")
    public AjaxResult getByNoticeNumber(@PathVariable("noticeNumber") String noticeNumber) {
        try {
            log.info("根据通知编号查询隐患数据: {}", noticeNumber);
            ApiHiddenTroubleRecord record = apiHiddenTroubleRecordService.selectByNoticeNumber(noticeNumber);
            
            if (record != null) {
                return AjaxResult.success("查询成功").put("data", record);
            } else {
                return error("未找到对应的隐患数据");
            }
        } catch (Exception e) {
            log.error("根据通知编号查询隐患数据失败", e);
            return error("查询失败: " + e.getMessage());
        }
    }
}
