package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.DataIronConcentrate;
import com.ruoyi.lxbi.domain.PlanMineralMonthly;
import com.ruoyi.lxbi.domain.table.WeeklyMineralReportTableVo;
import com.ruoyi.lxbi.service.IDataIronConcentrateService;
import com.ruoyi.lxbi.service.IPlanMineralMonthlyService;
import com.ruoyi.lxbi.table.params.WeeklyMineralReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 选矿数据周报表格处理器
 */
@Component
public class WeeklyMineralReportTableHandler extends BaseTableHandler<WeeklyMineralReportTableVo, WeeklyMineralReportQueryParams> {

    @Autowired
    private IPlanMineralMonthlyService planMineralMonthlyService;

    @Autowired
    private IDataIronConcentrateService dataIronConcentrateService;

    @Override
    public List<WeeklyMineralReportTableVo> queryTableData(WeeklyMineralReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date queryDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取财务周范围（周四到周三）
        Date[] weekRange = FinancialDateUtils.getFinancialWeekRange(queryDate);
        Date weekStart = weekRange[0];
        Date weekEnd = weekRange[1];

        // 获取财务月范围
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(queryDate);

        // 获取月度计划数据
        Map<String, BigDecimal> monthlyPlans = getMonthlyPlans(queryDate);

        // 获取月累计数据
        Map<String, BigDecimal> monthlyAccumulated = getMonthlyAccumulated(financialMonthStart, queryDate);

        // 获取周数据
        Map<String, BigDecimal> weeklyData = getWeeklyData(weekStart, weekEnd);

        // 构建表格数据
        return buildTableData(monthlyPlans, monthlyAccumulated, weeklyData);
    }

    /**
     * 获取月度计划数据
     */
    private Map<String, BigDecimal> getMonthlyPlans(Date operationDate) {
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        PlanMineralMonthly queryParam = new PlanMineralMonthly();
        queryParam.setPlanDate(financialMonth);

        List<PlanMineralMonthly> plans = planMineralMonthlyService.selectPlanMineralMonthlyList(queryParam);

        Map<String, BigDecimal> result = new HashMap<>();
        if (!plans.isEmpty()) {
            PlanMineralMonthly plan = plans.get(0);
            
            // 原矿处理量相关
            result.put("rawOreProcessingVolume", plan.getRawOreProcessingVolume());
            result.put("drySeparationVolume", plan.getDrySeparationVolume());
            result.put("grindingFeedVolume", plan.getGrindingFeedVolume());
            
            // 原矿品位相关
            result.put("rawOreGradeTfe", plan.getRawOreGradeTfe());
            result.put("rawOreGradeMfe", plan.getRawOreGradeMfe());
            
            // 精粉相关
            result.put("concentrateGrade", plan.getConcentrateGrade());
            result.put("concentrateFineness", plan.getConcentrateFineness());
            result.put("concentrateVolume", plan.getConcentrateVolume());
            
            // 铁精粉水分
            result.put("ironConcentrateMoisture", plan.getIronConcentrateMoisture());
            
            // 尾矿品位相关
            result.put("tailingGradeTfe", plan.getTailingGradeTfe());
            result.put("tailingGradeMfe", plan.getTailingGradeMfe());
            
            // 选比相关
            result.put("comprehensiveRatio", plan.getComprehensiveRatio());
            result.put("grindingRatio", plan.getGrindingRatio());
            
            // 尾矿相关
            result.put("tailingsAgitatorTank", plan.getTailingsAgitatorTank());
            result.put("overflowOfTailings", plan.getOverflowOfTailings());
        }

        return result;
    }

    /**
     * 获取月累计数据（从铁精粉数据表获取实际数据）
     */
    private Map<String, BigDecimal> getMonthlyAccumulated(Date startDate, Date endDate) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 查询日期范围内的所有铁精粉数据
        DataIronConcentrate queryParam = new DataIronConcentrate();
        queryParam.getParams().put("startDate", startDate);
        queryParam.getParams().put("endDate", endDate);
        List<DataIronConcentrate> monthlyDataList = dataIronConcentrateService.selectDataIronConcentrateList(queryParam);

        if (!monthlyDataList.isEmpty()) {
            // 计算月累计数据
            BigDecimal totalVolume = monthlyDataList.stream()
                    .map(DataIronConcentrate::getProductionVolume)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算平均值
            double avgTfeContent = monthlyDataList.stream()
                    .filter(data -> data.getTfeContent() != null)
                    .mapToDouble(data -> data.getTfeContent().doubleValue())
                    .average().orElse(0.0);

            double avgFineness = monthlyDataList.stream()
                    .filter(data -> data.getFinenessMinus500() != null)
                    .mapToDouble(data -> data.getFinenessMinus500().doubleValue())
                    .average().orElse(0.0);

            double avgMoisture = monthlyDataList.stream()
                    .filter(data -> data.getMoistureContent() != null)
                    .mapToDouble(data -> data.getMoistureContent().doubleValue())
                    .average().orElse(0.0);

            // 铁精粉相关数据（从实际数据计算）
            result.put("concentrateVolume", totalVolume.compareTo(BigDecimal.ZERO) > 0 ? totalVolume : null);
            result.put("concentrateGrade", avgTfeContent > 0 ? BigDecimal.valueOf(avgTfeContent) : null);
            result.put("concentrateFineness", avgFineness > 0 ? BigDecimal.valueOf(avgFineness) : null);
            result.put("ironConcentrateMoisture", avgMoisture > 0 ? BigDecimal.valueOf(avgMoisture) : null);
        } else {
            // 如果没有数据，设置铁精粉相关数据为null
            result.put("concentrateVolume", null);
            result.put("concentrateGrade", null);
            result.put("concentrateFineness", null);
            result.put("ironConcentrateMoisture", null);
        }

        // 其他数据暂时设为空（没有对应的数据表）
        result.put("rawOreProcessingVolume", null);
        result.put("drySeparationVolume", null);
        result.put("grindingFeedVolume", null);
        result.put("rawOreGradeTfe", null);
        result.put("rawOreGradeMfe", null);
        result.put("tailingGradeTfe", null);
        result.put("tailingGradeMfe", null);
        result.put("comprehensiveRatio", null);
        result.put("grindingRatio", null);
        result.put("tailingsAgitatorTank", null);
        result.put("overflowOfTailings", null);

        return result;
    }

    /**
     * 获取周数据（从铁精粉数据表获取实际数据）
     */
    private Map<String, BigDecimal> getWeeklyData(Date weekStart, Date weekEnd) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 查询周范围内的所有铁精粉数据
        DataIronConcentrate queryParam = new DataIronConcentrate();
        queryParam.getParams().put("startDate", weekStart);
        queryParam.getParams().put("endDate", weekEnd);
        List<DataIronConcentrate> weeklyDataList = dataIronConcentrateService.selectDataIronConcentrateList(queryParam);

        if (!weeklyDataList.isEmpty()) {
            // 计算周累计数据
            BigDecimal totalVolume = weeklyDataList.stream()
                    .map(DataIronConcentrate::getProductionVolume)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算平均值
            double avgTfeContent = weeklyDataList.stream()
                    .filter(data -> data.getTfeContent() != null)
                    .mapToDouble(data -> data.getTfeContent().doubleValue())
                    .average().orElse(0.0);

            double avgFineness = weeklyDataList.stream()
                    .filter(data -> data.getFinenessMinus500() != null)
                    .mapToDouble(data -> data.getFinenessMinus500().doubleValue())
                    .average().orElse(0.0);

            double avgMoisture = weeklyDataList.stream()
                    .filter(data -> data.getMoistureContent() != null)
                    .mapToDouble(data -> data.getMoistureContent().doubleValue())
                    .average().orElse(0.0);

            // 铁精粉相关数据（从实际数据计算）
            result.put("concentrateVolume", totalVolume.compareTo(BigDecimal.ZERO) > 0 ? totalVolume : null);
            result.put("concentrateGrade", avgTfeContent > 0 ? BigDecimal.valueOf(avgTfeContent) : null);
            result.put("concentrateFineness", avgFineness > 0 ? BigDecimal.valueOf(avgFineness) : null);
            result.put("ironConcentrateMoisture", avgMoisture > 0 ? BigDecimal.valueOf(avgMoisture) : null);
        } else {
            // 如果没有数据，设置铁精粉相关数据为null
            result.put("concentrateVolume", null);
            result.put("concentrateGrade", null);
            result.put("concentrateFineness", null);
            result.put("ironConcentrateMoisture", null);
        }

        // 其他数据暂时设为空（没有对应的数据表）
        result.put("rawOreProcessingVolume", null);
        result.put("drySeparationVolume", null);
        result.put("grindingFeedVolume", null);
        result.put("rawOreGradeTfe", null);
        result.put("rawOreGradeMfe", null);
        result.put("tailingGradeTfe", null);
        result.put("tailingGradeMfe", null);
        result.put("comprehensiveRatio", null);
        result.put("grindingRatio", null);
        result.put("tailingsAgitatorTank", null);
        result.put("overflowOfTailings", null);

        return result;
    }

    /**
     * 构建表格数据
     */
    private List<WeeklyMineralReportTableVo> buildTableData(Map<String, BigDecimal> monthlyPlans,
                                                            Map<String, BigDecimal> monthlyAccumulated,
                                                            Map<String, BigDecimal> weeklyData) {
        List<WeeklyMineralReportTableVo> result = new ArrayList<>();
        int serialNumber = 1;

        // 原矿处理量
        result.add(createReportItem(String.valueOf(serialNumber++), "原矿处理量", "原矿处理量", "t",
                monthlyPlans.get("rawOreProcessingVolume"), monthlyAccumulated.get("rawOreProcessingVolume"), 
                weeklyData.get("rawOreProcessingVolume")));
        result.add(createReportItem("", "", "干抛量", "t",
                monthlyPlans.get("drySeparationVolume"), monthlyAccumulated.get("drySeparationVolume"), 
                weeklyData.get("drySeparationVolume")));
        result.add(createReportItem("", "", "入磨量", "t",
                monthlyPlans.get("grindingFeedVolume"), monthlyAccumulated.get("grindingFeedVolume"), 
                weeklyData.get("grindingFeedVolume")));

        // 原矿入磨品位
        result.add(createReportItem(String.valueOf(serialNumber++), "原矿入磨品位", "TFe", "%",
                monthlyPlans.get("rawOreGradeTfe"), monthlyAccumulated.get("rawOreGradeTfe"), 
                weeklyData.get("rawOreGradeTfe")));
        result.add(createReportItem("", "", "mFe", "%",
                monthlyPlans.get("rawOreGradeMfe"), monthlyAccumulated.get("rawOreGradeMfe"), 
                weeklyData.get("rawOreGradeMfe")));

        // 精粉
        result.add(createReportItem(String.valueOf(serialNumber++), "精粉", "TFe", "%",
                monthlyPlans.get("concentrateGrade"), monthlyAccumulated.get("concentrateGrade"), 
                weeklyData.get("concentrateGrade")));
        result.add(createReportItem("", "", "精矿细度(-500目含量)", "%",
                monthlyPlans.get("concentrateFineness"), monthlyAccumulated.get("concentrateFineness"), 
                weeklyData.get("concentrateFineness")));
        result.add(createReportItem("", "", "产量", "t",
                monthlyPlans.get("concentrateVolume"), monthlyAccumulated.get("concentrateVolume"), 
                weeklyData.get("concentrateVolume")));

        // 铁精粉水分
        result.add(createReportItem(String.valueOf(serialNumber++), "铁精粉水分", "水分", "%",
                monthlyPlans.get("ironConcentrateMoisture"), monthlyAccumulated.get("ironConcentrateMoisture"), 
                weeklyData.get("ironConcentrateMoisture")));

        // 尾矿品位
        result.add(createReportItem(String.valueOf(serialNumber++), "尾矿品位", "TFe", "%",
                monthlyPlans.get("tailingGradeTfe"), monthlyAccumulated.get("tailingGradeTfe"), 
                weeklyData.get("tailingGradeTfe")));
        result.add(createReportItem("", "", "mFe", "%",
                monthlyPlans.get("tailingGradeMfe"), monthlyAccumulated.get("tailingGradeMfe"), 
                weeklyData.get("tailingGradeMfe")));

        // 综合选比
        result.add(createReportItem(String.valueOf(serialNumber++), "综合选比", "综合选比", "倍",
                monthlyPlans.get("comprehensiveRatio"), monthlyAccumulated.get("comprehensiveRatio"), 
                weeklyData.get("comprehensiveRatio")));

        // 入磨选比
        result.add(createReportItem(String.valueOf(serialNumber++), "入磨选比", "入磨选比", "倍",
                monthlyPlans.get("grindingRatio"), monthlyAccumulated.get("grindingRatio"), 
                weeklyData.get("grindingRatio")));

        // 尾矿搅拌槽
        result.add(createReportItem(String.valueOf(serialNumber++), "尾矿搅拌槽", "尾矿粒级(-200目)", "",
                monthlyPlans.get("tailingsAgitatorTank"), monthlyAccumulated.get("tailingsAgitatorTank"), 
                weeklyData.get("tailingsAgitatorTank")));

        // 尾矿脱水筛上量
        result.add(createReportItem(String.valueOf(serialNumber++), "尾矿脱水筛上量", "尾矿脱水筛上量", "t",
                monthlyPlans.get("overflowOfTailings"), monthlyAccumulated.get("overflowOfTailings"), 
                weeklyData.get("overflowOfTailings")));

        // 综合回收率（计算得出）
        BigDecimal concentrateVolumePlan = monthlyPlans.get("concentrateVolume");
        BigDecimal rawOreVolumePlan = monthlyPlans.get("rawOreProcessingVolume");
        BigDecimal recoveryRatePlan = calculateRecoveryRate(concentrateVolumePlan, rawOreVolumePlan);
        
        BigDecimal concentrateVolumeAccumulated = monthlyAccumulated.get("concentrateVolume");
        BigDecimal rawOreVolumeAccumulated = monthlyAccumulated.get("rawOreProcessingVolume");
        BigDecimal recoveryRateAccumulated = calculateRecoveryRate(concentrateVolumeAccumulated, rawOreVolumeAccumulated);
        
        BigDecimal concentrateVolumeWeekly = weeklyData.get("concentrateVolume");
        BigDecimal rawOreVolumeWeekly = weeklyData.get("rawOreProcessingVolume");
        BigDecimal recoveryRateWeekly = calculateRecoveryRate(concentrateVolumeWeekly, rawOreVolumeWeekly);
        
        result.add(createReportItem(String.valueOf(serialNumber++), "综合回收率", "综合回收率", "%",
                recoveryRatePlan, recoveryRateAccumulated, recoveryRateWeekly));

        return result;
    }

    /**
     * 计算回收率
     */
    private BigDecimal calculateRecoveryRate(BigDecimal concentrateVolume, BigDecimal rawOreVolume) {
        if (rawOreVolume == null || rawOreVolume.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (concentrateVolume == null) {
            return BigDecimal.ZERO;
        }
        return concentrateVolume.divide(rawOreVolume, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 创建报表项
     */
    private WeeklyMineralReportTableVo createReportItem(String serialNumber, String name, String subName, String unit,
                                                        BigDecimal monthlyPlan, BigDecimal monthlyAccumulated,
                                                        BigDecimal weeklyCompleted) {
        WeeklyMineralReportTableVo vo = new WeeklyMineralReportTableVo();
        vo.setSerialNumber(serialNumber);
        vo.setName(name);
        vo.setSubName(subName);
        vo.setUnit(unit);
        vo.setMonthlyPlan(monthlyPlan != null ? monthlyPlan : BigDecimal.ZERO);
        vo.setMonthlyAccumulated(monthlyAccumulated != null ? monthlyAccumulated : BigDecimal.ZERO);

        // 计算月完成率
        if (monthlyPlan != null && monthlyPlan.compareTo(BigDecimal.ZERO) > 0 && monthlyAccumulated != null) {
            BigDecimal monthlyRate = monthlyAccumulated.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setMonthlyCompletionRate(monthlyRate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setMonthlyCompletionRate("0.00%");
        }

        // 计算财务周计划（财务月包含4个财务周，所以为月计划的1/4）
        BigDecimal weeklyPlan = monthlyPlan != null ? monthlyPlan.divide(new BigDecimal("4"), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        vo.setWeeklyPlan(weeklyPlan);
        vo.setWeeklyCompleted(weeklyCompleted != null ? weeklyCompleted : BigDecimal.ZERO);

        // 计算周完成率
        if (weeklyPlan.compareTo(BigDecimal.ZERO) > 0 && weeklyCompleted != null) {
            BigDecimal weeklyRate = weeklyCompleted.divide(weeklyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setWeeklyCompletionRate(weeklyRate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setWeeklyCompletionRate("0.00%");
        }

        return vo;
    }
}
