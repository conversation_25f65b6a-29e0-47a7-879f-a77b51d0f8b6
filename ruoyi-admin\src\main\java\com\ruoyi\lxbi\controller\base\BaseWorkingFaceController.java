package com.ruoyi.lxbi.controller.base;

import java.util.List;

import com.ruoyi.common.core.domain.R;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.BaseWorkingFace;
import com.ruoyi.lxbi.service.IBaseWorkingFaceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 中段-工作面配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/base/face")
public class BaseWorkingFaceController extends BaseController {
    @Autowired
    private IBaseWorkingFaceService baseWorkingFaceService;

    /**
     * 查询中段-工作面配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:face:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaseWorkingFace baseWorkingFace) {
        startPage();
        List<BaseWorkingFace> list = baseWorkingFaceService.selectBaseWorkingFaceList(baseWorkingFace);
        return getDataTable(list);
    }

    /**
     * 查询中段-工作面配置列表（不分页）
     */
    @GetMapping("/listAll")
    public R<List<BaseWorkingFace>> listAll(BaseWorkingFace baseWorkingFace) {
        List<BaseWorkingFace> list = baseWorkingFaceService.selectBaseWorkingFaceList(baseWorkingFace);
        return R.ok(list);
    }

    /**
     * 导出中段-工作面配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:face:export')")
    @Log(title = "中段-工作面配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseWorkingFace baseWorkingFace) {
        List<BaseWorkingFace> list = baseWorkingFaceService.selectBaseWorkingFaceList(baseWorkingFace);
        ExcelUtil<BaseWorkingFace> util = new ExcelUtil<BaseWorkingFace>(BaseWorkingFace.class);
        util.exportExcel(response, list, "中段-工作面配置数据");
    }

    /**
     * 获取中段-工作面配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:face:query')")
    @GetMapping(value = "/{workingFaceId}")
    public AjaxResult getInfo(@PathVariable("workingFaceId") Long workingFaceId) {
        return success(baseWorkingFaceService.selectBaseWorkingFaceByWorkingFaceId(workingFaceId));
    }

    /**
     * 新增中段-工作面配置
     */
    @PreAuthorize("@ss.hasPermi('base:face:add')")
    @Log(title = "中段-工作面配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseWorkingFace baseWorkingFace)
    {
        return toAjax(baseWorkingFaceService.insertBaseWorkingFace(baseWorkingFace));
    }

    /**
     * 修改中段-工作面配置
     */
    @PreAuthorize("@ss.hasPermi('base:face:edit')")
    @Log(title = "中段-工作面配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaseWorkingFace baseWorkingFace)
    {
        return toAjax(baseWorkingFaceService.updateBaseWorkingFace(baseWorkingFace));
    }

    /**
     * 删除中段-工作面配置
     */
    @PreAuthorize("@ss.hasPermi('base:face:remove')")
    @Log(title = "中段-工作面配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{workingFaceIds}")
    public AjaxResult remove(@PathVariable Long[] workingFaceIds)
    {
        return toAjax(baseWorkingFaceService.deleteBaseWorkingFaceByWorkingFaceIds(workingFaceIds));
    }
}
