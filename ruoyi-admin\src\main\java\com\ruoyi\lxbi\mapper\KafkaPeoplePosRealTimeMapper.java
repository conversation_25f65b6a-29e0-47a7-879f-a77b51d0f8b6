package com.ruoyi.lxbi.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.lxbi.domain.KafkaPeoplePosRealTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 井下作业人员实时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Mapper
public interface KafkaPeoplePosRealTimeMapper
{
    /**
     * 查询井下作业人员实时数据
     * 
     * @param id 井下作业人员实时数据主键
     * @return 井下作业人员实时数据
     */
    public KafkaPeoplePosRealTime selectKafkaPeoplePosRealTimeById(Long id);

    /**
     * 查询井下作业人员实时数据列表
     * 
     * @param kafkaPeoplePosRealTime 井下作业人员实时数据
     * @return 井下作业人员实时数据集合
     */
    public List<KafkaPeoplePosRealTime> selectKafkaPeoplePosRealTimeList(KafkaPeoplePosRealTime kafkaPeoplePosRealTime);

    /**
     * 根据人员卡编码查询井下作业人员实时数据
     * 
     * @param personnelCardCode 人员卡编码
     * @return 井下作业人员实时数据
     */
    public KafkaPeoplePosRealTime selectByPersonnelCardCode(@Param("personnelCardCode") String personnelCardCode);

    /**
     * 统计截止日期前的下井人次
     * 
     * @param endDate 截止日期
     * @return 下井人次
     */
    public Long countDownWellPersonTimesByEndDate(@Param("endDate") String endDate);

    /**
     * 统计指定时间范围内的下井人次
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 下井人次
     */
    public Long countDownWellPersonTimesByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计指定时间范围内的井下人员分布
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 井下人员分布统计结果
     */
    public List<Map<String, Object>> selectUndergroundPersonnelDistribution(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 新增井下作业人员实时数据
     * 
     * @param kafkaPeoplePosRealTime 井下作业人员实时数据
     * @return 结果
     */
    public int insertKafkaPeoplePosRealTime(KafkaPeoplePosRealTime kafkaPeoplePosRealTime);

    /**
     * 修改井下作业人员实时数据
     * 
     * @param kafkaPeoplePosRealTime 井下作业人员实时数据
     * @return 结果
     */
    public int updateKafkaPeoplePosRealTime(KafkaPeoplePosRealTime kafkaPeoplePosRealTime);

    /**
     * 删除井下作业人员实时数据
     * 
     * @param id 井下作业人员实时数据主键
     * @return 结果
     */
    public int deleteKafkaPeoplePosRealTimeById(Long id);

    /**
     * 批量删除井下作业人员实时数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaPeoplePosRealTimeByIds(Long[] ids);
}
