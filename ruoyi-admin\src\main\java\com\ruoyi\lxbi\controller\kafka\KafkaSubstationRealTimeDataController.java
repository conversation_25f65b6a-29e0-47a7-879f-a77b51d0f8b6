package com.ruoyi.lxbi.controller.kafka;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.KafkaSubstationRealTimeData;
import com.ruoyi.lxbi.service.IKafkaSubstationRealTimeDataService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 分站实时数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@RestController
@RequestMapping("/kafka/SubstationRealTimeData")
public class KafkaSubstationRealTimeDataController extends BaseController {
    @Autowired
    private IKafkaSubstationRealTimeDataService kafkaSubstationRealTimeDataService;

    /**
     * 查询分站实时数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationRealTimeData:list')")
    @GetMapping("/list")
    public TableDataInfo list(KafkaSubstationRealTimeData kafkaSubstationRealTimeData) {
        startPage();
        List<KafkaSubstationRealTimeData> list = kafkaSubstationRealTimeDataService.selectKafkaSubstationRealTimeDataList(kafkaSubstationRealTimeData);
        return getDataTable(list);
    }

    /**
     * 导出分站实时数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationRealTimeData:export')")
    @Log(title = "分站实时数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KafkaSubstationRealTimeData kafkaSubstationRealTimeData) {
        List<KafkaSubstationRealTimeData> list = kafkaSubstationRealTimeDataService.selectKafkaSubstationRealTimeDataList(kafkaSubstationRealTimeData);
        ExcelUtil<KafkaSubstationRealTimeData> util = new ExcelUtil<KafkaSubstationRealTimeData>(KafkaSubstationRealTimeData.class);
        util.exportExcel(response, list, "分站实时数据数据");
    }

    /**
     * 获取分站实时数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationRealTimeData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kafkaSubstationRealTimeDataService.selectKafkaSubstationRealTimeDataById(id));
    }

    /**
     * 新增分站实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationRealTimeData:add')")
    @Log(title = "分站实时数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KafkaSubstationRealTimeData kafkaSubstationRealTimeData)
    {
        return toAjax(kafkaSubstationRealTimeDataService.insertKafkaSubstationRealTimeData(kafkaSubstationRealTimeData));
    }

    /**
     * 修改分站实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationRealTimeData:edit')")
    @Log(title = "分站实时数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KafkaSubstationRealTimeData kafkaSubstationRealTimeData)
    {
        return toAjax(kafkaSubstationRealTimeDataService.updateKafkaSubstationRealTimeData(kafkaSubstationRealTimeData));
    }

    /**
     * 删除分站实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationRealTimeData:remove')")
    @Log(title = "分站实时数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kafkaSubstationRealTimeDataService.deleteKafkaSubstationRealTimeDataByIds(ids));
    }
}
