package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 选矿销售月计划导入对象
 */
@Data
@ExcelImportTemplate(
        key = "plan_mineral_sale_monthly",
        name = "选矿销售月计划导入",
        description = "用于导入选矿销售月计划数据",
        sheetName = "选矿销售月计划"
)
public class PlanMineralSaleMonthlyImport {

    /**
     * 计划月份
     */
    @ExcelProperty(value = "计划月份", index = 0)
    @ExcelRequired(message = "计划月份不能为空")
    @ExcelSelected(prompt = "请输入计划月份，格式：yyyyMM，例如：202501")
    private String planDate;

    /**
     * 铁精粉量
     */
    @ExcelProperty(value = "铁精粉量", index = 1)
    @ExcelSelected(prompt = "请输入铁精粉量")
    private BigDecimal ironConcentrateVolume;

    /**
     * 选矿厂矿仓存矿销售
     */
    @ExcelProperty(value = "选矿厂矿仓存矿销售", index = 2)
    @ExcelSelected(prompt = "请输入选矿厂矿仓存矿销售量")
    private BigDecimal concentratorBinsStockVolume;

    /**
     * 入措施井地表存矿销售
     */
    @ExcelProperty(value = "入措施井地表存矿销售", index = 3)
    @ExcelSelected(prompt = "请输入入措施井地表存矿销售量")
    private BigDecimal serviceShaftSurfaceStockVolume;

    /**
     * 原矿品位-TFe
     */
    @ExcelProperty(value = "原矿品位-TFe(%)", index = 4)
    @ExcelSelected(prompt = "请输入原矿品位TFe，单位：%")
    private BigDecimal rawOreGrade;

    /**
     * 库存
     */
    @ExcelProperty(value = "库存", index = 5)
    @ExcelSelected(prompt = "请输入库存量")
    private BigDecimal stockVolume;
}
