package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 环境安全报警位置分布VO (雷达图数据)
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EnvironmentalSafetyLocationDistributionVO {
    
    /**
     * 位置名称
     */
    private String locationName;
    
    /**
     * 深度标识 (如: -992m, -960m等)
     */
    private String depthLevel;
    
    /**
     * 报警数量
     */
    private Long alarmCount;
    
    /**
     * X坐标 (用于雷达图定位)
     */
    private Double xCoordinate;
    
    /**
     * Y坐标 (用于雷达图定位)
     */
    private Double yCoordinate;
    
    /**
     * 位置代码
     */
    private String locationCode;
}
