<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataOrepassOperationStatsMapper">
    
    <!-- 统一 Stats 结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataOrepassOperationStats" id="DataOrepassOperationStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="totalTrips"           column="total_trips"            />
        <result property="totalOreTons"         column="total_ore_tons"         />
    </resultMap>
    
    <!-- 统一 PeriodStats 结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataOrepassOperationPeriodStats" id="DataOrepassOperationPeriodStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="workingPeriodId"      column="working_period_id"      />
        <result property="workingPeriodName"    column="working_period_name"    />
        <result property="totalTrips"           column="total_trips"            />
        <result property="totalOreTons"         column="total_ore_tons"         />
    </resultMap>

    <!-- 统一 OrepassStats 结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataOrepassOperationOrepassStats" id="DataOrepassOperationOrepassStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="orePassId"            column="ore_pass_id"            />
        <result property="orePassName"          column="ore_pass_name"          />
        <result property="totalTrips"           column="total_trips"            />
        <result property="totalOreTons"         column="total_ore_tons"         />
    </resultMap>

    <!-- 统一 DepartmentStats 结果映射 -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataOrepassOperationDepartmentStats" id="DataOrepassOperationDepartmentStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="projectDepartmentId"  column="project_department_id"  />
        <result property="projectDepartmentName" column="project_department_name" />
        <result property="totalTrips"           column="total_trips"            />
        <result property="totalOreTons"         column="total_ore_tons"         />
    </resultMap>
   
    <!-- 基础统计查询方法 -->
    <select id="selectDailyStats" resultMap="DataOrepassOperationStatsResult">
        select operation_date, total_trips, total_ore_tons
        from vdata_orepass_operation_daily_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date
    </select>
    
    <select id="selectWeeklyStats" resultMap="DataOrepassOperationStatsResult">
        select year, week_number, week_start_date, week_end_date, 
               total_trips, total_ore_tons
        from vdata_orepass_operation_weekly_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number
    </select>
    
    <select id="selectMonthlyStats" resultMap="DataOrepassOperationStatsResult">
        select year, month, total_trips, total_ore_tons
        from vdata_orepass_operation_monthly_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month
    </select>
    
    <!-- 班次统计查询方法 -->
    <select id="selectDailyPeriodStats" resultMap="DataOrepassOperationPeriodStatsResult">
        select operation_date, working_period_id, working_period_name, 
               total_trips, total_ore_tons
        from vdata_orepass_operation_daily_period_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date, working_period_id
    </select>
    
    <select id="selectWeeklyPeriodStats" resultMap="DataOrepassOperationPeriodStatsResult">
        select year, week_number, week_start_date, week_end_date, working_period_id, working_period_name, 
               total_trips, total_ore_tons
        from vdata_orepass_operation_weekly_period_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number, working_period_id
    </select>
    
    <select id="selectMonthlyPeriodStats" resultMap="DataOrepassOperationPeriodStatsResult">
        select year, month, working_period_id, working_period_name, 
               total_trips, total_ore_tons
        from vdata_orepass_operation_monthly_period_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month, working_period_id
    </select>

    <!-- 溜井统计查询方法 -->
    <select id="selectDailyOrepassStats" resultMap="DataOrepassOperationOrepassStatsResult">
        select operation_date, ore_pass_id, ore_pass_name,
               total_trips, total_ore_tons
        from vdata_orepass_operation_daily_orepass_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date, ore_pass_id
    </select>

    <select id="selectWeeklyOrepassStats" resultMap="DataOrepassOperationOrepassStatsResult">
        select year, week_number, week_start_date, week_end_date, ore_pass_id, ore_pass_name,
               total_trips, total_ore_tons
        from vdata_orepass_operation_weekly_orepass_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number, ore_pass_id
    </select>

    <select id="selectMonthlyOrepassStats" resultMap="DataOrepassOperationOrepassStatsResult">
        select year, month, ore_pass_id, ore_pass_name,
               total_trips, total_ore_tons
        from vdata_orepass_operation_monthly_orepass_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month, ore_pass_id
    </select>

    <!-- 项目部门统计查询方法 -->
    <select id="selectDailyDepartmentStats" resultMap="DataOrepassOperationDepartmentStatsResult">
        select operation_date, project_department_id, project_department_name,
               total_trips, total_ore_tons
        from vdata_orepass_operation_daily_department_stats
        <where>
            <if test="startDate != null">
                AND operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND operation_date &lt;= #{endDate}
            </if>
        </where>
        order by operation_date, project_department_id
    </select>

    <select id="selectWeeklyDepartmentStats" resultMap="DataOrepassOperationDepartmentStatsResult">
        select year, week_number, week_start_date, week_end_date, project_department_id, project_department_name,
               total_trips, total_ore_tons
        from vdata_orepass_operation_weekly_department_stats
        <where>
            <if test="startDate != null">
                AND week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND week_end_date &lt;= #{endDate}
            </if>
        </where>
        order by year, week_number, project_department_id
    </select>

    <select id="selectMonthlyDepartmentStats" resultMap="DataOrepassOperationDepartmentStatsResult">
        select year, month, project_department_id, project_department_name,
               total_trips, total_ore_tons
        from vdata_orepass_operation_monthly_department_stats
        <where>
            <if test="startDate != null">
                AND (year > EXTRACT(YEAR FROM #{startDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{startDate}::date) AND month >= EXTRACT(MONTH FROM #{startDate}::date)))
            </if>
            <if test="endDate != null">
                AND (year &lt; EXTRACT(YEAR FROM #{endDate}::date)
                     OR (year = EXTRACT(YEAR FROM #{endDate}::date) AND month &lt;= EXTRACT(MONTH FROM #{endDate}::date)))
            </if>
        </where>
        order by year, month, project_department_id
    </select>

    <!-- 年度统计查询方法 -->
    <select id="selectYearlyStats" resultMap="DataOrepassOperationStatsResult">
        select year, total_trips, total_ore_tons
        from vdata_orepass_operation_yearly_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year
    </select>

    <select id="selectYearlyPeriodStats" resultMap="DataOrepassOperationPeriodStatsResult">
        select year, working_period_id, working_period_name,
               total_trips, total_ore_tons
        from vdata_orepass_operation_yearly_period_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year, working_period_id
    </select>

    <select id="selectYearlyOrepassStats" resultMap="DataOrepassOperationOrepassStatsResult">
        select year, ore_pass_id, ore_pass_name,
               total_trips, total_ore_tons
        from vdata_orepass_operation_yearly_orepass_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year, ore_pass_id
    </select>

    <select id="selectYearlyDepartmentStats" resultMap="DataOrepassOperationDepartmentStatsResult">
        select year, project_department_id, project_department_name,
               total_trips, total_ore_tons
        from vdata_orepass_operation_yearly_department_stats
        <where>
            <if test="startDate != null">
                AND year >= EXTRACT(YEAR FROM #{startDate}::date)
            </if>
            <if test="endDate != null">
                AND year &lt;= EXTRACT(YEAR FROM #{endDate}::date)
            </if>
        </where>
        order by year, project_department_id
    </select>
</mapper>
