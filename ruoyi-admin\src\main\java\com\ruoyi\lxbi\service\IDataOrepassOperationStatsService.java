package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DataOrepassOperationStatsRequest;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationPeriodStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationOrepassStats;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationDepartmentStats;

import java.util.List;

/**
 * 溜井放矿数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IDataOrepassOperationStatsService {

    /**
     * 查询统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 统计数据集合
     */
    public List<DataOrepassOperationStats> selectStatsList(DataOrepassOperationStatsRequest request, String viewType);

    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    public List<DataOrepassOperationPeriodStats> selectPeriodStatsList(DataOrepassOperationStatsRequest request, String viewType);

    /**
     * 查询溜井统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 溜井统计数据集合
     */
    public List<DataOrepassOperationOrepassStats> selectOrepassStatsList(DataOrepassOperationStatsRequest request, String viewType);

    /**
     * 查询项目部门统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合
     */
    public List<DataOrepassOperationDepartmentStats> selectDepartmentStatsList(DataOrepassOperationStatsRequest request, String viewType);

}
