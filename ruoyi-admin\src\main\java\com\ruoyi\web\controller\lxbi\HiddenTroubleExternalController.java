package com.ruoyi.web.controller.lxbi;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.web.service.HiddenTroubleExternalService;
import com.ruoyi.web.dto.HiddenTroubleStatisticsData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 第三方隐患数据对接Controller
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@RestController
@RequestMapping("/lxbi/hiddenTrouble/external")
public class HiddenTroubleExternalController extends BaseController {

    @Autowired
    private HiddenTroubleExternalService hiddenTroubleExternalService;

    /**
     * 获取隐患统计数据（对接第三方API）
     *
     * @param startTime 开始时间（格式：yyyy-MM-dd）
     * @param endTime 结束时间（格式：yyyy-MM-dd）
     * @return 统计结果
     */
    @Anonymous
    @GetMapping("/statistics")
    @Log(title = "隐患统计", businessType = BusinessType.OTHER)
    public AjaxResult getStatistics(@RequestParam(required = false) String startTime,
                                   @RequestParam(required = false) String endTime) {
        try {
            log.info("获取隐患统计数据 - 开始时间: {}, 结束时间: {}", startTime, endTime);

            Map<String, Object> response = hiddenTroubleExternalService.getStatistics(startTime, endTime);

            if (response != null && "0".equals(String.valueOf(response.get("code")))) {
                log.info("隐患统计数据获取成功");
                return success(response.get("data"));
            } else {
                log.error("第三方API返回错误: {}", response != null ? response.get("message") : "未知错误");
                return error("获取隐患统计数据失败: " + (response != null ? response.get("message") : "未知错误"));
            }
        } catch (Exception e) {
            log.error("获取隐患统计数据异常", e);
            return error("获取隐患统计数据异常: " + e.getMessage());
        }
    }

    /**
     * 获取隐患列表数据（对接第三方API）
     *
     * @param current 当前页
     * @param size 每页大小
     * @param state 状态（0:待整改 1:已驳回 2:已整改 3:待复查 4:已超期）
     * @param startTime 开始时间（格式：yyyy-MM-dd）
     * @param endTime 结束时间（格式：yyyy-MM-dd）
     * @return 列表结果
     */
    @Anonymous
    @GetMapping("/statisticsPage")
    @Log(title = "隐患列表", businessType = BusinessType.OTHER)
    public AjaxResult getStatisticsPage(@RequestParam(defaultValue = "1") Integer current,
                                       @RequestParam(defaultValue = "10") Integer size,
                                       @RequestParam(required = false) String state,
                                       @RequestParam(required = false) String startTime,
                                       @RequestParam(required = false) String endTime) {
        try {
            log.info("获取隐患列表数据 - 页码: {}, 大小: {}, 状态: {}, 开始时间: {}, 结束时间: {}",
                current, size, state, startTime, endTime);

            Map<String, Object> response =
                hiddenTroubleExternalService.getStatisticsPage(current, size, state, startTime, endTime);

            if (response != null && "0".equals(String.valueOf(response.get("code")))) {
                log.info("隐患列表数据获取成功");
                return success(response.get("data"));
            } else {
                log.error("第三方API返回错误: {}", response != null ? response.get("message") : "未知错误");
                return error("获取隐患列表数据失败: " + (response != null ? response.get("message") : "未知错误"));
            }
        } catch (Exception e) {
            log.error("获取隐患列表数据异常", e);
            return error("获取隐患列表数据异常: " + e.getMessage());
        }
    }

    /**
     * POST方式获取隐患统计数据（兼容第三方API格式）
     *
     * @param request 请求参数
     * @return 统计结果
     */
    @Anonymous
    @PostMapping("/statistics")
    @Log(title = "隐患统计", businessType = BusinessType.OTHER)
    public AjaxResult postStatistics(@RequestBody(required = false) Map<String, Object> request) {
        try {
            String startTime = request != null ? (String) request.get("startTime") : null;
            String endTime = request != null ? (String) request.get("endTime") : null;

            log.info("POST获取隐患统计数据 - 开始时间: {}, 结束时间: {}", startTime, endTime);

            Map<String, Object> response = hiddenTroubleExternalService.getStatistics(startTime, endTime);

            // 直接返回第三方API的响应格式
            return success(response);
        } catch (Exception e) {
            log.error("POST获取隐患统计数据异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 500);
            errorResult.put("data", new HashMap<>());
            errorResult.put("message", "获取数据异常: " + e.getMessage());
            return success(errorResult);
        }
    }

    /**
     * POST方式获取隐患列表数据（兼容第三方API格式）
     *
     * @param request 请求参数
     * @return 列表结果
     */
    @Anonymous
    @PostMapping("/statisticsPage")
    @Log(title = "隐患列表", businessType = BusinessType.OTHER)
    public AjaxResult postStatisticsPage(@RequestBody Map<String, Object> request) {
        try {
            log.info("POST获取隐患列表数据 - 请求参数: {}", request);

            Integer current = request.get("current") != null ? (Integer) request.get("current") : 1;
            Integer size = request.get("size") != null ? (Integer) request.get("size") : 10;
            String state = (String) request.get("state");
            String startTime = (String) request.get("startTime");
            String endTime = (String) request.get("endTime");

            Map<String, Object> response =
                hiddenTroubleExternalService.getStatisticsPage(current, size, state, startTime, endTime);

            // 直接返回第三方API的响应格式
            return success(response);
        } catch (Exception e) {
            log.error("POST获取隐患列表数据异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 500);
            errorResult.put("data", new HashMap<>());
            errorResult.put("message", "获取数据异常: " + e.getMessage());
            return success(errorResult);
        }
    }

    /**
     * 测试第三方API连接
     * 
     * @return 测试结果
     */
    @Anonymous
    @GetMapping("/testConnection")
    @Log(title = "API连接测试", businessType = BusinessType.OTHER)
    public AjaxResult testConnection() {
        try {
            log.info("开始测试第三方隐患管理系统API连接");
            
            boolean isConnected = hiddenTroubleExternalService.testConnection();
            
            Map<String, Object> result = new HashMap<>();
            result.put("connected", isConnected);
            result.put("message", isConnected ? "连接成功" : "连接失败");
            result.put("timestamp", System.currentTimeMillis());
            
            if (isConnected) {
                log.info("第三方API连接测试成功");
                return success(result);
            } else {
                log.warn("第三方API连接测试失败");
                return AjaxResult.error("连接失败").put("data", result);
            }
        } catch (Exception e) {
            log.error("第三方API连接测试异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("connected", false);
            errorResult.put("message", "连接测试异常: " + e.getMessage());
            errorResult.put("timestamp", System.currentTimeMillis());
            return AjaxResult.error("连接测试异常: " + e.getMessage()).put("data", errorResult);
        }
    }

    /**
     * 获取API配置信息
     *
     * @return 配置信息
     */
    @Anonymous
    @GetMapping("/config")
    public AjaxResult getConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("statisticsUrl", "/lxbi/hiddenTrouble/external/statistics");
            config.put("statisticsPageUrl", "/lxbi/hiddenTrouble/external/statisticsPage");
            config.put("testConnectionUrl", "/lxbi/hiddenTrouble/external/testConnection");
            config.put("description", "第三方隐患管理系统API对接接口");

            return success(config);
        } catch (Exception e) {
            log.error("获取API配置信息异常", e);
            return error("获取配置信息失败: " + e.getMessage());
        }
    }

    /**
     * 直接代理第三方隐患统计接口（完全兼容第三方API格式）
     *
     * @param request 请求参数
     * @return 统计结果
     */
    @Anonymous
    @PostMapping("/api/defense/homepagerule/statistics")
    @Log(title = "隐患统计代理", businessType = BusinessType.OTHER)
    public Object proxyStatistics(@RequestBody(required = false) Map<String, Object> request) {
        try {
            log.info("代理第三方隐患统计接口 - 请求参数: {}", request);

            String startTime = request != null ? (String) request.get("startTime") : null;
            String endTime = request != null ? (String) request.get("endTime") : null;

            Map<String, Object> response = hiddenTroubleExternalService.getStatistics(startTime, endTime);

            log.info("代理第三方隐患统计接口 - 响应: {}", response);
            return response;
        } catch (Exception e) {
            log.error("代理第三方隐患统计接口异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 500);
            errorResult.put("data", new HiddenTroubleStatisticsData());
            errorResult.put("message", "获取数据异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 直接代理第三方隐患列表接口（完全兼容第三方API格式）
     *
     * @param request 请求参数
     * @return 列表结果
     */
    @Anonymous
    @PostMapping("/api/defense/homepagerule/statisticsPage")
    @Log(title = "隐患列表代理", businessType = BusinessType.OTHER)
    public Object proxyStatisticsPage(@RequestBody Map<String, Object> request) {
        try {
            log.info("代理第三方隐患列表接口 - 请求参数: {}", request);

            Integer current = request.get("current") != null ? (Integer) request.get("current") : 1;
            Integer size = request.get("size") != null ? (Integer) request.get("size") : 10;
            String state = (String) request.get("state");
            String startTime = (String) request.get("startTime");
            String endTime = (String) request.get("endTime");

            Map<String, Object> response =
                hiddenTroubleExternalService.getStatisticsPage(current, size, state, startTime, endTime);

            log.info("代理第三方隐患列表接口 - 响应记录数: {}",
                response.get("data") != null ? "有数据" : "无数据");
            return response;
        } catch (Exception e) {
            log.error("代理第三方隐患列表接口异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 500);
            errorResult.put("data", new HashMap<>());
            errorResult.put("message", "获取数据异常: " + e.getMessage());
            return errorResult;
        }
    }
}
