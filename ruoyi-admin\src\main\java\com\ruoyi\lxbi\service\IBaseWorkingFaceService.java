package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.BaseWorkingFace;

/**
 * 中段-工作面配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface IBaseWorkingFaceService 
{
    /**
     * 查询中段-工作面配置
     * 
     * @param workingFaceId 中段-工作面配置主键
     * @return 中段-工作面配置
     */
    public BaseWorkingFace selectBaseWorkingFaceByWorkingFaceId(Long workingFaceId);

    /**
     * 查询中段-工作面配置列表
     * 
     * @param baseWorkingFace 中段-工作面配置
     * @return 中段-工作面配置集合
     */
    public List<BaseWorkingFace> selectBaseWorkingFaceList(BaseWorkingFace baseWorkingFace);

    /**
     * 新增中段-工作面配置
     * 
     * @param baseWorkingFace 中段-工作面配置
     * @return 结果
     */
    public int insertBaseWorkingFace(BaseWorkingFace baseWorkingFace);

    /**
     * 修改中段-工作面配置
     * 
     * @param baseWorkingFace 中段-工作面配置
     * @return 结果
     */
    public int updateBaseWorkingFace(BaseWorkingFace baseWorkingFace);

    /**
     * 批量删除中段-工作面配置
     * 
     * @param workingFaceIds 需要删除的中段-工作面配置主键集合
     * @return 结果
     */
    public int deleteBaseWorkingFaceByWorkingFaceIds(Long[] workingFaceIds);

    /**
     * 删除中段-工作面配置信息
     * 
     * @param workingFaceId 中段-工作面配置主键
     * @return 结果
     */
    public int deleteBaseWorkingFaceByWorkingFaceId(Long workingFaceId);
}
