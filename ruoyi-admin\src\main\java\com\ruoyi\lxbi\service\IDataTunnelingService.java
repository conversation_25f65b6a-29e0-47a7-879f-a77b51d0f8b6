package com.ruoyi.lxbi.service;

import java.util.Date;
import java.util.List;
import com.ruoyi.lxbi.domain.DataTunneling;
import com.ruoyi.lxbi.domain.request.DataTunnelingBatchDto;
import com.ruoyi.lxbi.domain.response.DataTunnelingVo;

/**
 * 掘进数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IDataTunnelingService 
{
    /**
     * 查询掘进数据
     * 
     * @param id 掘进数据主键
     * @return 掘进数据
     */
    public DataTunneling selectDataTunnelingById(Long id);

    /**
     * 查询掘进数据列表
     *
     * @param dataTunneling 掘进数据
     * @return 掘进数据集合
     */
    public List<DataTunnelingVo> selectDataTunnelingList(DataTunneling dataTunneling);

    /**
     * 新增掘进数据
     * 
     * @param dataTunneling 掘进数据
     * @return 结果
     */
    public int insertDataTunneling(DataTunneling dataTunneling);

    /**
     * 修改掘进数据
     * 
     * @param dataTunneling 掘进数据
     * @return 结果
     */
    public int updateDataTunneling(DataTunneling dataTunneling);

    /**
     * 批量删除掘进数据
     * 
     * @param ids 需要删除的掘进数据主键集合
     * @return 结果
     */
    public int deleteDataTunnelingByIds(Long[] ids);

    /**
     * 删除掘进数据信息
     *
     * @param id 掘进数据主键
     * @return 结果
     */
    public int deleteDataTunnelingById(Long id);

    /**
     * 根据作业日期和项目部门查询掘进数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 掘进数据集合
     */
    public List<DataTunnelingVo> selectDataTunnelingByOperationDateAndProject(Date operationDate, Long projectDepartmentId);

    /**
     * 批量保存掘进数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    public int batchSaveDataTunneling(List<DataTunnelingBatchDto> batchDataList);
}
