package com.ruoyi.lxbi.admin.service.impl;

import com.ruoyi.lxbi.admin.domain.KafkaPeoplePositionTimeOver;
import com.ruoyi.lxbi.admin.service.IPersonnelSafetyStatService;
import com.ruoyi.lxbi.admin.service.IKafkaPeoplePositionTimeOverService;
import com.ruoyi.lxbi.admin.service.IKafkaPeoplePositionBasicInfoService;
import com.ruoyi.lxbi.admin.service.IKafkaPeopleLocationInfoService;
import com.ruoyi.lxbi.admin.service.IKafkaPeopleSosService;
import com.ruoyi.lxbi.admin.domain.KafkaPeoplePositionTimeOver;
import com.ruoyi.lxbi.admin.domain.KafkaPeopleLocationInfo;
import com.ruoyi.lxbi.domain.vo.*;
import com.ruoyi.lxbi.service.IApiAiAlarmService;
import com.ruoyi.lxbi.admin.service.IKafkaPeoplePosRealTimeService;
import com.ruoyi.lxbi.mapper.KafkaPeoplePosRealTimeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.ruoyi.lxbi.domain.ApiAiAlarm;

/**
 * 人员安全统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class PersonnelSafetyStatServiceImpl implements IPersonnelSafetyStatService {

    @Autowired
    private IKafkaPeoplePositionTimeOverService kafkaPeoplePositionTimeOverService;

    @Autowired
    private IKafkaPeoplePositionBasicInfoService kafkaPeoplePositionBasicInfoService;

    @Autowired
    private IKafkaPeopleLocationInfoService kafkaPeopleLocationInfoService;

    @Autowired
    private IKafkaPeopleSosService kafkaPeopleSosService;

    @Autowired
    private IApiAiAlarmService apiAiAlarmService;

    @Autowired
    private IKafkaPeoplePosRealTimeService kafkaPeoplePosRealTimeService;

    @Autowired
    private KafkaPeoplePosRealTimeMapper kafkaPeoplePosRealTimeMapper;

    /**
     * 获取人员安全概览统计
     */
    @Override
    public PersonnelSafetyOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate) {
        try {
            log.info("获取人员安全概览统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            PersonnelSafetyOverviewVO overview = new PersonnelSafetyOverviewVO();

            // 从真实数据源获取统计数据
            // 1. 下井人次 - 从井下作业人员实时数据统计
            Long downWellCount = kafkaPeoplePosRealTimeMapper.countDownWellPersonTimesByDateRange(startDate, endDate);
            overview.setDownWellCount(downWellCount != null ? downWellCount : 0L);

            // 2. 区域超时 - 从超时定位表统计
            Long areaTimeoutCount = kafkaPeoplePositionTimeOverService.countAreaTimeoutByDateRange(startDate, endDate);
            overview.setAreaTimeoutCount(areaTimeoutCount != null ? areaTimeoutCount : 0L);

            // 3. 教育培训 - 暂时使用模拟数据，后续可接入培训系统
            overview.setEducationTrainingCount(2L);

            // 4. 违规行为 - 使用AI报警系统的真实数据
            Long violationCount = getViolationCountFromAiAlarm(startDate, endDate);
            overview.setViolationCount(violationCount);

            overview.setStartDate(startDate);
            overview.setEndDate(endDate);
            overview.setPeriod(viewType);

            return overview;
        } catch (Exception e) {
            log.error("获取人员安全概览统计失败", e);
            throw new RuntimeException("获取人员安全概览统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取主要超时组分布统计
     */
    @Override
    public List<TimeoutGroupDistributionVO> getTimeoutGroupDistribution(String viewType, String startDate, String endDate) {
        try {
            log.info("获取主要超时组分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            List<TimeoutGroupDistributionVO> distributionList = new ArrayList<>();

            // 从数据库获取组别超时分布统计
            List<Map<String, Object>> groupStats = kafkaPeoplePositionTimeOverService
                    .selectTimeoutGroupDistributionByDateRange(startDate, endDate);

            if (groupStats != null && !groupStats.isEmpty()) {
                // 计算总数用于百分比计算
                long totalCount = groupStats.stream()
                        .mapToLong(stat -> ((Number) stat.get("timeout_count")).longValue())
                        .sum();

                for (Map<String, Object> stat : groupStats) {
                    TimeoutGroupDistributionVO distribution = new TimeoutGroupDistributionVO();
                    distribution.setGroupName((String) stat.get("group_name"));

                    Long timeoutCount = ((Number) stat.get("timeout_count")).longValue();
                    distribution.setTimeoutCount(timeoutCount);

                    // 计算百分比
                    if (totalCount > 0) {
                        BigDecimal percentage = new BigDecimal(timeoutCount)
                                .divide(new BigDecimal(totalCount), 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                        distribution.setPercentage(percentage);
                    } else {
                        distribution.setPercentage(BigDecimal.ZERO);
                    }

                    distributionList.add(distribution);
                }
            }

            return distributionList;
        } catch (Exception e) {
            log.error("获取主要超时组分布统计失败", e);
            throw new RuntimeException("获取主要超时组分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取区域超时人员名单
     */
    @Override
    public List<AreaTimeoutPersonnelVO> getAreaTimeoutPersonnel(String viewType, String startDate, String endDate) {
        try {
            log.info("获取区域超时人员名单，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            List<AreaTimeoutPersonnelVO> personnelList = new ArrayList<>();

            // 从数据库获取超时人员名单，限制返回前10条
            List<KafkaPeoplePositionTimeOver> timeoutRecords = kafkaPeoplePositionTimeOverService
                    .selectTimeoutPersonnelByDateRange(startDate, endDate, 10);

            if (timeoutRecords != null && !timeoutRecords.isEmpty()) {
                for (KafkaPeoplePositionTimeOver record : timeoutRecords) {
                    AreaTimeoutPersonnelVO personnel = new AreaTimeoutPersonnelVO();

                    // 设置人员基本信息
                    personnel.setPersonnelName(record.getPersonName());

                    // 获取区域信息并设置区域名称
                    String areaDisplayName = record.getAreaCode() != null ? record.getAreaCode() : "未知区域";
                    if (record.getAreaCode() != null) {
                        try {
                            KafkaPeopleLocationInfo areaInfo = kafkaPeopleLocationInfoService.selectByAreaCode(record.getAreaCode());
                            if (areaInfo != null && areaInfo.getAreaName() != null) {
                                areaDisplayName = areaInfo.getAreaName() + "(" + record.getAreaCode() + ")";
                            }
                        } catch (Exception e) {
                            log.warn("获取区域信息失败，区域编码: {}", record.getAreaCode(), e);
                        }
                    }
                    personnel.setCurrentTimeoutArea(areaDisplayName);

                    // 超时次数需要从聚合查询中获取，这里暂时设置为1
                    personnel.setTimeoutCount(1L);

                    // 平均驻留时间计算（简化处理）
                    if (record.getEnterCurrentAreaTime() != null && record.getDataUploadTime() != null) {
                        long diffInMillis = record.getDataUploadTime().getTime() - record.getEnterCurrentAreaTime().getTime();
                        long hours = diffInMillis / (1000 * 60 * 60);
                        personnel.setAverageStayTime(hours + "小时");
                    } else {
                        personnel.setAverageStayTime("未知");
                    }

                    personnelList.add(personnel);
                }
            }

            return personnelList;
        } catch (Exception e) {
            log.error("获取区域超时人员名单失败", e);
            throw new RuntimeException("获取区域超时人员名单失败: " + e.getMessage());
        }
    }

    /**
     * 获取来救数量趋势统计
     */
    @Override
    public List<RescueQuantityTrendVO> getRescueQuantityTrend(String viewType, String startDate, String endDate) {
        try {
            log.info("获取来救数量趋势统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            List<RescueQuantityTrendVO> trendList = new ArrayList<>();

            // 基于超时数据生成趋势统计
            // 获取按日期统计的超时数据
            List<Map<String, Object>> timeoutCountByDate = kafkaPeoplePositionTimeOverService.selectTimeoutCountByDate(startDate, endDate);

            // 根据viewType生成日期序列
            List<String> dateList = generateDateSequence(viewType, startDate, endDate);

            // 创建日期到超时数据的映射
            Map<String, Map<String, Object>> timeoutDataMap = new HashMap<>();
            if (timeoutCountByDate != null) {
                for (Map<String, Object> data : timeoutCountByDate) {
                    String dateStr = data.get("date").toString();
                    timeoutDataMap.put(dateStr, data);
                }
            }

            for (String date : dateList) {
                RescueQuantityTrendVO trend = new RescueQuantityTrendVO();
                trend.setDate(date);
                trend.setShortDate(date.substring(5)); // MM-dd

                // 从统计数据中获取超时次数
                Map<String, Object> dayData = timeoutDataMap.get(date);
                if (dayData != null) {
                    Object timeoutCount = dayData.get("timeout_count");
                    trend.setRescueCount(timeoutCount != null ? ((Number) timeoutCount).longValue() : 0L);

                    Object personnelCount = dayData.get("timeout_personnel_count");
                    // 将超时人员数作为培训需求指标
                    trend.setTrainingCount(personnelCount != null ? ((Number) personnelCount).longValue() : 0L);
                } else {
                    trend.setRescueCount(0L);
                    trend.setTrainingCount(0L);
                }

                // 计算百分比
                Long totalCount = trend.getRescueCount() + trend.getTrainingCount();
                if (totalCount > 0) {
                    trend.setRescuePercentage(new BigDecimal(trend.getRescueCount())
                            .divide(new BigDecimal(totalCount), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")));
                    trend.setTrainingPercentage(new BigDecimal(trend.getTrainingCount())
                            .divide(new BigDecimal(totalCount), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")));
                } else {
                    trend.setRescuePercentage(BigDecimal.ZERO);
                    trend.setTrainingPercentage(BigDecimal.ZERO);
                }

                trendList.add(trend);
            }

            return trendList;
        } catch (Exception e) {
            log.error("获取来救数量趋势统计失败", e);
            throw new RuntimeException("获取来救数量趋势统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取人员安全综合统计数据（用于仪表板）
     */
    @Override
    public PersonnelSafetyDashboardVO getDashboardData(String viewType, String startDate, String endDate) {
        try {
            log.info("获取人员安全综合统计数据，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            PersonnelSafetyDashboardVO dashboardData = new PersonnelSafetyDashboardVO();

            // 获取概览统计
            dashboardData.setOverview(getOverviewStatistics(viewType, startDate, endDate));

            // 获取主要超时组分布
            dashboardData.setTimeoutGroupDistribution(getTimeoutGroupDistribution(viewType, startDate, endDate));

            // 获取区域超时人员名单
            dashboardData.setAreaTimeoutPersonnel(getAreaTimeoutPersonnel(viewType, startDate, endDate));

            // 获取来救数量趋势
            dashboardData.setRescueQuantityTrend(getRescueQuantityTrend(viewType, startDate, endDate));

            return dashboardData;
        } catch (Exception e) {
            log.error("获取人员安全综合统计数据失败", e);
            throw new RuntimeException("获取人员安全综合统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取人员安全小结统计
     */
    @Override
    public PersonnelSafetySummaryVO getSafetySummary(String viewType, String startDate, String endDate) {
        try {
            log.info("获取人员安全小结统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            PersonnelSafetySummaryVO summary = new PersonnelSafetySummaryVO();

            // 1. 当前井下作业人员数 - 基于最近的活跃人员数据
            Long currentUndergroundPersonnel = kafkaPeoplePositionBasicInfoService.countActivePersonnelByDateRange(endDate, endDate);
            summary.setCurrentUndergroundPersonnel(currentUndergroundPersonnel != null ? currentUndergroundPersonnel : 0L);

            // 2. 检测到违规行为数 - 使用AI报警系统的真实数据
            Long detectedViolations = getViolationCountFromAiAlarm(startDate, endDate);
            summary.setDetectedViolations(detectedViolations);

            // 3. 人员求救报警数 - 基于求救数据统计
            Long distressAlarms = kafkaPeopleSosService.countSosCallsByDateRange(startDate, endDate);
            summary.setPersonnelDistressAlarms(distressAlarms != null ? distressAlarms : 0L);

            // 4. 存在超时人员数 - 基于超时定位数据统计
            Long timeoutPersonnel = kafkaPeoplePositionTimeOverService.countTimeoutPersonnelByDateRange(startDate, endDate);
            summary.setTimeoutPersonnel(timeoutPersonnel != null ? timeoutPersonnel : 0L);

            summary.setStartDate(startDate);
            summary.setEndDate(endDate);
            summary.setPeriod(viewType);

            return summary;
        } catch (Exception e) {
            log.error("获取人员安全小结统计失败", e);
            throw new RuntimeException("获取人员安全小结统计失败: " + e.getMessage());
        }
    }

    /**
     * 生成人员安全小结文本描述
     */
    @Override
    public PersonnelSafetySummaryTextVO generateSafetySummaryText(String viewType, String startDate, String endDate) {
        try {
            log.info("生成人员安全小结文本，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 获取人员安全小结统计数据
            PersonnelSafetySummaryVO summary = getSafetySummary(viewType, startDate, endDate);

            // 生成文本描述
            List<String> summaryTexts = new ArrayList<>();

            // 第一条：当前井下作业人员情况
            String text1 = String.format("当前井下作业人员 %d人。", summary.getCurrentUndergroundPersonnel());
            summaryTexts.add(text1);

            // 第二条：违规行为检测情况
            if (summary.getDetectedViolations() > 0) {
                String text2 = String.format("检测到违规行为 %d起（包括未戴安全帽、禁区停留）。", summary.getDetectedViolations());
                summaryTexts.add(text2);
            } else {
                summaryTexts.add("未检测到违规行为，安全状况良好。");
            }

            // 第三条：人员求救报警情况
            if (summary.getPersonnelDistressAlarms() > 0) {
                String text3 = String.format("人员求救报警 %d起，已于 3 分钟内响应并处理。", summary.getPersonnelDistressAlarms());
                summaryTexts.add(text3);
            } else {
                summaryTexts.add("无人员求救报警，作业环境安全。");
            }

            // 第四条：超时人员情况
            if (summary.getTimeoutPersonnel() > 0) {
                String text4 = String.format("存在 %d名人员井下滞留超过规定时间。", summary.getTimeoutPersonnel());
                summaryTexts.add(text4);

                // 添加超时区域详情
                try {
                    List<AreaTimeoutPersonnelVO> timeoutPersonnelList = getAreaTimeoutPersonnel(viewType, startDate, endDate);
                    if (timeoutPersonnelList != null && !timeoutPersonnelList.isEmpty()) {
                        StringBuilder areaInfo = new StringBuilder("主要涉及区域：");
                        for (int i = 0; i < Math.min(3, timeoutPersonnelList.size()); i++) {
                            if (i > 0) areaInfo.append("、");
                            areaInfo.append(timeoutPersonnelList.get(i).getCurrentTimeoutArea());
                        }
                        areaInfo.append("。");
                        summaryTexts.add(areaInfo.toString());
                    }
                } catch (Exception e) {
                    log.warn("获取超时区域详情失败", e);
                }
            } else {
                summaryTexts.add("所有人员作业时间正常，无超时情况。");
            }

            // 构建返回对象
            PersonnelSafetySummaryTextVO summaryText = new PersonnelSafetySummaryTextVO();
            summaryText.setTitle("人员安全");
            summaryText.setSummaryTexts(summaryTexts);
            summaryText.setStartDate(startDate);
            summaryText.setEndDate(endDate);
            summaryText.setViewType(viewType);
            summaryText.setGenerateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            return summaryText;
        } catch (Exception e) {
            log.error("生成人员安全小结文本失败", e);
            throw new RuntimeException("生成人员安全小结文本失败: " + e.getMessage());
        }
    }

    /**
     * 获取人员安全简化概览
     */
    @Override
    public PersonnelSafetySimpleOverviewVO getSimpleOverview(String viewType, String startDate, String endDate) {
        try {
            log.info("获取人员安全简化概览，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            PersonnelSafetySimpleOverviewVO simpleOverview = new PersonnelSafetySimpleOverviewVO();

            // 1. 下井人数 - 基于指定日期范围的活跃人员统计
            Long downWellPersonnel = kafkaPeoplePositionBasicInfoService.countActivePersonnelByDateRange(startDate, endDate);
            simpleOverview.setDownWellPersonnel(downWellPersonnel != null ? downWellPersonnel : 0L);

            // 2. 违规行为数 - 使用AI报警系统的真实数据
            Long violationBehavior = getViolationCountFromAiAlarm(startDate, endDate);
            simpleOverview.setViolationBehavior(violationBehavior);

            // 3. 求救数 - 基于求救数据统计
            Long distressCalls = kafkaPeopleSosService.countSosCallsByDateRange(startDate, endDate);
            simpleOverview.setDistressCalls(distressCalls != null ? distressCalls : 0L);

            simpleOverview.setStartDate(startDate);
            simpleOverview.setEndDate(endDate);
            simpleOverview.setPeriod(viewType);

            return simpleOverview;
        } catch (Exception e) {
            log.error("获取人员安全简化概览失败", e);
            throw new RuntimeException("获取人员安全简化概览失败: " + e.getMessage());
        }
    }

    /**
     * 根据视图类型生成日期序列
     *
     * @param viewType 视图类型（day/week/month）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期序列
     */
    private List<String> generateDateSequence(String viewType, String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();

        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            // 根据视图类型确定步长
            int stepDays = 1;
            switch (viewType.toLowerCase()) {
                case "week":
                    stepDays = 7;
                    break;
                case "month":
                    stepDays = 30;
                    break;
                default: // day
                    stepDays = 1;
                    break;
            }

            LocalDate current = start;
            while (!current.isAfter(end)) {
                dateList.add(current.toString());
                current = current.plusDays(stepDays);
            }

            // 如果没有生成任何日期，至少返回结束日期
            if (dateList.isEmpty()) {
                dateList.add(endDate);
            }

        } catch (Exception e) {
            log.warn("生成日期序列失败，使用默认日期: {}", e.getMessage());
            // 如果解析失败，返回最近7天的日期
            LocalDate today = LocalDate.now();
            for (int i = 6; i >= 0; i--) {
                dateList.add(today.minusDays(i).toString());
            }
        }

        return dateList;
    }

    /**
     * 从AI报警系统获取违规行为数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 违规行为数量
     */
    private Long getViolationCountFromAiAlarm(String startDate, String endDate) {
        try {
            // 解析日期
            LocalDateTime startDateTime = LocalDate.parse(startDate).atStartOfDay();
            LocalDateTime endDateTime = LocalDate.parse(endDate).atTime(23, 59, 59);

            // 从AI报警系统查询人员违规相关的报警数据
            List<ApiAiAlarm> alarms = apiAiAlarmService.getByTimeRange(startDateTime, endDateTime);

            // 筛选人员违规相关的报警类型
            long violationCount = alarms.stream()
                .filter(alarm -> isPersonnelViolationAlarm(alarm))
                .count();

            log.info("从AI报警系统获取违规行为数量: {} (时间范围: {} - {})", violationCount, startDate, endDate);

            return violationCount;

        } catch (Exception e) {
            log.error("从AI报警系统获取违规行为数量失败", e);
            // 如果获取失败，返回默认值
            return 0L;
        }
    }

    /**
     * 判断是否为人员违规相关的报警
     *
     * @param alarm AI报警数据
     * @return 是否为人员违规报警
     */
    private boolean isPersonnelViolationAlarm(ApiAiAlarm alarm) {
        if (alarm == null || alarm.getAlarmType() == null) {
            return false;
        }

        String alarmType = alarm.getAlarmType().toUpperCase();
        String alarmTitle = alarm.getAlarmTitle() != null ? alarm.getAlarmTitle().toUpperCase() : "";
        String alarmDescription = alarm.getAlarmDescription() != null ? alarm.getAlarmDescription().toUpperCase() : "";

        // 定义人员违规相关的关键词
        String[] violationKeywords = {
            "人员违规", "PERSONNEL_VIOLATION", "STAFF_VIOLATION",
            "未佩戴安全帽", "NO_HELMET", "HELMET_VIOLATION",
            "未穿安全服", "NO_SAFETY_CLOTHING", "CLOTHING_VIOLATION",
            "区域入侵", "AREA_INTRUSION", "UNAUTHORIZED_ACCESS",
            "人员超时", "PERSONNEL_OVERTIME", "STAFF_OVERTIME",
            "违规操作", "ILLEGAL_OPERATION", "VIOLATION_OPERATION",
            "安全违规", "SAFETY_VIOLATION", "SAFETY_BREACH",
            "人员安全", "PERSONNEL_SAFETY", "STAFF_SAFETY",
            "行为违规", "BEHAVIOR_VIOLATION", "CONDUCT_VIOLATION",
            "作业违规", "WORK_VIOLATION", "OPERATION_VIOLATION"
        };

        // 检查报警类型、标题、描述中是否包含违规关键词
        for (String keyword : violationKeywords) {
            if (alarmType.contains(keyword) ||
                alarmTitle.contains(keyword) ||
                alarmDescription.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取人员违规行为详细列表（用于详细查询）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 违规行为详细列表
     */
    public List<ApiAiAlarm> getViolationDetailList(String startDate, String endDate) {
        try {
            // 解析日期
            LocalDateTime startDateTime = LocalDate.parse(startDate).atStartOfDay();
            LocalDateTime endDateTime = LocalDate.parse(endDate).atTime(23, 59, 59);

            // 从AI报警系统查询人员违规相关的报警数据
            List<ApiAiAlarm> alarms = apiAiAlarmService.getByTimeRange(startDateTime, endDateTime);

            // 筛选并返回人员违规相关的报警
            return alarms.stream()
                .filter(alarm -> isPersonnelViolationAlarm(alarm))
                .collect(java.util.stream.Collectors.toList());

        } catch (Exception e) {
            log.error("获取人员违规行为详细列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取井下人员分布统计
     */
    @Override
    public List<UndergroundPersonnelDistributionVO> getUndergroundPersonnelDistribution(String viewType, String startDate, String endDate) {
        try {
            log.info("获取井下人员分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 使用统计SQL直接从数据库获取井下人员分布统计
            List<Map<String, Object>> distributionStats = kafkaPeoplePosRealTimeMapper.selectUndergroundPersonnelDistribution(startDate, endDate);

            // 构建结果列表
            List<UndergroundPersonnelDistributionVO> distributionList = new ArrayList<>();
            for (Map<String, Object> stat : distributionStats) {
                String areaName = (String) stat.get("area_name");
                Long personnelCount = ((Number) stat.get("personnel_count")).longValue();

                distributionList.add(new UndergroundPersonnelDistributionVO(
                    areaName, personnelCount, areaName
                ));
            }

            log.info("获取井下人员分布统计完成，共{}个区域", distributionList.size());
            return distributionList;
        } catch (Exception e) {
            log.error("获取井下人员分布统计失败", e);
            throw new RuntimeException("获取井下人员分布统计失败: " + e.getMessage());
        }
    }
}
