package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 充填数据采场统计对象 - 统一日/周/月/年采场统计
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class DataFillingStopeStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 周开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekStartDate;

    /**
     * 周结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekEndDate;

    /**
     * 采场ID
     */
    @Excel(name = "采场ID")
    private Long stopeId;

    /**
     * 采场名称
     */
    @Excel(name = "采场名称")
    private String stopeName;

    /**
     * 总浆液体积（立方米）
     */
    @Excel(name = "总浆液体积")
    private Double totalSlurryVolume;

    /**
     * 总水泥重量（吨）
     */
    @Excel(name = "总水泥重量")
    private Double totalCementWeight;

    /**
     * 平均充填浓度（%）
     */
    @Excel(name = "平均充填浓度")
    private Double avgFillingConcentration;

    /**
     * 计划浆液体积（立方米）
     */
    @Excel(name = "计划浆液体积")
    private Double planSlurryVolume;
}
