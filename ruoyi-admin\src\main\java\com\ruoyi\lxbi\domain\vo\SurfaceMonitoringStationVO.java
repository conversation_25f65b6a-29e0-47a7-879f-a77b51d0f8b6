package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 地表监测站点信息VO
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurfaceMonitoringStationVO {

    /**
     * 站点ID
     */
    private Long id;

    /**
     * 网关编号
     */
    private String wgbh;

    /**
     * 站点名称
     */
    private String cphm;

    /**
     * 站点标识
     */
    private String clbz;

    /**
     * 唯一编号
     */
    private String clbh;

    /**
     * 经度
     */
    private BigDecimal wxdwjd;

    /**
     * 纬度
     */
    private BigDecimal wxdwwd;

    /**
     * 网关IP
     */
    private String wgip;

    /**
     * 通信状态
     */
    private String txzt;

    /**
     * 车辆状态
     */
    private Integer clzt;

    /**
     * 定位状态
     */
    private Integer drzt;

    /**
     * 卫星数量
     */
    private Integer wxsl;

    /**
     * 最近更新时间
     */
    private Long zjgxsj;

    /**
     * 网关时间
     */
    private Long wgsj;

    /**
     * 操作时间
     */
    private String czsj;

    /**
     * 操作人员
     */
    private String czry;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 是否在线
     */
    private Boolean isOnline;
}
