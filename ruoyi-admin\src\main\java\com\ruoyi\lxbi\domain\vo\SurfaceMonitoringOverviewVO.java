package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 地表监测概览VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurfaceMonitoringOverviewVO {
    
    /**
     * X偏移距离总和
     */
    private Long xOffsetDistanceTotal;
    
    /**
     * Y偏移距离总和
     */
    private Long yOffsetDistanceTotal;
    
    /**
     * H偏移距离总和
     */
    private Long hOffsetDistanceTotal;
    
    /**
     * 偏移报警数
     */
    private Long offsetAlarmCount;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 统计周期
     */
    private String period;
}
