package com.ruoyi.lxbi.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.lxbi.mapper.DataIronConcentrateMapper;
import com.ruoyi.lxbi.domain.DataIronConcentrate;
import com.ruoyi.lxbi.service.IDataIronConcentrateService;

/**
 * 铁精粉生产数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@Service
public class DataIronConcentrateServiceImpl implements IDataIronConcentrateService 
{
    @Autowired
    private DataIronConcentrateMapper dataIronConcentrateMapper;

    /**
     * 查询铁精粉生产数据
     * 
     * @param id 铁精粉生产数据主键
     * @return 铁精粉生产数据
     */
    @Override
    public DataIronConcentrate selectDataIronConcentrateById(Long id)
    {
        return dataIronConcentrateMapper.selectDataIronConcentrateById(id);
    }

    /**
     * 查询铁精粉生产数据列表
     * 
     * @param dataIronConcentrate 铁精粉生产数据
     * @return 铁精粉生产数据
     */
    @Override
    public List<DataIronConcentrate> selectDataIronConcentrateList(DataIronConcentrate dataIronConcentrate)
    {
        return dataIronConcentrateMapper.selectDataIronConcentrateList(dataIronConcentrate);
    }

    /**
     * 新增铁精粉生产数据
     * 
     * @param dataIronConcentrate 铁精粉生产数据
     * @return 结果
     */
    @Override
    public int insertDataIronConcentrate(DataIronConcentrate dataIronConcentrate)
    {
        dataIronConcentrate.setCreateTime(DateUtils.getNowDate());
        return dataIronConcentrateMapper.insertDataIronConcentrate(dataIronConcentrate);
    }

    /**
     * 修改铁精粉生产数据
     * 
     * @param dataIronConcentrate 铁精粉生产数据
     * @return 结果
     */
    @Override
    public int updateDataIronConcentrate(DataIronConcentrate dataIronConcentrate)
    {
        dataIronConcentrate.setUpdateTime(DateUtils.getNowDate());
        return dataIronConcentrateMapper.updateDataIronConcentrate(dataIronConcentrate);
    }

    /**
     * 批量删除铁精粉生产数据
     * 
     * @param ids 需要删除的铁精粉生产数据主键
     * @return 结果
     */
    @Override
    public int deleteDataIronConcentrateByIds(Long[] ids)
    {
        return dataIronConcentrateMapper.deleteDataIronConcentrateByIds(ids);
    }

    /**
     * 删除铁精粉生产数据信息
     * 
     * @param id 铁精粉生产数据主键
     * @return 结果
     */
    @Override
    public int deleteDataIronConcentrateById(Long id)
    {
        return dataIronConcentrateMapper.deleteDataIronConcentrateById(id);
    }
}
