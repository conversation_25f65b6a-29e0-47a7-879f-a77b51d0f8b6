package com.ruoyi.lxbi.admin.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaPeoplePosRealTimeMapper;
import com.ruoyi.lxbi.domain.KafkaPeoplePosRealTime;
import com.ruoyi.lxbi.admin.service.IKafkaPeoplePosRealTimeService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * 井下作业人员实时数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Service
public class KafkaPeoplePosRealTimeServiceImpl implements IKafkaPeoplePosRealTimeService 
{
    @Autowired
    private KafkaPeoplePosRealTimeMapper kafkaPeoplePosRealTimeMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 查询井下作业人员实时数据
     * 
     * @param id 井下作业人员实时数据主键
     * @return 井下作业人员实时数据
     */
    @Override
    public KafkaPeoplePosRealTime selectKafkaPeoplePosRealTimeById(Long id)
    {
        return kafkaPeoplePosRealTimeMapper.selectKafkaPeoplePosRealTimeById(id);
    }

    /**
     * 查询井下作业人员实时数据列表
     * 
     * @param kafkaPeoplePosRealTime 井下作业人员实时数据
     * @return 井下作业人员实时数据
     */
    @Override
    public List<KafkaPeoplePosRealTime> selectKafkaPeoplePosRealTimeList(KafkaPeoplePosRealTime kafkaPeoplePosRealTime)
    {
        return kafkaPeoplePosRealTimeMapper.selectKafkaPeoplePosRealTimeList(kafkaPeoplePosRealTime);
    }

    /**
     * 新增井下作业人员实时数据
     * 
     * @param kafkaPeoplePosRealTime 井下作业人员实时数据
     * @return 结果
     */
    @Override
    public int insertKafkaPeoplePosRealTime(KafkaPeoplePosRealTime kafkaPeoplePosRealTime)
    {
        kafkaPeoplePosRealTime.setCreateTime(DateUtils.getNowDate());
        return kafkaPeoplePosRealTimeMapper.insertKafkaPeoplePosRealTime(kafkaPeoplePosRealTime);
    }

    /**
     * 修改井下作业人员实时数据
     * 
     * @param kafkaPeoplePosRealTime 井下作业人员实时数据
     * @return 结果
     */
    @Override
    public int updateKafkaPeoplePosRealTime(KafkaPeoplePosRealTime kafkaPeoplePosRealTime)
    {
        kafkaPeoplePosRealTime.setUpdateTime(DateUtils.getNowDate());
        return kafkaPeoplePosRealTimeMapper.updateKafkaPeoplePosRealTime(kafkaPeoplePosRealTime);
    }

    /**
     * 批量删除井下作业人员实时数据
     * 
     * @param ids 需要删除的井下作业人员实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeoplePosRealTimeByIds(Long[] ids)
    {
        return kafkaPeoplePosRealTimeMapper.deleteKafkaPeoplePosRealTimeByIds(ids);
    }

    /**
     * 删除井下作业人员实时数据信息
     * 
     * @param id 井下作业人员实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeoplePosRealTimeById(Long id)
    {
        return kafkaPeoplePosRealTimeMapper.deleteKafkaPeoplePosRealTimeById(id);
    }

    /**
     * 处理Kafka消息
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka井下作业人员实时数据消息");

            // 解析Kafka消息
            KafkaPeoplePosRealTime peoplePosData = parseKafkaMessage(kafkaMessage);
            if (peoplePosData == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(peoplePosData.getPersonnelCardCode())) {
                log.warn("人员卡编码为空，跳过处理");
                return false;
            }

            // 根据人员卡编码查询是否已存在
            KafkaPeoplePosRealTime existingData = kafkaPeoplePosRealTimeMapper.selectByPersonnelCardCode(
                peoplePosData.getPersonnelCardCode());

            int result;
            if (existingData != null) {
                // 存在则更新，保持原有的initDate不变
                peoplePosData.setId(existingData.getId());
                peoplePosData.setInitDate(existingData.getInitDate()); // 保持原有的初始日期
                peoplePosData.setUpdateTime(DateUtils.getNowDate());
                result = updateKafkaPeoplePosRealTime(peoplePosData);
                
                if (result > 0) {
                    log.info("成功更新井下作业人员实时数据，人员卡编码: {}, 姓名: {}",
                        peoplePosData.getPersonnelCardCode(), peoplePosData.getPersonnelName());
                    return true;
                } else {
                    log.warn("更新井下作业人员实时数据失败，人员卡编码: {}",
                        peoplePosData.getPersonnelCardCode());
                    return false;
                }
            } else {
                // 不存在则插入，设置initDate为当前的dataUploadTime
                peoplePosData.setInitDate(peoplePosData.getDataUploadTime());
                peoplePosData.setCreateTime(DateUtils.getNowDate());
                result = insertKafkaPeoplePosRealTime(peoplePosData);
                
                if (result > 0) {
                    log.info("成功插入井下作业人员实时数据，人员卡编码: {}, 姓名: {}, 初始日期: {}",
                        peoplePosData.getPersonnelCardCode(), peoplePosData.getPersonnelName(), peoplePosData.getInitDate());
                    return true;
                } else {
                    log.warn("插入井下作业人员实时数据失败，人员卡编码: {}",
                        peoplePosData.getPersonnelCardCode());
                    return false;
                }
            }

        } catch (Exception e) {
            log.error("处理Kafka井下作业人员实时数据消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息
     */
    private KafkaPeoplePosRealTime parseKafkaMessage(String kafkaMessage) {
        try {
            // 解析JSON消息，处理中文字段名到英文属性的映射
            com.fasterxml.jackson.databind.JsonNode jsonNode = objectMapper.readTree(kafkaMessage);

            KafkaPeoplePosRealTime peoplePosData = new KafkaPeoplePosRealTime();

            // 映射中文字段名到英文属性
            if (jsonNode.has("文件前缀")) {
                peoplePosData.setFileEncoding(jsonNode.get("文件前缀").asText());
            }
            if (jsonNode.has("煤矿编码")) {
                peoplePosData.setMineCode(jsonNode.get("煤矿编码").asText());
            }
            if (jsonNode.has("矿井名称")) {
                peoplePosData.setMineName(jsonNode.get("矿井名称").asText());
            }
            if (jsonNode.has("数据上传时间")) {
                String timeStr = jsonNode.get("数据上传时间").asText();
                peoplePosData.setDataUploadTime(parseDateTime(timeStr));
            }
            if (jsonNode.has("人员卡编码")) {
                peoplePosData.setPersonnelCardCode(jsonNode.get("人员卡编码").asText());
            }
            if (jsonNode.has("姓名")) {
                peoplePosData.setPersonnelName(jsonNode.get("姓名").asText());
            }
            if (jsonNode.has("出入井标志位")) {
                peoplePosData.setInOutWellFlag(jsonNode.get("出入井标志位").asText());
            }
            if (jsonNode.has("入井时刻")) {
                String timeStr = jsonNode.get("入井时刻").asText();
                if (StringUtils.hasText(timeStr)) {
                    peoplePosData.setInWellTime(parseDateTime(timeStr));
                }
            }
            if (jsonNode.has("出井时刻")) {
                String timeStr = jsonNode.get("出井时刻").asText();
                if (StringUtils.hasText(timeStr)) {
                    peoplePosData.setOutWellTime(parseDateTime(timeStr));
                }
            }
            if (jsonNode.has("区域编码")) {
                peoplePosData.setAreaCode(jsonNode.get("区域编码").asText());
            }
            if (jsonNode.has("进入当前区域时刻")) {
                String timeStr = jsonNode.get("进入当前区域时刻").asText();
                if (StringUtils.hasText(timeStr)) {
                    peoplePosData.setEnterCurrentAreaTime(parseDateTime(timeStr));
                }
            }
            if (jsonNode.has("基站编码")) {
                peoplePosData.setBaseStationCode(jsonNode.get("基站编码").asText());
            }
            if (jsonNode.has("进入当前所处基站时刻")) {
                String timeStr = jsonNode.get("进入当前所处基站时刻").asText();
                if (StringUtils.hasText(timeStr)) {
                    peoplePosData.setEnterCurrentBaseStationTime(parseDateTime(timeStr));
                }
            }
            if (jsonNode.has("劳动组织方式")) {
                peoplePosData.setLaborOrganizationMode(jsonNode.get("劳动组织方式").asText());
            }
            if (jsonNode.has("距离基站距离")) {
                String distanceStr = jsonNode.get("距离基站距离").asText();
                if (StringUtils.hasText(distanceStr)) {
                    try {
                        peoplePosData.setDistanceFromBaseStation(Double.parseDouble(distanceStr));
                    } catch (NumberFormatException e) {
                        log.warn("距离基站距离格式错误: {}", distanceStr);
                    }
                }
            }
            if (jsonNode.has("人员工作状态")) {
                peoplePosData.setPersonnelWorkStatus(jsonNode.get("人员工作状态").asText());
            }
            if (jsonNode.has("是否矿领导")) {
                peoplePosData.setIsMineLeader(jsonNode.get("是否矿领导").asText());
            }
            if (jsonNode.has("是否特种人员")) {
                peoplePosData.setIsSpecialPersonnel(jsonNode.get("是否特种人员").asText());
            }
            if (jsonNode.has("行进轨迹基站，时间集合")) {
                peoplePosData.setTrajectoryBaseStationTimeSet(jsonNode.get("行进轨迹基站，时间集合").asText());
            }

            return peoplePosData;
        } catch (Exception e) {
            log.error("解析Kafka井下作业人员实时数据消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 解析日期时间字符串
     */
    private java.util.Date parseDateTime(String timeStr) {
        if (!StringUtils.hasText(timeStr)) {
            return null;
        }
        try {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.parse(timeStr);
        } catch (Exception e) {
            log.warn("解析时间字符串失败: {}", timeStr, e);
            return null;
        }
    }
}
