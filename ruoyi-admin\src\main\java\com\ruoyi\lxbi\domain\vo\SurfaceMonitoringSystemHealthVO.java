package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 地表监测系统健康状态VO
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurfaceMonitoringSystemHealthVO {

    /**
     * 系统整体健康评分(0-100)
     */
    private Integer overallHealthScore;

    /**
     * 系统状态
     */
    private String systemStatus;

    /**
     * 总站点数
     */
    private Integer totalStations;

    /**
     * 在线站点数
     */
    private Integer onlineStations;

    /**
     * 离线站点数
     */
    private Integer offlineStations;

    /**
     * 故障站点数
     */
    private Integer faultStations;

    /**
     * 系统在线率(%)
     */
    private BigDecimal systemOnlineRate;

    /**
     * 数据完整率(%)
     */
    private BigDecimal dataIntegrityRate;

    /**
     * 今日数据采集次数
     */
    private Long todayDataCollections;

    /**
     * 今日报警次数
     */
    private Long todayAlarmCount;

    /**
     * 未处理报警数
     */
    private Long unhandledAlarmCount;

    /**
     * 高级报警数
     */
    private Long highLevelAlarmCount;

    /**
     * 网络连接状态
     */
    private String networkStatus;

    /**
     * 数据库连接状态
     */
    private String databaseStatus;

    /**
     * 第三方API连接状态
     */
    private String apiConnectionStatus;

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;

    /**
     * 系统运行时长(小时)
     */
    private Long systemUptime;

    /**
     * 各站点状态统计
     */
    private List<StationHealthSummary> stationHealthSummaries;

    /**
     * 系统性能指标
     */
    private SystemPerformanceMetrics performanceMetrics;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StationHealthSummary {
        
        /**
         * 站点ID
         */
        private Long stationId;

        /**
         * 站点名称
         */
        private String stationName;

        /**
         * 健康评分
         */
        private Integer healthScore;

        /**
         * 状态
         */
        private String status;

        /**
         * 最后通信时间
         */
        private String lastCommunicationTime;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SystemPerformanceMetrics {
        
        /**
         * 平均响应时间(ms)
         */
        private Long avgResponseTime;

        /**
         * 数据处理速度(条/秒)
         */
        private BigDecimal dataProcessingSpeed;

        /**
         * 内存使用率(%)
         */
        private BigDecimal memoryUsage;

        /**
         * CPU使用率(%)
         */
        private BigDecimal cpuUsage;

        /**
         * 磁盘使用率(%)
         */
        private BigDecimal diskUsage;

        /**
         * 网络带宽使用率(%)
         */
        private BigDecimal networkUsage;
    }
}
