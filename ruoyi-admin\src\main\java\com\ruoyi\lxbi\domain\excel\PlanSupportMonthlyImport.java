package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支护月计划导入对象
 */
@Data
@ExcelImportTemplate(
        key = "plan_support_monthly",
        name = "支护月计划导入",
        description = "用于导入支护月计划数据",
        sheetName = "支护月计划"
)
public class PlanSupportMonthlyImport {

    /**
     * 计划月份
     */
    @ExcelProperty(value = "计划月份", index = 0)
    @ExcelRequired(message = "计划月份不能为空")
    @ExcelSelected(prompt = "请输入计划月份，格式：yyyyMM，例如：202501")
    private String planDate;

    /**
     * 项目部门ID（存储值）
     */
    @ExcelSelected(
            optionKey = "projectDepartment",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long projectDepartmentId;

    /**
     * 工作面ID（存储值）
     */
    @ExcelSelected(
            optionKey = "workingFace",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long workingFaceId;

    /**
     * 采场ID（存储值）
     */
    @ExcelSelected(
            optionKey = "stope",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long stopeId;

    /**
     * 项目部门名称（显示值）
     */
    @ExcelProperty(value = "项目部门", index = 1)
    @ExcelRequired(message = "项目部门不能为空")
    @ExcelSelected(
            optionKey = "projectDepartment",
            prompt = "请选择项目部门",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String projectDepartmentName;

    /**
     * 锚网支护米数
     */
    @ExcelProperty(value = "锚网支护米数", index = 2)
    @ExcelSelected(prompt = "请输入锚网支护米数")
    private BigDecimal boltMeshSupportMeter;

    /**
     * 喷浆支护米数
     */
    @ExcelProperty(value = "喷浆支护米数", index = 3)
    @ExcelSelected(prompt = "请输入喷浆支护米数")
    private BigDecimal shotcreteSupportMeter;

    @ExcelProperty(value = "锚网支护方量", index = 4)
    @ExcelSelected(prompt = "请输入喷浆支护方量")
    private BigDecimal boltMeshSupportVolume;

    @ExcelProperty(value = "喷浆支护方量", index = 5)
    @ExcelSelected(prompt = "请输入喷浆支护方量")
    private BigDecimal shotcreteSupportVolume;

    @ExcelProperty(value = "支护方量合计", index = 6)
    @ExcelSelected(prompt = "请输入支护方量合计")
    private BigDecimal supportVolume;

    @ExcelProperty(value = "支护米数合计", index = 7)
    @ExcelSelected(prompt = "请输入支护米数合计")
    private BigDecimal supportMeter;
}
