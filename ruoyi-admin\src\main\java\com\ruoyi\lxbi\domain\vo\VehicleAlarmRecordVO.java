package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 车辆告警记录VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleAlarmRecordVO {
    
    /**
     * 序号
     */
    private Integer serialNumber;
    
    /**
     * 车牌号
     */
    private String vehiclePlateNumber;
    
    /**
     * 告警类型
     */
    private String alarmType;
    
    /**
     * 车辆位置
     */
    private String vehicleLocation;
    
    /**
     * 告警时间
     */
    private String alarmTime;
    
    /**
     * 解除时间
     */
    private String resolveTime;
    
    /**
     * 车辆编码
     */
    private String vehicleCode;
    
    /**
     * 告警状态
     */
    private String alarmStatus;
    
    /**
     * 处理人员
     */
    private String handlerName;
}
