package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 地表监测七日偏移趋势VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurfaceMonitoringTrendVO {
    
    /**
     * 日期
     */
    private String date;
    
    /**
     * 短日期格式 (1日, 2日, ...)
     */
    private String shortDate;
    
    /**
     * X方向偏移值
     */
    private BigDecimal xOffsetValue;
    
    /**
     * Y方向偏移值
     */
    private BigDecimal yOffsetValue;
    
    /**
     * H方向偏移值
     */
    private BigDecimal hOffsetValue;
}
