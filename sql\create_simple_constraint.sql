-- 创建简单的唯一约束解决方案
-- PostgreSQL

-- 1. 检查表是否存在
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'kafka_people_location_info'
) as table_exists;

-- 2. 如果表不存在，创建表
CREATE TABLE IF NOT EXISTS kafka_people_location_info (
    id BIGSERIAL PRIMARY KEY,
    mine_code VARCHAR(50),
    mine_name VARCHAR(100),
    data_upload_time TIMESTAMP,
    area_type VARCHAR(50),
    area_code VARCHAR(50) NOT NULL,
    area_approved_personnel BIGINT,
    area_name VARCHAR(100),
    status BIGINT DEFAULT 1,
    is_deleted BIGINT DEFAULT 0,
    create_by VARCHAR(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500) DEFAULT NULL
);

-- 3. 删除可能存在的重复数据
WITH ranked_data AS (
    SELECT 
        id,
        area_code,
        mine_code,
        ROW_NUMBER() OVER (
            PARTITION BY area_code, mine_code 
            ORDER BY data_upload_time DESC NULLS LAST, id DESC
        ) as rn
    FROM kafka_people_location_info 
    WHERE is_deleted = 0
)
DELETE FROM kafka_people_location_info 
WHERE id IN (
    SELECT id FROM ranked_data WHERE rn > 1
);

-- 4. 删除可能存在的旧约束和索引
ALTER TABLE kafka_people_location_info DROP CONSTRAINT IF EXISTS uk_area_mine_code;
DROP INDEX IF EXISTS idx_kpli_area_mine_unique;

-- 5. 创建表级唯一约束
ALTER TABLE kafka_people_location_info 
ADD CONSTRAINT uk_area_mine_code UNIQUE (area_code, mine_code);

-- 6. 创建其他必要的索引
CREATE INDEX IF NOT EXISTS idx_kpli_area_code ON kafka_people_location_info(area_code);
CREATE INDEX IF NOT EXISTS idx_kpli_mine_code ON kafka_people_location_info(mine_code);
CREATE INDEX IF NOT EXISTS idx_kpli_area_type ON kafka_people_location_info(area_type);
CREATE INDEX IF NOT EXISTS idx_kpli_data_upload_time ON kafka_people_location_info(data_upload_time);
CREATE INDEX IF NOT EXISTS idx_kpli_status_deleted ON kafka_people_location_info(status, is_deleted);

-- 7. 验证约束是否创建成功
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'kafka_people_location_info'::regclass
AND contype = 'u';

-- 8. 测试UPSERT操作
INSERT INTO kafka_people_location_info (
    mine_code, mine_name, data_upload_time, area_type, area_code,
    area_approved_personnel, area_name, status, is_deleted,
    create_by, create_time, update_by, update_time
) VALUES (
    'TEST001', '测试煤矿', CURRENT_TIMESTAMP, '测试区', 'TESTAREA001',
    10, '测试区域', 1, 0,
    'test_user', CURRENT_TIMESTAMP, 'test_user', CURRENT_TIMESTAMP
)
ON CONFLICT (area_code, mine_code) 
DO UPDATE SET
    mine_name = EXCLUDED.mine_name,
    data_upload_time = EXCLUDED.data_upload_time,
    area_type = EXCLUDED.area_type,
    area_approved_personnel = EXCLUDED.area_approved_personnel,
    area_name = EXCLUDED.area_name,
    status = EXCLUDED.status,
    is_deleted = EXCLUDED.is_deleted,
    update_by = EXCLUDED.update_by,
    update_time = EXCLUDED.update_time;

-- 9. 验证测试数据
SELECT * FROM kafka_people_location_info 
WHERE area_code = 'TESTAREA001' AND mine_code = 'TEST001';

-- 10. 再次执行相同的插入，验证更新功能
INSERT INTO kafka_people_location_info (
    mine_code, mine_name, data_upload_time, area_type, area_code,
    area_approved_personnel, area_name, status, is_deleted,
    create_by, create_time, update_by, update_time
) VALUES (
    'TEST001', '测试煤矿-更新', CURRENT_TIMESTAMP, '测试区-更新', 'TESTAREA001',
    20, '测试区域-更新', 1, 0,
    'test_user', CURRENT_TIMESTAMP, 'test_user_update', CURRENT_TIMESTAMP
)
ON CONFLICT (area_code, mine_code) 
DO UPDATE SET
    mine_name = EXCLUDED.mine_name,
    data_upload_time = EXCLUDED.data_upload_time,
    area_type = EXCLUDED.area_type,
    area_approved_personnel = EXCLUDED.area_approved_personnel,
    area_name = EXCLUDED.area_name,
    status = EXCLUDED.status,
    is_deleted = EXCLUDED.is_deleted,
    update_by = EXCLUDED.update_by,
    update_time = EXCLUDED.update_time;

-- 11. 验证更新结果
SELECT * FROM kafka_people_location_info 
WHERE area_code = 'TESTAREA001' AND mine_code = 'TEST001';

-- 12. 清理测试数据
DELETE FROM kafka_people_location_info 
WHERE area_code = 'TESTAREA001' AND mine_code = 'TEST001';

-- 13. 最终验证
SELECT 'kafka_people_location_info 唯一约束创建完成' as status;

-- 14. 显示表结构信息
\d kafka_people_location_info;
