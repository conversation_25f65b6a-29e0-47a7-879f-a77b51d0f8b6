<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.BasePriorityProjectMapper">
    
    <resultMap type="BasePriorityProject" id="BasePriorityProjectResult">
        <result property="id"    column="id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectBasePriorityProjectVo">
        select bpp.*, bs.stope_name, bwf.working_face_name
        from base_priority_project bpp
        left join base_stope bs on bpp.stope_id = bs.stope_id
        left join base_working_face bwf on bpp.working_face_id = bwf.working_face_id
    </sql>

    <select id="selectBasePriorityProjectList" parameterType="BasePriorityProject" resultType="com.ruoyi.lxbi.domain.response.BasePriorityProjectVo">
        <include refid="selectBasePriorityProjectVo"/>
        <where>
            <if test="isDelete != null "> and bpp.is_delete = #{isDelete}</if>
            <if test="isDelete == null "> and (bpp.is_delete = 0 or bpp.is_delete is null)</if>
            <if test="stopeId != null "> and bpp.stope_id = #{stopeId}</if>
            <if test="workingFaceId != null "> and bpp.working_face_id = #{workingFaceId}</if>
            <if test="status != null "> and bpp.status = #{status}</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and bpp.start_time between #{params.beginStartTime}::date and #{params.endStartTime}::date</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and bpp.end_time between #{params.beginEndTime}::date and #{params.endEndTime}::date</if>
        </where>
    </select>

    <select id="selectBasePriorityProjectListAll" parameterType="BasePriorityProject" resultType="com.ruoyi.lxbi.domain.response.BasePriorityProjectVo">
        select bpp.id, bpp.stope_id, bpp.working_face_id, bpp.status, bpp.create_by, bpp.create_time, bpp.update_by, bpp.update_time, bpp.start_time, bpp.end_time, bpp.is_delete,
               bs.stope_name, bwf.working_face_name
        from base_priority_project bpp
        left join base_stope bs on bpp.stope_id = bs.stope_id
        left join base_working_face bwf on bpp.working_face_id = bwf.working_face_id
        <where>
            <if test="stopeId != null "> and bpp.stope_id = #{stopeId}</if>
            <if test="workingFaceId != null "> and bpp.working_face_id = #{workingFaceId}</if>
            <if test="status != null "> and bpp.status = #{status}</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and bpp.start_time between #{params.beginStartTime}::date and #{params.endStartTime}::date</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and bpp.end_time between #{params.beginEndTime}::date and #{params.endEndTime}::date</if>
            and bpp.is_delete = 0
        </where>
    </select>

    <select id="selectBasePriorityProjectById" parameterType="Long" resultMap="BasePriorityProjectResult">
        <include refid="selectBasePriorityProjectVo"/>
        where bpp.id = #{id}
    </select>

    <insert id="insertBasePriorityProject" parameterType="BasePriorityProject">
        insert into base_priority_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="workingFaceId != null">working_face_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="workingFaceId != null">#{workingFaceId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updateBasePriorityProject" parameterType="BasePriorityProject">
        update base_priority_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="workingFaceId != null">working_face_id = #{workingFaceId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBasePriorityProjectById" parameterType="Long">
        delete from base_priority_project where id = #{id}
    </delete>

    <delete id="deleteBasePriorityProjectByIds" parameterType="String">
        delete from base_priority_project where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="logicDeleteBasePriorityProjectByIds" parameterType="String">
        update base_priority_project set is_delete = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>