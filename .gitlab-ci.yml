stages:
  - build
  - deploy

build:
  stage: build
  tags:
    - k3s
  image: maven:3.9.9-eclipse-temurin-21
  script:
    - mvn clean package -pl ruoyi-admin -am -Dmaven.repo.local=.m2/repository
  cache:
    key: maven-ci-cache
    paths:
      - .m2/repository
  artifacts:
    paths:
      - ruoyi-admin/target/*.jar
    expire_in: 1h

deploy:
  stage: deploy
  tags:
    - k3s
  image:
    name: moby/buildkit:rootless
    entrypoint: [ "sh", "-c" ]
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - |
      mkdir ~/.docker
      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
  script:
    - |
      buildctl-daemonless.sh \
          build --frontend=dockerfile.v0 \
          --export-cache type=inline \
          --local context=. \
          --local dockerfile=. \
          --output type=image,name=***************:20080/congeer/lxbi:master,push=true,registry.insecure=true

