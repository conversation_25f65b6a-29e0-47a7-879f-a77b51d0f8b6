package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaSafeMonitorErrorInfo;

/**
 * 安全监测实时数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IKafkaSafeMonitorErrorInfoService 
{
    /**
     * 查询安全监测实时数据
     * 
     * @param id 安全监测实时数据主键
     * @return 安全监测实时数据
     */
    public KafkaSafeMonitorErrorInfo selectKafkaSafeMonitorErrorInfoById(Long id);

    /**
     * 查询安全监测实时数据列表
     * 
     * @param kafkaSafeMonitorErrorInfo 安全监测实时数据
     * @return 安全监测实时数据集合
     */
    public List<KafkaSafeMonitorErrorInfo> selectKafkaSafeMonitorErrorInfoList(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo);

    /**
     * 新增安全监测实时数据
     * 
     * @param kafkaSafeMonitorErrorInfo 安全监测实时数据
     * @return 结果
     */
    public int insertKafkaSafeMonitorErrorInfo(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo);

    /**
     * 修改安全监测实时数据
     * 
     * @param kafkaSafeMonitorErrorInfo 安全监测实时数据
     * @return 结果
     */
    public int updateKafkaSafeMonitorErrorInfo(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo);

    /**
     * 批量删除安全监测实时数据
     * 
     * @param ids 需要删除的安全监测实时数据主键集合
     * @return 结果
     */
    public int deleteKafkaSafeMonitorErrorInfoByIds(Long[] ids);

    /**
     * 删除安全监测实时数据信息
     *
     * @param id 安全监测实时数据主键
     * @return 结果
     */
    public int deleteKafkaSafeMonitorErrorInfoById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaSafeMonitorErrorInfo parseKafkaMessage(String kafkaMessage);
}
