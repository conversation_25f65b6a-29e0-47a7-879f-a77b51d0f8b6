package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.PlanMineralMonthlyMapper;
import com.ruoyi.lxbi.domain.PlanMineralMonthly;
import com.ruoyi.lxbi.domain.request.PlanMineralMonthlyBatchDto;
import com.ruoyi.lxbi.service.IPlanMineralMonthlyService;

/**
 * 选矿整体月计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@Service
public class PlanMineralMonthlyServiceImpl implements IPlanMineralMonthlyService 
{
    @Autowired
    private PlanMineralMonthlyMapper planMineralMonthlyMapper;

    /**
     * 查询选矿整体月计划
     * 
     * @param id 选矿整体月计划主键
     * @return 选矿整体月计划
     */
    @Override
    public PlanMineralMonthly selectPlanMineralMonthlyById(Long id)
    {
        return planMineralMonthlyMapper.selectPlanMineralMonthlyById(id);
    }

    /**
     * 查询选矿整体月计划列表
     * 
     * @param planMineralMonthly 选矿整体月计划
     * @return 选矿整体月计划
     */
    @Override
    public List<PlanMineralMonthly> selectPlanMineralMonthlyList(PlanMineralMonthly planMineralMonthly)
    {
        return planMineralMonthlyMapper.selectPlanMineralMonthlyList(planMineralMonthly);
    }

    /**
     * 新增选矿整体月计划
     * 
     * @param planMineralMonthly 选矿整体月计划
     * @return 结果
     */
    @Override
    public int insertPlanMineralMonthly(PlanMineralMonthly planMineralMonthly)
    {
        planMineralMonthly.setCreateTime(DateUtils.getNowDate());
        return planMineralMonthlyMapper.insertPlanMineralMonthly(planMineralMonthly);
    }

    /**
     * 修改选矿整体月计划
     * 
     * @param planMineralMonthly 选矿整体月计划
     * @return 结果
     */
    @Override
    public int updatePlanMineralMonthly(PlanMineralMonthly planMineralMonthly)
    {
        planMineralMonthly.setUpdateTime(DateUtils.getNowDate());
        return planMineralMonthlyMapper.updatePlanMineralMonthly(planMineralMonthly);
    }

    /**
     * 批量删除选矿整体月计划
     * 
     * @param ids 需要删除的选矿整体月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanMineralMonthlyByIds(Long[] ids)
    {
        return planMineralMonthlyMapper.deletePlanMineralMonthlyByIds(ids);
    }

    /**
     * 删除选矿整体月计划信息
     *
     * @param id 选矿整体月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanMineralMonthlyById(Long id)
    {
        return planMineralMonthlyMapper.deletePlanMineralMonthlyById(id);
    }

    /**
     * 批量保存选矿整体月计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePlanMineralMonthly(List<PlanMineralMonthlyBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个月份的数据
        String planMonth = batchDataList.get(0).getPlanMonth();
        if (StringUtils.isBlank(planMonth)) {
            throw new ServiceException("计划月份不能为空");
        }

        boolean allSameMonth = batchDataList.stream()
                .allMatch(data -> planMonth.equals(data.getPlanMonth()));
        if (!allSameMonth) {
            throw new ServiceException("批量数据必须是同一个计划月份");
        }

        // 查询该月份的现有数据
        PlanMineralMonthly queryParam = new PlanMineralMonthly();
        queryParam.setPlanDate(planMonth);
        List<PlanMineralMonthly> existingDataList = planMineralMonthlyMapper.selectPlanMineralMonthlyList(queryParam);
        Map<Long, PlanMineralMonthly> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(PlanMineralMonthly::getId, data -> data));

        List<PlanMineralMonthly> toInsert = new ArrayList<>();
        List<PlanMineralMonthly> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (PlanMineralMonthlyBatchDto batchData : batchDataList) {
            if ("add".equals(batchData.getOperationType())) {
                // 新增数据
                PlanMineralMonthly newData = new PlanMineralMonthly();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if ("edit".equals(batchData.getOperationType()) && batchData.getId() != null) {
                // 更新现有数据
                if (existingDataMap.containsKey(batchData.getId())) {
                    PlanMineralMonthly updateData = new PlanMineralMonthly();
                    copyProperties(batchData, updateData);
                    updateData.setId(batchData.getId());
                    updateData.setUpdateBy(SecurityUtils.getUsername());
                    updateData.setUpdateTime(DateUtils.getNowDate());
                    toUpdate.add(updateData);
                    toDelete.remove(batchData.getId());
                }
            }
        }

        int totalProcessed = 0;

        // 执行批量操作
        if (!toInsert.isEmpty()) {
            for (PlanMineralMonthly data : toInsert) {
                totalProcessed += planMineralMonthlyMapper.insertPlanMineralMonthly(data);
            }
        }

        if (!toUpdate.isEmpty()) {
            for (PlanMineralMonthly data : toUpdate) {
                totalProcessed += planMineralMonthlyMapper.updatePlanMineralMonthly(data);
            }
        }

        if (!toDelete.isEmpty()) {
            totalProcessed += planMineralMonthlyMapper.deletePlanMineralMonthlyByIds(toDelete.toArray(new Long[0]));
        }

        return totalProcessed;
    }

    /**
     * 复制属性
     */
    private void copyProperties(PlanMineralMonthlyBatchDto source, PlanMineralMonthly target) {
        target.setPlanDate(source.getPlanMonth());
        target.setRawOreProcessingVolume(source.getOreQuantity());
        target.setRawOreGradeTfe(source.getOreGrade());
        target.setConcentrateVolume(source.getGoldOutput());
        target.setDrySeparationVolume(source.getCrushingQuantity());
        target.setGrindingFeedVolume(source.getGrindingQuantity());
        target.setConcentrateGrade(source.getFlotationQuantity());
        target.setConcentrateFineness(source.getConcentrateFineness());
        target.setIronConcentrateMoisture(source.getIronConcentrateMoisture());
        target.setComprehensiveRatio(source.getComprehensiveRatio());
        target.setGrindingRatio(source.getGrindingRatio());
        target.setTailingsAgitatorTank(source.getTailingsAgitatorTank());
        target.setOverflowOfTailings(source.getOverflowOfTailings());
        target.setRawOreGradeMfe(source.getRawOreGradeMfe());
        target.setTailingGradeMfe(source.getTailingGradeMfe());
        if (StringUtils.isNotBlank(source.getRemark())) {
            target.setRemark(source.getRemark());
        }
    }
}
