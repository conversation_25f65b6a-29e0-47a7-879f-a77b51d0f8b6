-- 为 api_hidden_trouble_record 表添加 completion_time 字段
-- 该字段用于记录隐患的完成时间，使用审查时间

-- 添加 completion_time 字段
ALTER TABLE api_hidden_trouble_record 
ADD COLUMN completion_time DATE;

-- 添加字段注释
COMMENT ON COLUMN api_hidden_trouble_record.completion_time IS '完成时间（使用审查时间）';

-- 将现有的 review_date 数据复制到 completion_time 字段
-- 只有当状态为已整改(status=2)时才设置完成时间
UPDATE api_hidden_trouble_record 
SET completion_time = review_date 
WHERE status = 2 AND review_date IS NOT NULL;

-- 创建索引以提高查询性能
CREATE INDEX idx_api_hidden_trouble_record_completion_time 
ON api_hidden_trouble_record(completion_time);

-- 创建复合索引用于统计查询
CREATE INDEX idx_api_hidden_trouble_record_completion_time_date_range 
ON api_hidden_trouble_record(completion_time, trouble_date);

-- 验证数据迁移结果
SELECT 
    COUNT(*) as total_records,
    COUNT(completion_time) as records_with_completion_time,
    COUNT(CASE WHEN status = 2 THEN 1 END) as completed_status_count,
    COUNT(CASE WHEN status = 2 AND completion_time IS NOT NULL THEN 1 END) as completed_with_time_count
FROM api_hidden_trouble_record;
