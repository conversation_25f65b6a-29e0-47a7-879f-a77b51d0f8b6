package com.ruoyi.lxbi.controller.plan;

import java.util.List;

import com.ruoyi.lxbi.domain.response.PlanDriftMonthlyVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.PlanDriftMonthly;
import com.ruoyi.lxbi.domain.request.PlanDriftMonthlyBatchDto;
import com.ruoyi.lxbi.service.IPlanDriftMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 掘进月计划Controller
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/plan/planDriftMonthly")
public class PlanDriftMonthlyController extends BaseController {
    @Autowired
    private IPlanDriftMonthlyService planDriftMonthlyService;

    /**
     * 查询掘进月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planDriftMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanDriftMonthly planDriftMonthly) {
        startPage();
        List<PlanDriftMonthlyVo> list = planDriftMonthlyService.selectPlanDriftMonthlyList(planDriftMonthly);
        return getDataTable(list);
    }

    /**
     * 导出掘进月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planDriftMonthly:export')")
    @Log(title = "掘进月计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanDriftMonthly planDriftMonthly) {
        List<PlanDriftMonthlyVo> list = planDriftMonthlyService.selectPlanDriftMonthlyList(planDriftMonthly);
        ExcelUtil<PlanDriftMonthlyVo> util = new ExcelUtil<>(PlanDriftMonthlyVo.class);
        util.exportExcel(response, list, "掘进月计划数据");
    }

    /**
     * 获取掘进月计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('plan:planDriftMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planDriftMonthlyService.selectPlanDriftMonthlyById(id));
    }

    /**
     * 新增掘进月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planDriftMonthly:add')")
    @Log(title = "掘进月计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanDriftMonthly planDriftMonthly)
    {
        return toAjax(planDriftMonthlyService.insertPlanDriftMonthly(planDriftMonthly));
    }

    /**
     * 修改掘进月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planDriftMonthly:edit')")
    @Log(title = "掘进月计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanDriftMonthly planDriftMonthly)
    {
        return toAjax(planDriftMonthlyService.updatePlanDriftMonthly(planDriftMonthly));
    }

    /**
     * 删除掘进月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planDriftMonthly:remove')")
    @Log(title = "掘进月计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(planDriftMonthlyService.deletePlanDriftMonthlyByIds(ids));
    }

    /**
     * 批量保存掘进月计划（增删改查）
     * 传入批量列表，验证是否同一个月份的数据，然后查询这个月份的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('plan:planDriftMonthly:edit')")
    @Log(title = "掘进月计划批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<PlanDriftMonthlyBatchDto> batchDataList)
    {
        try {
            int result = planDriftMonthlyService.batchSavePlanDriftMonthly(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
