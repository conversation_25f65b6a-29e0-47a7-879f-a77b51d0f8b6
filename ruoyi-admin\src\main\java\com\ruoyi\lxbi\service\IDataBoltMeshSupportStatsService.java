package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.response.DataSupportTypeTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportTypeDepartmentWithPlanStats;

import java.util.List;

/**
 * 锚网支护数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IDataBoltMeshSupportStatsService {

    /**
     * 查询锚网支护总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 锚网支护总体统计数据集合（含计划量）
     */
    public List<DataSupportTypeTotalWithPlanStats> selectTotalWithPlanStatsList(DataSupportStatsRequest request, String viewType);

    /**
     * 查询锚网支护项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 锚网支护项目部门统计数据集合（含计划量）
     */
    public List<DataSupportTypeDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataSupportStatsRequest request, String viewType);
}
