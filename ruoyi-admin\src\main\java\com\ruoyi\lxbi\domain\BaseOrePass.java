package com.ruoyi.lxbi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;
import java.util.Date;

/**
 * 溜井配置对象 base_ore_pass
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseOrePass extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 溜井ID */
    private Long orePassId;

    /** 溜井名称 */
    @Excel(name = "溜井名称")
    private String orePassName;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 溜井开始时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "溜井开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 溜井结束时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "溜井结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 是否删除 */
    private Long isDelete;

}
