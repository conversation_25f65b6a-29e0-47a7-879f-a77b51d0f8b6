package com.ruoyi.framework.service;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import com.ruoyi.framework.config.MqttConfig;

import java.util.HashMap;
import java.util.Map;

/**
 * MQTT连接服务 - 支持多个broker连接
 * 负责建立和管理多个MQTT连接
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Slf4j
@Service
public class MqttConnectionService implements ApplicationRunner {

    @Autowired
    private MqttConfig mqttConfig;

    @Autowired
    private MqttMessageService mqttMessageService;

    // 存储多个MQTT客户端连接
    private Map<String, MqttAsyncClient> mqttClients = new HashMap<>();

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 暂时屏蔽MQTT连接，专注于Kafka监控
        log.info("MQTT连接已暂时屏蔽，专注于Kafka监控功能");
        // connectToAllMqttBrokers();
    }

    /**
     * 连接到所有配置的MQTT Broker
     */
    public void connectToAllMqttBrokers() {
        if (mqttConfig.getConnections() == null || mqttConfig.getConnections().isEmpty()) {
            log.warn("没有配置MQTT连接");
            return;
        }

        log.info("开始连接到所有MQTT Broker...");

        for (Map.Entry<String, MqttConfig.MqttConnectionConfig> entry : mqttConfig.getConnections().entrySet()) {
            String connectionName = entry.getKey();
            MqttConfig.MqttConnectionConfig config = entry.getValue();

            try {
                connectToMqttBroker(connectionName, config);
            } catch (Exception e) {
                log.error("连接MQTT Broker失败 - 连接名: {}", connectionName, e);
            }
        }
    }

    /**
     * 连接到单个MQTT Broker
     */
    private void connectToMqttBroker(String connectionName, MqttConfig.MqttConnectionConfig config) {
        try {
            log.info("连接MQTT Broker - 连接名: {}, Broker: {}, 客户端ID: {}",
                connectionName, config.getBrokerUrl(), config.getClientId());
            log.info("订阅主题: {}", config.getTopics());

            // 创建MQTT客户端
            MqttAsyncClient mqttClient = new MqttAsyncClient(config.getBrokerUrl(), config.getClientId());

            // 设置回调
            mqttClient.setCallback(new MqttCallbackHandler(connectionName));

            // 设置连接选项
            MqttConnectOptions options = createMqttConnectOptions();

            // 连接到broker
            IMqttToken connectToken = mqttClient.connect(options);
            connectToken.waitForCompletion();

            if (mqttClient.isConnected()) {
                log.info("MQTT连接成功 - 连接名: {}", connectionName);

                // 存储客户端
                mqttClients.put(connectionName, mqttClient);

                // 订阅主题
                subscribeToTopics(connectionName, mqttClient, config.getTopics());
            } else {
                log.error("MQTT连接失败 - 连接名: {}", connectionName);
            }

        } catch (Exception e) {
            log.error("连接MQTT Broker时发生错误 - 连接名: {}", connectionName, e);
        }
    }

    /**
     * 创建MQTT连接选项
     */
    private MqttConnectOptions createMqttConnectOptions() {
        MqttConnectOptions options = new MqttConnectOptions();

        if (mqttConfig.getCommon() != null) {
            options.setConnectionTimeout(mqttConfig.getCommon().getConnectionTimeout());
            options.setKeepAliveInterval(mqttConfig.getCommon().getKeepAliveInterval());
            options.setAutomaticReconnect(mqttConfig.getCommon().isAutomaticReconnect());
            options.setCleanSession(mqttConfig.getCommon().isCleanSession());

            if (mqttConfig.getCommon().getUsername() != null && !mqttConfig.getCommon().getUsername().trim().isEmpty()) {
                options.setUserName(mqttConfig.getCommon().getUsername());
            }
            if (mqttConfig.getCommon().getPassword() != null && !mqttConfig.getCommon().getPassword().trim().isEmpty()) {
                options.setPassword(mqttConfig.getCommon().getPassword().toCharArray());
            }
        }

        return options;
    }

    /**
     * 订阅主题
     */
    private void subscribeToTopics(String connectionName, MqttAsyncClient mqttClient, java.util.List<String> topics) {
        try {
            for (String topic : topics) {
                IMqttToken subscribeToken = mqttClient.subscribe(topic, 1);
                subscribeToken.waitForCompletion();
                log.info("成功订阅主题 - 连接名: {}, 主题: {}", connectionName, topic);
            }
        } catch (Exception e) {
            log.error("订阅主题时发生错误 - 连接名: {}", connectionName, e);
        }
    }

    /**
     * 检查所有连接状态
     */
    public Map<String, Boolean> getConnectionStatus() {
        Map<String, Boolean> status = new HashMap<>();
        for (Map.Entry<String, MqttAsyncClient> entry : mqttClients.entrySet()) {
            status.put(entry.getKey(), entry.getValue().isConnected());
        }
        return status;
    }

    /**
     * 断开所有连接
     */
    public void disconnectAll() {
        for (Map.Entry<String, MqttAsyncClient> entry : mqttClients.entrySet()) {
            try {
                if (entry.getValue().isConnected()) {
                    entry.getValue().disconnect();
                    log.info("MQTT连接已断开 - 连接名: {}", entry.getKey());
                }
            } catch (Exception e) {
                log.error("断开MQTT连接时发生错误 - 连接名: {}", entry.getKey(), e);
            }
        }
    }

    /**
     * MQTT回调处理器
     */
    private class MqttCallbackHandler implements MqttCallback {
        private final String connectionName;

        public MqttCallbackHandler(String connectionName) {
            this.connectionName = connectionName;
        }

        @Override
        public void connectionLost(Throwable cause) {
            log.warn("MQTT连接丢失 - 连接名: {}", connectionName, cause);
            // 自动重连逻辑已在连接选项中设置
        }

        @Override
        public void messageArrived(String topic, MqttMessage message) throws Exception {
            try {
                String payload = new String(message.getPayload());
                log.info("收到MQTT消息 - 连接名: {}, 主题: {}, QoS: {}, 内容: {}",
                    connectionName, topic, message.getQos(), payload);

                // 委托给消息处理服务
                mqttMessageService.processMessage(topic, payload, connectionName);

            } catch (Exception e) {
                log.error("处理MQTT消息时发生错误 - 连接名: {}, 主题: {}", connectionName, topic, e);
            }
        }

        @Override
        public void deliveryComplete(IMqttDeliveryToken token) {
            // 消息发送完成回调（如果需要发送消息的话）
            log.debug("消息发送完成 - 连接名: {}", connectionName);
        }
    }
}
