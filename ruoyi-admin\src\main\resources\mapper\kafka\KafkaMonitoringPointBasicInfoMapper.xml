<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaMonitoringPointBasicInfoMapper">
    
    <resultMap type="KafkaMonitoringPointBasicInfo" id="KafkaMonitoringPointBasicInfoResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="monitoringPointCode"    column="monitoring_point_code"    />
        <result property="systemCode"    column="system_code"    />
        <result property="substationCode"    column="substation_code"    />
        <result property="sensorType"    column="sensor_type"    />
        <result property="monitoringPointValueType"    column="monitoring_point_value_type"    />
        <result property="monitoringPointValueUnit"    column="monitoring_point_value_unit"    />
        <result property="highRange"    column="high_range"    />
        <result property="lowRange"    column="low_range"    />
        <result property="upperLimitAlarmThreshold"    column="upper_limit_alarm_threshold"    />
        <result property="upperLimitReportThreshold"    column="upper_limit_report_threshold"    />
        <result property="lowerLimitAlarmThreshold"    column="lower_limit_alarm_threshold"    />
        <result property="lowerLimitReportThreshold"    column="lower_limit_report_threshold"    />
        <result property="upperLimitPowerOffThreshold"    column="upper_limit_power_off_threshold"    />
        <result property="upperLimitPowerOnThreshold"    column="upper_limit_power_on_threshold"    />
        <result property="lowerLimitPowerOffThreshold"    column="lower_limit_power_off_threshold"    />
        <result property="lowerLimitPowerOnThreshold"    column="lower_limit_power_on_threshold"    />
        <result property="startDescription"    column="start_description"    />
        <result property="stopDescription"    column="stop_description"    />
        <result property="monitoringPointInstallationLocation"    column="monitoring_point_installation_location"    />
        <result property="positionX"    column="position_x"    />
        <result property="positionY"    column="position_y"    />
        <result property="positionZ"    column="position_z"    />
        <result property="sensorAssociationRelationship"    column="sensor_association_relationship"    />
        <result property="dataTime"    column="data_time"    />
    </resultMap>

    <sql id="selectKafkaMonitoringPointBasicInfoVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, mine_name, data_upload_time, monitoring_point_code, system_code, substation_code, sensor_type, monitoring_point_value_type, monitoring_point_value_unit, high_range, low_range, upper_limit_alarm_threshold, upper_limit_report_threshold, lower_limit_alarm_threshold, lower_limit_report_threshold, upper_limit_power_off_threshold, upper_limit_power_on_threshold, lower_limit_power_off_threshold, lower_limit_power_on_threshold, start_description, stop_description, monitoring_point_installation_location, position_x, position_y, position_z, sensor_association_relationship, data_time from kafka_monitoring_point_basic_info
    </sql>

    <select id="selectKafkaMonitoringPointBasicInfoList" parameterType="KafkaMonitoringPointBasicInfo" resultMap="KafkaMonitoringPointBasicInfoResult">
        <include refid="selectKafkaMonitoringPointBasicInfoVo"/>
        <where>  
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="params.beginDataUploadTime != null and params.beginDataUploadTime != '' and params.endDataUploadTime != null and params.endDataUploadTime != ''"> and data_upload_time between #{params.beginDataUploadTime}::date and #{params.endDataUploadTime}::date</if>
            <if test="monitoringPointCode != null  and monitoringPointCode != ''"> and monitoring_point_code = #{monitoringPointCode}</if>
            <if test="systemCode != null  and systemCode != ''"> and system_code = #{systemCode}</if>
            <if test="substationCode != null  and substationCode != ''"> and substation_code = #{substationCode}</if>
            <if test="sensorType != null  and sensorType != ''"> and sensor_type = #{sensorType}</if>
            <if test="monitoringPointValueType != null  and monitoringPointValueType != ''"> and monitoring_point_value_type = #{monitoringPointValueType}</if>
            <if test="monitoringPointValueUnit != null  and monitoringPointValueUnit != ''"> and monitoring_point_value_unit = #{monitoringPointValueUnit}</if>
            <if test="highRange != null "> and high_range = #{highRange}</if>
            <if test="lowRange != null "> and low_range = #{lowRange}</if>
            <if test="upperLimitAlarmThreshold != null "> and upper_limit_alarm_threshold = #{upperLimitAlarmThreshold}</if>
            <if test="upperLimitReportThreshold != null "> and upper_limit_report_threshold = #{upperLimitReportThreshold}</if>
            <if test="lowerLimitAlarmThreshold != null "> and lower_limit_alarm_threshold = #{lowerLimitAlarmThreshold}</if>
            <if test="lowerLimitReportThreshold != null "> and lower_limit_report_threshold = #{lowerLimitReportThreshold}</if>
            <if test="upperLimitPowerOffThreshold != null "> and upper_limit_power_off_threshold = #{upperLimitPowerOffThreshold}</if>
            <if test="upperLimitPowerOnThreshold != null "> and upper_limit_power_on_threshold = #{upperLimitPowerOnThreshold}</if>
            <if test="lowerLimitPowerOffThreshold != null "> and lower_limit_power_off_threshold = #{lowerLimitPowerOffThreshold}</if>
            <if test="lowerLimitPowerOnThreshold != null "> and lower_limit_power_on_threshold = #{lowerLimitPowerOnThreshold}</if>
            <if test="startDescription != null  and startDescription != ''"> and start_description = #{startDescription}</if>
            <if test="stopDescription != null  and stopDescription != ''"> and stop_description = #{stopDescription}</if>
            <if test="monitoringPointInstallationLocation != null  and monitoringPointInstallationLocation != ''"> and monitoring_point_installation_location = #{monitoringPointInstallationLocation}</if>
            <if test="positionX != null "> and position_x = #{positionX}</if>
            <if test="positionY != null "> and position_y = #{positionY}</if>
            <if test="positionZ != null "> and position_z = #{positionZ}</if>
            <if test="sensorAssociationRelationship != null  and sensorAssociationRelationship != ''"> and sensor_association_relationship = #{sensorAssociationRelationship}</if>
            <if test="params.beginDataTime != null and params.beginDataTime != '' and params.endDataTime != null and params.endDataTime != ''"> and data_time between #{params.beginDataTime}::date and #{params.endDataTime}::date</if>
        </where>
    </select>
    
    <select id="selectKafkaMonitoringPointBasicInfoById" parameterType="Long" resultMap="KafkaMonitoringPointBasicInfoResult">
        <include refid="selectKafkaMonitoringPointBasicInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertKafkaMonitoringPointBasicInfo" parameterType="KafkaMonitoringPointBasicInfo" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_monitoring_point_basic_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null">file_encoding,</if>
            <if test="mineCode != null">mine_code,</if>
            <if test="mineName != null">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="monitoringPointCode != null">monitoring_point_code,</if>
            <if test="systemCode != null">system_code,</if>
            <if test="substationCode != null">substation_code,</if>
            <if test="sensorType != null">sensor_type,</if>
            <if test="monitoringPointValueType != null">monitoring_point_value_type,</if>
            <if test="monitoringPointValueUnit != null">monitoring_point_value_unit,</if>
            <if test="highRange != null">high_range,</if>
            <if test="lowRange != null">low_range,</if>
            <if test="upperLimitAlarmThreshold != null">upper_limit_alarm_threshold,</if>
            <if test="upperLimitReportThreshold != null">upper_limit_report_threshold,</if>
            <if test="lowerLimitAlarmThreshold != null">lower_limit_alarm_threshold,</if>
            <if test="lowerLimitReportThreshold != null">lower_limit_report_threshold,</if>
            <if test="upperLimitPowerOffThreshold != null">upper_limit_power_off_threshold,</if>
            <if test="upperLimitPowerOnThreshold != null">upper_limit_power_on_threshold,</if>
            <if test="lowerLimitPowerOffThreshold != null">lower_limit_power_off_threshold,</if>
            <if test="lowerLimitPowerOnThreshold != null">lower_limit_power_on_threshold,</if>
            <if test="startDescription != null">start_description,</if>
            <if test="stopDescription != null">stop_description,</if>
            <if test="monitoringPointInstallationLocation != null">monitoring_point_installation_location,</if>
            <if test="positionX != null">position_x,</if>
            <if test="positionY != null">position_y,</if>
            <if test="positionZ != null">position_z,</if>
            <if test="sensorAssociationRelationship != null">sensor_association_relationship,</if>
            <if test="dataTime != null">data_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null">#{fileEncoding},</if>
            <if test="mineCode != null">#{mineCode},</if>
            <if test="mineName != null">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="monitoringPointCode != null">#{monitoringPointCode},</if>
            <if test="systemCode != null">#{systemCode},</if>
            <if test="substationCode != null">#{substationCode},</if>
            <if test="sensorType != null">#{sensorType},</if>
            <if test="monitoringPointValueType != null">#{monitoringPointValueType},</if>
            <if test="monitoringPointValueUnit != null">#{monitoringPointValueUnit},</if>
            <if test="highRange != null">#{highRange},</if>
            <if test="lowRange != null">#{lowRange},</if>
            <if test="upperLimitAlarmThreshold != null">#{upperLimitAlarmThreshold},</if>
            <if test="upperLimitReportThreshold != null">#{upperLimitReportThreshold},</if>
            <if test="lowerLimitAlarmThreshold != null">#{lowerLimitAlarmThreshold},</if>
            <if test="lowerLimitReportThreshold != null">#{lowerLimitReportThreshold},</if>
            <if test="upperLimitPowerOffThreshold != null">#{upperLimitPowerOffThreshold},</if>
            <if test="upperLimitPowerOnThreshold != null">#{upperLimitPowerOnThreshold},</if>
            <if test="lowerLimitPowerOffThreshold != null">#{lowerLimitPowerOffThreshold},</if>
            <if test="lowerLimitPowerOnThreshold != null">#{lowerLimitPowerOnThreshold},</if>
            <if test="startDescription != null">#{startDescription},</if>
            <if test="stopDescription != null">#{stopDescription},</if>
            <if test="monitoringPointInstallationLocation != null">#{monitoringPointInstallationLocation},</if>
            <if test="positionX != null">#{positionX},</if>
            <if test="positionY != null">#{positionY},</if>
            <if test="positionZ != null">#{positionZ},</if>
            <if test="sensorAssociationRelationship != null">#{sensorAssociationRelationship},</if>
            <if test="dataTime != null">#{dataTime},</if>
         </trim>
    </insert>

    <update id="updateKafkaMonitoringPointBasicInfo" parameterType="KafkaMonitoringPointBasicInfo">
        update kafka_monitoring_point_basic_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="mineName != null">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="monitoringPointCode != null">monitoring_point_code = #{monitoringPointCode},</if>
            <if test="systemCode != null">system_code = #{systemCode},</if>
            <if test="substationCode != null">substation_code = #{substationCode},</if>
            <if test="sensorType != null">sensor_type = #{sensorType},</if>
            <if test="monitoringPointValueType != null">monitoring_point_value_type = #{monitoringPointValueType},</if>
            <if test="monitoringPointValueUnit != null">monitoring_point_value_unit = #{monitoringPointValueUnit},</if>
            <if test="highRange != null">high_range = #{highRange},</if>
            <if test="lowRange != null">low_range = #{lowRange},</if>
            <if test="upperLimitAlarmThreshold != null">upper_limit_alarm_threshold = #{upperLimitAlarmThreshold},</if>
            <if test="upperLimitReportThreshold != null">upper_limit_report_threshold = #{upperLimitReportThreshold},</if>
            <if test="lowerLimitAlarmThreshold != null">lower_limit_alarm_threshold = #{lowerLimitAlarmThreshold},</if>
            <if test="lowerLimitReportThreshold != null">lower_limit_report_threshold = #{lowerLimitReportThreshold},</if>
            <if test="upperLimitPowerOffThreshold != null">upper_limit_power_off_threshold = #{upperLimitPowerOffThreshold},</if>
            <if test="upperLimitPowerOnThreshold != null">upper_limit_power_on_threshold = #{upperLimitPowerOnThreshold},</if>
            <if test="lowerLimitPowerOffThreshold != null">lower_limit_power_off_threshold = #{lowerLimitPowerOffThreshold},</if>
            <if test="lowerLimitPowerOnThreshold != null">lower_limit_power_on_threshold = #{lowerLimitPowerOnThreshold},</if>
            <if test="startDescription != null">start_description = #{startDescription},</if>
            <if test="stopDescription != null">stop_description = #{stopDescription},</if>
            <if test="monitoringPointInstallationLocation != null">monitoring_point_installation_location = #{monitoringPointInstallationLocation},</if>
            <if test="positionX != null">position_x = #{positionX},</if>
            <if test="positionY != null">position_y = #{positionY},</if>
            <if test="positionZ != null">position_z = #{positionZ},</if>
            <if test="sensorAssociationRelationship != null">sensor_association_relationship = #{sensorAssociationRelationship},</if>
            <if test="dataTime != null">data_time = #{dataTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaMonitoringPointBasicInfoById" parameterType="Long">
        delete from kafka_monitoring_point_basic_info where id = #{id}
    </delete>

    <delete id="deleteKafkaMonitoringPointBasicInfoByIds" parameterType="String">
        delete from kafka_monitoring_point_basic_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>