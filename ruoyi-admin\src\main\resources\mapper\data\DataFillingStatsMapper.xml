<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataFillingStatsMapper">

    <!-- 总体统计结果映射（含计划量） -->
    <resultMap type="DataFillingTotalWithPlanStats" id="DataFillingTotalWithPlanStatsResult">
        <result property="year"                     column="year"                   />
        <result property="month"                    column="month"                  />
        <result property="weekNumber"               column="week"                   />
        <result property="operationDate"            column="operation_date"         />
        <result property="weekStartDate"            column="week_start_date"        />
        <result property="weekEndDate"              column="week_end_date"          />
        <result property="totalSlurryVolume"        column="total_slurry_volume"    />
        <result property="totalCementWeight"        column="total_cement_weight"    />
        <result property="avgFillingConcentration"  column="avg_filling_concentration" />
        <result property="planSlurryVolume"         column="plan_slurry_volume"     />
    </resultMap>

    <!-- 采场统计结果映射 -->
    <resultMap type="DataFillingStopeStats" id="DataFillingStopeStatsResult">
        <result property="year"                     column="year"                   />
        <result property="month"                    column="month"                  />
        <result property="weekNumber"               column="week"                   />
        <result property="operationDate"            column="operation_date"         />
        <result property="weekStartDate"            column="week_start_date"        />
        <result property="weekEndDate"              column="week_end_date"          />
        <result property="stopeId"                  column="stope_id"               />
        <result property="stopeName"                column="stope_name"             />
        <result property="totalSlurryVolume"        column="total_slurry_volume"    />
        <result property="totalCementWeight"        column="total_cement_weight"    />
        <result property="avgFillingConcentration"  column="avg_filling_concentration" />
        <result property="planSlurryVolume"         column="plan_slurry_volume"     />
    </resultMap>

    <!-- ========== 总体统计查询方法（含计划量） ========== -->
    
    <!-- 查询总体统计数据列表（含计划量） (日) -->
    <select id="selectDailyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataFillingTotalWithPlanStatsResult">
        SELECT 
            NULL as year,
            NULL as month,
            NULL as week,
            v.operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.total_slurry_volume,
            v.total_cement_weight,
            v.avg_filling_concentration,
            COALESCE(p.plan_slurry_volume, 0) as plan_slurry_volume
        FROM vdata_filling_daily_total_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                SUM(pbm.filling_volume) / EXTRACT(DAY FROM DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1)) + INTERVAL '1 month' - INTERVAL '1 day') as plan_slurry_volume
            FROM plan_backfilling_monthly pbm
            CROSS JOIN get_financial_month(TO_DATE(pbm.plan_date, 'YYYYMM')) AS fm
            GROUP BY fm.financial_year, fm.financial_month
        ) p ON EXTRACT(YEAR FROM v.operation_date) = p.year AND EXTRACT(MONTH FROM v.operation_date) = p.month
        <where>
            <if test="startDate != null">
                AND v.operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.operation_date
    </select>

    <!-- 查询总体统计数据列表（含计划量） (周) -->
    <select id="selectWeeklyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataFillingTotalWithPlanStatsResult">
        SELECT 
            v.year,
            NULL as month,
            v.week,
            NULL as operation_date,
            v.week_start_date,
            v.week_end_date,
            v.total_slurry_volume,
            v.total_cement_weight,
            v.avg_filling_concentration,
            COALESCE(p.plan_slurry_volume, 0) as plan_slurry_volume
        FROM vdata_filling_weekly_total_stats v
        LEFT JOIN (
            SELECT
                wk.week_year as year,
                wk.week_number as week,
                SUM(pbm.filling_volume) / 4 as plan_slurry_volume
            FROM plan_backfilling_monthly pbm
            CROSS JOIN get_financial_month(TO_DATE(pbm.plan_date, 'YYYYMM')) AS fm
            CROSS JOIN get_week_thu_to_wed(fm.month_start_date) AS wk
            GROUP BY wk.week_year, wk.week_number
        ) p ON v.year = p.year AND v.week = p.week
        <where>
            <if test="startDate != null">
                AND v.week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.week
    </select>

    <!-- 查询总体统计数据列表（含计划量） (月) -->
    <select id="selectMonthlyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataFillingTotalWithPlanStatsResult">
        SELECT 
            v.year,
            v.month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.total_slurry_volume,
            v.total_cement_weight,
            v.avg_filling_concentration,
            COALESCE(p.plan_slurry_volume, 0) as plan_slurry_volume
        FROM vdata_filling_monthly_total_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                SUM(pbm.filling_volume) as plan_slurry_volume
            FROM plan_backfilling_monthly pbm
            CROSS JOIN get_financial_month(TO_DATE(pbm.plan_date, 'YYYYMM')) AS fm
            GROUP BY fm.financial_year, fm.financial_month
        ) p ON v.year = p.year AND v.month = p.month
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.month
    </select>

    <!-- 查询总体统计数据列表（含计划量） (年) -->
    <select id="selectYearlyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataFillingTotalWithPlanStatsResult">
        SELECT 
            v.year,
            NULL as month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.total_slurry_volume,
            v.total_cement_weight,
            v.avg_filling_concentration,
            COALESCE(p.plan_slurry_volume, 0) as plan_slurry_volume
        FROM vdata_filling_yearly_total_stats v
        LEFT JOIN (
            SELECT
                fy.financial_year as year,
                SUM(pbm.filling_volume) as plan_slurry_volume
            FROM plan_backfilling_monthly pbm
            CROSS JOIN get_financial_year(TO_DATE(pbm.plan_date, 'YYYYMM')) AS fy
            GROUP BY fy.financial_year
        ) p ON v.year = p.year
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, 1, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, 1, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year
    </select>

    <!-- ========== 采场统计查询方法 ========== -->
    
    <!-- 查询采场统计数据列表 (日) -->
    <select id="selectDailyStopeStats" parameterType="java.util.Date" resultMap="DataFillingStopeStatsResult">
        SELECT 
            NULL as year,
            NULL as month,
            NULL as week,
            v.operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.stope_id,
            v.stope_name,
            v.total_slurry_volume,
            v.total_cement_weight,
            v.avg_filling_concentration,
            COALESCE(p.plan_slurry_volume, 0) as plan_slurry_volume
        FROM vdata_filling_daily_stope_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                pbm.stope_id,
                SUM(pbm.filling_volume) / EXTRACT(DAY FROM DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1)) + INTERVAL '1 month' - INTERVAL '1 day') as plan_slurry_volume
            FROM plan_backfilling_monthly pbm
            CROSS JOIN get_financial_month(TO_DATE(pbm.plan_date, 'YYYYMM')) AS fm
            GROUP BY fm.financial_year, fm.financial_month, pbm.stope_id
        ) p ON EXTRACT(YEAR FROM v.operation_date) = p.year AND EXTRACT(MONTH FROM v.operation_date) = p.month AND v.stope_id = p.stope_id
        <where>
            <if test="startDate != null">
                AND v.operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.operation_date, v.stope_id
    </select>

    <!-- 查询采场统计数据列表 (周) -->
    <select id="selectWeeklyStopeStats" parameterType="java.util.Date" resultMap="DataFillingStopeStatsResult">
        SELECT 
            v.year,
            NULL as month,
            v.week,
            NULL as operation_date,
            v.week_start_date,
            v.week_end_date,
            v.stope_id,
            v.stope_name,
            v.total_slurry_volume,
            v.total_cement_weight,
            v.avg_filling_concentration,
            COALESCE(p.plan_slurry_volume, 0) as plan_slurry_volume
        FROM vdata_filling_weekly_stope_stats v
        LEFT JOIN (
            SELECT 
                wk.week_year as year,
                wk.week_number as week,
                pm.stope_id,
                SUM(pm.slurry_volume) / 4 as plan_slurry_volume
            FROM plan_filling_monthly pm
            CROSS JOIN get_financial_month(pm.plan_month) AS fm
            CROSS JOIN get_week_thu_to_wed(fm.month_start_date) AS wk
            GROUP BY wk.week_year, wk.week_number, pm.stope_id
        ) p ON v.year = p.year AND v.week = p.week AND v.stope_id = p.stope_id
        <where>
            <if test="startDate != null">
                AND v.week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.week, v.stope_id
    </select>

    <!-- 查询采场统计数据列表 (月) -->
    <select id="selectMonthlyStopeStats" parameterType="java.util.Date" resultMap="DataFillingStopeStatsResult">
        SELECT 
            v.year,
            v.month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.stope_id,
            v.stope_name,
            v.total_slurry_volume,
            v.total_cement_weight,
            v.avg_filling_concentration,
            COALESCE(p.plan_slurry_volume, 0) as plan_slurry_volume
        FROM vdata_filling_monthly_stope_stats v
        LEFT JOIN (
            SELECT 
                fm.financial_year as year,
                fm.financial_month as month,
                pm.stope_id,
                SUM(pm.slurry_volume) as plan_slurry_volume
            FROM plan_filling_monthly pm
            CROSS JOIN get_financial_month(pm.plan_month) AS fm
            GROUP BY fm.financial_year, fm.financial_month, pm.stope_id
        ) p ON v.year = p.year AND v.month = p.month AND v.stope_id = p.stope_id
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.month, v.stope_id
    </select>

    <!-- 查询采场统计数据列表 (年) -->
    <select id="selectYearlyStopeStats" parameterType="java.util.Date" resultMap="DataFillingStopeStatsResult">
        SELECT 
            v.year,
            NULL as month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.stope_id,
            v.stope_name,
            v.total_slurry_volume,
            v.total_cement_weight,
            v.avg_filling_concentration,
            COALESCE(p.plan_slurry_volume, 0) as plan_slurry_volume
        FROM vdata_filling_yearly_stope_stats v
        LEFT JOIN (
            SELECT 
                fy.financial_year as year,
                pm.stope_id,
                SUM(pm.slurry_volume) as plan_slurry_volume
            FROM plan_filling_monthly pm
            CROSS JOIN get_financial_year(pm.plan_month) AS fy
            GROUP BY fy.financial_year, pm.stope_id
        ) p ON v.year = p.year AND v.stope_id = p.stope_id
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, 1, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, 1, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.stope_id
    </select>

</mapper>
