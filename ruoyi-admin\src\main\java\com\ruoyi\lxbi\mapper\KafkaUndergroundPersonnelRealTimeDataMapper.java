package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaUndergroundPersonnelRealTimeData;
import org.apache.ibatis.annotations.Mapper;

/**
 * 井下作业人员实时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface KafkaUndergroundPersonnelRealTimeDataMapper
{
    /**
     * 查询井下作业人员实时数据
     * 
     * @param id 井下作业人员实时数据主键
     * @return 井下作业人员实时数据
     */
    public KafkaUndergroundPersonnelRealTimeData selectKafkaUndergroundPersonnelRealTimeDataById(Long id);

    /**
     * 查询井下作业人员实时数据列表
     * 
     * @param kafkaUndergroundPersonnelRealTimeData 井下作业人员实时数据
     * @return 井下作业人员实时数据集合
     */
    public List<KafkaUndergroundPersonnelRealTimeData> selectKafkaUndergroundPersonnelRealTimeDataList(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData);

    /**
     * 新增井下作业人员实时数据
     * 
     * @param kafkaUndergroundPersonnelRealTimeData 井下作业人员实时数据
     * @return 结果
     */
    public int insertKafkaUndergroundPersonnelRealTimeData(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData);

    /**
     * 修改井下作业人员实时数据
     * 
     * @param kafkaUndergroundPersonnelRealTimeData 井下作业人员实时数据
     * @return 结果
     */
    public int updateKafkaUndergroundPersonnelRealTimeData(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData);

    /**
     * 删除井下作业人员实时数据
     * 
     * @param id 井下作业人员实时数据主键
     * @return 结果
     */
    public int deleteKafkaUndergroundPersonnelRealTimeDataById(Long id);

    /**
     * 批量删除井下作业人员实时数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaUndergroundPersonnelRealTimeDataByIds(Long[] ids);
}
