package com.ruoyi.common.core.excel;

import cn.idev.excel.FastExcel;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.read.listener.ReadListener;
import cn.idev.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.TypeReference;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.excel.*;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.excel.ExcelErrorWriteHandler;
import com.ruoyi.common.utils.excel.ExcelSelectedHandler;
import com.ruoyi.common.utils.excel.FastExcelUtil;
import com.ruoyi.common.utils.reflect.ReflectUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Excel导入处理器抽象基类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class ExcelImportHandler<T> {

    private static final String tmpKey = "excel:tmp:";
    private static final String failKey = "excel:fail:";

    /**
     * 默认示例数据实现 - 不提供示例数据
     */
    public List<T> exampleData() {
        return Collections.emptyList();
    }

    /**
     * 验证单条数据
     */
    protected abstract void validateData(ExcelDataInfo<T> dataInfo, ExcelImportContext context);

    /**
     * 验证单行数据（公共方法）
     */
    public ExcelDataInfo<T> validateRow(ExcelDataInfo<T> rowData) {
        Class<T> entityClass = getEntityClass();
        ExcelImportTemplate template = entityClass.getAnnotation(ExcelImportTemplate.class);
        assert template != null;

        // 创建上下文
        ExcelImportContext ctx = createContext(template.key());
        ctx.setCurrentRowIndex(rowData.getRow());

        // 创建新的数据信息对象，避免修改原对象
        ExcelDataInfo<T> result = new ExcelDataInfo<>();
        result.setRow(rowData.getRow());
        T data = JSON.parseObject(JSON.toJSONString(rowData.getData()), getEntityClass());
        result.setData(data);

        try {
            // 处理选项值
            handleOptionValue(data, ctx);
            // 基础验证
            validateBase(result, ctx);
            // 业务验证
            validateData(result, ctx);
        } catch (Exception e) {
            result.addError(null, e.getMessage());
            log.error("验证单行数据失败", e);
        }

        return result;
    }

    /**
     * 保存单条数据
     */
    protected abstract void saveData(T data, ExcelImportContext context);

    /**
     * 初始化上下文（在处理数据前调用）
     */
    protected void initContext(ExcelImportContext context) {
    }

    protected void afterAllValidated(ExcelImportContext ctx) {
    }

    protected void afterAllImported(ExcelImportContext ctx) {
    }

    /**
     * 导出Excel模板
     */
    public void template(HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            ExcelTemplateInfo templateInfo = getTemplateInfo();
            String fileName = URLEncoder.encode(templateInfo.getName() + "模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            template(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException("导出Excel模板失败", e);
        }
    }

    public void template(OutputStream out) {
        ExcelTemplateInfo templateInfo = getTemplateInfo();
        HorizontalCellStyleStrategy styleStrategy = FastExcelUtil.createStyleStrategy();
        ExcelSelectedHandler enhancedHandler = new ExcelSelectedHandler(
                templateInfo.getFieldOptions(),
                templateInfo.getMaxRows()
        );
        FastExcel.write(out, getEntityClass())
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(enhancedHandler)
                .sheet(templateInfo.getSheetName())
                .doWrite(exampleData());
    }

    /**
     * 获取实体类类型
     */
    protected Class<T> getEntityClass() {
        return ReflectUtils.getClassGenricType(getClass(), 0);
    }

    private Map<String, ExcelOptionMapping> getOptionMappings() {
        Map<String, ExcelOptionMapping> mappings = new HashMap<>();
        Field[] declaredFields = getEntityClass().getDeclaredFields();
        for (Field field : declaredFields) {
            ExcelSelected optionField = field.getAnnotation(ExcelSelected.class);
            if (optionField == null) {
                continue;
            }
            if (optionField.link() == ExcelSelected.FieldLinkType.LABEL) {
                String key = StringUtils.isBlank(optionField.optionKey()) ? field.getName() : optionField.optionKey();
                if (!mappings.containsKey(key)) {
                    mappings.put(key, new ExcelOptionMapping());
                }
                ExcelOptionMapping mapping = mappings.get(key);
                mapping.setLabel(field);
            } else if (optionField.link() == ExcelSelected.FieldLinkType.VALUE) {
                String key = StringUtils.isBlank(optionField.optionKey()) ? field.getName() : optionField.optionKey();
                if (!mappings.containsKey(key)) {
                    mappings.put(key, new ExcelOptionMapping());
                }
                ExcelOptionMapping mapping = mappings.get(key);
                mapping.setValue(field);
            }
        }
        return mappings;
    }

    private void handleOptionValue(T data, ExcelImportContext ctx) {
        for (Map.Entry<String, ExcelOptionMapping> entry : ctx.getOptionMappings().entrySet()) {
            String optionKey = entry.getKey();
            List<ExcelOptionInfo> options = ctx.getOptions(optionKey);
            if (options == null) {
                continue;
            }
            Map<String, ExcelOptionInfo> optionMap = options.stream()
                    .collect(Collectors.toMap(ExcelOptionInfo::getLabel, option -> option));
            ExcelOptionMapping mapping = entry.getValue();
            if (mapping.getValue() != null && mapping.getLabel() != null) {
                try {
                    mapping.getValue().setAccessible(true);
                    mapping.getLabel().setAccessible(true);
                    String label = (String) mapping.getLabel().get(data);
                    if (StringUtils.isNotBlank(label)) {
                        ExcelOptionInfo option = optionMap.get(label);
                        if (label.equals(option.getLabel())) {
                            mapping.getValue().set(data, option.getValue());
                        }
                    }
                    mapping.getValue().setAccessible(false);
                    mapping.getLabel().setAccessible(false);
                } catch (Exception e) {
                    log.error("处理选项值失败", e);
                }
            }
        }
    }

    private void handleOptionLabel(T data, ExcelImportContext ctx) {
        for (Map.Entry<String, ExcelOptionMapping> entry : ctx.getOptionMappings().entrySet()) {
            String optionKey = entry.getKey();
            List<ExcelOptionInfo> options = ctx.getOptions(optionKey);
            if (options == null) {
                continue;
            }
            Map<String, ExcelOptionInfo> optionMap = options.stream()
                    .collect(Collectors.toMap(ExcelOptionInfo::getLabel, option -> option));
            ExcelOptionMapping mapping = entry.getValue();
            if (mapping.getValue() != null && mapping.getLabel() != null) {
                try {
                    mapping.getValue().setAccessible(true);
                    mapping.getLabel().setAccessible(true);
                    String value = (String) mapping.getValue().get(data);
                    if (StringUtils.isNotBlank(value)) {
                        ExcelOptionInfo option = optionMap.get(value);
                        if (option != null) {
                            mapping.getLabel().set(data, option.getLabel());
                        }
                    }
                    mapping.getValue().setAccessible(false);
                    mapping.getLabel().setAccessible(false);
                } catch (Exception e) {
                    log.error("处理选项值失败", e);
                }
            }
        }
    }

    /**
     * 获取模板信息
     */
    public ExcelTemplateInfo getTemplateInfo() {
        Class<T> entityClass = getEntityClass();
        ExcelImportTemplate template = entityClass.getAnnotation(ExcelImportTemplate.class);

        if (template == null) {
            throw new RuntimeException("实体类必须使用@ExcelImportTemplate注解");
        }

        ExcelTemplateInfo templateInfo = new ExcelTemplateInfo();
        templateInfo.setKey(template.key());
        templateInfo.setName(template.name());
        templateInfo.setDescription(template.description());
        templateInfo.setSheetName(template.sheetName());
        templateInfo.setTitleRows(template.titleRows());
        templateInfo.setMaxRows(template.maxRows());


        // 创建上下文并初始化选项
        ExcelImportContext context = createContext(template.key());
        // 解析字段信息
        templateInfo.setFields(context.getFields());
        // 解析字段选项
        templateInfo.setFieldOptions(context.getOptionMap());

        return templateInfo;
    }

    /**
     * 解析字段信息
     */
    private List<ExcelFieldInfo> parseFields(Class<T> entityClass) {
        List<ExcelFieldInfo> fields = new ArrayList<>();

        Field[] declaredFields = entityClass.getDeclaredFields();
        for (Field field : declaredFields) {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                fields.add(getFieldInfo(field, excelProperty));
            }
        }

        // 按index排序
        fields.sort(Comparator.comparing(ExcelFieldInfo::getSort));
        return fields;
    }

    private static ExcelFieldInfo getFieldInfo(Field field, ExcelProperty excelProperty) {
        ExcelFieldInfo fieldInfo = new ExcelFieldInfo();
        fieldInfo.setFieldName(field.getName());

        // 使用ExcelProperty的value作为显示名称
        String[] values = excelProperty.value();
        String displayName = values.length > 0 ? values[0] : field.getName();
        fieldInfo.setDisplayName(displayName);

        fieldInfo.setFieldType(field.getType().getSimpleName());
        fieldInfo.setSort(excelProperty.index());

        ExcelRequired annotation = field.getAnnotation(ExcelRequired.class);
        fieldInfo.setRequired(annotation != null && annotation.enabled());

        // 检查是否有选项配置
        ExcelSelected option = field.getAnnotation(ExcelSelected.class);
        if (option == null) {
            return fieldInfo;
        }
        fieldInfo.setHasOptions(option.link() == ExcelSelected.FieldLinkType.LABEL);
        if (fieldInfo.getHasOptions()) {
            fieldInfo.setOptionKey(StringUtils.isNotBlank(option.optionKey()) ? option.optionKey() : field.getName());
            fieldInfo.setDictType(option.type());
            fieldInfo.setDictCode(option.dictCode());
        }
        return fieldInfo;
    }

    /**
     * 获取字典选项
     */
    private List<ExcelOptionInfo> getDictOptions(String dictType) {
        List<ExcelOptionInfo> options = new ArrayList<>();
        try {
            List<SysDictData> dictDataList = DictUtils.getDictCache(dictType);
            if (dictDataList != null) {
                for (SysDictData dictData : dictDataList) {
                    options.add(new ExcelOptionInfo(dictData.getDictValue(), dictData.getDictLabel()));
                }
            }
        } catch (Exception e) {
            log.error("获取字典选项失败", e);
        }
        return options;
    }

    public ExcelImportResult<T> validateExcel(InputStream inputStream) {
        ExcelImportResult<T> result = new ExcelImportResult<>();
        ExcelImportResult<T> cache = new ExcelImportResult<>();
        cache.setId(result.getId());
        Class<T> entityClass = getEntityClass();
        ExcelImportTemplate template = entityClass.getAnnotation(ExcelImportTemplate.class);
        // 创建上下文并初始化
        assert template != null;
        ExcelImportContext ctx = createContext(template.key());

        // 创建读取监听器
        ReadListener<T> readListener = new ReadListener<>() {
            @Override
            public void invoke(T data, AnalysisContext context) {
                ExcelDataInfo<T> dataInfo = new ExcelDataInfo<>();
                dataInfo.setRow(context.readRowHolder().getRowIndex() + 1);
                dataInfo.setData(data);
                try {
                    validateDataInfo(dataInfo, ctx);
                } catch (Exception e) {
                    dataInfo.addError(null, e.getMessage());
                    log.error("处理选项值失败", e);
                }
                result.addFail(dataInfo);
                cache.addData(dataInfo);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                ctx.setTotalRows(result.getTotal());
                afterAllValidated(ctx);
            }
        };

        FastExcel.read(inputStream, getEntityClass(), readListener)
                .sheet()
                .doRead();
        // 缓存验证数据到Redis
        RedisTemplate<String, String> redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
        redisTemplate.opsForValue().set(tmpKey + result.getId(), JSON.toJSONString(cache, JSONWriter.Feature.WriteClassName), 1, TimeUnit.HOURS);
        return result;
    }

    private void validateBase(ExcelDataInfo<T> info, ExcelImportContext ctx) {
        for (ExcelFieldInfo field : ctx.getFields()) {
            String fieldName = field.getFieldName();
            String columnName = field.getDisplayName();
            T data = info.getData();
            Field fieldObj = ReflectUtils.getAccessibleField(data, fieldName);
            if (fieldObj == null) {
                info.addError(fieldName, "字段访问异常");
                continue;
            }
            try {
                Object value = fieldObj.get(data);
                String stringValue = value != null ? value.toString() : "";

                // 必填字段验证
                ExcelRequired excelRequired = fieldObj.getAnnotation(ExcelRequired.class);
                if (excelRequired != null && excelRequired.enabled()) {
                    if (value == null || org.apache.commons.lang3.StringUtils.isEmpty(stringValue.trim())) {
                        String message = org.apache.commons.lang3.StringUtils.isNotEmpty(excelRequired.message()) ?
                                excelRequired.message() : columnName + "为必填项";
                        info.addError(fieldName, message);
                        continue; // 必填验证失败，跳过其他验证
                    }
                }

                // 数字类型验证
                if (fieldObj.getType() == Integer.class || fieldObj.getType() == int.class ||
                        fieldObj.getType() == Long.class || fieldObj.getType() == long.class ||
                        fieldObj.getType() == Double.class || fieldObj.getType() == double.class) {
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(stringValue)) {
                        try {
                            if (fieldObj.getType() == Integer.class || fieldObj.getType() == int.class) {
                                Integer.parseInt(stringValue);
                            } else if (fieldObj.getType() == Long.class || fieldObj.getType() == long.class) {
                                Long.parseLong(stringValue);
                            } else {
                                Double.parseDouble(stringValue);
                            }
                        } catch (NumberFormatException e) {
                            info.addError(fieldName, "数字格式不正确");
                            log.error("处理选项值失败", e);
                        }
                    }
                }

            } catch (IllegalAccessException e) {
                info.addError(fieldName, "字段访问异常");
            }
        }
    }

    public ExcelImportResult<T> processImport(String cacheId, List<ExcelDataInfo<T>> dataList) {
        ExcelImportResult<T> result = new ExcelImportResult<>();
        result.setId(cacheId);

        // 从Redis中获取缓存数据
        RedisTemplate<String, String> redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
        String cacheData = redisTemplate.opsForValue().get(tmpKey + cacheId);
        if (cacheData == null) {
            throw new RuntimeException("缓存数据已过期，请重新上传文件");
        }

        ExcelImportResult<T> cache = JSON.parseObject(cacheData, new TypeReference<>() {
        }, JSONReader.Feature.SupportAutoType);
        if (cache == null) {
            throw new RuntimeException("缓存数据已过期，请重新上传文件");
        }

        Class<T> entityClass = getEntityClass();
        ExcelImportTemplate template = entityClass.getAnnotation(ExcelImportTemplate.class);
        // 创建上下文并初始化
        assert template != null;
        ExcelImportContext ctx = createContext(template.key());
        Map<Integer, ExcelDataInfo<T>> fixData = dataList.stream().collect(Collectors.toMap(ExcelDataInfo::getRow, data -> data));

        cache.getDataList().sort(Comparator.comparingInt(ExcelDataInfo::getRow));
        // 从缓存中获取数据
        for (ExcelDataInfo<T> dataInfo : cache.getDataList()) {
            try {
                ctx.setCurrentRowIndex(dataInfo.getRow());
                if (dataInfo.getType() == null) {
                    saveData(dataInfo.getData(), ctx);
                    result.addFail(new ExcelDataInfo<>());
                } else {
                    ExcelDataInfo<T> fixDataInfo = fixData.get(dataInfo.getRow());
                    if (fixDataInfo != null) {
                        dataInfo = fixDataInfo;
                        T data = JSON.parseObject(JSON.toJSONString(fixDataInfo.getData()), getEntityClass());
                        dataInfo.setData(data);
                        validateDataInfo(dataInfo, ctx);
                        if (dataInfo.getType() != ExcelDataInfo.RemindType.ERROR) {
                            saveData(dataInfo.getData(), ctx);
                        }
                        result.addFail(dataInfo);
                    } else if (dataInfo.getType() == ExcelDataInfo.RemindType.WARNING) {
                        saveData(dataInfo.getData(), ctx);
                        result.addFail(new ExcelDataInfo<>());
                    } else {
                        result.addFail(dataInfo);
                    }
                }
            } catch (Exception e) {
                dataInfo.addError(null, e.getMessage());
                result.addFail(dataInfo);
                log.error("处理选项值失败", e);
            }
        }
        ctx.setTotalRows(result.getTotal());
        afterAllImported(ctx);

        redisTemplate.delete(tmpKey + cacheId);
        redisTemplate.opsForValue().set(failKey + cacheId, JSON.toJSONString(result, JSONWriter.Feature.WriteClassName), 1, TimeUnit.HOURS);
        return result;
    }

    private void validateDataInfo(ExcelDataInfo<T> dataInfo, ExcelImportContext ctx) {
        ctx.setCurrentRowIndex(dataInfo.getRow());
        handleOptionValue(dataInfo.getData(), ctx);
        validateBase(dataInfo, ctx);
        validateData(dataInfo, ctx);
    }

    public void downloadFailures(String cacheId, OutputStream out) {
        RedisTemplate<String, String> redisTemplate = SpringUtils.getBean(StringRedisTemplate.class);
        String failData = redisTemplate.opsForValue().get(failKey + cacheId);
        if (failData == null) {
            throw new RuntimeException("失败数据已过期，请重新上传文件");
        }

        ExcelImportResult<T> result = JSON.parseObject(failData, new TypeReference<>() {
        }, JSONReader.Feature.SupportAutoType);

        Class<T> entityClass = getEntityClass();
        ExcelImportTemplate template = entityClass.getAnnotation(ExcelImportTemplate.class);
        // 创建上下文并初始化
        assert template != null;
        ExcelImportContext ctx = createContext(template.key());

        for (ExcelDataInfo<T> info : result.getDataList()) {
            handleOptionLabel(info.getData(), ctx);
        }
        List<String> messages = result.getDataList().stream()
                .map(data -> data.getMessages().stream().map(ExcelDataInfo.Message::getMessage)
                        .collect(Collectors.joining(","))
                ).toList();

        HorizontalCellStyleStrategy styleStrategy = FastExcelUtil.createStyleStrategy();
        ExcelSelectedHandler enhancedHandler = new ExcelSelectedHandler(
                ctx.getOptionMap(),
                template.maxRows()
        );

        FastExcel.write(out, getEntityClass())
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(enhancedHandler)
                .registerWriteHandler(new ExcelErrorWriteHandler(messages))
                .sheet(template.sheetName() + "-失败数据")
                .doWrite(result.getDataList().stream().map(ExcelDataInfo::getData).toList());
        redisTemplate.delete(failKey + cacheId);
    }

    private ExcelImportContext createContext(String templateKey) {
        ExcelImportContext ctx = new ExcelImportContext();
        ctx.setTemplateKey(templateKey);
        ctx.setOptionMappings(getOptionMappings());
        ctx.setFields(parseFields(getEntityClass()));
        initContext(ctx);
        ctx.getFields().stream().filter(ExcelFieldInfo::getHasOptions)
                .filter(v -> "dict".equals(v.getDictType()))
                .forEach(v -> ctx.setOptions(v.getOptionKey(), getDictOptions(v.getDictCode())));
        return ctx;
    }

    public void downloadFailures(String cacheId, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        ExcelTemplateInfo templateInfo = getTemplateInfo();
        String fileName = URLEncoder.encode(templateInfo.getName() + "-失败数据", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        try (OutputStream out = response.getOutputStream()) {
            downloadFailures(cacheId, out);
        }
    }

}
