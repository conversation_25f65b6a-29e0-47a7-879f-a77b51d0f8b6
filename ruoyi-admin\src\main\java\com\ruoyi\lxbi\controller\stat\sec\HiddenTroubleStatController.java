package com.ruoyi.lxbi.controller.stat.sec;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.admin.service.IHiddenTroubleStatService;
import com.ruoyi.lxbi.domain.vo.*;
import com.ruoyi.lxbi.utils.DateRangeCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 隐患统计Controller
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Slf4j
@RestController
@RequestMapping("/sec/hiddenTroubleStat")
public class HiddenTroubleStatController extends BaseController {
    
    @Autowired
    private IHiddenTroubleStatService hiddenTroubleStatService;

    /**
     * 获取隐患分析概览数据
     * 包括：隐患总数、重大隐患数、危险源数、风险管控数
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTroubleStat:overview')")
    @Anonymous
    @GetMapping("/overview")
    @Log(title = "隐患分析概览", businessType = BusinessType.OTHER)
    public R<HiddenTroubleOverviewVO> getOverview(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取隐患分析概览数据，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证视图类型参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            HiddenTroubleOverviewVO overview = hiddenTroubleStatService.getOverviewStatistics(viewType, startDate, endDate);
            return R.ok(overview);
        } catch (Exception e) {
            log.error("获取隐患分析概览数据失败", e);
            return R.fail("获取概览数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门隐患分布统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *
     **/
//    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTroubleStat:department')")
    @Anonymous
    @GetMapping("/departmentDistribution")
    @Log(title = "部门隐患分布统计", businessType = BusinessType.OTHER)
    public R<List<DepartmentDistributionVO>> getDepartmentDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取部门隐患分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<DepartmentDistributionVO> distribution = hiddenTroubleStatService.getDepartmentDistribution(viewType, startDate, endDate);
            return R.ok(distribution);
        } catch (Exception e) {
            log.error("获取部门隐患分布统计失败", e);
            return R.fail("获取部门分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取高频发隐患位置统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTroubleStat:location')")
    @GetMapping("/locationFrequency")
    @Anonymous
    @Log(title = "高频发隐患位置统计", businessType = BusinessType.OTHER)
    public R<List<LocationFrequencyVO>> getLocationFrequency(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取高频发隐患位置统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<LocationFrequencyVO> locationStats = hiddenTroubleStatService.getLocationFrequency(viewType, startDate, endDate);
            return R.ok(locationStats);
        } catch (Exception e) {
            log.error("获取高频发隐患位置统计失败", e);
            return R.fail("获取位置频率统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取超期隐患趋势统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTroubleStat:trend')")
    @Anonymous
    @GetMapping("/trend")
    @Log(title = "超期隐患趋势统计", businessType = BusinessType.OTHER)
    public R<TrendResponseVO> getTrendStatistics(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取超期隐患趋势统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            // 对于按日查询，如果天数少于7天，向前推到7天
            if ("daily".equals(viewType)) {
                startDate = DateRangeCalculator.ensureMinimumDaysForDaily(startDate, endDate, 7);
            }

            List<TrendStatisticsVO> trendData = hiddenTroubleStatService.getOverdueTrendStatistics(viewType, startDate, endDate);

            TrendResponseVO result = new TrendResponseVO();
            result.setPeriod(viewType);
            result.setStartDate(startDate);
            result.setEndDate(endDate);
            result.setData(trendData);

            return R.ok(result);
        } catch (Exception e) {
            log.error("获取超期隐患趋势统计失败", e);
            return R.fail("获取超期隐患趋势统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取隐患状态分布统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTroubleStat:status')")
    @Anonymous
    @GetMapping("/statusDistribution")
    @Log(title = "隐患状态分布统计", businessType = BusinessType.OTHER)
    public R<List<StatusDistributionVO>> getStatusDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取隐患状态分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<StatusDistributionVO> statusStats = hiddenTroubleStatService.getStatusDistribution(viewType, startDate, endDate);
            return R.ok(statusStats);
        } catch (Exception e) {
            log.error("获取隐患状态分布统计失败", e);
            return R.fail("获取状态分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取隐患等级分布统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
//    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTroubleStat:grade')")
    @GetMapping("/gradeDistribution")
    @Log(title = "隐患等级分布统计", businessType = BusinessType.OTHER)
    public R<List<GradeDistributionVO>> getGradeDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取隐患等级分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<GradeDistributionVO> gradeStats = hiddenTroubleStatService.getGradeDistribution(viewType, startDate, endDate);
            return R.ok(gradeStats);
        } catch (Exception e) {
            log.error("获取隐患等级分布统计失败", e);
            return R.fail("获取等级分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取综合统计数据（用于仪表板）
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/dashboard")
    @Log(title = "综合统计数据", businessType = BusinessType.OTHER)
    public R<DashboardDataVO> getDashboardData(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取综合统计数据，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            DashboardDataVO dashboardData = hiddenTroubleStatService.getDashboardData(viewType, startDate, endDate);
            return R.ok(dashboardData);
        } catch (Exception e) {
            log.error("获取综合统计数据失败", e);
            return R.fail("获取综合统计数据失败: " + e.getMessage());
        }
    }



    /**
     * 获取隐患类别分布统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *
     **/
    @Anonymous
//    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTroubleStat:category')")
    @GetMapping("/categoryDistribution")
    @Log(title = "隐患类别分布统计", businessType = BusinessType.OTHER)
    public R<List<LocationFrequencyVO>> getCategoryDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取隐患类别分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<LocationFrequencyVO> categoryStats = hiddenTroubleStatService.getCategoryDistribution(viewType, startDate, endDate);
            return R.ok(categoryStats);
        } catch (Exception e) {
            log.error("获取隐患类别分布统计失败", e);
            return R.fail("获取类别分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取超期隐患统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *
     *      **/@Anonymous
//    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTroubleStat:overdue')")
    @GetMapping("/overdueStatistics")
    @Log(title = "超期隐患统计", businessType = BusinessType.OTHER)
    public R<OverdueStatisticsVO> getOverdueStatistics(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取超期隐患统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            OverdueStatisticsVO overdueStats = hiddenTroubleStatService.getOverdueStatistics(viewType, startDate, endDate);
            return R.ok(overdueStats);
        } catch (Exception e) {
            log.error("获取超期隐患统计失败", e);
            return R.fail("获取超期隐患统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取近期隐患趋势（最近30天）
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *
     *      **/
    @Anonymous
    @GetMapping("/recentTrend")
    @Log(title = "近期隐患趋势", businessType = BusinessType.OTHER)
    public R<List<TrendStatisticsVO>> getRecentTrendData(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取近期隐患趋势数据，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<TrendStatisticsVO> trendData = hiddenTroubleStatService.getRecentTrendData(viewType, startDate, endDate);
            return R.ok(trendData);
        } catch (Exception e) {
            log.error("获取近期隐患趋势数据失败", e);
            return R.fail("获取近期趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取隐患整改效率统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *
     *      **/
    @Anonymous
//    @PreAuthorize("@ss.hasPermi('lxbi:hiddenTroubleStat:efficiency')")
    @GetMapping("/rectificationEfficiency")
    @Log(title = "隐患整改效率统计", businessType = BusinessType.OTHER)
    public R<List<RectificationEfficiencyVO>> getRectificationEfficiency(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取隐患整改效率统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<RectificationEfficiencyVO> efficiencyData = hiddenTroubleStatService.getRectificationEfficiency(viewType, startDate, endDate);
            return R.ok(efficiencyData);
        } catch (Exception e) {
            log.error("获取隐患整改效率统计失败", e);
            return R.fail("获取整改效率统计失败: " + e.getMessage());
        }
    }



    /**
     * 获取安全小结统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *
     *      **/
    @Anonymous
    @GetMapping("/safetySummary")
    @Log(title = "安全小结统计", businessType = BusinessType.OTHER)
    public R<SafetySummaryVO> getSafetySummary(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取安全小结统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            SafetySummaryVO safetySummary = hiddenTroubleStatService.getSafetySummary(viewType, startDate, endDate);
            return R.ok(safetySummary);
        } catch (Exception e) {
            log.error("获取安全小结统计失败", e);
            return R.fail("获取安全小结统计失败: " + e.getMessage());
        }
    }

    /**
     * 生成安全小结文本描述
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     *
     *      **/
    @Anonymous
    @GetMapping("/safetySummaryText")
    @Log(title = "安全小结文本生成", businessType = BusinessType.OTHER)
    public R<SafetySummaryTextVO> generateSafetySummaryText(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("生成安全小结文本，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证视图类型参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            SafetySummaryTextVO summaryText = hiddenTroubleStatService.generateSafetySummaryText(viewType, startDate, endDate);
            return R.ok(summaryText);
        } catch (Exception e) {
            log.error("生成安全小结文本失败", e);
            return R.fail("生成安全小结文本失败: " + e.getMessage());
        }
    }
}
