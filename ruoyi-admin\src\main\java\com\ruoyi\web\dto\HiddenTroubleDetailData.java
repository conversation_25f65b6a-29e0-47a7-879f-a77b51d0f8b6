package com.ruoyi.web.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 隐患详情数据DTO
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class HiddenTroubleDetailData {

    /** 隐患任务序列号 */
    @JsonProperty("hiddenTroubleTaskSn")
    private String hiddenTroubleTaskSn;

    /** 隐患序列号 */
    @JsonProperty("hiddenTroubleSn")
    private String hiddenTroubleSn;

    /** 检查时间 */
    @JsonProperty("checkTime")
    private String checkTime;

    /** 检查人姓名 */
    @JsonProperty("checkUserName")
    private String checkUserName;

    /** 整改期限 */
    @JsonProperty("deadlineTime")
    private String deadlineTime;

    /** 风险部位 */
    @JsonProperty("riskPart")
    private String riskPart;

    /** 管控绩效 */
    @JsonProperty("controlPerformance")
    private String controlPerformance;

    /** 隐患等级 */
    @JsonProperty("hiddenTroubleGrade")
    private String hiddenTroubleGrade;

    /** 整改部门名称 */
    @JsonProperty("rectificationDepartmentName")
    private String rectificationDepartmentName;

    /** 整改人姓名 */
    @JsonProperty("rectificationUserName")
    private String rectificationUserName;

    /** 整改意见 */
    @JsonProperty("rectificationOpinion")
    private String rectificationOpinion;

    /** 复查人姓名 */
    @JsonProperty("reexaminationUserName")
    private String reexaminationUserName;

    /** 监督人姓名 */
    @JsonProperty("superviseUserName")
    private String superviseUserName;

    /** 复查时间 */
    @JsonProperty("reexaminationTime")
    private String reexaminationTime;

    /** 状态 (0:待整改 1:已驳回 2:已整改 3:待复查 4:已超期) */
    @JsonProperty("status")
    private Integer status;

    /** 隐患编号 */
    @JsonProperty("hiddenTroubleNumber")
    private String hiddenTroubleNumber;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待整改";
            case 1: return "已驳回";
            case 2: return "已整改";
            case 3: return "待复查";
            case 4: return "已超期";
            default: return "未知";
        }
    }

    /**
     * 获取隐患等级描述
     */
    public String getHiddenTroubleGradeDesc() {
        if ("0".equals(hiddenTroubleGrade)) {
            return "一般隐患";
        } else if ("1".equals(hiddenTroubleGrade)) {
            return "重大隐患";
        }
        return "未知";
    }
}
