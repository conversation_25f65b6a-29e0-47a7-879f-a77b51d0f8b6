<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaUndergroundPersonnelRealTimeDataMapper">
    
    <resultMap type="KafkaUndergroundPersonnelRealTimeData" id="KafkaUndergroundPersonnelRealTimeDataResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="personnelCardCode"    column="personnel_card_code"    />
        <result property="name"    column="name"    />
        <result property="entryAndExitFlag"    column="entry_and_exit_flag"    />
        <result property="entryTime"    column="entry_time"    />
        <result property="exitTime"    column="exit_time"    />
        <result property="areaCode"    column="area_code"    />
        <result property="entryCurrentAreaTime"    column="entry_current_area_time"    />
        <result property="baseStationCode"    column="base_station_code"    />
        <result property="entryCurrentBaseStationTime"    column="entry_current_base_station_time"    />
        <result property="laborOrganizationMethod"    column="labor_organization_method"    />
        <result property="distanceFromBaseStation"    column="distance_from_base_station"    />
        <result property="personnelWorkStatus"    column="personnel_work_status"    />
        <result property="isMineLeader"    column="is_mine_leader"    />
        <result property="isSpecialPersonnel"    column="is_special_personnel"    />
        <result property="trajectoryBaseStationTimeCollection"    column="trajectory_base_station_time_collection"    />
        <result property="dataTime"    column="data_time"    />
    </resultMap>

    <sql id="selectKafkaUndergroundPersonnelRealTimeDataVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, mine_name, data_upload_time, personnel_card_code, name, entry_and_exit_flag, entry_time, exit_time, area_code, entry_current_area_time, base_station_code, entry_current_base_station_time, labor_organization_method, distance_from_base_station, personnel_work_status, is_mine_leader, is_special_personnel, trajectory_base_station_time_collection, data_time from kafka_underground_personnel_real_time_data
    </sql>

    <select id="selectKafkaUndergroundPersonnelRealTimeDataList" parameterType="KafkaUndergroundPersonnelRealTimeData" resultMap="KafkaUndergroundPersonnelRealTimeDataResult">
        <include refid="selectKafkaUndergroundPersonnelRealTimeDataVo"/>
        <where>  
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="params.beginDataUploadTime != null and params.beginDataUploadTime != '' and params.endDataUploadTime != null and params.endDataUploadTime != ''"> and data_upload_time between #{params.beginDataUploadTime}::date and #{params.endDataUploadTime}::date</if>
            <if test="personnelCardCode != null  and personnelCardCode != ''"> and personnel_card_code = #{personnelCardCode}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="entryAndExitFlag != null  and entryAndExitFlag != ''"> and entry_and_exit_flag = #{entryAndExitFlag}</if>
            <if test="params.beginEntryTime != null and params.beginEntryTime != '' and params.endEntryTime != null and params.endEntryTime != ''"> and entry_time between #{params.beginEntryTime}::date and #{params.endEntryTime}::date</if>
            <if test="params.beginExitTime != null and params.beginExitTime != '' and params.endExitTime != null and params.endExitTime != ''"> and exit_time between #{params.beginExitTime}::date and #{params.endExitTime}::date</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="params.beginEntryCurrentAreaTime != null and params.beginEntryCurrentAreaTime != '' and params.endEntryCurrentAreaTime != null and params.endEntryCurrentAreaTime != ''"> and entry_current_area_time between #{params.beginEntryCurrentAreaTime}::date and #{params.endEntryCurrentAreaTime}::date</if>
            <if test="baseStationCode != null  and baseStationCode != ''"> and base_station_code = #{baseStationCode}</if>
            <if test="params.beginEntryCurrentBaseStationTime != null and params.beginEntryCurrentBaseStationTime != '' and params.endEntryCurrentBaseStationTime != null and params.endEntryCurrentBaseStationTime != ''"> and entry_current_base_station_time between #{params.beginEntryCurrentBaseStationTime}::date and #{params.endEntryCurrentBaseStationTime}::date</if>
            <if test="laborOrganizationMethod != null  and laborOrganizationMethod != ''"> and labor_organization_method = #{laborOrganizationMethod}</if>
            <if test="distanceFromBaseStation != null "> and distance_from_base_station = #{distanceFromBaseStation}</if>
            <if test="personnelWorkStatus != null  and personnelWorkStatus != ''"> and personnel_work_status = #{personnelWorkStatus}</if>
            <if test="isMineLeader != null  and isMineLeader != ''"> and is_mine_leader = #{isMineLeader}</if>
            <if test="isSpecialPersonnel != null  and isSpecialPersonnel != ''"> and is_special_personnel = #{isSpecialPersonnel}</if>
            <if test="trajectoryBaseStationTimeCollection != null  and trajectoryBaseStationTimeCollection != ''"> and trajectory_base_station_time_collection = #{trajectoryBaseStationTimeCollection}</if>
            <if test="params.beginDataTime != null and params.beginDataTime != '' and params.endDataTime != null and params.endDataTime != ''"> and data_time between #{params.beginDataTime}::date and #{params.endDataTime}::date</if>
        </where>
    </select>
    
    <select id="selectKafkaUndergroundPersonnelRealTimeDataById" parameterType="Long" resultMap="KafkaUndergroundPersonnelRealTimeDataResult">
        <include refid="selectKafkaUndergroundPersonnelRealTimeDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertKafkaUndergroundPersonnelRealTimeData" parameterType="KafkaUndergroundPersonnelRealTimeData" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_underground_personnel_real_time_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null">file_encoding,</if>
            <if test="mineCode != null">mine_code,</if>
            <if test="mineName != null">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="personnelCardCode != null">personnel_card_code,</if>
            <if test="name != null">name,</if>
            <if test="entryAndExitFlag != null">entry_and_exit_flag,</if>
            <if test="entryTime != null">entry_time,</if>
            <if test="exitTime != null">exit_time,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="entryCurrentAreaTime != null">entry_current_area_time,</if>
            <if test="baseStationCode != null">base_station_code,</if>
            <if test="entryCurrentBaseStationTime != null">entry_current_base_station_time,</if>
            <if test="laborOrganizationMethod != null">labor_organization_method,</if>
            <if test="distanceFromBaseStation != null">distance_from_base_station,</if>
            <if test="personnelWorkStatus != null">personnel_work_status,</if>
            <if test="isMineLeader != null">is_mine_leader,</if>
            <if test="isSpecialPersonnel != null">is_special_personnel,</if>
            <if test="trajectoryBaseStationTimeCollection != null">trajectory_base_station_time_collection,</if>
            <if test="dataTime != null">data_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null">#{fileEncoding},</if>
            <if test="mineCode != null">#{mineCode},</if>
            <if test="mineName != null">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="personnelCardCode != null">#{personnelCardCode},</if>
            <if test="name != null">#{name},</if>
            <if test="entryAndExitFlag != null">#{entryAndExitFlag},</if>
            <if test="entryTime != null">#{entryTime},</if>
            <if test="exitTime != null">#{exitTime},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="entryCurrentAreaTime != null">#{entryCurrentAreaTime},</if>
            <if test="baseStationCode != null">#{baseStationCode},</if>
            <if test="entryCurrentBaseStationTime != null">#{entryCurrentBaseStationTime},</if>
            <if test="laborOrganizationMethod != null">#{laborOrganizationMethod},</if>
            <if test="distanceFromBaseStation != null">#{distanceFromBaseStation},</if>
            <if test="personnelWorkStatus != null">#{personnelWorkStatus},</if>
            <if test="isMineLeader != null">#{isMineLeader},</if>
            <if test="isSpecialPersonnel != null">#{isSpecialPersonnel},</if>
            <if test="trajectoryBaseStationTimeCollection != null">#{trajectoryBaseStationTimeCollection},</if>
            <if test="dataTime != null">#{dataTime},</if>
         </trim>
    </insert>

    <update id="updateKafkaUndergroundPersonnelRealTimeData" parameterType="KafkaUndergroundPersonnelRealTimeData">
        update kafka_underground_personnel_real_time_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="mineName != null">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="personnelCardCode != null">personnel_card_code = #{personnelCardCode},</if>
            <if test="name != null">name = #{name},</if>
            <if test="entryAndExitFlag != null">entry_and_exit_flag = #{entryAndExitFlag},</if>
            <if test="entryTime != null">entry_time = #{entryTime},</if>
            <if test="exitTime != null">exit_time = #{exitTime},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="entryCurrentAreaTime != null">entry_current_area_time = #{entryCurrentAreaTime},</if>
            <if test="baseStationCode != null">base_station_code = #{baseStationCode},</if>
            <if test="entryCurrentBaseStationTime != null">entry_current_base_station_time = #{entryCurrentBaseStationTime},</if>
            <if test="laborOrganizationMethod != null">labor_organization_method = #{laborOrganizationMethod},</if>
            <if test="distanceFromBaseStation != null">distance_from_base_station = #{distanceFromBaseStation},</if>
            <if test="personnelWorkStatus != null">personnel_work_status = #{personnelWorkStatus},</if>
            <if test="isMineLeader != null">is_mine_leader = #{isMineLeader},</if>
            <if test="isSpecialPersonnel != null">is_special_personnel = #{isSpecialPersonnel},</if>
            <if test="trajectoryBaseStationTimeCollection != null">trajectory_base_station_time_collection = #{trajectoryBaseStationTimeCollection},</if>
            <if test="dataTime != null">data_time = #{dataTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaUndergroundPersonnelRealTimeDataById" parameterType="Long">
        delete from kafka_underground_personnel_real_time_data where id = #{id}
    </delete>

    <delete id="deleteKafkaUndergroundPersonnelRealTimeDataByIds" parameterType="String">
        delete from kafka_underground_personnel_real_time_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>