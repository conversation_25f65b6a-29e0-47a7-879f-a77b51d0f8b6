<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.generator.mapper.GenTableMapper">

    <resultMap type="GenTable" id="GenTableResult">
        <id property="tableId" column="table_id"/>
        <result property="tableName" column="table_name"/>
        <result property="tableComment" column="table_comment"/>
        <result property="subTableName" column="sub_table_name"/>
        <result property="subTableFkName" column="sub_table_fk_name"/>
        <result property="className" column="class_name"/>
        <result property="tplCategory" column="tpl_category"/>
        <result property="tplWebType" column="tpl_web_type"/>
        <result property="packageName" column="package_name"/>
        <result property="moduleName" column="module_name"/>
        <result property="businessName" column="business_name"/>
        <result property="functionName" column="function_name"/>
        <result property="functionAuthor" column="function_author"/>
        <result property="genType" column="gen_type"/>
        <result property="genPath" column="gen_path"/>
        <result property="options" column="options"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <collection property="columns" javaType="java.util.List" resultMap="GenTableColumnResult"/>
    </resultMap>

    <resultMap type="GenTableColumn" id="GenTableColumnResult">
        <id property="columnId" column="column_id"/>
        <result property="tableId" column="table_id"/>
        <result property="columnName" column="column_name"/>
        <result property="columnComment" column="column_comment"/>
        <result property="columnType" column="column_type"/>
        <result property="javaType" column="java_type"/>
        <result property="javaField" column="java_field"/>
        <result property="isPk" column="is_pk"/>
        <result property="isIncrement" column="is_increment"/>
        <result property="isRequired" column="is_required"/>
        <result property="isInsert" column="is_insert"/>
        <result property="isEdit" column="is_edit"/>
        <result property="isList" column="is_list"/>
        <result property="isQuery" column="is_query"/>
        <result property="queryType" column="query_type"/>
        <result property="htmlType" column="html_type"/>
        <result property="dictType" column="dict_type"/>
        <result property="sort" column="sort"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectGenTableVo">
        select table_id,
               table_name,
               table_comment,
               sub_table_name,
               sub_table_fk_name,
               class_name,
               tpl_category,
               tpl_web_type,
               package_name,
               module_name,
               business_name,
               function_name,
               function_author,
               gen_type,
               gen_path,
               options,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from gen_table
    </sql>

    <select id="selectGenTableList" parameterType="GenTable" resultMap="GenTableResult">
        <include refid="selectGenTableVo"/>
        <where>
            <if test="tableName != null and tableName != ''">
                AND lower(table_name) like lower(concat('%', #{tableName}, '%'))
            </if>
            <if test="tableComment != null and tableComment != ''">
                AND lower(table_comment) like lower(concat('%', #{tableComment}, '%'))
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and to_char(create_time,'yyyy-MM-dd') &gt;= to_char(#{params.beginTime},'yyyy-MM-dd')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and to_char(create_time,'yyyy-MM-dd') &lt;= to_char(#{params.endTime},'yyyy-MM-dd')
            </if>
        </where>
    </select>

    <select id="selectDbTableList" parameterType="GenTable" resultMap="GenTableResult">
        SELECT table_name, table_comment, create_time, update_time
        FROM list_table
        WHERE table_name NOT LIKE 'qrtz_%' AND table_name NOT LIKE 'gen_%'
        AND table_name NOT IN (select table_name from gen_table)
        <if test="tableName != null and tableName != ''">
            AND lower(table_name) like lower(concat('%', #{tableName}, '%'))
        </if>
        <if test="tableComment != null and tableComment != ''">
            AND lower(table_comment) like lower(concat('%', #{tableComment}, '%'))
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and create_time::date &gt;= to_date(#{params.beginTime},'yyyy-MM-dd')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and create_time::date &lt;= to_date(#{params.endTime},'yyyy-MM-dd')
        </if>
        order by create_time desc
    </select>

    <select id="selectDbTableListByNames" resultMap="GenTableResult">
        SELECT table_name, table_comment, create_time, update_time
        FROM list_table
        where table_name NOT LIKE 'qrtz_%' AND table_name NOT LIKE 'gen_%'
        and table_name in
        <foreach collection="array" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>

    <select id="selectTableByName" parameterType="String" resultMap="GenTableResult">
        SELECT table_name, table_comment, create_time, update_time
        FROM list_table
        where table_name = #{tableName}
    </select>

    <select id="selectGenTableById" parameterType="Long" resultMap="GenTableResult">
        SELECT t.table_id,
               t.table_name,
               t.table_comment,
               t.sub_table_name,
               t.sub_table_fk_name,
               t.class_name,
               t.tpl_category,
               t.tpl_web_type,
               t.package_name,
               t.module_name,
               t.business_name,
               t.function_name,
               t.function_author,
               t.gen_type,
               t.gen_path,
               t.options,
               t.remark,
               c.column_id,
               c.column_name,
               c.column_comment,
               c.column_type,
               c.java_type,
               c.java_field,
               c.is_pk,
               c.is_increment,
               c.is_required,
               c.is_insert,
               c.is_edit,
               c.is_list,
               c.is_query,
               c.query_type,
               c.html_type,
               c.dict_type,
               c.sort
        FROM gen_table t
                 LEFT JOIN gen_table_column c ON t.table_id = c.table_id::bigint
        where t.table_id = #{tableId}
        order by c.sort
    </select>

    <select id="selectGenTableByName" parameterType="String" resultMap="GenTableResult">
        SELECT t.table_id,
               t.table_name,
               t.table_comment,
               t.sub_table_name,
               t.sub_table_fk_name,
               t.class_name,
               t.tpl_category,
               t.tpl_web_type,
               t.package_name,
               t.module_name,
               t.business_name,
               t.function_name,
               t.function_author,
               t.gen_type,
               t.gen_path,
               t.options,
               t.remark,
               c.column_id,
               c.column_name,
               c.column_comment,
               c.column_type,
               c.java_type,
               c.java_field,
               c.is_pk,
               c.is_increment,
               c.is_required,
               c.is_insert,
               c.is_edit,
               c.is_list,
               c.is_query,
               c.query_type,
               c.html_type,
               c.dict_type,
               c.sort
        FROM gen_table t
                 LEFT JOIN gen_table_column c ON t.table_id = c.table_id::bigint
        where t.table_name = #{tableName}
        order by c.sort
    </select>

    <select id="selectGenTableAll" parameterType="String" resultMap="GenTableResult">
        SELECT t.table_id,
               t.table_name,
               t.table_comment,
               t.sub_table_name,
               t.sub_table_fk_name,
               t.class_name,
               t.tpl_category,
               t.tpl_web_type,
               t.package_name,
               t.module_name,
               t.business_name,
               t.function_name,
               t.function_author,
               t.options,
               t.remark,
               c.column_id,
               c.column_name,
               c.column_comment,
               c.column_type,
               c.java_type,
               c.java_field,
               c.is_pk,
               c.is_increment,
               c.is_required,
               c.is_insert,
               c.is_edit,
               c.is_list,
               c.is_query,
               c.query_type,
               c.html_type,
               c.dict_type,
               c.sort
        FROM gen_table t
                 LEFT JOIN gen_table_column c ON t.table_id = c.table_id::bigint
        order by c.sort
    </select>

    <insert id="insertGenTable" parameterType="GenTable" useGeneratedKeys="true" keyProperty="tableId">
        insert into gen_table (
        <if test="tableName != null">table_name,</if>
        <if test="tableComment != null and tableComment != ''">table_comment,</if>
        <if test="className != null and className != ''">class_name,</if>
        <if test="tplCategory != null and tplCategory != ''">tpl_category,</if>
        <if test="packageName != null and packageName != ''">package_name,</if>
        <if test="moduleName != null and moduleName != ''">module_name,</if>
        <if test="businessName != null and businessName != ''">business_name,</if>
        <if test="functionName != null and functionName != ''">function_name,</if>
        <if test="functionAuthor != null and functionAuthor != ''">function_author,</if>
        <if test="genType != null and genType != ''">gen_type,</if>
        <if test="genPath != null and genPath != ''">gen_path,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="tableName != null">#{tableName},</if>
        <if test="tableComment != null and tableComment != ''">#{tableComment},</if>
        <if test="className != null and className != ''">#{className},</if>
        <if test="tplCategory != null and tplCategory != ''">#{tplCategory},</if>
        <if test="packageName != null and packageName != ''">#{packageName},</if>
        <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
        <if test="businessName != null and businessName != ''">#{businessName},</if>
        <if test="functionName != null and functionName != ''">#{functionName},</if>
        <if test="functionAuthor != null and functionAuthor != ''">#{functionAuthor},</if>
        <if test="genType != null and genType != ''">#{genType},</if>
        <if test="genPath != null and genPath != ''">#{genPath},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        current_timestamp
        )
    </insert>

    <update id="updateGenTable" parameterType="GenTable">
        update gen_table
        <set>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="tableComment != null and tableComment != ''">table_comment = #{tableComment},</if>
            <if test="subTableName != null">sub_table_name = #{subTableName},</if>
            <if test="subTableFkName != null">sub_table_fk_name = #{subTableFkName},</if>
            <if test="className != null and className != ''">class_name = #{className},</if>
            <if test="functionAuthor != null and functionAuthor != ''">function_author = #{functionAuthor},</if>
            <if test="genType != null and genType != ''">gen_type = #{genType},</if>
            <if test="genPath != null and genPath != ''">gen_path = #{genPath},</if>
            <if test="tplCategory != null and tplCategory != ''">tpl_category = #{tplCategory},</if>
            <if test="packageName != null and packageName != ''">package_name = #{packageName},</if>
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            <if test="businessName != null and businessName != ''">business_name = #{businessName},</if>
            <if test="functionName != null and functionName != ''">function_name = #{functionName},</if>
            <if test="tplWebType != null and tplWebType != ''">tpl_web_type = #{tplWebType},</if>
            <if test="options != null and options != ''">options = #{options},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = current_timestamp
        </set>
        where table_id = #{tableId}
    </update>

    <delete id="deleteGenTableByIds" parameterType="Long">
        delete from gen_table where table_id::bigint in
        <foreach collection="array" item="tableId" open="(" separator="," close=")">
            #{tableId}
        </foreach>
    </delete>

</mapper>