package com.ruoyi.lxbi.controller.kafka;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.KafkaVehicleProperty;
import com.ruoyi.lxbi.service.IKafkaVehiclePropertyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 车辆属性数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@RestController
@RequestMapping("/kafka/VehicleProperty")
public class KafkaVehiclePropertyController extends BaseController {
    @Autowired
    private IKafkaVehiclePropertyService kafkaVehiclePropertyService;

    /**
     * 查询车辆属性数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleProperty:list')")
    @GetMapping("/list")
    public TableDataInfo list(KafkaVehicleProperty kafkaVehicleProperty) {
        startPage();
        List<KafkaVehicleProperty> list = kafkaVehiclePropertyService.selectKafkaVehiclePropertyList(kafkaVehicleProperty);
        return getDataTable(list);
    }

    /**
     * 导出车辆属性数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleProperty:export')")
    @Log(title = "车辆属性数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KafkaVehicleProperty kafkaVehicleProperty) {
        List<KafkaVehicleProperty> list = kafkaVehiclePropertyService.selectKafkaVehiclePropertyList(kafkaVehicleProperty);
        ExcelUtil<KafkaVehicleProperty> util = new ExcelUtil<KafkaVehicleProperty>(KafkaVehicleProperty.class);
        util.exportExcel(response, list, "车辆属性数据数据");
    }

    /**
     * 获取车辆属性数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleProperty:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kafkaVehiclePropertyService.selectKafkaVehiclePropertyById(id));
    }

    /**
     * 新增车辆属性数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleProperty:add')")
    @Log(title = "车辆属性数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KafkaVehicleProperty kafkaVehicleProperty)
    {
        return toAjax(kafkaVehiclePropertyService.insertKafkaVehicleProperty(kafkaVehicleProperty));
    }

    /**
     * 修改车辆属性数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleProperty:edit')")
    @Log(title = "车辆属性数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KafkaVehicleProperty kafkaVehicleProperty)
    {
        return toAjax(kafkaVehiclePropertyService.updateKafkaVehicleProperty(kafkaVehicleProperty));
    }

    /**
     * 删除车辆属性数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehicleProperty:remove')")
    @Log(title = "车辆属性数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kafkaVehiclePropertyService.deleteKafkaVehiclePropertyByIds(ids));
    }
}
