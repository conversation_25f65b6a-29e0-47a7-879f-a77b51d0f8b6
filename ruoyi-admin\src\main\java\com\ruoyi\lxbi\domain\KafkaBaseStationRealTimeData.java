package com.ruoyi.lxbi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 基站实时数据对象 kafka_base_station_real_time_data
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KafkaBaseStationRealTimeData extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long isDeleted;

    /** 文件编码 */
    @Excel(name = "文件编码")
    private String fileEncoding;

    /** 煤矿编码 */
    @Excel(name = "煤矿编码")
    private String mineCode;

    /** 矿井名称 */
    @Excel(name = "矿井名称")
    private String mineName;

    /** 数据上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataUploadTime;

    /** 基站编码 */
    @Excel(name = "基站编码")
    private String baseStationCode;

    /** 基站运行状态 */
    @Excel(name = "基站运行状态")
    private String baseStationRunningStatus;

    /** 基站供电状态 */
    @Excel(name = "基站供电状态")
    private String baseStationPowerSupplyStatus;

    /** 数据时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataTime;

}
