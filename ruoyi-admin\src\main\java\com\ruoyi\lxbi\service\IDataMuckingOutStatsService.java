package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DataMuckingOutStatsRequest;
import com.ruoyi.lxbi.domain.response.DataMuckingOutTotalStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutPeriodStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutDepartmentStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStopeStats;

import java.util.List;

/**
 * 出矿数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IDataMuckingOutStatsService {

    /**
     * 查询总体统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合
     */
    public List<DataMuckingOutTotalStats> selectTotalStatsList(DataMuckingOutStatsRequest request, String viewType);

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    public List<DataMuckingOutTotalWithPlanStats> selectTotalWithPlanStatsList(DataMuckingOutStatsRequest request, String viewType);

    /**
     * 查询详细统计数据列表 (日/周/月/年) - 按作业时段、项目部门、采场
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 详细统计数据集合
     */
    public List<DataMuckingOutStats> selectStatsList(DataMuckingOutStatsRequest request, String viewType);

    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    public List<DataMuckingOutPeriodStats> selectPeriodStatsList(DataMuckingOutStatsRequest request, String viewType);

    /**
     * 查询项目部门统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合
     */
    public List<DataMuckingOutDepartmentStats> selectDepartmentStatsList(DataMuckingOutStatsRequest request, String viewType);

    /**
     * 查询采场统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 采场统计数据集合
     */
    public List<DataMuckingOutStopeStats> selectStopeStatsList(DataMuckingOutStatsRequest request, String viewType);

}
