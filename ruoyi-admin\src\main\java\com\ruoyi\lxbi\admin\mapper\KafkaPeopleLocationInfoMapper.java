package com.ruoyi.lxbi.admin.mapper;

import java.util.List;
import com.ruoyi.lxbi.admin.domain.KafkaPeopleLocationInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 区域基本信息数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface KafkaPeopleLocationInfoMapper 
{
    /**
     * 查询区域基本信息数据
     * 
     * @param id 区域基本信息数据主键
     * @return 区域基本信息数据
     */
    public KafkaPeopleLocationInfo selectKafkaPeopleLocationInfoById(Long id);

    /**
     * 查询区域基本信息数据列表
     * 
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 区域基本信息数据集合
     */
    public List<KafkaPeopleLocationInfo> selectKafkaPeopleLocationInfoList(KafkaPeopleLocationInfo kafkaPeopleLocationInfo);

    /**
     * 新增区域基本信息数据
     * 
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 结果
     */
    public int insertKafkaPeopleLocationInfo(KafkaPeopleLocationInfo kafkaPeopleLocationInfo);

    /**
     * 修改区域基本信息数据
     * 
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 结果
     */
    public int updateKafkaPeopleLocationInfo(KafkaPeopleLocationInfo kafkaPeopleLocationInfo);

    /**
     * 删除区域基本信息数据
     * 
     * @param id 区域基本信息数据主键
     * @return 结果
     */
    public int deleteKafkaPeopleLocationInfoById(Long id);

    /**
     * 批量删除区域基本信息数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaPeopleLocationInfoByIds(Long[] ids);

    /**
     * 根据区域编码查询区域信息
     * 
     * @param areaCode 区域编码
     * @return 区域基本信息数据
     */
    public KafkaPeopleLocationInfo selectByAreaCode(@Param("areaCode") String areaCode);

    /**
     * 根据区域编码和煤矿编码查询区域信息
     * 
     * @param areaCode 区域编码
     * @param mineCode 煤矿编码
     * @return 区域基本信息数据
     */
    public KafkaPeopleLocationInfo selectByAreaCodeAndMineCode(@Param("areaCode") String areaCode, @Param("mineCode") String mineCode);

    /**
     * 查询所有有效的区域信息
     * 
     * @return 区域基本信息数据集合
     */
    public List<KafkaPeopleLocationInfo> selectAllValidAreas();

    /**
     * 根据区域类型查询区域信息
     * 
     * @param areaType 区域类型
     * @return 区域基本信息数据集合
     */
    public List<KafkaPeopleLocationInfo> selectByAreaType(@Param("areaType") String areaType);

    /**
     * 统计区域总数
     * 
     * @return 区域总数
     */
    public Long countTotalAreas();

    /**
     * 统计指定煤矿的区域数量
     * 
     * @param mineCode 煤矿编码
     * @return 区域数量
     */
    public Long countAreasByMineCode(@Param("mineCode") String mineCode);

    /**
     * 批量插入或更新（PostgreSQL UPSERT）
     * 根据区域编码和煤矿编码进行唯一性判断
     *
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 结果
     */
    public int upsertKafkaPeopleLocationInfo(KafkaPeopleLocationInfo kafkaPeopleLocationInfo);
}
