package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.BaseOrePass;

/**
 * 溜井配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface IBaseOrePassService 
{
    /**
     * 查询溜井配置
     * 
     * @param orePassId 溜井配置主键
     * @return 溜井配置
     */
    public BaseOrePass selectBaseOrePassByOrePassId(Long orePassId);

    /**
     * 查询溜井配置列表
     * 
     * @param baseOrePass 溜井配置
     * @return 溜井配置集合
     */
    public List<BaseOrePass> selectBaseOrePassList(BaseOrePass baseOrePass);

    /**
     * 新增溜井配置
     * 
     * @param baseOrePass 溜井配置
     * @return 结果
     */
    public int insertBaseOrePass(BaseOrePass baseOrePass);

    /**
     * 修改溜井配置
     * 
     * @param baseOrePass 溜井配置
     * @return 结果
     */
    public int updateBaseOrePass(BaseOrePass baseOrePass);

    /**
     * 批量删除溜井配置
     * 
     * @param orePassIds 需要删除的溜井配置主键集合
     * @return 结果
     */
    public int deleteBaseOrePassByOrePassIds(Long[] orePassIds);

    /**
     * 删除溜井配置信息
     * 
     * @param orePassId 溜井配置主键
     * @return 结果
     */
    public int deleteBaseOrePassByOrePassId(Long orePassId);
}
