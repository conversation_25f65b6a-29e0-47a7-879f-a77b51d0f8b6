package com.ruoyi.lxbi.domain.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 充填月计划批量操作DTO
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class PlanBackfillingMonthlyBatchDto {

    /** 主键ID */
    private Long id;

    /** 计划月份 */
    private String planMonth;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 工作面ID */
    private Long workingFaceId;

    /** 采场ID */
    private Long stopeId;

    /** 充填方量 */
    private BigDecimal fillingVolume;

    /** 备注 */
    private String remark;

    /** 操作类型（新增、更新、删除） */
    private String operationType;

}
