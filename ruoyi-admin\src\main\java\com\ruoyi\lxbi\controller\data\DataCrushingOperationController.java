package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataCrushingOperation;
import com.ruoyi.lxbi.domain.response.DataCrushingOperationVo;
import com.ruoyi.lxbi.domain.request.DataCrushingOperationBatchDto;
import com.ruoyi.lxbi.service.IDataCrushingOperationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 破碎数据管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/data/crushing")
public class DataCrushingOperationController extends BaseController {
    @Autowired
    private IDataCrushingOperationService dataCrushingOperationService;

    /**
     * 查询破碎数据管理列表
     */
    @PreAuthorize("@ss.hasPermi('data:crushing:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataCrushingOperation dataCrushingOperation) {
        startPage();
        List<DataCrushingOperationVo> list = dataCrushingOperationService.selectDataCrushingOperationList(dataCrushingOperation);
        return getDataTable(list);
    }

    /**
     * 导出破碎数据管理列表
     */
    @PreAuthorize("@ss.hasPermi('data:crushing:export')")
    @Log(title = "破碎数据管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataCrushingOperation dataCrushingOperation) {
        List<DataCrushingOperationVo> list = dataCrushingOperationService.selectDataCrushingOperationList(dataCrushingOperation);
        ExcelUtil<DataCrushingOperationVo> util = new ExcelUtil<DataCrushingOperationVo>(DataCrushingOperationVo.class);
        util.exportExcel(response, list, "破碎数据管理数据");
    }

    /**
     * 获取破碎数据管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:crushing:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataCrushingOperationService.selectDataCrushingOperationById(id));
    }

    /**
     * 新增破碎数据管理
     */
    @PreAuthorize("@ss.hasPermi('data:crushing:add')")
    @Log(title = "破碎数据管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataCrushingOperation dataCrushingOperation)
    {
        return toAjax(dataCrushingOperationService.insertDataCrushingOperation(dataCrushingOperation));
    }

    /**
     * 修改破碎数据管理
     */
    @PreAuthorize("@ss.hasPermi('data:crushing:edit')")
    @Log(title = "破碎数据管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataCrushingOperation dataCrushingOperation)
    {
        return toAjax(dataCrushingOperationService.updateDataCrushingOperation(dataCrushingOperation));
    }

    /**
     * 删除破碎数据管理
     */
    @PreAuthorize("@ss.hasPermi('data:crushing:remove')")
    @Log(title = "破碎数据管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataCrushingOperationService.deleteDataCrushingOperationByIds(ids));
    }

    /**
     * 批量保存破碎数据（增删改查）
     * 传入批量列表，验证是否同一个日期的数据，然后查询这个日期的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('data:crushing:edit')")
    @Log(title = "破碎数据批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<DataCrushingOperationBatchDto> batchDataList)
    {
        try {
            int result = dataCrushingOperationService.batchSaveDataCrushingOperation(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
