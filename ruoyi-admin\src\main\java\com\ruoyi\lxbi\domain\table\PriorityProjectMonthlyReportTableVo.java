package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.core.table.TableColumnDataBase;
import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 重点工程月报表格VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableConfig(code = "priority_project_monthly_report", name = "重点工程月报", description = "重点工程数据月报统计表格")
public class PriorityProjectMonthlyReportTableVo extends TableColumnDataBase {

    @TableHeader(label = "序号", order = 1, width = 5)
    private String serialNumber;

    @TableHeader(label = "项目", order = 2, enableRowMerge = true)
    private String name;

    @TableHeader(label = "单位", order = 3, width = 5)
    private String unit;

    @TableHeader(label = "月计划", order = 4)
    private BigDecimal monthlyPlan;

    @TableHeader(label = "月完成", order = 5)
    private BigDecimal monthlyCompleted;

    @TableHeader(label = "月完成率", order = 6)
    private String monthlyCompletionRate;

    @TableHeader(label = "月超欠", order = 7)
    private BigDecimal monthlyOverUnder;
}
