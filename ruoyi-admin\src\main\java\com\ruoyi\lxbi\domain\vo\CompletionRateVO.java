package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 整改完成率VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompletionRateVO {
    
    /**
     * 隐患总数
     */
    private Long totalCount;
    
    /**
     * 已完成数
     */
    private Long completedCount;
    
    /**
     * 完成率
     */
    private BigDecimal completionRate;
}
