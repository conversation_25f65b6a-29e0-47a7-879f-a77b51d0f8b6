package com.ruoyi.lxbi.controller.stat.sec;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.admin.service.ISurfaceMonitoringStatService;
import com.ruoyi.lxbi.domain.vo.*;

import com.ruoyi.lxbi.utils.DateRangeCalculator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 地表监测统计Controller
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */

@RestController
@RequestMapping("/sec/stat/surface-monitoring")
public class SurfaceMonitoringStatController {

    @Autowired
    private ISurfaceMonitoringStatService surfaceMonitoringStatService;

    /**
     * 获取地表监测概览统计
     */
    @Anonymous
    @GetMapping("/overview")
    public R<SurfaceMonitoringOverviewVO> getOverviewStatistics(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        SurfaceMonitoringOverviewVO overview = surfaceMonitoringStatService.getOverviewStatistics(viewType, startDate, endDate);
        return R.ok(overview);
    }

    /**
     * 获取地表监测偏移等级分布统计
     */
    @Anonymous
    @GetMapping("/level-distribution")
    public R<List<SurfaceMonitoringLevelDistributionVO>> getLevelDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<SurfaceMonitoringLevelDistributionVO> distribution = surfaceMonitoringStatService.getLevelDistribution(viewType, startDate, endDate);
        return R.ok(distribution);
    }

    /**
     * 获取地表监测七日偏移趋势统计
     */
    @Anonymous
    @GetMapping("/seven-day-trend")
    public R<List<SurfaceMonitoringTrendVO>> getSevenDayTrend(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<SurfaceMonitoringTrendVO> trend = surfaceMonitoringStatService.getSevenDayTrend(viewType, startDate, endDate);
        return R.ok(trend);
    }

    /**
     * 获取监测站点列表
     */
    @Anonymous
    @GetMapping("/stations")
    public R<List<SurfaceMonitoringStationVO>> getMonitoringStations() {
        List<SurfaceMonitoringStationVO> stations = surfaceMonitoringStatService.getMonitoringStations();
        return R.ok(stations);
    }

    /**
     * 获取监测站点实时状态
     */
    @Anonymous
    @GetMapping("/stations/status")
    public R<List<SurfaceMonitoringStationStatusVO>> getStationsStatus() {
        List<SurfaceMonitoringStationStatusVO> stationsStatus = surfaceMonitoringStatService.getStationsStatus();
        return R.ok(stationsStatus);
    }

    /**
     * 获取指定监测站的详细数据
     */
    @Anonymous
    @GetMapping("/station/{stationId}/details")
    public R<SurfaceMonitoringStationDetailVO> getStationDetails(
            @PathVariable("stationId") Long stationId,
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        SurfaceMonitoringStationDetailVO stationDetail = surfaceMonitoringStatService.getStationDetails(stationId, viewType, startDate, endDate);
        return R.ok(stationDetail);
    }

    /**
     * 获取监测数据历史趋势
     */
    @Anonymous
    @GetMapping("/data-trend")
    public R<List<SurfaceMonitoringDataTrendVO>> getDataTrend(
            @RequestParam(required = false) Long stationId,
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<SurfaceMonitoringDataTrendVO> dataTrend = surfaceMonitoringStatService.getDataTrend(stationId, viewType, startDate, endDate);
        return R.ok(dataTrend);
    }

    /**
     * 获取报警事件列表
     */
    @Anonymous
    @GetMapping("/alarms")
    public R<List<SurfaceMonitoringAlarmVO>> getAlarmEvents(
            @RequestParam(required = false) Long stationId,
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<SurfaceMonitoringAlarmVO> alarms = surfaceMonitoringStatService.getAlarmEvents(stationId, viewType, startDate, endDate);
        return R.ok(alarms);
    }

    /**
     * 获取系统健康状态
     */
    @Anonymous
    @GetMapping("/system-health")
    public R<SurfaceMonitoringSystemHealthVO> getSystemHealth() {
        SurfaceMonitoringSystemHealthVO systemHealth = surfaceMonitoringStatService.getSystemHealth();
        return R.ok(systemHealth);
    }
}
