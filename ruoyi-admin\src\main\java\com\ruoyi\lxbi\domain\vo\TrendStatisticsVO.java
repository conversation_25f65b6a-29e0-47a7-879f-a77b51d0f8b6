package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 隐患趋势统计VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrendStatisticsVO {
    
    /**
     * 日期（YYYY-MM-DD）
     */
    private String date;
    
    /**
     * 短日期（MM-DD）
     */
    private String shortDate;
    
    /**
     * 周标签（YYYY年第XX周）
     */
    private String weekLabel;
    
    /**
     * 月标签（YYYY年MM月）
     */
    private String monthLabel;
    
    /**
     * 隐患总数
     */
    private Long totalCount;
    
    /**
     * 重大隐患数
     */
    private Long majorCount;
    
    /**
     * 一般隐患数
     */
    private Long generalCount;
    
    /**
     * 已完成数
     */
    private Long completedCount;
    
    /**
     * 超期数
     */
    private Long overdueCount;

    /**
     * 主要统计数量（用于趋势图显示）
     */
    private Long count;

    /**
     * 超期率
     */
    private BigDecimal overdueRate;
}
