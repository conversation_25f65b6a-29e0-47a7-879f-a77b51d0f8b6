package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DataMineHoistingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataMineHoistingPeriodStats;
import com.ruoyi.lxbi.domain.response.DataMineHoistingStats;
import com.ruoyi.lxbi.service.IDataMineHoistingStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 矿井提升数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/data/stats/hoisting")
public class DataMineHoistingStatsController {
    @Autowired
    private IDataMineHoistingStatsService dataMineHoistingStatsService;

    /**
     * 查询月度提升量柱状图数据
     * 对应图表一：按月度显示提升量和故障率的柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:hoisting:01a')")
    @GetMapping("/01a")
    public R<List<DataMineHoistingStats>> volumeAll(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                     @RequestParam(value = "startDate", required = false) String startDate,
                                                     @RequestParam(value = "endDate", required = false) String endDate) {
        DataMineHoistingStatsRequest request = new DataMineHoistingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMineHoistingStats> stats = dataMineHoistingStatsService.selectStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询班次提升量分布柱状图数据
     * 对应图表二：按班次分组的提升量柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:hoisting:01b')")
    @GetMapping("/01b")
    public R<List<DataMineHoistingPeriodStats>> volumePeriod(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                              @RequestParam(value = "startDate", required = false) String startDate,
                                                              @RequestParam(value = "endDate", required = false) String endDate) {
        DataMineHoistingStatsRequest request = new DataMineHoistingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMineHoistingPeriodStats> stats = dataMineHoistingStatsService.selectPeriodStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询总操作时间和故障时间柱状图数据
     * 对应图表三：总操作时间和故障时间柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:hoisting:02a')")
    @GetMapping("/02a")
    public R<List<DataMineHoistingStats>> timeAll(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                   @RequestParam(value = "startDate", required = false) String startDate,
                                                   @RequestParam(value = "endDate", required = false) String endDate) {
        DataMineHoistingStatsRequest request = new DataMineHoistingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMineHoistingStats> stats = dataMineHoistingStatsService.selectStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询日期班次提升量堆叠柱状图数据
     * 对应图表四：按日期和班次的堆叠柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:hoisting:02b')")
    @GetMapping("/02b")
    public R<List<DataMineHoistingPeriodStats>> dailyPeriodVolumeChart(
            @RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate) {
        DataMineHoistingStatsRequest request = new DataMineHoistingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMineHoistingPeriodStats> stats = dataMineHoistingStatsService.selectPeriodStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询提升斗数分布柱状图数据
     * 对应图表五：按日期的提升斗数柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:hoisting:04a')")
    @GetMapping("/04a")
    public R<List<DataMineHoistingStats>> bucketAll(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                     @RequestParam(value = "startDate", required = false) String startDate,
                                                     @RequestParam(value = "endDate", required = false) String endDate) {
        DataMineHoistingStatsRequest request = new DataMineHoistingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMineHoistingStats> stats = dataMineHoistingStatsService.selectStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询班次操作时间占比饼图数据
     * 对应图表六：班次操作时间占比饼图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:hoisting:04b')")
    @GetMapping("/04b")
    public R<List<DataMineHoistingPeriodStats>> periodOperationTime(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                     @RequestParam(value = "startDate", required = false) String startDate,
                                                                     @RequestParam(value = "endDate", required = false) String endDate) {
        DataMineHoistingStatsRequest request = new DataMineHoistingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMineHoistingPeriodStats> stats = dataMineHoistingStatsService.selectPeriodStatsList(request, viewType);
        return R.ok(stats);
    }

}
