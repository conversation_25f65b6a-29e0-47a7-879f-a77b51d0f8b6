package com.ruoyi.lxbi.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaMonitoringPointBasicInfoMapper;
import com.ruoyi.lxbi.domain.KafkaMonitoringPointBasicInfo;
import com.ruoyi.lxbi.service.IKafkaMonitoringPointBasicInfoService;

/**
 * 测点基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class KafkaMonitoringPointBasicInfoServiceImpl implements IKafkaMonitoringPointBasicInfoService
{
    @Autowired
    private KafkaMonitoringPointBasicInfoMapper kafkaMonitoringPointBasicInfoMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询测点基本信息
     * 
     * @param id 测点基本信息主键
     * @return 测点基本信息
     */
    @Override
    public KafkaMonitoringPointBasicInfo selectKafkaMonitoringPointBasicInfoById(Long id)
    {
        return kafkaMonitoringPointBasicInfoMapper.selectKafkaMonitoringPointBasicInfoById(id);
    }

    /**
     * 查询测点基本信息列表
     * 
     * @param kafkaMonitoringPointBasicInfo 测点基本信息
     * @return 测点基本信息
     */
    @Override
    public List<KafkaMonitoringPointBasicInfo> selectKafkaMonitoringPointBasicInfoList(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo)
    {
        return kafkaMonitoringPointBasicInfoMapper.selectKafkaMonitoringPointBasicInfoList(kafkaMonitoringPointBasicInfo);
    }

    /**
     * 新增测点基本信息
     * 
     * @param kafkaMonitoringPointBasicInfo 测点基本信息
     * @return 结果
     */
    @Override
    public int insertKafkaMonitoringPointBasicInfo(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo)
    {
        kafkaMonitoringPointBasicInfo.setCreateTime(DateUtils.getNowDate());
        return kafkaMonitoringPointBasicInfoMapper.insertKafkaMonitoringPointBasicInfo(kafkaMonitoringPointBasicInfo);
    }

    /**
     * 修改测点基本信息
     * 
     * @param kafkaMonitoringPointBasicInfo 测点基本信息
     * @return 结果
     */
    @Override
    public int updateKafkaMonitoringPointBasicInfo(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo)
    {
        kafkaMonitoringPointBasicInfo.setUpdateTime(DateUtils.getNowDate());
        return kafkaMonitoringPointBasicInfoMapper.updateKafkaMonitoringPointBasicInfo(kafkaMonitoringPointBasicInfo);
    }

    /**
     * 批量删除测点基本信息
     * 
     * @param ids 需要删除的测点基本信息主键
     * @return 结果
     */
    @Override
    public int deleteKafkaMonitoringPointBasicInfoByIds(Long[] ids)
    {
        return kafkaMonitoringPointBasicInfoMapper.deleteKafkaMonitoringPointBasicInfoByIds(ids);
    }

    /**
     * 删除测点基本信息信息
     *
     * @param id 测点基本信息主键
     * @return 结果
     */
    @Override
    public int deleteKafkaMonitoringPointBasicInfoById(Long id)
    {
        return kafkaMonitoringPointBasicInfoMapper.deleteKafkaMonitoringPointBasicInfoById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka测点基本信息消息");

            // 解析Kafka消息
            KafkaMonitoringPointBasicInfo monitoringPoint = parseKafkaMessage(kafkaMessage);
            if (monitoringPoint == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(monitoringPoint.getMonitoringPointCode())) {
                log.warn("测点编码为空，跳过处理");
                return false;
            }

            // 执行插入操作
            int result = insertKafkaMonitoringPointBasicInfo(monitoringPoint);

            if (result > 0) {
                log.info("成功插入测点基本信息数据，测点编码: {}, 煤矿代码: {}",
                    monitoringPoint.getMonitoringPointCode(), monitoringPoint.getMineCode());
                return true;
            } else {
                log.warn("插入测点基本信息数据失败，测点编码: {}",
                    monitoringPoint.getMonitoringPointCode());
                return false;
            }

        } catch (Exception e) {
            log.error("处理Kafka测点基本信息消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaMonitoringPointBasicInfo parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaMonitoringPointBasicInfo monitoringPoint = new KafkaMonitoringPointBasicInfo();

            // 基础信息
            monitoringPoint.setFileEncoding(getStringValue(jsonNode, "文件前缀"));
            monitoringPoint.setMineCode(getStringValue(jsonNode, "矿井安全监控系统编码"));
            monitoringPoint.setMineName(getStringValue(jsonNode, "煤矿名称"));
            monitoringPoint.setDataUploadTime(getDateValue(jsonNode, "数据上传时间"));
            monitoringPoint.setMonitoringPointCode(getStringValue(jsonNode, "测点编码"));
            monitoringPoint.setSystemCode(getStringValue(jsonNode, "系统编码"));
            monitoringPoint.setSubstationCode(getStringValue(jsonNode, "分站编码"));
            monitoringPoint.setSensorType(getStringValue(jsonNode, "传感器类型"));
            monitoringPoint.setMonitoringPointValueType(getStringValue(jsonNode, "测点数值类型"));
            monitoringPoint.setMonitoringPointValueUnit(getStringValue(jsonNode, "测点数值单位"));
            monitoringPoint.setHighRange(getBigDecimalValue(jsonNode, "高量程"));
            monitoringPoint.setLowRange(getBigDecimalValue(jsonNode, "低量程"));
            monitoringPoint.setUpperLimitAlarmThreshold(getBigDecimalValue(jsonNode, "上限报警门限"));
            monitoringPoint.setUpperLimitReportThreshold(getBigDecimalValue(jsonNode, "上限解报门限"));
            monitoringPoint.setLowerLimitAlarmThreshold(getBigDecimalValue(jsonNode, "下限报警门限"));
            monitoringPoint.setLowerLimitReportThreshold(getBigDecimalValue(jsonNode, "下限解报门限"));
            monitoringPoint.setUpperLimitPowerOffThreshold(getBigDecimalValue(jsonNode, "上限断电门限"));
            monitoringPoint.setUpperLimitPowerOnThreshold(getBigDecimalValue(jsonNode, "上限复电门限"));
            monitoringPoint.setLowerLimitPowerOffThreshold(getBigDecimalValue(jsonNode, "下限断电门限"));
            monitoringPoint.setLowerLimitPowerOnThreshold(getBigDecimalValue(jsonNode, "下限复电门限"));
            monitoringPoint.setStartDescription(getStringValue(jsonNode, "开描述"));
            monitoringPoint.setStopDescription(getStringValue(jsonNode, "停描述"));
            monitoringPoint.setMonitoringPointInstallationLocation(getStringValue(jsonNode, "测点安装位置"));
            monitoringPoint.setPositionX(getBigDecimalValue(jsonNode, "位置X"));
            monitoringPoint.setPositionY(getBigDecimalValue(jsonNode, "位置Y"));
            monitoringPoint.setPositionZ(getBigDecimalValue(jsonNode, "位置Z"));
            monitoringPoint.setSensorAssociationRelationship(getStringValue(jsonNode, "传感器关联关系"));
            monitoringPoint.setDataTime(getDateValue(jsonNode, "数据时间"));

            // 默认值
            monitoringPoint.setIsDeleted(0L);
            monitoringPoint.setCreateTime(DateUtils.getNowDate());
            monitoringPoint.setUpdateTime(DateUtils.getNowDate());

            return monitoringPoint;

        } catch (Exception e) {
            log.error("解析Kafka测点基本信息消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null && !node.isNull() ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private java.util.Date getDateValue(JsonNode jsonNode, String fieldName) {
        try {
            String dateStr = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            }
        } catch (Exception e) {
            log.warn("解析日期字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }

    /**
     * 从JsonNode中获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(JsonNode jsonNode, String fieldName) {
        try {
            String value = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(value)) {
                return new BigDecimal(value);
            }
        } catch (Exception e) {
            log.warn("解析BigDecimal字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }
}
