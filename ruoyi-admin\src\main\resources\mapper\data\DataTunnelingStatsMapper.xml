<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataTunnelingStatsMapper">

    <!-- 总体统计结果映射（含计划量） -->
    <resultMap type="DataTunnelingTotalWithPlanStats" id="DataTunnelingTotalWithPlanStatsResult">
        <result property="year"                     column="year"                   />
        <result property="month"                    column="month"                  />
        <result property="weekNumber"               column="week"                   />
        <result property="operationDate"            column="operation_date"         />
        <result property="weekStartDate"            column="week_start_date"        />
        <result property="weekEndDate"              column="week_end_date"          />
        <result property="totalTunnelingLength"     column="total_tunneling_length" />
        <result property="totalTunnelingVolume"     column="total_tunneling_volume" />
        <result property="planTunnelingLength"      column="plan_tunneling_length"  />
    </resultMap>

    <!-- 项目部门统计结果映射（含计划量） -->
    <resultMap type="DataTunnelingDepartmentWithPlanStats" id="DataTunnelingDepartmentWithPlanStatsResult">
        <result property="year"                     column="year"                   />
        <result property="month"                    column="month"                  />
        <result property="weekNumber"               column="week"                   />
        <result property="operationDate"            column="operation_date"         />
        <result property="weekStartDate"            column="week_start_date"        />
        <result property="weekEndDate"              column="week_end_date"          />
        <result property="projectDepartmentId"      column="project_department_id"  />
        <result property="projectDepartmentName"    column="project_department_name" />
        <result property="totalTunnelingLength"     column="total_tunneling_length" />
        <result property="totalTunnelingVolume"     column="total_tunneling_volume" />
        <result property="planTunnelingLength"      column="plan_tunneling_length"  />
    </resultMap>

    <!-- 采场统计结果映射 -->
    <resultMap type="DataTunnelingStopeStats" id="DataTunnelingStopeStatsResult">
        <result property="year"                     column="year"                   />
        <result property="month"                    column="month"                  />
        <result property="weekNumber"               column="week"                   />
        <result property="operationDate"            column="operation_date"         />
        <result property="weekStartDate"            column="week_start_date"        />
        <result property="weekEndDate"              column="week_end_date"          />
        <result property="stopeId"                  column="stope_id"               />
        <result property="stopeName"                column="stope_name"             />
        <result property="totalTunnelingLength"     column="total_tunneling_length" />
        <result property="totalTunnelingVolume"     column="total_tunneling_volume" />
    </resultMap>

    <!-- 工作面统计结果映射 -->
    <resultMap type="DataTunnelingWorkingFaceStats" id="DataTunnelingWorkingFaceStatsResult">
        <result property="year"                     column="year"                   />
        <result property="month"                    column="month"                  />
        <result property="weekNumber"               column="week"                   />
        <result property="operationDate"            column="operation_date"         />
        <result property="weekStartDate"            column="week_start_date"        />
        <result property="weekEndDate"              column="week_end_date"          />
        <result property="workingFaceId"            column="working_face_id"        />
        <result property="workingFaceName"          column="working_face_name"      />
        <result property="totalTunnelingLength"     column="total_tunneling_length" />
        <result property="totalTunnelingVolume"     column="total_tunneling_volume" />
    </resultMap>

    <!-- ========== 总体统计查询方法（含计划量） ========== -->
    
    <!-- 查询总体统计数据列表（含计划量） (日) -->
    <select id="selectDailyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingTotalWithPlanStatsResult">
        SELECT 
            NULL as year,
            NULL as month,
            NULL as week,
            v.operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_daily_total_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                SUM(pdm.drift_meter) / EXTRACT(DAY FROM DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1)) + INTERVAL '1 month' - INTERVAL '1 day') as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            GROUP BY fm.financial_year, fm.financial_month
        ) p ON EXTRACT(YEAR FROM v.operation_date) = p.year AND EXTRACT(MONTH FROM v.operation_date) = p.month
        <where>
            <if test="startDate != null">
                AND v.operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.operation_date
    </select>

    <!-- 查询总体统计数据列表（含计划量） (周) -->
    <select id="selectWeeklyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingTotalWithPlanStatsResult">
        SELECT 
            v.year,
            NULL as month,
            v.week,
            NULL as operation_date,
            v.week_start_date,
            v.week_end_date,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_weekly_total_stats v
        LEFT JOIN (
            SELECT
                wk.week_year as year,
                wk.week_number as week,
                SUM(pdm.drift_meter) / 4 as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            CROSS JOIN get_week_thu_to_wed(fm.month_start_date) AS wk
            GROUP BY wk.week_year, wk.week_number
        ) p ON v.year = p.year AND v.week = p.week
        <where>
            <if test="startDate != null">
                AND v.week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.week
    </select>

    <!-- 查询总体统计数据列表（含计划量） (月) -->
    <select id="selectMonthlyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingTotalWithPlanStatsResult">
        SELECT 
            v.year,
            v.month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_monthly_total_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                SUM(pdm.drift_meter) as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            GROUP BY fm.financial_year, fm.financial_month
        ) p ON v.year = p.year AND v.month = p.month
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.month
    </select>

    <!-- 查询总体统计数据列表（含计划量） (年) -->
    <select id="selectYearlyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingTotalWithPlanStatsResult">
        SELECT 
            v.year,
            NULL as month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_yearly_total_stats v
        LEFT JOIN (
            SELECT
                fy.financial_year as year,
                SUM(pdm.drift_meter) as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_year(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fy
            GROUP BY fy.financial_year
        ) p ON v.year = p.year
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, 1, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, 1, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year
    </select>

    <!-- ========== 项目部门统计查询方法（含计划量） ========== -->
    
    <!-- 查询项目部门统计数据列表（含计划量） (日) -->
    <select id="selectDailyDepartmentWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingDepartmentWithPlanStatsResult">
        SELECT 
            NULL as year,
            NULL as month,
            NULL as week,
            v.operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.project_department_id,
            v.project_department_name,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_daily_department_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                pdm.project_department_id,
                SUM(pdm.drift_meter) / EXTRACT(DAY FROM DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1)) + INTERVAL '1 month' - INTERVAL '1 day') as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            GROUP BY fm.financial_year, fm.financial_month, pdm.project_department_id
        ) p ON EXTRACT(YEAR FROM v.operation_date) = p.year AND EXTRACT(MONTH FROM v.operation_date) = p.month AND v.project_department_id = p.project_department_id
        <where>
            <if test="startDate != null">
                AND v.operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.operation_date, v.project_department_id
    </select>

    <!-- 查询项目部门统计数据列表（含计划量） (周) -->
    <select id="selectWeeklyDepartmentWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingDepartmentWithPlanStatsResult">
        SELECT 
            v.year,
            NULL as month,
            v.week,
            NULL as operation_date,
            v.week_start_date,
            v.week_end_date,
            v.project_department_id,
            v.project_department_name,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_weekly_department_stats v
        LEFT JOIN (
            SELECT
                wk.week_year as year,
                wk.week_number as week,
                pdm.project_department_id,
                SUM(pdm.drift_meter) / 4 as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            CROSS JOIN get_week_thu_to_wed(fm.month_start_date) AS wk
            GROUP BY wk.week_year, wk.week_number, pdm.project_department_id
        ) p ON v.year = p.year AND v.week = p.week AND v.project_department_id = p.project_department_id
        <where>
            <if test="startDate != null">
                AND v.week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.week, v.project_department_id
    </select>

    <!-- 查询项目部门统计数据列表（含计划量） (月) -->
    <select id="selectMonthlyDepartmentWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingDepartmentWithPlanStatsResult">
        SELECT 
            v.year,
            v.month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.project_department_id,
            v.project_department_name,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_monthly_department_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                pdm.project_department_id,
                SUM(pdm.drift_meter) as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            GROUP BY fm.financial_year, fm.financial_month, pdm.project_department_id
        ) p ON v.year = p.year AND v.month = p.month AND v.project_department_id = p.project_department_id
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.month, v.project_department_id
    </select>

    <!-- 查询项目部门统计数据列表（含计划量） (年) -->
    <select id="selectYearlyDepartmentWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingDepartmentWithPlanStatsResult">
        SELECT 
            v.year,
            NULL as month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.project_department_id,
            v.project_department_name,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_yearly_department_stats v
        LEFT JOIN (
            SELECT
                fy.financial_year as year,
                pdm.project_department_id,
                SUM(pdm.drift_meter) as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_year(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fy
            GROUP BY fy.financial_year, pdm.project_department_id
        ) p ON v.year = p.year AND v.project_department_id = p.project_department_id
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, 1, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, 1, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.project_department_id
    </select>

    <!-- ========== 重点工程总体统计查询方法（含计划量） ========== -->

    <!-- 查询重点工程总体统计数据列表（含计划量） (日) -->
    <select id="selectDailyPriorityProjectTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingTotalWithPlanStatsResult">
        SELECT
            NULL as year,
            NULL as month,
            NULL as week,
            v.operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_priority_daily_total_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                SUM(pdm.drift_meter) / EXTRACT(DAY FROM DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1)) + INTERVAL '1 month' - INTERVAL '1 day') as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            WHERE EXISTS (
                SELECT 1 FROM base_priority_project pp
                WHERE pp.stope_id = pdm.stope_id
                AND pp.working_face_id = pdm.working_face_id
                AND pp.is_delete = 0
                AND pp.status = 1
            )
            GROUP BY fm.financial_year, fm.financial_month
        ) p ON EXTRACT(YEAR FROM v.operation_date) = p.year AND EXTRACT(MONTH FROM v.operation_date) = p.month
        <where>
            <if test="startDate != null">
                AND v.operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.operation_date
    </select>

    <!-- 查询重点工程总体统计数据列表（含计划量） (周) -->
    <select id="selectWeeklyPriorityProjectTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingTotalWithPlanStatsResult">
        SELECT
            v.year,
            NULL as month,
            v.week,
            NULL as operation_date,
            v.week_start_date,
            v.week_end_date,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_priority_weekly_total_stats v
        LEFT JOIN (
            SELECT
                wk.week_year as year,
                wk.week_number as week,
                SUM(pdm.drift_meter) / 4 as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            CROSS JOIN get_week_thu_to_wed(MAKE_DATE(fm.financial_year, fm.financial_month, 15)) AS wk
            WHERE EXISTS (
                SELECT 1 FROM base_priority_project pp
                WHERE pp.stope_id = pdm.stope_id
                AND pp.working_face_id = pdm.working_face_id
                AND pp.is_delete = 0
                AND pp.status = 1
            )
            GROUP BY wk.week_year, wk.week_number
        ) p ON v.year = p.year AND v.week = p.week
        <where>
            <if test="startDate != null">
                AND v.week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.week
    </select>

    <!-- 查询重点工程总体统计数据列表（含计划量） (月) -->
    <select id="selectMonthlyPriorityProjectTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingTotalWithPlanStatsResult">
        SELECT
            v.year,
            v.month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_priority_monthly_total_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                SUM(pdm.drift_meter) as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            WHERE EXISTS (
                SELECT 1 FROM base_priority_project pp
                WHERE pp.stope_id = pdm.stope_id
                AND pp.working_face_id = pdm.working_face_id
                AND pp.is_delete = 0
                AND pp.status = 1
            )
            GROUP BY fm.financial_year, fm.financial_month
        ) p ON v.year = p.year AND v.month = p.month
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.month
    </select>

    <!-- 查询重点工程总体统计数据列表（含计划量） (年) -->
    <select id="selectYearlyPriorityProjectTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingTotalWithPlanStatsResult">
        SELECT
            v.year,
            NULL as month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_priority_yearly_total_stats v
        LEFT JOIN (
            SELECT
                fy.financial_year as year,
                SUM(pdm.drift_meter) as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_year(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fy
            WHERE EXISTS (
                SELECT 1 FROM base_priority_project pp
                WHERE pp.stope_id = pdm.stope_id
                AND pp.working_face_id = pdm.working_face_id
                AND pp.is_delete = 0
                AND pp.status = 1
            )
            GROUP BY fy.financial_year
        ) p ON v.year = p.year
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, 1, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, 1, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year
    </select>

    <!-- ========== 重点工程项目部门统计查询方法（含计划量） ========== -->

    <!-- 查询重点工程项目部门统计数据列表（含计划量） (日) -->
    <select id="selectDailyPriorityProjectDepartmentWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingDepartmentWithPlanStatsResult">
        SELECT
            NULL as year,
            NULL as month,
            NULL as week,
            v.operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.project_department_id,
            v.project_department_name,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_priority_daily_department_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                pdm.project_department_id,
                SUM(pdm.drift_meter) / EXTRACT(DAY FROM DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1)) + INTERVAL '1 month' - INTERVAL '1 day') as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            WHERE EXISTS (
                SELECT 1 FROM base_priority_project pp
                WHERE pp.stope_id = pdm.stope_id
                AND pp.working_face_id = pdm.working_face_id
                AND pp.is_delete = 0
                AND pp.status = 1
            )
            GROUP BY fm.financial_year, fm.financial_month, pdm.project_department_id
        ) p ON EXTRACT(YEAR FROM v.operation_date) = p.year AND EXTRACT(MONTH FROM v.operation_date) = p.month AND v.project_department_id = p.project_department_id
        <where>
            <if test="startDate != null">
                AND v.operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.operation_date, v.project_department_id
    </select>

    <!-- 查询重点工程项目部门统计数据列表（含计划量） (周) -->
    <select id="selectWeeklyPriorityProjectDepartmentWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingDepartmentWithPlanStatsResult">
        SELECT
            v.year,
            NULL as month,
            v.week,
            NULL as operation_date,
            v.week_start_date,
            v.week_end_date,
            v.project_department_id,
            v.project_department_name,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_priority_weekly_department_stats v
        LEFT JOIN (
            SELECT
                wk.week_year as year,
                wk.week_number as week,
                pdm.project_department_id,
                SUM(pdm.drift_meter) / 4 as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            CROSS JOIN get_week_thu_to_wed(MAKE_DATE(fm.financial_year, fm.financial_month, 15)) AS wk
            WHERE EXISTS (
                SELECT 1 FROM base_priority_project pp
                WHERE pp.stope_id = pdm.stope_id
                AND pp.working_face_id = pdm.working_face_id
                AND pp.is_delete = 0
                AND pp.status = 1
            )
            GROUP BY wk.week_year, wk.week_number, pdm.project_department_id
        ) p ON v.year = p.year AND v.week = p.week AND v.project_department_id = p.project_department_id
        <where>
            <if test="startDate != null">
                AND v.week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.week, v.project_department_id
    </select>

    <!-- 查询重点工程项目部门统计数据列表（含计划量） (月) -->
    <select id="selectMonthlyPriorityProjectDepartmentWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingDepartmentWithPlanStatsResult">
        SELECT
            v.year,
            v.month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.project_department_id,
            v.project_department_name,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_priority_monthly_department_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                pdm.project_department_id,
                SUM(pdm.drift_meter) as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_month(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fm
            WHERE EXISTS (
                SELECT 1 FROM base_priority_project pp
                WHERE pp.stope_id = pdm.stope_id
                AND pp.working_face_id = pdm.working_face_id
                AND pp.is_delete = 0
                AND pp.status = 1
            )
            GROUP BY fm.financial_year, fm.financial_month, pdm.project_department_id
        ) p ON v.year = p.year AND v.month = p.month AND v.project_department_id = p.project_department_id
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.month, v.project_department_id
    </select>

    <!-- 查询重点工程项目部门统计数据列表（含计划量） (年) -->
    <select id="selectYearlyPriorityProjectDepartmentWithPlanStats" parameterType="java.util.Date" resultMap="DataTunnelingDepartmentWithPlanStatsResult">
        SELECT
            v.year,
            NULL as month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.project_department_id,
            v.project_department_name,
            v.total_tunneling_length,
            v.total_tunneling_volume,
            COALESCE(p.plan_tunneling_length, 0) as plan_tunneling_length
        FROM vdata_tunneling_priority_yearly_department_stats v
        LEFT JOIN (
            SELECT
                fy.financial_year as year,
                pdm.project_department_id,
                SUM(pdm.drift_meter) as plan_tunneling_length
            FROM plan_drift_monthly pdm
            CROSS JOIN get_financial_year(TO_DATE(pdm.plan_date, 'YYYYMM')) AS fy
            WHERE EXISTS (
                SELECT 1 FROM base_priority_project pp
                WHERE pp.stope_id = pdm.stope_id
                AND pp.working_face_id = pdm.working_face_id
                AND pp.is_delete = 0
                AND pp.status = 1
            )
            GROUP BY fy.financial_year, pdm.project_department_id
        ) p ON v.year = p.year AND v.project_department_id = p.project_department_id
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, 1, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, 1, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.project_department_id
    </select>

    <!-- ==================== 重点工程采场统计查询 ==================== -->

    <!-- 查询重点工程采场统计数据列表 (日) -->
    <select id="selectDailyPriorityProjectStopeStats" parameterType="java.util.Date" resultMap="DataTunnelingStopeStatsResult">
        SELECT
            v.operation_date,
            v.stope_id,
            v.stope_name,
            v.total_tunneling_length,
            v.total_tunneling_volume
        FROM vdata_tunneling_priority_daily_stope_stats v
        <where>
            <if test="startDate != null">
                AND v.operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.operation_date, v.stope_id
    </select>

    <!-- 查询重点工程采场统计数据列表 (周) -->
    <select id="selectWeeklyPriorityProjectStopeStats" parameterType="java.util.Date" resultMap="DataTunnelingStopeStatsResult">
        SELECT
            v.year,
            v.week,
            v.week_start_date,
            v.week_end_date,
            v.stope_id,
            v.stope_name,
            v.total_tunneling_length,
            v.total_tunneling_volume
        FROM vdata_tunneling_priority_weekly_stope_stats v
        <where>
            <if test="startDate != null">
                AND v.week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.week, v.stope_id
    </select>

    <!-- 查询重点工程采场统计数据列表 (月) -->
    <select id="selectMonthlyPriorityProjectStopeStats" parameterType="java.util.Date" resultMap="DataTunnelingStopeStatsResult">
        SELECT
            v.year,
            v.month,
            v.stope_id,
            v.stope_name,
            v.total_tunneling_length,
            v.total_tunneling_volume
        FROM vdata_tunneling_priority_monthly_stope_stats v
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.month, v.stope_id
    </select>

    <!-- 查询重点工程采场统计数据列表 (年) -->
    <select id="selectYearlyPriorityProjectStopeStats" parameterType="java.util.Date" resultMap="DataTunnelingStopeStatsResult">
        SELECT
            v.year,
            v.stope_id,
            v.stope_name,
            v.total_tunneling_length,
            v.total_tunneling_volume
        FROM vdata_tunneling_priority_yearly_stope_stats v
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, 1, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, 1, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.stope_id
    </select>

    <!-- ==================== 重点工程工作面统计查询 ==================== -->

    <!-- 查询重点工程工作面统计数据列表 (日) -->
    <select id="selectDailyPriorityProjectWorkingFaceStats" parameterType="java.util.Date" resultMap="DataTunnelingWorkingFaceStatsResult">
        SELECT
            v.operation_date,
            v.working_face_id,
            v.working_face_name,
            v.total_tunneling_length,
            v.total_tunneling_volume
        FROM vdata_tunneling_priority_daily_face_stats v
        <where>
            <if test="startDate != null">
                AND v.operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.operation_date, v.working_face_id
    </select>

    <!-- 查询重点工程工作面统计数据列表 (周) -->
    <select id="selectWeeklyPriorityProjectWorkingFaceStats" parameterType="java.util.Date" resultMap="DataTunnelingWorkingFaceStatsResult">
        SELECT
            v.year,
            v.week,
            v.week_start_date,
            v.week_end_date,
            v.working_face_id,
            v.working_face_name,
            v.total_tunneling_length,
            v.total_tunneling_volume
        FROM vdata_tunneling_priority_weekly_face_stats v
        <where>
            <if test="startDate != null">
                AND v.week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.week, v.working_face_id
    </select>

    <!-- 查询重点工程工作面统计数据列表 (月) -->
    <select id="selectMonthlyPriorityProjectWorkingFaceStats" parameterType="java.util.Date" resultMap="DataTunnelingWorkingFaceStatsResult">
        SELECT
            v.year,
            v.month,
            v.working_face_id,
            v.working_face_name,
            v.total_tunneling_length,
            v.total_tunneling_volume
        FROM vdata_tunneling_priority_monthly_face_stats v
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.month, v.working_face_id
    </select>

    <!-- 查询重点工程工作面统计数据列表 (年) -->
    <select id="selectYearlyPriorityProjectWorkingFaceStats" parameterType="java.util.Date" resultMap="DataTunnelingWorkingFaceStatsResult">
        SELECT
            v.year,
            v.working_face_id,
            v.working_face_name,
            v.total_tunneling_length,
            v.total_tunneling_volume
        FROM vdata_tunneling_priority_yearly_face_stats v
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, 1, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, 1, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.working_face_id
    </select>

</mapper>
