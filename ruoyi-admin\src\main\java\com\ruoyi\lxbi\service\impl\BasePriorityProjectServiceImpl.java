package com.ruoyi.lxbi.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.lxbi.mapper.BasePriorityProjectMapper;
import com.ruoyi.lxbi.domain.BasePriorityProject;
import com.ruoyi.lxbi.domain.response.BasePriorityProjectVo;
import com.ruoyi.lxbi.service.IBasePriorityProjectService;

/**
 * 重点工程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
public class BasePriorityProjectServiceImpl implements IBasePriorityProjectService 
{
    @Autowired
    private BasePriorityProjectMapper basePriorityProjectMapper;

    /**
     * 查询重点工程
     * 
     * @param id 重点工程主键
     * @return 重点工程
     */
    @Override
    public BasePriorityProject selectBasePriorityProjectById(Long id)
    {
        return basePriorityProjectMapper.selectBasePriorityProjectById(id);
    }

    /**
     * 查询重点工程列表
     *
     * @param basePriorityProject 重点工程
     * @return 重点工程
     */
    @Override
    public List<BasePriorityProjectVo> selectBasePriorityProjectList(BasePriorityProject basePriorityProject)
    {
        return basePriorityProjectMapper.selectBasePriorityProjectList(basePriorityProject);
    }

    /**
     * 查询所有重点工程列表（不分页）
     *
     * @return 重点工程
     */
    @Override
    public List<BasePriorityProjectVo> selectBasePriorityProjectListAll(BasePriorityProject basePriorityProject)
    {
        return basePriorityProjectMapper.selectBasePriorityProjectListAll(basePriorityProject);
    }

    /**
     * 新增重点工程
     * 
     * @param basePriorityProject 重点工程
     * @return 结果
     */
    @Override
    public int insertBasePriorityProject(BasePriorityProject basePriorityProject)
    {
        basePriorityProject.setCreateTime(DateUtils.getNowDate());
        return basePriorityProjectMapper.insertBasePriorityProject(basePriorityProject);
    }

    /**
     * 修改重点工程
     * 
     * @param basePriorityProject 重点工程
     * @return 结果
     */
    @Override
    public int updateBasePriorityProject(BasePriorityProject basePriorityProject)
    {
        basePriorityProject.setUpdateTime(DateUtils.getNowDate());
        return basePriorityProjectMapper.updateBasePriorityProject(basePriorityProject);
    }

    /**
     * 批量删除重点工程
     * 
     * @param ids 需要删除的重点工程主键
     * @return 结果
     */
    @Override
    public int deleteBasePriorityProjectByIds(Long[] ids)
    {
        return basePriorityProjectMapper.deleteBasePriorityProjectByIds(ids);
    }

    /**
     * 删除重点工程信息
     *
     * @param id 重点工程主键
     * @return 结果
     */
    @Override
    public int deleteBasePriorityProjectById(Long id)
    {
        return basePriorityProjectMapper.deleteBasePriorityProjectById(id);
    }

    /**
     * 批量逻辑删除重点工程
     *
     * @param ids 需要删除的重点工程主键
     * @return 结果
     */
    @Override
    public int logicDeleteBasePriorityProjectByIds(Long[] ids)
    {
        return basePriorityProjectMapper.logicDeleteBasePriorityProjectByIds(ids);
    }
}
