package com.ruoyi.lxbi.controller.stat.sec;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.admin.service.IFillingLeakageStatService;
import com.ruoyi.lxbi.domain.vo.FillingLeakageLocationDistributionVO;
import com.ruoyi.lxbi.domain.vo.FillingLeakageOverviewVO;
import com.ruoyi.lxbi.domain.vo.FillingLeakageTrendVO;
import com.ruoyi.lxbi.utils.DateRangeCalculator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 充填漏浆检测统计Controller
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@RestController
@RequestMapping("/sec/stat/filling-leakage")
public class FillingLeakageStatController {

    @Autowired
    private IFillingLeakageStatService fillingLeakageStatService;

    /**
     * 获取充填漏浆检测概览统计
     */
    @Anonymous
    @GetMapping("/overview")
    public R<FillingLeakageOverviewVO> getOverviewStatistics(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        FillingLeakageOverviewVO overview = fillingLeakageStatService.getOverviewStatistics(viewType, startDate, endDate);
        return R.ok(overview);
    }

    /**
     * 获取充填漏浆检测趋势统计
     */
    @Anonymous
    @GetMapping("/alarm-trend")
    public R<List<FillingLeakageTrendVO>> getAlarmTrend(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<FillingLeakageTrendVO> trend = fillingLeakageStatService.getAlarmTrend(viewType, startDate, endDate);
        return R.ok(trend);
    }

    /**
     * 获取充填漏浆检测位置分布统计 (雷达图数据)
     */
    @Anonymous
    @GetMapping("/location-distribution")
    public R<List<FillingLeakageLocationDistributionVO>> getLocationDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<FillingLeakageLocationDistributionVO> distribution = fillingLeakageStatService.getLocationDistribution(viewType, startDate, endDate);
        return R.ok(distribution);
    }
}
