<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataOrepassOperationMapper">
    
    <resultMap type="DataOrepassOperation" id="DataOrepassOperationResult">
        <result property="id"    column="id"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="orePassId"    column="ore_pass_id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="trips"    column="trips"    />
        <result property="oreTons"    column="ore_tons"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataOrepassOperationVo">
        select doo.id,
               doo.project_department_id,
               doo.ore_pass_id,
               doo.working_period_id,
               doo.operation_date,
               doo.trips,
               doo.ore_tons,
               doo.create_by,
               doo.create_time,
               doo.update_by,
               doo.update_time,
               bwp.working_period_name,
               bpd.project_department_name,
               bop.ore_pass_name
        from data_orepass_operation doo
                 left join base_working_period bwp on bwp.working_period_id = doo.working_period_id
                 left join base_project_department bpd on bpd.project_department_id = doo.project_department_id
                 left join base_ore_pass bop on bop.ore_pass_id = doo.ore_pass_id
    </sql>

    <select id="selectDataOrepassOperationList" parameterType="DataOrepassOperation" resultType="com.ruoyi.lxbi.domain.response.DataOrepassOperationVo">
        <include refid="selectDataOrepassOperationVo"/>
        <where>
            <if test="operationDate != null"> and doo.operation_date = #{operationDate}</if>
            <if test="projectDepartmentId != null "> and doo.project_department_id = #{projectDepartmentId}</if>
            <if test="orePassId != null "> and doo.ore_pass_id = #{orePassId}</if>
            <if test="workingPeriodId != null "> and doo.working_period_id = #{workingPeriodId}</if>
            <if test="params.beginOperationDate != null and params.beginOperationDate != '' and params.endOperationDate != null and params.endOperationDate != ''"> and doo.operation_date between #{params.beginOperationDate}::date and #{params.endOperationDate}::date</if>
            <if test="trips != null "> and doo.trips = #{trips}</if>
            <if test="oreTons != null"> and doo.ore_tons = #{oreTons}</if>
        </where>
    </select>
    
    <select id="selectDataOrepassOperationById" parameterType="Long" resultMap="DataOrepassOperationResult">
        <include refid="selectDataOrepassOperationVo"/>
        where doo.id = #{id}
    </select>

    <insert id="insertDataOrepassOperation" parameterType="DataOrepassOperation" useGeneratedKeys="true" keyProperty="id">
        insert into data_orepass_operation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="orePassId != null">ore_pass_id,</if>
            <if test="workingPeriodId != null">working_period_id,</if>
            <if test="operationDate != null">operation_date,</if>
            <if test="trips != null">trips,</if>
            <if test="oreTons != null">ore_tons,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="orePassId != null">#{orePassId},</if>
            <if test="workingPeriodId != null">#{workingPeriodId},</if>
            <if test="operationDate != null">#{operationDate},</if>
            <if test="trips != null">#{trips},</if>
            <if test="oreTons != null">#{oreTons},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataOrepassOperation" parameterType="DataOrepassOperation">
        update data_orepass_operation
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="orePassId != null">ore_pass_id = #{orePassId},</if>
            <if test="workingPeriodId != null">working_period_id = #{workingPeriodId},</if>
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="trips != null">trips = #{trips},</if>
            <if test="oreTons != null">ore_tons = #{oreTons},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataOrepassOperationById" parameterType="Long">
        delete from data_orepass_operation where id = #{id}
    </delete>

    <delete id="deleteDataOrepassOperationByIds" parameterType="String">
        delete from data_orepass_operation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据作业日期和项目部门查询溜井运行数据列表 -->
    <select id="selectDataOrepassOperationByOperationDateAndProject" resultType="com.ruoyi.lxbi.domain.response.DataOrepassOperationVo">
        <include refid="selectDataOrepassOperationVo"/>
        where doo.operation_date = #{operationDate}::date
        and doo.project_department_id = #{projectDepartmentId}
        order by doo.working_period_id, doo.ore_pass_id
    </select>

    <!-- 批量新增溜井运行数据 -->
    <insert id="batchInsertDataOrepassOperation" parameterType="java.util.List">
        insert into data_orepass_operation (project_department_id, ore_pass_id, working_period_id, operation_date, trips, ore_tons, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectDepartmentId}, #{item.orePassId}, #{item.workingPeriodId}, #{item.operationDate}, #{item.trips}, #{item.oreTons}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>