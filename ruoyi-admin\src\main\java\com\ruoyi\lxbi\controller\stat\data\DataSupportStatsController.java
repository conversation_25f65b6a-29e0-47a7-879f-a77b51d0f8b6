package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.response.DataSupportTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportDepartmentWithPlanStats;
import com.ruoyi.lxbi.service.IDataSupportStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 支护数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/data/stats/support")
public class DataSupportStatsController {
    @Autowired
    private IDataSupportStatsService dataSupportStatsService;

    /**
     * 查询总体支护统计数据（含计划量）
     * 对应图表一：总体支护统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:support:01a')")
    @GetMapping("/01a")
    public R<List<DataSupportTotalWithPlanStats>> totalSupportWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                        @RequestParam(value = "startDate", required = false) String startDate,
                                                                        @RequestParam(value = "endDate", required = false) String endDate) {
        DataSupportStatsRequest request = new DataSupportStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataSupportTotalWithPlanStats> stats = dataSupportStatsService.selectTotalWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询按部门分组的支护统计数据（含计划量）
     * 对应图表二：按部门分组的支护统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:support:01b')")
    @GetMapping("/01b")
    public R<List<DataSupportDepartmentWithPlanStats>> departmentSupportWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                                  @RequestParam(value = "startDate", required = false) String startDate,
                                                                                  @RequestParam(value = "endDate", required = false) String endDate) {
        DataSupportStatsRequest request = new DataSupportStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataSupportDepartmentWithPlanStats> stats = dataSupportStatsService.selectDepartmentWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }
}
