package com.ruoyi.lxbi.domain.response;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.lxbi.domain.BasePriorityProject;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 重点工程VO对象
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BasePriorityProjectVo extends BasePriorityProject {

    /** 采场ID - 不导出 */
    @Excel(name = "采场ID", isExport = false)
    private Long stopeId;

    /** 工作面ID - 不导出 */
    @Excel(name = "工作面ID", isExport = false)
    private Long workingFaceId;

    /** 采场名称 */
    @Excel(name = "采场名称", sort = 1)
    private String stopeName;

    /** 工作面名称 */
    @Excel(name = "工作面名称", sort = 2)
    private String workingFaceName;

}
