package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.DataIronConcentrate;

/**
 * 铁精粉生产数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public interface IDataIronConcentrateService 
{
    /**
     * 查询铁精粉生产数据
     * 
     * @param id 铁精粉生产数据主键
     * @return 铁精粉生产数据
     */
    public DataIronConcentrate selectDataIronConcentrateById(Long id);

    /**
     * 查询铁精粉生产数据列表
     * 
     * @param dataIronConcentrate 铁精粉生产数据
     * @return 铁精粉生产数据集合
     */
    public List<DataIronConcentrate> selectDataIronConcentrateList(DataIronConcentrate dataIronConcentrate);

    /**
     * 新增铁精粉生产数据
     * 
     * @param dataIronConcentrate 铁精粉生产数据
     * @return 结果
     */
    public int insertDataIronConcentrate(DataIronConcentrate dataIronConcentrate);

    /**
     * 修改铁精粉生产数据
     * 
     * @param dataIronConcentrate 铁精粉生产数据
     * @return 结果
     */
    public int updateDataIronConcentrate(DataIronConcentrate dataIronConcentrate);

    /**
     * 批量删除铁精粉生产数据
     * 
     * @param ids 需要删除的铁精粉生产数据主键集合
     * @return 结果
     */
    public int deleteDataIronConcentrateByIds(Long[] ids);

    /**
     * 删除铁精粉生产数据信息
     * 
     * @param id 铁精粉生产数据主键
     * @return 结果
     */
    public int deleteDataIronConcentrateById(Long id);
}
