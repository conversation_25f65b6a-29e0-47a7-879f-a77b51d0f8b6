package com.ruoyi.lxbi.controller.plan;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.PlanStopeMonthly;
import com.ruoyi.lxbi.domain.request.PlanStopeMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanStopeMonthlyVo;
import com.ruoyi.lxbi.service.IPlanStopeMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 采场月度计划Controller
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/plan/planStopeMonthly")
public class PlanStopeMonthlyController extends BaseController {
    @Autowired
    private IPlanStopeMonthlyService planStopeMonthlyService;

    /**
     * 查询采场月度计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planStopeMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanStopeMonthly planStopeMonthly) {
        startPage();
        List<PlanStopeMonthlyVo> list = planStopeMonthlyService.selectPlanStopeMonthlyList(planStopeMonthly);
        return getDataTable(list);
    }

    /**
     * 导出采场月度计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planStopeMonthly:export')")
    @Log(title = "采场月度计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanStopeMonthly planStopeMonthly) {
        List<PlanStopeMonthlyVo> list = planStopeMonthlyService.selectPlanStopeMonthlyList(planStopeMonthly);
        ExcelUtil<PlanStopeMonthlyVo> util = new ExcelUtil<PlanStopeMonthlyVo>(PlanStopeMonthlyVo.class);
        util.exportExcel(response, list, "采场月度计划数据");
    }

    /**
     * 获取采场月度计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('plan:planStopeMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planStopeMonthlyService.selectPlanStopeMonthlyById(id));
    }

    /**
     * 新增采场月度计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planStopeMonthly:add')")
    @Log(title = "采场月度计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanStopeMonthly planStopeMonthly)
    {
        return toAjax(planStopeMonthlyService.insertPlanStopeMonthly(planStopeMonthly));
    }

    /**
     * 修改采场月度计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planStopeMonthly:edit')")
    @Log(title = "采场月度计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanStopeMonthly planStopeMonthly)
    {
        return toAjax(planStopeMonthlyService.updatePlanStopeMonthly(planStopeMonthly));
    }

    /**
     * 删除采场月度计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planStopeMonthly:remove')")
    @Log(title = "采场月度计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(planStopeMonthlyService.deletePlanStopeMonthlyByIds(ids));
    }

    /**
     * 批量保存采场月度计划（增删改查）
     * 传入批量列表，验证是否同一个月份的数据，然后查询这个月份的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('plan:planStopeMonthly:edit')")
    @Log(title = "采场月度计划批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<PlanStopeMonthlyBatchDto> batchDataList)
    {
        try {
            int result = planStopeMonthlyService.batchSavePlanStopeMonthly(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
