package com.ruoyi.lxbi.controller.kafka;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.KafkaSubstationBasicInfo;
import com.ruoyi.lxbi.service.IKafkaSubstationBasicInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 分站基本信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@RestController
@RequestMapping("/kafka/SubstationBasicInfo")
public class KafkaSubstationBasicInfoController extends BaseController {
    @Autowired
    private IKafkaSubstationBasicInfoService kafkaSubstationBasicInfoService;

    /**
     * 查询分站基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationBasicInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(KafkaSubstationBasicInfo kafkaSubstationBasicInfo) {
        startPage();
        List<KafkaSubstationBasicInfo> list = kafkaSubstationBasicInfoService.selectKafkaSubstationBasicInfoList(kafkaSubstationBasicInfo);
        return getDataTable(list);
    }

    /**
     * 导出分站基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationBasicInfo:export')")
    @Log(title = "分站基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KafkaSubstationBasicInfo kafkaSubstationBasicInfo) {
        List<KafkaSubstationBasicInfo> list = kafkaSubstationBasicInfoService.selectKafkaSubstationBasicInfoList(kafkaSubstationBasicInfo);
        ExcelUtil<KafkaSubstationBasicInfo> util = new ExcelUtil<KafkaSubstationBasicInfo>(KafkaSubstationBasicInfo.class);
        util.exportExcel(response, list, "分站基本信息数据");
    }

    /**
     * 获取分站基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationBasicInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kafkaSubstationBasicInfoService.selectKafkaSubstationBasicInfoById(id));
    }

    /**
     * 新增分站基本信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationBasicInfo:add')")
    @Log(title = "分站基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KafkaSubstationBasicInfo kafkaSubstationBasicInfo)
    {
        return toAjax(kafkaSubstationBasicInfoService.insertKafkaSubstationBasicInfo(kafkaSubstationBasicInfo));
    }

    /**
     * 修改分站基本信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationBasicInfo:edit')")
    @Log(title = "分站基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KafkaSubstationBasicInfo kafkaSubstationBasicInfo)
    {
        return toAjax(kafkaSubstationBasicInfoService.updateKafkaSubstationBasicInfo(kafkaSubstationBasicInfo));
    }

    /**
     * 删除分站基本信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:SubstationBasicInfo:remove')")
    @Log(title = "分站基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kafkaSubstationBasicInfoService.deleteKafkaSubstationBasicInfoByIds(ids));
    }
}
