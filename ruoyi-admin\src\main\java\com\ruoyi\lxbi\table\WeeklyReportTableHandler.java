package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.PlanMiningMonthly;
import com.ruoyi.lxbi.domain.request.DataDrillingStatsRequest;
import com.ruoyi.lxbi.domain.request.DataFillingStatsRequest;
import com.ruoyi.lxbi.domain.request.DataMineHoistingStatsRequest;
import com.ruoyi.lxbi.domain.request.DataMuckingOutStatsRequest;
import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.request.DataTunnelingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataFillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataMineHoistingStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStopeStats;
import com.ruoyi.lxbi.domain.response.DataSupportTypeTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.PlanMiningMonthlyVo;
import com.ruoyi.lxbi.domain.table.WeeklyReportTableVo;
import com.ruoyi.lxbi.service.IDataDeepHoleStatsService;
import com.ruoyi.lxbi.service.IDataDthStatsService;
import com.ruoyi.lxbi.service.IDataFillingStatsService;
import com.ruoyi.lxbi.service.IDataMineHoistingStatsService;
import com.ruoyi.lxbi.service.IDataMuckingOutStatsService;
import com.ruoyi.lxbi.service.IDataShotcreteSupportStatsService;
import com.ruoyi.lxbi.service.IDataTunnelingStatsService;
import com.ruoyi.lxbi.service.IPlanMiningMonthlyService;
import com.ruoyi.lxbi.table.params.WeeklyReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 周报数据表格处理器
 */
@Component
public class WeeklyReportTableHandler extends BaseTableHandler<WeeklyReportTableVo, WeeklyReportQueryParams> {

    @Autowired
    private IDataMineHoistingStatsService dataMineHoistingStatsService;

    @Autowired
    private IDataMuckingOutStatsService dataMuckingOutStatsService;

    @Autowired
    private IPlanMiningMonthlyService planMiningMonthlyService;

    @Autowired
    private IDataTunnelingStatsService dataTunnelingStatsService;

    @Autowired
    private IDataShotcreteSupportStatsService dataShotcreteSupportStatsService;

    @Autowired
    private IDataFillingStatsService dataFillingStatsService;

    @Autowired
    private IDataDthStatsService dataDthStatsService;

    @Autowired
    private IDataDeepHoleStatsService dataDeepHoleStatsService;

    @Override
    public List<WeeklyReportTableVo> queryTableData(WeeklyReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date queryDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取财务周范围（周四到周三）
        Date[] weekRange = FinancialDateUtils.getFinancialWeekRange(queryDate);
        Date weekStart = weekRange[0];
        Date weekEnd = weekRange[1];

        // 获取财务月范围
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(queryDate);

        // 获取月度计划数据
        Map<String, BigDecimal> monthlyPlans = getMonthlyPlans(queryDate);

        // 获取月累计数据
        Map<String, BigDecimal> monthlyAccumulated = getMonthlyAccumulated(financialMonthStart, queryDate);

        // 获取周数据
        Map<String, BigDecimal> weeklyData = getWeeklyData(weekStart, weekEnd);

        // 构建表格数据
        return buildTableData(monthlyPlans, monthlyAccumulated, weeklyData);
    }



    /**
     * 获取月度计划数据
     */
    private Map<String, BigDecimal> getMonthlyPlans(Date operationDate) {
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        PlanMiningMonthly queryParam = new PlanMiningMonthly();
        queryParam.setPlanDate(financialMonth);

        List<PlanMiningMonthlyVo> plans = planMiningMonthlyService.selectPlanMiningMonthlyList(queryParam);

        Map<String, BigDecimal> result = new HashMap<>();
        for (PlanMiningMonthlyVo plan : plans) {
            if (plan.getDriftMeter() != null) {
                result.put("drift", result.getOrDefault("drift", BigDecimal.ZERO).add(plan.getDriftMeter()));
            }
            if (plan.getRawOreVolume() != null) {
                result.put("rawOre", result.getOrDefault("rawOre", BigDecimal.ZERO).add(plan.getRawOreVolume()));
            }
            if (plan.getSupportMeter() != null) {
                result.put("support", result.getOrDefault("support", BigDecimal.ZERO).add(plan.getSupportMeter()));
            }
            if (plan.getFillingVolume() != null) {
                result.put("filling", result.getOrDefault("filling", BigDecimal.ZERO).add(plan.getFillingVolume()));
            }
            if (plan.getDthMeter() != null) {
                result.put("dth", result.getOrDefault("dth", BigDecimal.ZERO).add(plan.getDthMeter()));
            }
            if (plan.getDeepHoleMeter() != null) {
                result.put("deepHole", result.getOrDefault("deepHole", BigDecimal.ZERO).add(plan.getDeepHoleMeter()));
            }
        }

        return result;
    }

    /**
     * 获取月累计数据
     */
    private Map<String, BigDecimal> getMonthlyAccumulated(Date startDate, Date endDate) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 获取提升数据（原矿量）
        DataMineHoistingStatsRequest hoistingRequest = new DataMineHoistingStatsRequest();
        hoistingRequest.setStartDate(startDate);
        hoistingRequest.setEndDate(endDate);
        List<DataMineHoistingStats> hoistingStats = dataMineHoistingStatsService.selectStatsList(hoistingRequest, "daily");
        
        BigDecimal totalRawOre = hoistingStats.stream()
                .map(stats -> stats.getTotalWeight() != null ? BigDecimal.valueOf(stats.getTotalWeight()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("rawOre", totalRawOre);

        // 获取出矿数据
        DataMuckingOutStatsRequest muckingRequest = new DataMuckingOutStatsRequest();
        muckingRequest.setStartDate(startDate);
        muckingRequest.setEndDate(endDate);
        List<DataMuckingOutStopeStats> muckingStats = dataMuckingOutStatsService.selectStopeStatsList(muckingRequest, "daily");
        
        BigDecimal totalMucking = muckingStats.stream()
                .map(stats -> stats.getTotalTons() != null ? BigDecimal.valueOf(stats.getTotalTons()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("mucking", totalMucking);

        // 获取掘进数据
        DataTunnelingStatsRequest tunnelingRequest = new DataTunnelingStatsRequest();
        tunnelingRequest.setStartDate(startDate);
        tunnelingRequest.setEndDate(endDate);
        List<DataTunnelingTotalWithPlanStats> tunnelingStats = dataTunnelingStatsService.selectTotalWithPlanStatsList(tunnelingRequest, "daily");

        BigDecimal totalTunneling = tunnelingStats.stream()
                .filter(stats -> stats.getTotalTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("drift", totalTunneling);

        // 获取支护数据
        DataSupportStatsRequest supportRequest = new DataSupportStatsRequest();
        supportRequest.setStartDate(startDate);
        supportRequest.setEndDate(endDate);
        List<DataSupportTypeTotalWithPlanStats> supportStats = dataShotcreteSupportStatsService.selectTotalWithPlanStatsList(supportRequest, "daily");

        BigDecimal totalSupport = supportStats.stream()
                .filter(stats -> stats.getTotalSupportLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSupportLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("support", totalSupport);

        // 获取充填数据
        DataFillingStatsRequest fillingRequest = new DataFillingStatsRequest();
        fillingRequest.setStartDate(startDate);
        fillingRequest.setEndDate(endDate);
        List<DataFillingTotalWithPlanStats> fillingStats = dataFillingStatsService.selectTotalWithPlanStatsList(fillingRequest, "daily");

        BigDecimal totalFilling = fillingStats.stream()
                .filter(stats -> stats.getTotalSlurryVolume() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSlurryVolume()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("filling", totalFilling);

        // 获取潜孔数据
        DataDrillingStatsRequest dthRequest = new DataDrillingStatsRequest();
        dthRequest.setStartDate(startDate);
        dthRequest.setEndDate(endDate);
        List<DataDrillingTotalWithPlanStats> dthStats = dataDthStatsService.selectTotalWithPlanStatsList(dthRequest, "daily");

        BigDecimal totalDth = dthStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("dth", totalDth);

        // 获取中深孔数据
        DataDrillingStatsRequest deepHoleRequest = new DataDrillingStatsRequest();
        deepHoleRequest.setStartDate(startDate);
        deepHoleRequest.setEndDate(endDate);
        List<DataDrillingTotalWithPlanStats> deepHoleStats = dataDeepHoleStatsService.selectTotalWithPlanStatsList(deepHoleRequest, "daily");

        BigDecimal totalDeepHole = deepHoleStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("deepHole", totalDeepHole);

        return result;
    }

    /**
     * 获取周数据
     */
    private Map<String, BigDecimal> getWeeklyData(Date weekStart, Date weekEnd) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 获取周提升数据（原矿量）
        DataMineHoistingStatsRequest hoistingRequest = new DataMineHoistingStatsRequest();
        hoistingRequest.setStartDate(weekStart);
        hoistingRequest.setEndDate(weekEnd);
        List<DataMineHoistingStats> hoistingStats = dataMineHoistingStatsService.selectStatsList(hoistingRequest, "daily");
        
        BigDecimal weeklyRawOre = hoistingStats.stream()
                .map(stats -> stats.getTotalWeight() != null ? BigDecimal.valueOf(stats.getTotalWeight()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("rawOre", weeklyRawOre);

        // 获取周出矿数据
        DataMuckingOutStatsRequest muckingRequest = new DataMuckingOutStatsRequest();
        muckingRequest.setStartDate(weekStart);
        muckingRequest.setEndDate(weekEnd);
        List<DataMuckingOutStopeStats> muckingStats = dataMuckingOutStatsService.selectStopeStatsList(muckingRequest, "daily");
        
        BigDecimal weeklyMucking = muckingStats.stream()
                .map(stats -> stats.getTotalTons() != null ? BigDecimal.valueOf(stats.getTotalTons()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("mucking", weeklyMucking);

        // 获取周掘进数据
        DataTunnelingStatsRequest weeklyTunnelingRequest = new DataTunnelingStatsRequest();
        weeklyTunnelingRequest.setStartDate(weekStart);
        weeklyTunnelingRequest.setEndDate(weekEnd);
        List<DataTunnelingTotalWithPlanStats> weeklyTunnelingStats = dataTunnelingStatsService.selectTotalWithPlanStatsList(weeklyTunnelingRequest, "daily");

        BigDecimal weeklyTotalTunneling = weeklyTunnelingStats.stream()
                .filter(stats -> stats.getTotalTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("drift", weeklyTotalTunneling);

        // 获取周支护数据
        DataSupportStatsRequest weeklySupportRequest = new DataSupportStatsRequest();
        weeklySupportRequest.setStartDate(weekStart);
        weeklySupportRequest.setEndDate(weekEnd);
        List<DataSupportTypeTotalWithPlanStats> weeklySupportStats = dataShotcreteSupportStatsService.selectTotalWithPlanStatsList(weeklySupportRequest, "daily");

        BigDecimal weeklyTotalSupport = weeklySupportStats.stream()
                .filter(stats -> stats.getTotalSupportLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSupportLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("support", weeklyTotalSupport);

        // 获取周充填数据
        DataFillingStatsRequest weeklyFillingRequest = new DataFillingStatsRequest();
        weeklyFillingRequest.setStartDate(weekStart);
        weeklyFillingRequest.setEndDate(weekEnd);
        List<DataFillingTotalWithPlanStats> weeklyFillingStats = dataFillingStatsService.selectTotalWithPlanStatsList(weeklyFillingRequest, "daily");

        BigDecimal weeklyTotalFilling = weeklyFillingStats.stream()
                .filter(stats -> stats.getTotalSlurryVolume() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSlurryVolume()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("filling", weeklyTotalFilling);

        // 获取周潜孔数据
        DataDrillingStatsRequest weeklyDthRequest = new DataDrillingStatsRequest();
        weeklyDthRequest.setStartDate(weekStart);
        weeklyDthRequest.setEndDate(weekEnd);
        List<DataDrillingTotalWithPlanStats> weeklyDthStats = dataDthStatsService.selectTotalWithPlanStatsList(weeklyDthRequest, "daily");

        BigDecimal weeklyTotalDth = weeklyDthStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("dth", weeklyTotalDth);

        // 获取周中深孔数据
        DataDrillingStatsRequest weeklyDeepHoleRequest = new DataDrillingStatsRequest();
        weeklyDeepHoleRequest.setStartDate(weekStart);
        weeklyDeepHoleRequest.setEndDate(weekEnd);
        List<DataDrillingTotalWithPlanStats> weeklyDeepHoleStats = dataDeepHoleStatsService.selectTotalWithPlanStatsList(weeklyDeepHoleRequest, "daily");

        BigDecimal weeklyTotalDeepHole = weeklyDeepHoleStats.stream()
                .filter(stats -> stats.getTotalProgressMeters() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalProgressMeters()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("deepHole", weeklyTotalDeepHole);

        return result;
    }

    /**
     * 构建表格数据
     */
    private List<WeeklyReportTableVo> buildTableData(Map<String, BigDecimal> monthlyPlans,
                                                     Map<String, BigDecimal> monthlyAccumulated,
                                                     Map<String, BigDecimal> weeklyData) {
        List<WeeklyReportTableVo> result = new ArrayList<>();

        // 巷道掘进
        result.add(createReportItem("一", "巷道掘进", "m", 
                monthlyPlans.getOrDefault("drift", BigDecimal.ZERO),
                monthlyAccumulated.getOrDefault("drift", BigDecimal.ZERO),
                weeklyData.getOrDefault("drift", BigDecimal.ZERO)));

        // 原矿量
        result.add(createReportItem("二", "原矿量", "t", 
                monthlyPlans.getOrDefault("rawOre", BigDecimal.ZERO),
                monthlyAccumulated.getOrDefault("rawOre", BigDecimal.ZERO),
                weeklyData.getOrDefault("rawOre", BigDecimal.ZERO)));

        // 支护
        result.add(createReportItem("三", "支护", "m", 
                monthlyPlans.getOrDefault("support", BigDecimal.ZERO),
                monthlyAccumulated.getOrDefault("support", BigDecimal.ZERO),
                weeklyData.getOrDefault("support", BigDecimal.ZERO)));

        // 充填
        result.add(createReportItem("四", "充填", "m³", 
                monthlyPlans.getOrDefault("filling", BigDecimal.ZERO),
                monthlyAccumulated.getOrDefault("filling", BigDecimal.ZERO),
                weeklyData.getOrDefault("filling", BigDecimal.ZERO)));

        // 潜孔
        result.add(createReportItem("五", "潜孔", "m", 
                monthlyPlans.getOrDefault("dth", BigDecimal.ZERO),
                monthlyAccumulated.getOrDefault("dth", BigDecimal.ZERO),
                weeklyData.getOrDefault("dth", BigDecimal.ZERO)));

        // 中深孔
        result.add(createReportItem("六", "中深孔", "m", 
                monthlyPlans.getOrDefault("deepHole", BigDecimal.ZERO),
                monthlyAccumulated.getOrDefault("deepHole", BigDecimal.ZERO),
                weeklyData.getOrDefault("deepHole", BigDecimal.ZERO)));

        // 采场出矿
        result.add(createReportItem("七", "采场出矿", "t", 
                monthlyPlans.getOrDefault("mucking", BigDecimal.ZERO),
                monthlyAccumulated.getOrDefault("mucking", BigDecimal.ZERO),
                weeklyData.getOrDefault("mucking", BigDecimal.ZERO)));

        return result;
    }

    /**
     * 创建报表项
     */
    private WeeklyReportTableVo createReportItem(String serialNumber, String name, String unit,
                                                 BigDecimal monthlyPlan, BigDecimal monthlyAccumulated,
                                                 BigDecimal weeklyCompleted) {
        WeeklyReportTableVo vo = new WeeklyReportTableVo();
        vo.setSerialNumber(serialNumber);
        vo.setName(name);
        vo.setUnit(unit);
        vo.setMonthlyPlan(monthlyPlan);
        vo.setMonthlyAccumulated(monthlyAccumulated);

        // 计算月完成率
        if (monthlyPlan != null && monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal monthlyRate = monthlyAccumulated.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setMonthlyCompletionRate(monthlyRate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setMonthlyCompletionRate("0.00%");
        }

        // 计算财务周计划（财务月包含4个财务周，所以为月计划的1/4）
        BigDecimal weeklyPlan = monthlyPlan != null ? monthlyPlan.divide(new BigDecimal("4"), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        vo.setWeeklyPlan(weeklyPlan);
        vo.setWeeklyCompleted(weeklyCompleted);

        // 计算周完成率
        if (weeklyPlan.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal weeklyRate = weeklyCompleted.divide(weeklyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setWeeklyCompletionRate(weeklyRate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setWeeklyCompletionRate("0.00%");
        }

        return vo;
    }
}
