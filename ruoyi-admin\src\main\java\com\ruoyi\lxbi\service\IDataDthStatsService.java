package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DataDrillingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalStats;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataDrillingPeriodStats;
import com.ruoyi.lxbi.domain.response.DataDrillingDepartmentStats;
import com.ruoyi.lxbi.domain.response.DataDrillingDepartmentWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataDrillingStopeStats;
import com.ruoyi.lxbi.domain.response.DataDrillingWorkingFaceStats;

import java.util.List;

/**
 * 潜孔施工数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface IDataDthStatsService {

    /**
     * 查询总体统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合
     */
    public List<DataDrillingTotalStats> selectTotalStatsList(DataDrillingStatsRequest request, String viewType);

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    public List<DataDrillingTotalWithPlanStats> selectTotalWithPlanStatsList(DataDrillingStatsRequest request, String viewType);

    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    public List<DataDrillingPeriodStats> selectPeriodStatsList(DataDrillingStatsRequest request, String viewType);

    /**
     * 查询项目部门统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合
     */
    public List<DataDrillingDepartmentStats> selectDepartmentStatsList(DataDrillingStatsRequest request, String viewType);

    /**
     * 查询项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合（含计划量）
     */
    public List<DataDrillingDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataDrillingStatsRequest request, String viewType);

    /**
     * 查询采场统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 采场统计数据集合
     */
    public List<DataDrillingStopeStats> selectStopeStatsList(DataDrillingStatsRequest request, String viewType);

    /**
     * 查询工作面统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 工作面统计数据集合
     */
    public List<DataDrillingWorkingFaceStats> selectWorkingFaceStatsList(DataDrillingStatsRequest request, String viewType);

}
