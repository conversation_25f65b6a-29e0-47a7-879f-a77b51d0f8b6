package com.ruoyi.framework.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeClusterResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.Node;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.stereotype.Service;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;

import java.util.*;
import java.util.concurrent.*;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Kafka监控服务
 * 
 * <AUTHOR>
 * @date 2025-08-16
 */
@Slf4j
@Service
public class KafkaMonitoringService implements ApplicationRunner {

    @Autowired
    private AdminClient kafkaAdminClient;

    // AdminClient重连相关
    private volatile boolean adminClientClosed = false;
    private final Object adminClientLock = new Object();
    
    @Autowired
    private ConsumerFactory<String, String> consumerFactory;
    
    @Value("${spring.kafka.consumer.group-id}")
    private String groupId;

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final ExecutorService consumerScheduler = Executors.newCachedThreadPool();
    private volatile boolean isKafkaHealthy = false;
    private volatile long lastHealthCheckTime = 0;
    
    // 存储每个topic的消费者
    private final Map<String, KafkaConsumer<String, String>> topicConsumers = new ConcurrentHashMap<>();
    private final Map<String, Boolean> topicRunningStatus = new ConcurrentHashMap<>();

    // 数据输出目录
    private static final String OUTPUT_DIR = "kafka-data";
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    // 需要监控的所有topic列表
    private static final List<String> MONITORING_TOPICS = Arrays.asList(
//        // 人员定位相关主题
//        "PeoplePos_People_RealTime",
//        "PeoplePos_People_BasicInfo",
//        "PeoplePos_Station_RealTIme",
//        "PeoplePos_Station_BasicInfo",
//        "PeoplePos_Location_Info",
//        "PeoplePos_People_SOS",
//        "PeoplePos_People_TimeOver",
//
//        // 安全监控相关主题
//        "SafeMoni_MonitorRealTime",
//        "SafeMoni_StationRealTime",
//        "SafeMoni_MonitorBasicInfo",
//        "SafeMoni_StationBasicInfo",
//        "SafeMoni_StatisticData",
//        "SafeMoni_ErrorInfo",
//
//        // 车辆相关主题
//        "DB14_VehiclePosition",
//        "DB14_VehicleSpeedOver",
//        "DianJiChe",
//        "ShiShaiPiDaiCheng",
//
//        // 设备相关主题
//        "Surface_Fan",
//        "960_Fan",
//        "1020_Fan",
//        "ZhiNengGongPeiDian",
//        "XuanKuang_ShuiBengZhan",
//        "JingXia_GeiShui",
//        "JingXia_PaiShui",
//        "ReLiXiTong",
//
//        // 其他重要主题
//        "ChongTianJiaoBanZhan",
//        "ZhuJing",
//        "BianPo7TianPianYi",
//        "FuJing_XiaoTi",
//        "FuJing_DaTi",
//        "kafka_ERP",
//        "kafka_1500_alm"
    );

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("启动Kafka集群监控服务...");

        // 创建数据输出目录
        createOutputDirectory();

        checkKafkaClusterStatus();
        
        // 为每个topic创建独立的消费者
        initializeTopicConsumers();

        // 定期检查Kafka集群状态
        startPeriodicHealthCheck();
    }

    /**
     * 创建数据输出目录
     */
    private void createOutputDirectory() {
        try {
            Path outputPath = Paths.get(OUTPUT_DIR);
            if (!Files.exists(outputPath)) {
                Files.createDirectories(outputPath);
                log.info("创建Kafka数据输出目录: {}", outputPath.toAbsolutePath());
            } else {
                log.info("Kafka数据输出目录已存在: {}", outputPath.toAbsolutePath());
            }
        } catch (IOException e) {
            log.error("创建输出目录失败", e);
        }
    }

    /**
     * 将消息数据写入对应主题的文件
     */
    private void writeMessageToFile(String topic, String message) {
        try {
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMATTER);
            String fileName = topic + ".txt";
            Path filePath = Paths.get(OUTPUT_DIR, fileName);

            // 格式化输出内容：时间戳 + 消息内容
            String formattedMessage = String.format("[%s] %s%n", timestamp, message);

            // 追加写入文件
            try (FileWriter writer = new FileWriter(filePath.toFile(), true)) {
                writer.write(formattedMessage);
                writer.flush();
            }

            log.debug("消息已写入文件 - 主题: {}, 文件: {}", topic, filePath.toAbsolutePath());

        } catch (IOException e) {
            log.error("写入消息到文件失败 - 主题: {}", topic, e);
        }
    }

    /**
     * 检查Kafka集群状态
     */
    public void checkKafkaClusterStatus() {
        try {
            log.info("开始检查Kafka集群状态...");

            AdminClient client = getHealthyAdminClient();
            if (client == null) {
                log.error("无法获取健康的AdminClient，跳过集群状态检查");
                return;
            }

            // 获取集群信息
            DescribeClusterResult clusterResult = client.describeCluster();
            Collection<Node> nodes = clusterResult.nodes().get(10, TimeUnit.SECONDS);
            Node controller = clusterResult.controller().get(10, TimeUnit.SECONDS);
            
            log.info("Kafka集群节点数量: {}", nodes.size());
            log.info("Kafka控制器节点: {}", controller);
            
            for (Node node : nodes) {
                log.info("Kafka节点: ID={}, Host={}, Port={}", 
                    node.id(), node.host(), node.port());
            }
            
            // 获取主题列表
            ListTopicsResult topicsResult = client.listTopics();
            Set<String> topicNames = topicsResult.names().get(10, TimeUnit.SECONDS);
            log.info("Kafka主题数量: {}", topicNames.size());
            log.info("Kafka主题列表: {}", topicNames);
            
            log.info("Kafka集群状态检查完成");
            
        } catch (Exception e) {
            log.error("检查Kafka集群状态时发生错误", e);
        }
    }

    /**
     * 监听特定主题的消息（用于监控目的）
     * 注意：这里可以配置需要监控的特定主题
     */
    // 暂时注释掉Kafka监听器，避免循环依赖问题
    // 如果需要监听特定主题，可以在这里配置
    /*
    @KafkaListener(topics = {"monitoring-topic"},
                   groupId = "lxbi-monitoring-group")
    public void monitorKafkaMessages(@Payload String message,
                                   @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                   @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                   @Header(KafkaHeaders.OFFSET) long offset,
                                   Acknowledgment acknowledgment) {
        try {
            log.info("监控到Kafka消息 - 主题: {}, 分区: {}, 偏移量: {}, 内容: {}",
                topic, partition, offset, message);

            // 处理监控逻辑
            processMonitoringMessage(topic, message);

            // 手动确认消息
            if (acknowledgment != null) {
                acknowledgment.acknowledge();
            }

        } catch (Exception e) {
            log.error("处理Kafka监控消息时发生错误 - 主题: {}", topic, e);
        }
    }
    */

    /**
     * 初始化每个topic的独立消费者
     */
    private void initializeTopicConsumers() {
        log.info("开始为{}个topic创建独立消费者...", MONITORING_TOPICS.size());
        
        for (String topic : MONITORING_TOPICS) {
            createTopicConsumer(topic);
        }
        
        log.info("所有topic消费者创建完成，共{}个", topicConsumers.size());
    }
    
    /**
     * 为指定topic创建独立的消费者
     */
    private void createTopicConsumer(String topic) {
        if (topicConsumers.containsKey(topic)) {
            log.warn("topic {} 的消费者已存在，跳过创建", topic);
            return;
        }
        
        try {
            // 创建消费者 - 所有topic使用同一个group-id
            KafkaConsumer<String, String> consumer = (KafkaConsumer<String, String>) consumerFactory.createConsumer(
                groupId, // 使用统一的group-id
                "lxbi-" + topic + "-client-" + UUID.randomUUID().toString() // 唯一的client id
            );
            
            // 订阅指定topic
            consumer.subscribe(Collections.singletonList(topic));
            
            // 存储消费者
            topicConsumers.put(topic, consumer);
            topicRunningStatus.put(topic, true);
            
            // 在独立线程中启动消费者
            consumerScheduler.submit(() -> startTopicConsumer(topic, consumer));
            
            log.info("为topic {} 创建独立消费者成功", topic);
            
        } catch (Exception e) {
            log.error("为topic {} 创建消费者失败", topic, e);
        }
    }
    
    /**
     * 启动指定topic的消费者循环
     */
    private void startTopicConsumer(String topic, KafkaConsumer<String, String> consumer) {
        log.info("启动topic {} 的消费者循环", topic);
        
        while (topicRunningStatus.getOrDefault(topic, false)) {
            try {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(300000)); // 5分钟超时
                
                for (ConsumerRecord<String, String> record : records) {
                    processTopicMessage(topic, record);
                }
                
                // 手动提交offset
                if (!records.isEmpty()) {
                    consumer.commitSync();
                }
                
            } catch (Exception e) {
                log.error("topic {} 消费者处理消息时发生错误", topic, e);
                // 发生错误时稍作等待，避免快速重试
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("topic {} 消费者线程被中断", topic);
                    break;
                }
            }
        }
        
        // 关闭消费者
        try {
            consumer.close();
            log.info("topic {} 的消费者已关闭", topic);
        } catch (Exception e) {
            log.error("关闭topic {} 消费者时发生错误", topic, e);
        }
    }
    
    /**
     * 处理指定topic的消息
     */
    private void processTopicMessage(String topic, ConsumerRecord<String, String> record) {
        try {
            String message = record.value();
            log.info("收到{}消息 - 分区: {}, 偏移量: {}", topic, record.partition(), record.offset());
            log.debug("{}数据: {}", topic, message);
            
            // 将消息写入对应主题的文件
            writeMessageToFile(topic, message);
            
            // 根据topic类型进行特定处理
            if (topic.startsWith("PeoplePos_")) {
                processPeoplePositionMessage(topic, message);
            } else if (topic.startsWith("SafeMoni_")) {
                processSafetyMonitoringMessage(topic, message);
            } else if (topic.startsWith("DB14_") || topic.equals("DianJiChe") || topic.equals("ShiShaiPiDaiCheng")) {
                processVehicleMessage(topic, message);
            } else if (topic.contains("Fan") || topic.contains("Shui") || topic.contains("GongPei") || topic.contains("ReLi")) {
                processEquipmentMessage(topic, message);
            } else {
                processGeneralMessage(topic, message);
            }
            
        } catch (Exception e) {
            log.error("处理topic {} 消息时发生错误 - 偏移量: {}, 错误: {}", topic, record.offset(), e.getMessage());
        }
    }

    /**
     * 处理人员定位消息
     */
    private void processPeoplePositionMessage(String topic, String message) {
        try {
            // 解析JSON消息
            if (isValidJson(message)) {
                com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(message);
                log.info("解析人员定位JSON数据 - 主题: {}", topic);

                // 根据不同主题处理不同的人员定位数据
                switch (topic) {
                    case "PeoplePos_People_RealTime":
                        log.info("处理人员实时位置数据");
                        break;
                    case "PeoplePos_People_SOS":
                        log.warn("收到人员SOS求救信号！");
                        break;
                    case "PeoplePos_People_TimeOver":
                        log.warn("人员超时告警！");
                        break;
                    default:
                        log.info("处理人员定位数据 - 主题: {}", topic);
                        break;
                }
            }
        } catch (Exception e) {
            log.error("处理人员定位消息失败 - 主题: {}", topic, e);
        }
    }

    /**
     * 处理安全监控消息
     */
    private void processSafetyMonitoringMessage(String topic, String message) {
        try {
            // 解析JSON消息
            if (isValidJson(message)) {
                com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(message);
                log.info("解析安全监控JSON数据 - 主题: {}", topic);

                // 根据不同主题处理不同的安全监控数据
                switch (topic) {
                    case "SafeMoni_MonitorRealTime":
                        log.info("处理实时安全监控数据");
                        break;
                    case "SafeMoni_ErrorInfo":
                        log.error("收到安全监控错误信息！");
                        break;
                    default:
                        log.info("处理安全监控数据 - 主题: {}", topic);
                        break;
                }
            }
        } catch (Exception e) {
            log.error("处理安全监控消息失败 - 主题: {}", topic, e);
        }
    }

    /**
     * 处理车辆监控消息
     */
    private void processVehicleMessage(String topic, String message) {
        try {
            // 解析JSON消息
            if (isValidJson(message)) {
                com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(message);
                log.info("解析车辆监控JSON数据 - 主题: {}", topic);

                // 根据不同主题处理不同的车辆数据
                switch (topic) {
                    case "DB14_VehiclePosition":
                        log.info("处理车辆位置数据");
                        break;
                    case "DB14_VehicleSpeedOver":
                        log.warn("车辆超速告警！");
                        break;
                    default:
                        log.info("处理车辆监控数据 - 主题: {}", topic);
                        break;
                }
            }
        } catch (Exception e) {
            log.error("处理车辆监控消息失败 - 主题: {}", topic, e);
        }
    }

    /**
     * 处理设备监控消息
     */
    private void processEquipmentMessage(String topic, String message) {
        try {
            // 解析JSON消息
            if (isValidJson(message)) {
                com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(message);
                log.info("解析设备监控JSON数据 - 主题: {}", topic);

                // 根据不同主题处理不同的设备数据
                if (topic.contains("Fan")) {
                    log.info("处理风机设备数据 - {}", topic);
                } else if (topic.contains("GeiShui") || topic.contains("PaiShui")) {
                    log.info("处理给排水设备数据 - {}", topic);
                } else {
                    log.info("处理设备监控数据 - 主题: {}", topic);
                }
            }
        } catch (Exception e) {
            log.error("处理设备监控消息失败 - 主题: {}", topic, e);
        }
    }

    /**
     * 处理通用监控消息
     */
    private void processGeneralMessage(String topic, String message) {
        try {
            // 解析JSON消息
            if (isValidJson(message)) {
                com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(message);
                log.info("解析通用监控JSON数据 - 主题: {}", topic);

                // 根据不同主题处理不同的通用数据
                if (topic.contains("ERP")) {
                    log.info("处理ERP系统数据");
                } else if (topic.contains("alm")) {
                    log.warn("处理告警数据");
                } else {
                    log.info("处理通用监控数据 - 主题: {}", topic);
                }
            }
        } catch (Exception e) {
            log.error("处理通用监控消息失败 - 主题: {}", topic, e);
        }
    }

    /**
     * 检查字符串是否为有效的JSON格式
     */
    private boolean isValidJson(String str) {
        try {
            com.alibaba.fastjson2.JSON.parseObject(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 处理监控消息
     */
    private void processMonitoringMessage(String topic, String message) {
        // 这里可以添加具体的监控逻辑
        // 例如：统计消息数量、检查消息格式、触发告警等
        log.debug("处理监控消息 - 主题: {}, 消息长度: {}", topic, message.length());
    }

    /**
     * 获取需要监控的主题列表
     */
    public List<String> getMonitoringTopics() {
        return new ArrayList<>(MONITORING_TOPICS);
    }
    
    /**
     * 获取消费者状态信息
     */
    public Map<String, Object> getConsumerStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("totalTopics", MONITORING_TOPICS.size());
        status.put("activeConsumers", topicConsumers.size());
        status.put("runningConsumers", topicRunningStatus.values().stream().mapToLong(v -> v ? 1 : 0).sum());
        status.put("groupId", groupId); // 显示使用的group-id
        status.put("consumerMode", "独立消费者模式（统一Group）"); // 消费者模式说明
        
        Map<String, String> topicStatus = new HashMap<>();
        for (String topic : MONITORING_TOPICS) {
            if (topicConsumers.containsKey(topic)) {
                topicStatus.put(topic, topicRunningStatus.getOrDefault(topic, false) ? "运行中" : "已停止");
            } else {
                topicStatus.put(topic, "未创建");
            }
        }
        status.put("topicStatus", topicStatus);
        
        return status;
    }
    
    /**
     * 停止指定topic的消费者
     */
    public void stopTopicConsumer(String topic) {
        if (topicRunningStatus.containsKey(topic)) {
            topicRunningStatus.put(topic, false);
            log.info("已标记停止topic {} 的消费者", topic);
        } else {
            log.warn("topic {} 的消费者不存在", topic);
        }
    }
    
    /**
     * 重启指定topic的消费者
     */
    public void restartTopicConsumer(String topic) {
        // 先停止
        stopTopicConsumer(topic);
        
        // 等待一段时间确保消费者完全停止
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 移除旧的消费者
        KafkaConsumer<String, String> oldConsumer = topicConsumers.remove(topic);
        if (oldConsumer != null) {
            try {
                oldConsumer.close();
            } catch (Exception e) {
                log.error("关闭旧消费者时发生错误", e);
            }
        }
        
        // 重新创建
        createTopicConsumer(topic);
        log.info("topic {} 的消费者已重启", topic);
    }
    
    /**
     * 停止所有消费者
     */
    public void stopAllConsumers() {
        log.info("开始停止所有topic消费者...");
        
        for (String topic : new HashSet<>(topicRunningStatus.keySet())) {
            stopTopicConsumer(topic);
        }
        
        // 等待所有消费者停止
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 关闭所有消费者
        for (Map.Entry<String, KafkaConsumer<String, String>> entry : topicConsumers.entrySet()) {
            try {
                entry.getValue().close();
            } catch (Exception e) {
                log.error("关闭topic {} 消费者时发生错误", entry.getKey(), e);
            }
        }
        
        topicConsumers.clear();
        topicRunningStatus.clear();
        
        log.info("所有topic消费者已停止");
    }

    /**
     * 获取Kafka集群健康状态
     */
    public boolean isKafkaClusterHealthy() {
        try {
            AdminClient client = getHealthyAdminClient();
            if (client == null) {
                return false;
            }

            DescribeClusterResult clusterResult = client.describeCluster();
            Collection<Node> nodes = clusterResult.nodes().get(5, TimeUnit.SECONDS);
            return !nodes.isEmpty();
        } catch (Exception e) {
            log.error("检查Kafka集群健康状态时发生错误", e);
            // 如果是AdminClient关闭错误，标记需要重连
            if (e.getMessage() != null && e.getMessage().contains("AdminClient is closing")) {
                markAdminClientClosed();
            }
            return false;
        }
    }

    /**
     * 获取健康的AdminClient，如果当前client已关闭则尝试重连
     */
    private AdminClient getHealthyAdminClient() {
        synchronized (adminClientLock) {
            if (adminClientClosed || isAdminClientClosed()) {
                log.warn("AdminClient已关闭，尝试重新创建连接");
                try {
                    recreateAdminClient();
                } catch (Exception e) {
                    log.error("重新创建AdminClient失败", e);
                    return null;
                }
            }
            return kafkaAdminClient;
        }
    }

    /**
     * 检查AdminClient是否已关闭
     */
    private boolean isAdminClientClosed() {
        try {
            // 尝试一个简单的操作来检查连接状态
            kafkaAdminClient.describeCluster().clusterId().get(1, TimeUnit.SECONDS);
            return false;
        } catch (Exception e) {
            if (e.getMessage() != null && e.getMessage().contains("AdminClient is closing")) {
                return true;
            }
            return false;
        }
    }

    /**
     * 标记AdminClient为已关闭状态
     */
    private void markAdminClientClosed() {
        synchronized (adminClientLock) {
            adminClientClosed = true;
        }
    }

    /**
     * 重新创建AdminClient
     */
    private void recreateAdminClient() {
        try {
            // 关闭旧的AdminClient
            if (kafkaAdminClient != null) {
                try {
                    kafkaAdminClient.close();
                } catch (Exception e) {
                    log.warn("关闭旧AdminClient时发生错误", e);
                }
            }

            // 创建新的AdminClient
            Properties props = new Properties();
            props.put("bootstrap.servers", bootstrapServers);
            props.put("request.timeout.ms", 30000);
            props.put("connections.max.idle.ms", 60000);
            props.put("reconnect.backoff.ms", 1000);
            props.put("reconnect.backoff.max.ms", 10000);
            props.put("retries", 3);

            kafkaAdminClient = AdminClient.create(props);
            adminClientClosed = false;

            log.info("AdminClient重新创建成功");
        } catch (Exception e) {
            log.error("重新创建AdminClient失败", e);
            throw e;
        }
    }

    /**
     * 获取Kafka集群信息
     */
    public String getKafkaClusterInfo() {
        try {
            AdminClient client = getHealthyAdminClient();
            if (client == null) {
                return "AdminClient连接失败";
            }

            DescribeClusterResult clusterResult = client.describeCluster();
            Collection<Node> nodes = clusterResult.nodes().get(5, TimeUnit.SECONDS);
            
            StringBuilder info = new StringBuilder();
            info.append("集群节点数: ").append(nodes.size()).append("\n");
            for (Node node : nodes) {
                info.append("节点: ").append(node.host()).append(":").append(node.port()).append("\n");
            }
            
            return info.toString();
        } catch (Exception e) {
            log.error("获取Kafka集群信息时发生错误", e);
            return "获取集群信息失败: " + e.getMessage();
        }
    }

    /**
     * 启动定期健康检查
     */
    private void startPeriodicHealthCheck() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                boolean healthy = isKafkaClusterHealthy();
                if (healthy != isKafkaHealthy) {
                    isKafkaHealthy = healthy;
                    log.info("Kafka集群健康状态变更: {}", healthy ? "健康" : "不健康");
                }
                lastHealthCheckTime = System.currentTimeMillis();
            } catch (Exception e) {
                log.error("定期健康检查失败", e);
                isKafkaHealthy = false;
            }
        }, 30, 60, TimeUnit.SECONDS); // 30秒后开始，每60秒检查一次

        log.info("Kafka定期健康检查已启动，每60秒检查一次");
    }

    /**
     * 获取Kafka集群详细状态
     */
    public KafkaClusterStatus getDetailedClusterStatus() {
        KafkaClusterStatus status = new KafkaClusterStatus();

        try {
            AdminClient client = getHealthyAdminClient();
            if (client == null) {
                status.setHealthy(false);
                status.setErrorMessage("AdminClient连接失败");
                return status;
            }

            // 获取集群信息
            DescribeClusterResult clusterResult = client.describeCluster();
            Collection<Node> nodes = clusterResult.nodes().get(10, TimeUnit.SECONDS);
            Node controller = clusterResult.controller().get(10, TimeUnit.SECONDS);

            status.setHealthy(true);
            status.setNodeCount(nodes.size());
            status.setController(controller.host() + ":" + controller.port());
            status.setLastCheckTime(lastHealthCheckTime);

            // 获取主题信息
            ListTopicsResult topicsResult = client.listTopics();
            Set<String> topicNames = topicsResult.names().get(10, TimeUnit.SECONDS);
            status.setTopicCount(topicNames.size());
            status.setTopics(topicNames);

            // 设置节点信息
            StringBuilder nodesInfo = new StringBuilder();
            for (Node node : nodes) {
                if (nodesInfo.length() > 0) nodesInfo.append(", ");
                nodesInfo.append(node.host()).append(":").append(node.port());
            }
            status.setNodes(nodesInfo.toString());

        } catch (Exception e) {
            log.error("获取Kafka集群详细状态失败", e);
            status.setHealthy(false);
            status.setErrorMessage(e.getMessage());
        }

        return status;
    }

    /**
     * Kafka集群状态信息类
     */
    public static class KafkaClusterStatus {
        private boolean healthy;
        private int nodeCount;
        private String controller;
        private int topicCount;
        private Set<String> topics;
        private String nodes;
        private long lastCheckTime;
        private String errorMessage;

        // Getters and Setters
        public boolean isHealthy() { return healthy; }
        public void setHealthy(boolean healthy) { this.healthy = healthy; }

        public int getNodeCount() { return nodeCount; }
        public void setNodeCount(int nodeCount) { this.nodeCount = nodeCount; }

        public String getController() { return controller; }
        public void setController(String controller) { this.controller = controller; }

        public int getTopicCount() { return topicCount; }
        public void setTopicCount(int topicCount) { this.topicCount = topicCount; }

        public Set<String> getTopics() { return topics; }
        public void setTopics(Set<String> topics) { this.topics = topics; }

        public String getNodes() { return nodes; }
        public void setNodes(String nodes) { this.nodes = nodes; }

        public long getLastCheckTime() { return lastCheckTime; }
        public void setLastCheckTime(long lastCheckTime) { this.lastCheckTime = lastCheckTime; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }

    /**
     * 应用关闭时清理资源
     */
    @EventListener(ContextClosedEvent.class)
    public void shutdown() {
        log.info("正在关闭Kafka监控服务...");

        // 停止定期健康检查
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭AdminClient
        synchronized (adminClientLock) {
            if (kafkaAdminClient != null) {
                try {
                    kafkaAdminClient.close();
                    log.info("AdminClient已关闭");
                } catch (Exception e) {
                    log.warn("关闭AdminClient时发生错误", e);
                }
            }
            adminClientClosed = true;
        }

        // 关闭所有消费者
        for (KafkaConsumer<String, String> consumer : topicConsumers.values()) {
            try {
                consumer.close();
            } catch (Exception e) {
                log.warn("关闭消费者时发生错误", e);
            }
        }

        log.info("Kafka监控服务已关闭");
    }
}
