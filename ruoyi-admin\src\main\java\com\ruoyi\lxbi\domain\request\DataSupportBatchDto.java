package com.ruoyi.lxbi.domain.request;

import com.ruoyi.lxbi.domain.DataSupport;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支护数据批量操作DTO
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataSupportBatchDto extends DataSupport {
    
    /** 是否为新增数据 */
    private Boolean isNew;
    
    /** 作业时段名称 */
    private String workingPeriodName;
    
    /** 项目部门名称 */
    private String projectDepartmentName;
    
    /** 采场名称 */
    private String stopeName;
    
    /** 工作面名称 */
    private String workingFaceName;

    /** 支护类型名称 */
    private String supportTypeName;
}
