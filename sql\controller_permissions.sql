-- ===================================================================
-- 四个统计控制器的权限菜单SQL (基于URL路径的权限点)
-- 生成时间: 2025-07-11
-- 控制器权限已更新为与URL路径一致
-- ===================================================================

-- ===================================================================
-- 1. 溜井放矿操作统计控制器权限 (DataOrepassOperationStatsController)
-- URL路径: /data/stats/orepass
-- 父菜单ID: 2138 (溜井相关)
-- ===================================================================

-- 溜井放矿量整体统计权限 (/01a)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2200, '溜井放矿量整体统计', 2138, 6, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:orepass:01a', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '溜井放矿量整体统计权限');

-- 溜井放矿量按项目部统计权限 (/01b)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2201, '溜井放矿量按项目部统计', 2138, 7, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:orepass:01b', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '溜井放矿量按项目部统计权限');

-- 溜井放矿量按班次统计权限 (/01c)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2202, '溜井放矿量按班次统计', 2138, 8, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:orepass:01c', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '溜井放矿量按班次统计权限');

-- 溜井放矿量按溜井统计权限 (/01d)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2203, '溜井放矿量按溜井统计', 2138, 9, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:orepass:01d', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '溜井放矿量按溜井统计权限');

-- ===================================================================
-- 2. 破碎操作统计控制器权限 (DateCrushingOperationStatsController)
-- URL路径: /data/stats/crushing
-- 父菜单ID: 2105 (破碎相关)
-- ===================================================================

-- 破碎量整体统计权限 (/01a)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2204, '破碎量整体统计', 2105, 12, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:crushing:01a', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '破碎量整体统计权限');

-- 破碎量按班次统计权限 (/01b)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2205, '破碎量按班次统计', 2105, 13, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:crushing:01b', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '破碎量按班次统计权限');

-- 破碎运行时间整体统计权限 (/02a)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2206, '破碎运行时间整体统计', 2105, 14, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:crushing:02a', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '破碎运行时间整体统计权限');

-- 破碎运行时间按班次统计权限 (/02b)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2207, '破碎运行时间按班次统计', 2105, 15, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:crushing:02b', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '破碎运行时间按班次统计权限');

-- 破碎故障统计权限 (/03a)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2208, '破碎故障统计', 2105, 16, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:crushing:03a', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '破碎故障统计权限');

-- ===================================================================
-- 3. 出矿统计控制器权限 (DataMuckingOutStatsController)
-- URL路径: /data/stats/out
-- 父菜单ID: 2300 (假设出矿相关父菜单)
-- ===================================================================

-- 出矿总量统计权限 (/01a)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2209, '出矿总量统计', 2300, 1, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:out:01a', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '出矿总量统计权限');

-- 出矿量按采场统计权限 (/01b)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2210, '出矿量按采场统计', 2300, 2, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:out:01b', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '出矿量按采场统计权限');

-- 出矿量按项目部统计权限 (/01c)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2211, '出矿量按项目部统计', 2300, 3, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:out:01c', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '出矿量按项目部统计权限');

-- 出矿量按班次统计权限 (/01e)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2212, '出矿量按班次统计', 2300, 4, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:out:01e', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '出矿量按班次统计权限');

-- ===================================================================
-- 4. 矿井提升统计控制器权限 (DataMineHoistingStatsController)
-- URL路径: /data/stats/hoisting
-- 父菜单ID: 2000 (提升相关)
-- ===================================================================

-- 提升量整体统计权限 (/01a)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2213, '提升量整体统计', 2000, 15, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:hoisting:01a', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '提升量整体统计权限');

-- 提升量按班次统计权限 (/01b)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2214, '提升量按班次统计', 2000, 16, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:hoisting:01b', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '提升量按班次统计权限');

-- 提升运行时间整体统计权限 (/02a)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2215, '提升运行时间整体统计', 2000, 17, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:hoisting:02a', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '提升运行时间整体统计权限');

-- 提升运行时间按班次统计权限 (/02b)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2216, '提升运行时间按班次统计', 2000, 18, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:hoisting:02b', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '提升运行时间按班次统计权限');

-- 提升斗数统计权限 (/04a)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2217, '提升斗数统计', 2000, 19, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:hoisting:04a', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '提升斗数统计权限');

-- 提升班次操作时间统计权限 (/04b)
INSERT INTO public.sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2218, '提升班次操作时间统计', 2000, 20, '#', '', null, '', 1, 0, 'F', '0', 0, 'data:stats:hoisting:04b', '#', 'admin', '2025-07-11 00:00:00.000000', '', null, '提升班次操作时间统计权限');


