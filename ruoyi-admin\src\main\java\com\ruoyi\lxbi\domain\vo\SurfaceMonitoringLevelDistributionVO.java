package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 地表监测偏移等级分布VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurfaceMonitoringLevelDistributionVO {
    
    /**
     * 偏移方向 (X/Y/H)
     */
    private String offsetDirection;
    
    /**
     * 偏移等级
     */
    private Integer offsetLevel;
    
    /**
     * 偏移数量
     */
    private Long offsetCount;
    
    /**
     * 占比百分比
     */
    private BigDecimal percentage;
    
    /**
     * 偏移方向代码
     */
    private String directionCode;
}
