package com.ruoyi.lxbi.controller.stat.sec;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.admin.service.IVehicleSafetyStatService;
import com.ruoyi.lxbi.domain.vo.*;
import com.ruoyi.lxbi.utils.DateRangeCalculator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 车辆安全统计Controller
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@RestController
@RequestMapping("/sec/stat/vehicle-safety")
public class VehicleSafetyStatController {

    @Autowired
    private IVehicleSafetyStatService vehicleSafetyStatService;

    /**
     * 获取车辆安全概览统计
     */
    @Anonymous
    @GetMapping("/overview")
    public R<VehicleSafetyOverviewVO> getOverviewStatistics(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        VehicleSafetyOverviewVO overview = vehicleSafetyStatService.getOverviewStatistics(viewType, startDate, endDate);
        return R.ok(overview);
    }

    /**
     * 获取车辆报警部门分布统计
     */
    @Anonymous
    @GetMapping("/alarm-department-distribution")
    public R<List<VehicleAlarmDepartmentDistributionVO>> getAlarmDepartmentDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<VehicleAlarmDepartmentDistributionVO> distribution = vehicleSafetyStatService.getAlarmDepartmentDistribution(viewType, startDate, endDate);
        return R.ok(distribution);
    }

    /**
     * 获取车辆告警类型分布统计
     */
    @Anonymous
    @GetMapping("/alarm-type-distribution")
    public R<List<VehicleAlarmTypeDistributionVO>> getAlarmTypeDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<VehicleAlarmTypeDistributionVO> distribution = vehicleSafetyStatService.getAlarmTypeDistribution(viewType, startDate, endDate);
        return R.ok(distribution);
    }

    /**
     * 获取车辆告警记录列表
     */
    @Anonymous
    @GetMapping("/alarm-records")
    public R<List<VehicleAlarmRecordVO>> getAlarmRecords(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证视图类型参数
        if (!DateRangeCalculator.isValidViewType(viewType)) {
            return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
        }

        // 设置默认日期范围或根据viewType计算日期范围
        if (startDate == null || endDate == null) {
            Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
            startDate = calculatedDates.get("startDate");
            endDate = calculatedDates.get("endDate");
        }

        List<VehicleAlarmRecordVO> records = vehicleSafetyStatService.getAlarmRecords(viewType, startDate, endDate);
        return R.ok(records);
    }

    /**
     * 获取车辆类型分布统计
     */
    @Anonymous
    @GetMapping("/vehicle-type-distribution")
    public R<List<VehicleTypeDistributionVO>> getVehicleTypeDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        List<VehicleTypeDistributionVO> distribution = vehicleSafetyStatService.getVehicleTypeDistribution(viewType, startDate, endDate);
        return R.ok(distribution);
    }

    /**
     * 获取车辆状态分布统计
     */
    @Anonymous
    @GetMapping("/vehicle-status-distribution")
    public R<List<VehicleStatusDistributionVO>> getVehicleStatusDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        List<VehicleStatusDistributionVO> distribution = vehicleSafetyStatService.getVehicleStatusDistribution(viewType, startDate, endDate);
        return R.ok(distribution);
    }
}
