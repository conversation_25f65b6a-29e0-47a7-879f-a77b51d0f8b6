package com.ruoyi.lxbi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 井下作业人员实时数据对象 kafka_people_pos_real_time
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
public class KafkaPeoplePosRealTime extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Integer isDeleted;

    /** 文件编码 */
    @Excel(name = "文件编码")
    private String fileEncoding;

    /** 煤矿编码 */
    @Excel(name = "煤矿编码")
    private String mineCode;

    /** 矿井名称 */
    @Excel(name = "矿井名称")
    private String mineName;

    /** 数据上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "数据上传时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dataUploadTime;

    /** 人员卡编码 */
    @Excel(name = "人员卡编码")
    private String personnelCardCode;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personnelName;

    /** 出入井标志位 */
    @Excel(name = "出入井标志位")
    private String inOutWellFlag;

    /** 入井时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入井时刻", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date inWellTime;

    /** 出井时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "出井时刻", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date outWellTime;

    /** 区域编码 */
    @Excel(name = "区域编码")
    private String areaCode;

    /** 进入当前区域时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "进入当前区域时刻", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date enterCurrentAreaTime;

    /** 基站编码 */
    @Excel(name = "基站编码")
    private String baseStationCode;

    /** 进入当前所处基站时刻 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "进入当前所处基站时刻", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date enterCurrentBaseStationTime;

    /** 劳动组织方式 */
    @Excel(name = "劳动组织方式")
    private String laborOrganizationMode;

    /** 距离基站距离 */
    @Excel(name = "距离基站距离")
    private Double distanceFromBaseStation;

    /** 人员工作状态 */
    @Excel(name = "人员工作状态")
    private String personnelWorkStatus;

    /** 是否矿领导 */
    @Excel(name = "是否矿领导")
    private String isMineLeader;

    /** 是否特种人员 */
    @Excel(name = "是否特种人员")
    private String isSpecialPersonnel;

    /** 行进轨迹基站时间集合 */
    @Excel(name = "行进轨迹基站时间集合")
    private String trajectoryBaseStationTimeSet;

    /** 初始日期 (第一次数据上传时间) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initDate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    public void setFileEncoding(String fileEncoding) 
    {
        this.fileEncoding = fileEncoding;
    }

    public String getFileEncoding() 
    {
        return fileEncoding;
    }

    public void setMineCode(String mineCode) 
    {
        this.mineCode = mineCode;
    }

    public String getMineCode() 
    {
        return mineCode;
    }

    public void setMineName(String mineName) 
    {
        this.mineName = mineName;
    }

    public String getMineName() 
    {
        return mineName;
    }

    public void setDataUploadTime(Date dataUploadTime) 
    {
        this.dataUploadTime = dataUploadTime;
    }

    public Date getDataUploadTime() 
    {
        return dataUploadTime;
    }

    public void setPersonnelCardCode(String personnelCardCode) 
    {
        this.personnelCardCode = personnelCardCode;
    }

    public String getPersonnelCardCode() 
    {
        return personnelCardCode;
    }

    public void setPersonnelName(String personnelName) 
    {
        this.personnelName = personnelName;
    }

    public String getPersonnelName() 
    {
        return personnelName;
    }

    public void setInOutWellFlag(String inOutWellFlag) 
    {
        this.inOutWellFlag = inOutWellFlag;
    }

    public String getInOutWellFlag() 
    {
        return inOutWellFlag;
    }

    public void setInWellTime(Date inWellTime) 
    {
        this.inWellTime = inWellTime;
    }

    public Date getInWellTime() 
    {
        return inWellTime;
    }

    public void setOutWellTime(Date outWellTime) 
    {
        this.outWellTime = outWellTime;
    }

    public Date getOutWellTime() 
    {
        return outWellTime;
    }

    public void setAreaCode(String areaCode) 
    {
        this.areaCode = areaCode;
    }

    public String getAreaCode() 
    {
        return areaCode;
    }

    public void setEnterCurrentAreaTime(Date enterCurrentAreaTime) 
    {
        this.enterCurrentAreaTime = enterCurrentAreaTime;
    }

    public Date getEnterCurrentAreaTime() 
    {
        return enterCurrentAreaTime;
    }

    public void setBaseStationCode(String baseStationCode) 
    {
        this.baseStationCode = baseStationCode;
    }

    public String getBaseStationCode() 
    {
        return baseStationCode;
    }

    public void setEnterCurrentBaseStationTime(Date enterCurrentBaseStationTime) 
    {
        this.enterCurrentBaseStationTime = enterCurrentBaseStationTime;
    }

    public Date getEnterCurrentBaseStationTime() 
    {
        return enterCurrentBaseStationTime;
    }

    public void setLaborOrganizationMode(String laborOrganizationMode) 
    {
        this.laborOrganizationMode = laborOrganizationMode;
    }

    public String getLaborOrganizationMode() 
    {
        return laborOrganizationMode;
    }

    public void setDistanceFromBaseStation(Double distanceFromBaseStation) 
    {
        this.distanceFromBaseStation = distanceFromBaseStation;
    }

    public Double getDistanceFromBaseStation() 
    {
        return distanceFromBaseStation;
    }

    public void setPersonnelWorkStatus(String personnelWorkStatus) 
    {
        this.personnelWorkStatus = personnelWorkStatus;
    }

    public String getPersonnelWorkStatus() 
    {
        return personnelWorkStatus;
    }

    public void setIsMineLeader(String isMineLeader) 
    {
        this.isMineLeader = isMineLeader;
    }

    public String getIsMineLeader() 
    {
        return isMineLeader;
    }

    public void setIsSpecialPersonnel(String isSpecialPersonnel) 
    {
        this.isSpecialPersonnel = isSpecialPersonnel;
    }

    public String getIsSpecialPersonnel() 
    {
        return isSpecialPersonnel;
    }

    public void setTrajectoryBaseStationTimeSet(String trajectoryBaseStationTimeSet) 
    {
        this.trajectoryBaseStationTimeSet = trajectoryBaseStationTimeSet;
    }

    public String getTrajectoryBaseStationTimeSet() 
    {
        return trajectoryBaseStationTimeSet;
    }

    public void setInitDate(Date initDate) 
    {
        this.initDate = initDate;
    }

    public Date getInitDate() 
    {
        return initDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("isDeleted", getIsDeleted())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("fileEncoding", getFileEncoding())
            .append("mineCode", getMineCode())
            .append("mineName", getMineName())
            .append("dataUploadTime", getDataUploadTime())
            .append("personnelCardCode", getPersonnelCardCode())
            .append("personnelName", getPersonnelName())
            .append("inOutWellFlag", getInOutWellFlag())
            .append("inWellTime", getInWellTime())
            .append("outWellTime", getOutWellTime())
            .append("areaCode", getAreaCode())
            .append("enterCurrentAreaTime", getEnterCurrentAreaTime())
            .append("baseStationCode", getBaseStationCode())
            .append("enterCurrentBaseStationTime", getEnterCurrentBaseStationTime())
            .append("laborOrganizationMode", getLaborOrganizationMode())
            .append("distanceFromBaseStation", getDistanceFromBaseStation())
            .append("personnelWorkStatus", getPersonnelWorkStatus())
            .append("isMineLeader", getIsMineLeader())
            .append("isSpecialPersonnel", getIsSpecialPersonnel())
            .append("trajectoryBaseStationTimeSet", getTrajectoryBaseStationTimeSet())
            .append("initDate", getInitDate())
            .toString();
    }
}
