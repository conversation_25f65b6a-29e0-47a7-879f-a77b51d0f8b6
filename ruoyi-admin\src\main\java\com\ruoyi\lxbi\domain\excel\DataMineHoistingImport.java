package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import com.ruoyi.common.utils.excel.BaseDateConverter;
import lombok.Data;

import java.time.LocalTime;
import java.util.Date;

/**
 * 矿井提升数据导入对象
 */
@Data
@ExcelImportTemplate(
        key = "data_mine_hoisting",
        name = "矿井提升数据导入",
        description = "用于导入矿井提升数据",
        sheetName = "提升数据"
)
public class DataMineHoistingImport {

    /**
     * 作业日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "作业日期", index = 0)
    @ExcelRequired(message = "作业日期不能为空")
    @ExcelSelected(prompt = "请输入作业日期，格式：yyyy-MM-dd，例如：2025-01-15")
    private Date operationDate;

    /**
     * 作业时段ID（存储值）
     */
    @ExcelSelected(
            optionKey = "workingPeriod",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long workingPeriodId;

    /**
     * 作业时段名称（显示值）
     */
    @ExcelProperty(value = "作业时段", index = 1)
    @ExcelRequired(message = "作业时段不能为空")
    @ExcelSelected(
            optionKey = "workingPeriod",
            prompt = "请选择作业时段",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String workingPeriodName;

    /**
     * 运行时间（分钟）
     */
    @ExcelProperty(value = "运行时间(分钟)", index = 2)
    @ExcelRequired(message = "运行时间不能为空")
    @ExcelSelected(prompt = "请输入运行时间，单位：分钟")
    private Long operationTime;

    /**
     * 故障时长（分钟）
     */
    @ExcelProperty(value = "故障时长(分钟)", index = 3)
    @ExcelSelected(prompt = "请输入故障时长，单位：分钟，无故障填0")
    private Long faultTime;

    /**
     * 提升斗数
     */
    @ExcelProperty(value = "提升斗数", index = 4)
    @ExcelSelected(prompt = "请输入提升斗数")
    private Long buckets;

    /**
     * 提升量（吨）
     */
    @ExcelProperty(value = "提升量(吨)", index = 5)
    @ExcelSelected(prompt = "请输入提升量，单位：吨")
    private Double weight;

    /**
     * 故障原因
     */
    @ExcelProperty(value = "故障原因", index = 6)
    @ExcelSelected(prompt = "请输入故障原因，无故障可不填")
    private String faultReason;

    /**
     * 故障开始时间
     */
    @ExcelProperty(value = "故障开始时间", index = 7, converter = BaseDateConverter.LocalTimeConverter.class)
    @ExcelSelected(prompt = "格式：HH:mm:ss，无故障可不填")
    private LocalTime faultStartTime;

    /**
     * 故障结束时间
     */
    @ExcelProperty(value = "故障结束时间", index = 8, converter = BaseDateConverter.LocalTimeConverter.class)
    @ExcelSelected(prompt = "格式：HH:mm:ss，无故障可不填")
    private LocalTime faultEndTime;
}
