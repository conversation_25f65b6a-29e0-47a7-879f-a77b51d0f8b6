package com.ruoyi.lxbi.service;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.domain.DataDrilling;
import com.ruoyi.lxbi.domain.response.DataDrillingVo;
import com.ruoyi.lxbi.domain.request.DataDrillingBatchDto;

/**
 * 钻孔施工数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface IDataDrillingService 
{
    /**
     * 查询钻孔施工数据
     * 
     * @param id 钻孔施工数据主键
     * @return 钻孔施工数据
     */
    public DataDrilling selectDataDrillingById(Long id);

    /**
     * 查询钻孔施工数据列表
     *
     * @param dataDrilling 钻孔施工数据
     * @return 钻孔施工数据集合
     */
    public List<DataDrillingVo> selectDataDrillingList(DataDrilling dataDrilling);

    /**
     * 新增钻孔施工数据
     * 
     * @param dataDrilling 钻孔施工数据
     * @return 结果
     */
    public int insertDataDrilling(DataDrilling dataDrilling);

    /**
     * 修改钻孔施工数据
     * 
     * @param dataDrilling 钻孔施工数据
     * @return 结果
     */
    public int updateDataDrilling(DataDrilling dataDrilling);

    /**
     * 批量删除钻孔施工数据
     * 
     * @param ids 需要删除的钻孔施工数据主键集合
     * @return 结果
     */
    public int deleteDataDrillingByIds(Long[] ids);

    /**
     * 删除钻孔施工数据信息
     *
     * @param id 钻孔施工数据主键
     * @return 结果
     */
    public int deleteDataDrillingById(Long id);

    /**
     * 根据作业日期查询钻孔施工数据列表
     *
     * @param operationDate 作业日期
     * @return 钻孔施工数据集合
     */
    public List<DataDrillingVo> selectDataDrillingByOperationDate(Date operationDate);

    /**
     * 批量保存钻孔施工数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    public int batchSaveDataDrilling(List<DataDrillingBatchDto> batchDataList);
}
