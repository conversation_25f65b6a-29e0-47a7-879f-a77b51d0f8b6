package com.ruoyi.lxbi.domain.request;

import com.ruoyi.lxbi.domain.DataOrepassOperation;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 溜井运行数据批量操作DTO
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataOrepassOperationBatchDto extends DataOrepassOperation {
    
    /** 是否为新增数据 */
    private Boolean isNew;
    
    /** 作业时段名称 */
    private String workingPeriodName;
    
    /** 项目部门名称 */
    private String projectDepartmentName;
    
    /** 溜井名称 */
    private String orePassName;
}
