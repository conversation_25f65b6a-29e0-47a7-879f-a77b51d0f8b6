package com.ruoyi.lxbi.admin.service.impl;

import com.ruoyi.lxbi.admin.service.IEnvironmentalSafetyStatService;
import com.ruoyi.lxbi.domain.vo.*;
import com.ruoyi.lxbi.service.IKafkaSafeMonitorErrorInfoService;
import com.ruoyi.lxbi.service.IKafkaMonitoringPointBasicInfoService;
import com.ruoyi.lxbi.domain.KafkaSafeMonitorErrorInfo;
import com.ruoyi.lxbi.domain.KafkaMonitoringPointBasicInfo;
import com.ruoyi.lxbi.mapper.KafkaSafeMonitorErrorInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 环境安全统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class EnvironmentalSafetyStatServiceImpl implements IEnvironmentalSafetyStatService {

    @Autowired
    private IKafkaSafeMonitorErrorInfoService kafkaSafeMonitorErrorInfoService;

    @Autowired
    private IKafkaMonitoringPointBasicInfoService kafkaMonitoringPointBasicInfoService;

    @Autowired
    private KafkaSafeMonitorErrorInfoMapper kafkaSafeMonitorErrorInfoMapper;

    /**
     * 获取环境安全概览统计
     */
    @Override
    public EnvironmentalSafetyOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate) {
        try {
            log.info("获取环境安全概览统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // TODO: 实际实现时需要从数据库查询真实数据
            // 这里先返回模拟数据
            EnvironmentalSafetyOverviewVO overview = new EnvironmentalSafetyOverviewVO();
            overview.setCarbonMonoxideValue(10L);
            overview.setCarbonDioxideValue(10L);
            overview.setNitrogenMonoxideValue(10L);
            overview.setOxygenValue(10L);
            overview.setTemperatureValue(10L);
            overview.setPressureValue(10L);
            overview.setStartDate(startDate);
            overview.setEndDate(endDate);
            overview.setPeriod(viewType);

            return overview;
        } catch (Exception e) {
            log.error("获取环境安全概览统计失败", e);
            throw new RuntimeException("获取环境安全概览统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取环境安全报警位置分布统计 (雷达图数据)
     */
    @Override
    public List<EnvironmentalSafetyLocationDistributionVO> getLocationDistribution(String viewType, String startDate, String endDate) {
        try {
            log.info("获取环境安全报警位置分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 使用统计SQL直接从数据库获取位置分布统计
            List<Map<String, Object>> locationStats = kafkaSafeMonitorErrorInfoMapper.selectLocationDistributionStats(startDate, endDate);

            // 构建结果列表
            List<EnvironmentalSafetyLocationDistributionVO> distributionList = new ArrayList<>();
            int index = 0;

            for (Map<String, Object> stat : locationStats) {
                String locationName = (String) stat.get("location_name");
                Long alarmCount = ((Number) stat.get("alarm_count")).longValue();

                // 从位置名称中提取深度信息
                String depthLevel = extractDepthFromLocation(locationName);

                // 生成雷达图坐标（简单的圆形分布）
                double angle = 2 * Math.PI * index / locationStats.size();
                double radius = 0.8; // 固定半径
                double xCoordinate = radius * Math.cos(angle);
                double yCoordinate = radius * Math.sin(angle);

                distributionList.add(new EnvironmentalSafetyLocationDistributionVO(
                    locationName,
                    depthLevel,
                    alarmCount,
                    xCoordinate,
                    yCoordinate,
                    "LOC_" + String.format("%03d", index + 1)
                ));

                index++;
            }

            log.info("获取环境安全报警位置分布统计完成，共{}个位置", distributionList.size());
            return distributionList;
        } catch (Exception e) {
            log.error("获取环境安全报警位置分布统计失败", e);
            throw new RuntimeException("获取环境安全报警位置分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取环境安全报警数量趋势统计
     */
    @Override
    public List<EnvironmentalSafetyDataTrendVO> getDataTrend(String viewType, String startDate, String endDate) {
        try {
            log.info("获取环境安全报警数量趋势统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            if ("hourly".equals(viewType)) {
                // 按小时统计
                return getHourlyDataTrend(startDate, endDate);
            } else {
                // 按日期统计
                return getDailyDataTrend(startDate, endDate, viewType);
            }
        } catch (Exception e) {
            log.error("获取环境安全报警数量趋势统计失败", e);
            throw new RuntimeException("获取环境安全报警数量趋势统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取按小时统计的趋势数据
     */
    private List<EnvironmentalSafetyDataTrendVO> getHourlyDataTrend(String startDate, String endDate) {
        // 使用统计SQL获取按小时的数据
        List<Map<String, Object>> hourlyStats = kafkaSafeMonitorErrorInfoMapper.selectAlarmTrendByHour(startDate, endDate);

        List<EnvironmentalSafetyDataTrendVO> trendList = new ArrayList<>();

        // 生成24小时的时间序列（0-23点）
        for (int hour = 0; hour < 24; hour++) {
            // 查找该小时的统计数据
            int finalHour = hour;
            Map<String, Object> hourData = hourlyStats.stream()
                .filter(stat -> {
                    Number hourNum = (Number) stat.get("trend_hour");
                    return hourNum != null && hourNum.intValue() == finalHour;
                })
                .findFirst()
                .orElse(null);

            long alarmCount = 0;
            long endEventCount = 0;

            if (hourData != null) {
                alarmCount = ((Number) hourData.get("alarm_count")).longValue();
                endEventCount = ((Number) hourData.get("end_event_count")).longValue();
            }

            String timeLabel = String.format("%02d:00", hour);

            trendList.add(new EnvironmentalSafetyDataTrendVO(
                timeLabel,
                endEventCount,
                alarmCount
            ));
        }

        return trendList;
    }

    /**
     * 获取按日期统计的趋势数据
     */
    private List<EnvironmentalSafetyDataTrendVO> getDailyDataTrend(String startDate, String endDate, String viewType) {
        // 使用统计SQL获取按日期的数据
        List<Map<String, Object>> dailyStats = kafkaSafeMonitorErrorInfoMapper.selectAlarmTrendByDate(startDate, endDate);

        // 生成日期序列
        List<LocalDate> dateRange = generateDateRange(startDate, endDate, viewType);
        List<EnvironmentalSafetyDataTrendVO> trendList = new ArrayList<>();

        for (int i = 0; i < dateRange.size(); i++) {
            LocalDate currentDate = dateRange.get(i);

            // 查找该日期的统计数据
            Map<String, Object> dayData = dailyStats.stream()
                .filter(stat -> {
                    java.sql.Date trendDate = (java.sql.Date) stat.get("trend_date");
                    return trendDate != null && trendDate.toLocalDate().equals(currentDate);
                })
                .findFirst()
                .orElse(null);

            long alarmCount = 0;
            long endEventCount = 0;

            if (dayData != null) {
                alarmCount = ((Number) dayData.get("alarm_count")).longValue();
                endEventCount = ((Number) dayData.get("end_event_count")).longValue();
            }

            // 生成时间标签
            String timeLabel = generateTimeLabel(currentDate, viewType, i + 1);

            trendList.add(new EnvironmentalSafetyDataTrendVO(
                timeLabel,
                endEventCount,
                alarmCount
            ));
        }

        return trendList;
    }

    /**
     * 根据时间范围过滤异常信息数据
     */
    private List<KafkaSafeMonitorErrorInfo> filterErrorsByDateRange(List<KafkaSafeMonitorErrorInfo> errors, String startDate, String endDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            final Date start = sdf.parse(startDate);
            Date endTemp = sdf.parse(endDate);

            // 设置结束时间为当天的23:59:59
            Calendar cal = Calendar.getInstance();
            cal.setTime(endTemp);
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
            final Date end = cal.getTime();

            return errors.stream()
                .filter(error -> error.getAbnormalStartTime() != null)
                .filter(error -> !error.getAbnormalStartTime().before(start) && !error.getAbnormalStartTime().after(end))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("日期过滤失败，返回所有数据: {}", e.getMessage());
            return errors;
        }
    }

    /**
     * 从位置名称中提取深度信息
     */
    private String extractDepthFromLocation(String locationName) {
        if (!StringUtils.hasText(locationName)) {
            return "";
        }

        // 使用正则表达式提取深度信息（如-992m, -1130m等）
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("-\\d+m");
        java.util.regex.Matcher matcher = pattern.matcher(locationName);
        if (matcher.find()) {
            return matcher.group();
        }

        return "";
    }

    /**
     * 生成日期范围
     */
    private List<LocalDate> generateDateRange(String startDate, String endDate, String viewType) {
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            List<LocalDate> dateRange = new ArrayList<>();

            LocalDate current = start;
            while (!current.isAfter(end)) {
                dateRange.add(current);

                // 根据视图类型确定步长
                switch (viewType.toLowerCase()) {
                    case "weekly":
                        current = current.plusWeeks(1);
                        break;
                    case "monthly":
                        current = current.plusMonths(1);
                        break;
                    default: // daily
                        current = current.plusDays(1);
                        break;
                }
            }

            return dateRange;
        } catch (Exception e) {
            log.warn("生成日期范围失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 生成时间标签
     */
    private String generateTimeLabel(LocalDate date, String viewType, int timePoint) {
        switch (viewType.toLowerCase()) {
            case "weekly":
                return date.format(DateTimeFormatter.ofPattern("第ww周"));
            case "monthly":
                return date.format(DateTimeFormatter.ofPattern("MM月"));
            default: // daily
                return date.format(DateTimeFormatter.ofPattern("MM-dd"));
        }
    }
}
