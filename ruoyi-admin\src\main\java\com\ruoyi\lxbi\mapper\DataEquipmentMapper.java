package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import com.ruoyi.lxbi.domain.DataEquipment;
import com.ruoyi.lxbi.domain.response.DataEquipmentEfficiencyStats;
import com.ruoyi.lxbi.domain.response.DataEquipmentUtilizationStats;
import org.apache.ibatis.annotations.Param;

/**
 * 设备数据管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface DataEquipmentMapper 
{
    /**
     * 查询设备数据管理
     * 
     * @param id 设备数据管理主键
     * @return 设备数据管理
     */
    public DataEquipment selectDataEquipmentById(Long id);

    /**
     * 查询设备数据管理列表
     * 
     * @param dataEquipment 设备数据管理
     * @return 设备数据管理集合
     */
    public List<DataEquipment> selectDataEquipmentList(DataEquipment dataEquipment);

    /**
     * 新增设备数据管理
     * 
     * @param dataEquipment 设备数据管理
     * @return 结果
     */
    public int insertDataEquipment(DataEquipment dataEquipment);

    /**
     * 修改设备数据管理
     * 
     * @param dataEquipment 设备数据管理
     * @return 结果
     */
    public int updateDataEquipment(DataEquipment dataEquipment);

    /**
     * 删除设备数据管理
     *
     * @param id 设备数据管理主键
     * @return 结果
     */
    public int deleteDataEquipmentById(Long id);

    /**
     * 批量插入设备数据
     *
     * @param dataEquipmentList 设备数据列表
     * @return 结果
     */
    public int batchInsertDataEquipment(List<DataEquipment> dataEquipmentList);

    /**
     * 查询设备台效统计数据
     *
     * @param equipmentType 设备类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param timeType 时间聚合类型（week/month/year）
     * @return 台效统计数据
     */
    public List<DataEquipmentEfficiencyStats> getEfficiencyStats(@Param("equipmentType") Long equipmentType,
                                                                 @Param("startTime") Date startTime,
                                                                 @Param("endTime") Date endTime,
                                                                 @Param("timeType") String timeType);

    /**
     * 查询设备作业率统计数据
     *
     * @param equipmentType 设备类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param timeType 时间聚合类型（week/month/year）
     * @return 作业率统计数据
     */
    public List<DataEquipmentUtilizationStats> getUtilizationStats(@Param("equipmentType") Long equipmentType,
                                                                   @Param("startTime") Date startTime,
                                                                   @Param("endTime") Date endTime,
                                                                   @Param("timeType") String timeType);

    /**
     * 批量删除设备数据管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataEquipmentByIds(Long[] ids);
}
