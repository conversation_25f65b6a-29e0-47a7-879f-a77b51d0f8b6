package com.ruoyi.framework.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

import com.ruoyi.framework.service.MqttMessageService;

/**
 * MQTT消息处理器
 * 
 * <AUTHOR>
 * @date 2025-08-16
 */
@Slf4j
@Component
public class MqttMessageHandler implements MessageHandler {

    @Autowired
    private MqttMessageService mqttMessageService;

    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        try {
            String topic = message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC, String.class);
            String payload = message.getPayload().toString();
            
            log.info("收到MQTT消息 - 主题: {}, 内容: {}", topic, payload);
            
            // 委托给服务类处理具体的业务逻辑
            mqttMessageService.processMessage(topic, payload);
            
        } catch (Exception e) {
            log.error("处理MQTT消息时发生错误", e);
        }
    }
}
