<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.admin.mapper.MicroseismicStatMapper">

    <!-- 统计微震事件总数 -->
    <select id="countEvents" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM excel_microseismic_events
        <where>
            <if test="startDate != null and endDate != null">
                AND event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
            </if>
        </where>
    </select>

    <!-- 统计目标事件数 -->
    <select id="countTargetEvents" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM excel_microseismic_events
        <where>
            <if test="startDate != null and endDate != null">
                AND event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
            </if>
            AND signal_type = 12
        </where>
    </select>

    <!-- 统计设备事件总数 -->
    <select id="sumDeviceEventTotal" resultType="java.lang.Long">
        SELECT COALESCE(SUM(triggered_sensor_count), 0)
        FROM excel_microseismic_events
        <where>
            <if test="startDate != null and endDate != null">
                AND event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
            </if>
        </where>
    </select>

    <!-- 统计微震监测设备数量 -->
    <select id="countMicroseismicDevice" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT server_id)
        FROM excel_microseismic_events
        <where>
            <if test="startDate != null and endDate != null">
                AND event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
            </if>
            AND server_id IS NOT NULL
        </where>
    </select>

    <!-- 统计P波辐射能总和 -->
    <select id="sumPWaveRadiatedEnergy" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(p_wave_radiated_energy), 0)
        FROM excel_microseismic_events
        <where>
            <if test="startDate != null and endDate != null">
                AND event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
            </if>
            AND p_wave_radiated_energy IS NOT NULL
        </where>
    </select>

    <!-- 统计S波辐射能总和 -->
    <select id="sumSWaveRadiatedEnergy" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(s_wave_radiated_energy), 0)
        FROM excel_microseismic_events
        <where>
            <if test="startDate != null and endDate != null">
                AND event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
            </if>
            AND s_wave_radiated_energy IS NOT NULL
        </where>
    </select>

    <!-- 统计微震事件辐射能总和 -->
    <select id="sumRadiatedEnergy" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(radiated_energy), 0)
        FROM excel_microseismic_events
        <where>
            <if test="startDate != null and endDate != null">
                AND event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
            </if>
            AND radiated_energy IS NOT NULL
        </where>
    </select>

    <!-- 统计各等级震级次数 -->
    <select id="getMagnitudeLevelDistribution" resultType="java.util.Map">
        SELECT
            magnitude_level,
            COUNT(*) as event_count
        FROM (
            SELECT
                CASE
                    WHEN moment_magnitude &lt; -2.0 THEN '1级'
                    WHEN moment_magnitude &gt;= -2.0 AND moment_magnitude &lt; -1.0 THEN '2级'
                    WHEN moment_magnitude &gt;= -1.0 AND moment_magnitude &lt; 0.0 THEN '3级'
                    WHEN moment_magnitude &gt;= 0.0 AND moment_magnitude &lt; 1.0 THEN '4级'
                    ELSE '5级'
                END as magnitude_level,
                CASE
                    WHEN moment_magnitude &lt; -2.0 THEN 1
                    WHEN moment_magnitude &gt;= -2.0 AND moment_magnitude &lt; -1.0 THEN 2
                    WHEN moment_magnitude &gt;= -1.0 AND moment_magnitude &lt; 0.0 THEN 3
                    WHEN moment_magnitude &gt;= 0.0 AND moment_magnitude &lt; 1.0 THEN 4
                    ELSE 5
                END as sort_order
            FROM excel_microseismic_events
            <where>
                <if test="startDate != null and endDate != null">
                    AND event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
                </if>
                AND moment_magnitude IS NOT NULL
            </where>
        ) t
        GROUP BY magnitude_level, sort_order
        ORDER BY sort_order
    </select>

    <!-- 获取微震散点图原始数据 -->
    <select id="getScatterRawData" resultType="java.util.Map">
        SELECT
            event_name,
            x_coordinate,
            y_coordinate,
            z_coordinate,
            moment_magnitude,
            richter_magnitude,
            local_magnitude,
            radiated_energy,
            event_date,
            event_time,
            signal_type
        FROM excel_microseismic_events
        <where>
            <if test="startDate != null and endDate != null">
                AND event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
            </if>
            AND x_coordinate IS NOT NULL
            AND y_coordinate IS NOT NULL
            AND moment_magnitude IS NOT NULL
        </where>
        ORDER BY event_date DESC, event_time DESC
    </select>

</mapper>
