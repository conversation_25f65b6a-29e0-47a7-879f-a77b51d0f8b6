package com.ruoyi.lxbi.controller.plan;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.PlanMiningMonthly;
import com.ruoyi.lxbi.domain.request.PlanMiningMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanMiningMonthlyVo;
import com.ruoyi.lxbi.service.IPlanMiningMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 采矿整体月计划Controller
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@RestController
@RequestMapping("/plan/planMiningMonthly")
public class PlanMiningMonthlyController extends BaseController {
    @Autowired
    private IPlanMiningMonthlyService planMiningMonthlyService;

    /**
     * 查询采矿整体月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planMiningMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanMiningMonthly planMiningMonthly) {
        startPage();
        List<PlanMiningMonthlyVo> list = planMiningMonthlyService.selectPlanMiningMonthlyList(planMiningMonthly);
        return getDataTable(list);
    }

    /**
     * 导出采矿整体月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planMiningMonthly:export')")
    @Log(title = "采矿整体月计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanMiningMonthly planMiningMonthly) {
        List<PlanMiningMonthlyVo> list = planMiningMonthlyService.selectPlanMiningMonthlyList(planMiningMonthly);
        ExcelUtil<PlanMiningMonthlyVo> util = new ExcelUtil<PlanMiningMonthlyVo>(PlanMiningMonthlyVo.class);
        util.exportExcel(response, list, "采矿整体月计划数据");
    }

    /**
     * 获取采矿整体月计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('plan:planMiningMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planMiningMonthlyService.selectPlanMiningMonthlyById(id));
    }

    /**
     * 新增采矿整体月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planMiningMonthly:add')")
    @Log(title = "采矿整体月计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanMiningMonthly planMiningMonthly)
    {
        return toAjax(planMiningMonthlyService.insertPlanMiningMonthly(planMiningMonthly));
    }

    /**
     * 修改采矿整体月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planMiningMonthly:edit')")
    @Log(title = "采矿整体月计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanMiningMonthly planMiningMonthly)
    {
        return toAjax(planMiningMonthlyService.updatePlanMiningMonthly(planMiningMonthly));
    }

    /**
     * 删除采矿整体月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planMiningMonthly:remove')")
    @Log(title = "采矿整体月计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(planMiningMonthlyService.deletePlanMiningMonthlyByIds(ids));
    }

    /**
     * 批量保存采矿整体月计划（增删改查）
     * 传入批量列表，验证是否同一个月份的数据，然后查询这个月份的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('plan:planMiningMonthly:edit')")
    @Log(title = "采矿整体月计划批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<PlanMiningMonthlyBatchDto> batchDataList)
    {
        try {
            int result = planMiningMonthlyService.batchSavePlanMiningMonthly(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
