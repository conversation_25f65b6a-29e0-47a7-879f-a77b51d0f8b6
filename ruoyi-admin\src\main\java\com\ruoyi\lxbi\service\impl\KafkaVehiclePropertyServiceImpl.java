package com.ruoyi.lxbi.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaVehiclePropertyMapper;
import com.ruoyi.lxbi.domain.KafkaVehicleProperty;
import com.ruoyi.lxbi.service.IKafkaVehiclePropertyService;

/**
 * 车辆属性数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class KafkaVehiclePropertyServiceImpl implements IKafkaVehiclePropertyService
{
    @Autowired
    private KafkaVehiclePropertyMapper kafkaVehiclePropertyMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询车辆属性数据
     * 
     * @param id 车辆属性数据主键
     * @return 车辆属性数据
     */
    @Override
    public KafkaVehicleProperty selectKafkaVehiclePropertyById(Long id)
    {
        return kafkaVehiclePropertyMapper.selectKafkaVehiclePropertyById(id);
    }

    /**
     * 查询车辆属性数据列表
     * 
     * @param kafkaVehicleProperty 车辆属性数据
     * @return 车辆属性数据
     */
    @Override
    public List<KafkaVehicleProperty> selectKafkaVehiclePropertyList(KafkaVehicleProperty kafkaVehicleProperty)
    {
        return kafkaVehiclePropertyMapper.selectKafkaVehiclePropertyList(kafkaVehicleProperty);
    }

    /**
     * 新增车辆属性数据
     * 
     * @param kafkaVehicleProperty 车辆属性数据
     * @return 结果
     */
    @Override
    public int insertKafkaVehicleProperty(KafkaVehicleProperty kafkaVehicleProperty)
    {
        kafkaVehicleProperty.setCreateTime(DateUtils.getNowDate());
        return kafkaVehiclePropertyMapper.insertKafkaVehicleProperty(kafkaVehicleProperty);
    }

    /**
     * 修改车辆属性数据
     * 
     * @param kafkaVehicleProperty 车辆属性数据
     * @return 结果
     */
    @Override
    public int updateKafkaVehicleProperty(KafkaVehicleProperty kafkaVehicleProperty)
    {
        kafkaVehicleProperty.setUpdateTime(DateUtils.getNowDate());
        return kafkaVehiclePropertyMapper.updateKafkaVehicleProperty(kafkaVehicleProperty);
    }

    /**
     * 批量删除车辆属性数据
     * 
     * @param ids 需要删除的车辆属性数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaVehiclePropertyByIds(Long[] ids)
    {
        return kafkaVehiclePropertyMapper.deleteKafkaVehiclePropertyByIds(ids);
    }

    /**
     * 删除车辆属性数据信息
     *
     * @param id 车辆属性数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaVehiclePropertyById(Long id)
    {
        return kafkaVehiclePropertyMapper.deleteKafkaVehiclePropertyById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka车辆属性消息");

            // 解析Kafka消息
            KafkaVehicleProperty vehicleProperty = parseKafkaMessage(kafkaMessage);
            if (vehicleProperty == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(vehicleProperty.getVehicleLocationCardNumber())) {
                log.warn("车辆定位卡号为空，跳过处理");
                return false;
            }

            // 根据车辆定位卡号查询是否已存在
            KafkaVehicleProperty existingVehicle = kafkaVehiclePropertyMapper.selectByVehicleLocationCardNumber(
                vehicleProperty.getVehicleLocationCardNumber());

            int result;
            if (existingVehicle != null) {
                // 存在则更新
                vehicleProperty.setId(existingVehicle.getId());
                vehicleProperty.setUpdateTime(DateUtils.getNowDate());
                result = updateKafkaVehicleProperty(vehicleProperty);

                if (result > 0) {
                    log.info("成功更新车辆属性数据，车辆定位卡号: {}, 车辆编码: {}",
                        vehicleProperty.getVehicleLocationCardNumber(), vehicleProperty.getVehicleCode());
                    return true;
                } else {
                    log.warn("更新车辆属性数据失败，车辆定位卡号: {}",
                        vehicleProperty.getVehicleLocationCardNumber());
                    return false;
                }
            } else {
                // 不存在则插入
                vehicleProperty.setCreateTime(DateUtils.getNowDate());
                result = insertKafkaVehicleProperty(vehicleProperty);

                if (result > 0) {
                    log.info("成功插入车辆属性数据，车辆定位卡号: {}, 车辆编码: {}",
                        vehicleProperty.getVehicleLocationCardNumber(), vehicleProperty.getVehicleCode());
                    return true;
                } else {
                    log.warn("插入车辆属性数据失败，车辆定位卡号: {}",
                        vehicleProperty.getVehicleLocationCardNumber());
                    return false;
                }
            }

        } catch (Exception e) {
            log.error("处理Kafka车辆属性消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaVehicleProperty parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaVehicleProperty vehicleProperty = new KafkaVehicleProperty();

            // 基础信息
            vehicleProperty.setFileEncoding(getStringValue(jsonNode, "文件编码"));
            vehicleProperty.setMineCode(getStringValue(jsonNode, "煤矿代码"));
            vehicleProperty.setDataGenerationTime(getDateValue(jsonNode, "数据生成时间"));
            vehicleProperty.setVehicleLocationCardNumber(getStringValue(jsonNode, "车辆定位卡号"));
            vehicleProperty.setVehicleCode(getStringValue(jsonNode, "车辆编码"));
            vehicleProperty.setVehicleType(getStringValue(jsonNode, "车辆类型"));
            vehicleProperty.setDepartment(getStringValue(jsonNode, "部门"));
            vehicleProperty.setLoadWeight(getBigDecimalValue(jsonNode, "载重"));
            vehicleProperty.setLoadPersonnel(getBigDecimalValue(jsonNode, "载员"));
            vehicleProperty.setVehicleStatus(getStringValue(jsonNode, "车辆状态"));
            vehicleProperty.setFactoryDate(getDateValue(jsonNode, "出厂日期"));

            // 默认值
            vehicleProperty.setIsDeleted(0L);
            vehicleProperty.setCreateTime(DateUtils.getNowDate());
            vehicleProperty.setUpdateTime(DateUtils.getNowDate());

            return vehicleProperty;

        } catch (Exception e) {
            log.error("解析Kafka车辆属性消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null && !node.isNull() ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private java.util.Date getDateValue(JsonNode jsonNode, String fieldName) {
        try {
            String dateStr = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            }
        } catch (Exception e) {
            log.warn("解析日期字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }

    /**
     * 从JsonNode中获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(JsonNode jsonNode, String fieldName) {
        try {
            String value = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(value)) {
                return new BigDecimal(value);
            }
        } catch (Exception e) {
            log.warn("解析BigDecimal字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }
}
