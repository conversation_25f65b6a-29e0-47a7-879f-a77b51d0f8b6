<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataSupportMapper">
    
    <resultMap type="DataSupport" id="DataSupportResult">
        <result property="id"    column="id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="supportType"    column="support_type"    />
        <result property="supportLength"    column="support_length"    />
        <result property="supportVolume"    column="support_volume"    />
        <result property="remarks"    column="remarks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.ruoyi.lxbi.domain.response.DataSupportVo" id="DataSupportVoResult">
        <result property="id"    column="id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="supportType"    column="support_type"    />
        <result property="supportLength"    column="support_length"    />
        <result property="supportVolume"    column="support_volume"    />
        <result property="remarks"    column="remarks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="projectDepartmentName"    column="project_department_name"    />
        <result property="stopeName"    column="stope_name"    />
        <result property="workingFaceName"    column="working_face_name"    />
        <result property="workingPeriodName"    column="working_period_name"    />
        <result property="supportTypeName"    column="support_type_name"    />
    </resultMap>

    <sql id="selectDataSupportVo">
        select id, operation_date, project_department_id, working_face_id, stope_id, working_period_id, support_type, support_length, support_volume, remarks, create_by, create_time, update_by, update_time from data_support
    </sql>

    <sql id="selectDataSupportVoWithJoin">
        select ds.id, ds.operation_date, ds.project_department_id, ds.working_face_id, ds.stope_id, ds.working_period_id, ds.support_type, ds.support_length, ds.support_volume, ds.remarks, ds.create_by, ds.create_time, ds.update_by, ds.update_time,
               bpd.project_department_name,
               bs.stope_name,
               bwf.working_face_name,
               bwp.working_period_name,
               CASE
                   WHEN ds.support_type = '1' THEN '锚网支护'
                   WHEN ds.support_type = '2' THEN '喷浆支护'
                   ELSE '未知类型'
               END as support_type_name
        from data_support ds
        left join base_project_department bpd on ds.project_department_id = bpd.project_department_id
        left join base_stope bs on ds.stope_id = bs.stope_id
        left join base_working_face bwf on ds.working_face_id = bwf.working_face_id
        left join base_working_period bwp on ds.working_period_id = bwp.working_period_id
    </sql>

    <select id="selectDataSupportList" parameterType="DataSupport" resultMap="DataSupportResult">
        <include refid="selectDataSupportVo"/>
        <where>
            <if test="params.beginOperationDate != null and params.beginOperationDate != '' and params.endOperationDate != null and params.endOperationDate != ''"> and operation_date between #{params.beginOperationDate}::date and #{params.endOperationDate}::date</if>
            <if test="projectDepartmentId != null "> and project_department_id = #{projectDepartmentId}</if>
            <if test="workingFaceId != null "> and working_face_id = #{workingFaceId}</if>
            <if test="stopeId != null "> and stope_id = #{stopeId}</if>
            <if test="workingPeriodId != null "> and working_period_id = #{workingPeriodId}</if>
            <if test="supportLength != null  and supportLength != ''"> and support_length = #{supportLength}</if>
            <if test="supportVolume != null  and supportVolume != ''"> and support_volume = #{supportVolume}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
        </where>
    </select>

    <select id="selectDataSupportVoList" parameterType="DataSupport" resultMap="DataSupportVoResult">
        <include refid="selectDataSupportVoWithJoin"/>
        <where>
            <if test="params.beginOperationDate != null and params.beginOperationDate != '' and params.endOperationDate != null and params.endOperationDate != ''"> and ds.operation_date between #{params.beginOperationDate}::date and #{params.endOperationDate}::date</if>
            <if test="projectDepartmentId != null "> and ds.project_department_id = #{projectDepartmentId}</if>
            <if test="workingFaceId != null "> and ds.working_face_id = #{workingFaceId}</if>
            <if test="stopeId != null "> and ds.stope_id = #{stopeId}</if>
            <if test="workingPeriodId != null "> and ds.working_period_id = #{workingPeriodId}</if>
            <if test="supportType != null and supportType != ''"> and ds.support_type = #{supportType}</if>
            <if test="supportLength != null  and supportLength != ''"> and ds.support_length = #{supportLength}</if>
            <if test="supportVolume != null  and supportVolume != ''"> and ds.support_volume = #{supportVolume}</if>
            <if test="remarks != null  and remarks != ''"> and ds.remarks = #{remarks}</if>
        </where>
        order by ds.operation_date desc, ds.create_time desc
    </select>
    
    <select id="selectDataSupportById" parameterType="Long" resultMap="DataSupportResult">
        <include refid="selectDataSupportVo"/>
        where id = #{id}
    </select>

    <select id="selectDataSupportVoByOperationDate" parameterType="Date" resultMap="DataSupportVoResult">
        <include refid="selectDataSupportVoWithJoin"/>
        where ds.operation_date = #{operationDate}
        order by ds.project_department_id, ds.stope_id, ds.working_face_id, ds.working_period_id, ds.support_type
    </select>

    <insert id="insertDataSupport" parameterType="DataSupport" useGeneratedKeys="true" keyProperty="id">
        insert into data_support
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">operation_date,</if>
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="workingFaceId != null">working_face_id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="workingPeriodId != null">working_period_id,</if>
            <if test="supportType != null">support_type,</if>
            <if test="supportLength != null">support_length,</if>
            <if test="supportVolume != null">support_volume,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">#{operationDate},</if>
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="workingFaceId != null">#{workingFaceId},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="workingPeriodId != null">#{workingPeriodId},</if>
            <if test="supportType != null">#{supportType},</if>
            <if test="supportLength != null">#{supportLength},</if>
            <if test="supportVolume != null">#{supportVolume},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataSupport" parameterType="DataSupport">
        update data_support
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="workingFaceId != null">working_face_id = #{workingFaceId},</if>
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="workingPeriodId != null">working_period_id = #{workingPeriodId},</if>
            <if test="supportType != null">support_type = #{supportType},</if>
            <if test="supportLength != null">support_length = #{supportLength},</if>
            <if test="supportVolume != null">support_volume = #{supportVolume},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataSupportById" parameterType="Long">
        delete from data_support where id = #{id}
    </delete>

    <delete id="deleteDataSupportByIds" parameterType="String">
        delete from data_support where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertDataSupport" parameterType="java.util.List">
        insert into data_support (operation_date, project_department_id, working_face_id, stope_id, working_period_id, support_type, support_length, support_volume, remarks, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.operationDate}, #{item.projectDepartmentId}, #{item.workingFaceId}, #{item.stopeId}, #{item.workingPeriodId}, #{item.supportType}, #{item.supportLength}, #{item.supportVolume}, #{item.remarks}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <update id="batchUpdateDataSupport" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update data_support
            <set>
                operation_date = #{item.operationDate},
                project_department_id = #{item.projectDepartmentId},
                working_face_id = #{item.workingFaceId},
                stope_id = #{item.stopeId},
                working_period_id = #{item.workingPeriodId},
                support_type = #{item.supportType},
                support_length = #{item.supportLength},
                support_volume = #{item.supportVolume},
                remarks = #{item.remarks},
                update_by = #{item.updateBy},
                update_time = #{item.updateTime}
            </set>
            where id = #{item.id}
        </foreach>
    </update>
</mapper>