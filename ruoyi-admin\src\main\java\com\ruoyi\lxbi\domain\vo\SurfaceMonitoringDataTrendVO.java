package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 地表监测数据趋势VO
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurfaceMonitoringDataTrendVO {

    /**
     * 时间点
     */
    private String timePoint;

    /**
     * 短时间格式(用于图表显示)
     */
    private String shortTime;

    /**
     * 站点ID
     */
    private Long stationId;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * X偏移量(mm)
     */
    private BigDecimal xOffset;

    /**
     * Y偏移量(mm)
     */
    private BigDecimal yOffset;

    /**
     * H偏移量(mm)
     */
    private BigDecimal hOffset;

    /**
     * 总偏移量(mm)
     */
    private BigDecimal totalOffset;

    /**
     * 偏移速率(mm/天)
     */
    private BigDecimal offsetRate;

    /**
     * 卫星数量
     */
    private Integer satelliteCount;

    /**
     * 数据质量评分
     */
    private Integer dataQuality;

    /**
     * 温度(°C)
     */
    private BigDecimal temperature;

    /**
     * 湿度(%)
     */
    private BigDecimal humidity;

    /**
     * 电池电压(V)
     */
    private BigDecimal batteryVoltage;

    /**
     * 信号强度
     */
    private Integer signalStrength;

    /**
     * 是否有报警
     */
    private Boolean hasAlarm;

    /**
     * 报警等级
     */
    private String alarmLevel;
}
