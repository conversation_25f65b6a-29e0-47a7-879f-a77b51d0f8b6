package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaSubstationRealTimeData;
import org.apache.ibatis.annotations.Mapper;

/**
 * 分站实时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface KafkaSubstationRealTimeDataMapper
{
    /**
     * 查询分站实时数据
     * 
     * @param id 分站实时数据主键
     * @return 分站实时数据
     */
    public KafkaSubstationRealTimeData selectKafkaSubstationRealTimeDataById(Long id);

    /**
     * 查询分站实时数据列表
     * 
     * @param kafkaSubstationRealTimeData 分站实时数据
     * @return 分站实时数据集合
     */
    public List<KafkaSubstationRealTimeData> selectKafkaSubstationRealTimeDataList(KafkaSubstationRealTimeData kafkaSubstationRealTimeData);

    /**
     * 新增分站实时数据
     * 
     * @param kafkaSubstationRealTimeData 分站实时数据
     * @return 结果
     */
    public int insertKafkaSubstationRealTimeData(KafkaSubstationRealTimeData kafkaSubstationRealTimeData);

    /**
     * 修改分站实时数据
     * 
     * @param kafkaSubstationRealTimeData 分站实时数据
     * @return 结果
     */
    public int updateKafkaSubstationRealTimeData(KafkaSubstationRealTimeData kafkaSubstationRealTimeData);

    /**
     * 删除分站实时数据
     * 
     * @param id 分站实时数据主键
     * @return 结果
     */
    public int deleteKafkaSubstationRealTimeDataById(Long id);

    /**
     * 批量删除分站实时数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaSubstationRealTimeDataByIds(Long[] ids);
}
