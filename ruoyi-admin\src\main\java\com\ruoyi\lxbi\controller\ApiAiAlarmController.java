package com.ruoyi.lxbi.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.ApiAiAlarm;
import com.ruoyi.lxbi.service.IApiAiAlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * AI报警数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
@RequestMapping("/lxbi/ai-alarm")
public class ApiAiAlarmController extends BaseController {

    @Autowired
    private IApiAiAlarmService apiAiAlarmService;

    /**
     * 手动同步AI报警数据
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:sync')")
    @Log(title = "AI报警数据", businessType = BusinessType.IMPORT)
    @PostMapping("/sync")
    public R<Map<String, Object>> syncAiAlarmData(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        // 如果没有指定日期，默认同步前一天的数据
        if (startDate == null || endDate == null) {
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            String defaultDate = yesterday.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            startDate = startDate != null ? startDate : defaultDate;
            endDate = endDate != null ? endDate : defaultDate;
        }
        
        Map<String, Object> result = apiAiAlarmService.syncAiAlarmData(startDate, endDate);
        
        if ((Boolean) result.getOrDefault("success", false)) {
            return R.ok(result, "同步成功");
        } else {
            return R.fail(result.get("message").toString());
        }
    }

    /**
     * 查询AI报警数据列表
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:list')")
    @GetMapping("/list")
    public TableDataInfo list(
            @RequestParam(required = false) String alarmType,
            @RequestParam(required = false) String alarmLevel,
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        startPage();
        
        // 构建查询条件
        List<ApiAiAlarm> list;
        
        if (startTime != null && endTime != null) {
            LocalDateTime start = LocalDateTime.parse(startTime + " 00:00:00", 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime end = LocalDateTime.parse(endTime + " 23:59:59", 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            list = apiAiAlarmService.getByTimeRange(start, end);
        } else if (alarmType != null) {
            list = apiAiAlarmService.getByAlarmType(alarmType);
        } else if (deviceId != null) {
            list = apiAiAlarmService.getByDeviceId(deviceId);
        } else {
            // 默认查询最近7天的数据
            LocalDateTime end = LocalDateTime.now();
            LocalDateTime start = end.minusDays(7);
            list = apiAiAlarmService.getByTimeRange(start, end);
        }
        
        return getDataTable(list);
    }

    /**
     * 获取AI报警数据详细信息
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:query')")
    @GetMapping("/{id}")
    public R<ApiAiAlarm> getInfo(@PathVariable("id") Long id) {
        ApiAiAlarm alarm = apiAiAlarmService.getById(id);
        return R.ok(alarm);
    }

    /**
     * 根据外部ID查询报警数据
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:query')")
    @GetMapping("/external/{externalId}")
    public R<ApiAiAlarm> getByExternalId(@PathVariable("externalId") String externalId) {
        ApiAiAlarm alarm = apiAiAlarmService.getByExternalId(externalId);
        return R.ok(alarm);
    }

    /**
     * 获取同步状态统计
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:query')")
    @GetMapping("/sync-statistics")
    public R<Map<String, Object>> getSyncStatistics() {
        Map<String, Object> statistics = apiAiAlarmService.getSyncStatusStatistics();
        return R.ok(statistics);
    }

    /**
     * 重新同步失败的数据
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:sync')")
    @Log(title = "AI报警数据", businessType = BusinessType.UPDATE)
    @PostMapping("/resync-failed")
    public R<Map<String, Object>> resyncFailedData() {
        Map<String, Object> result = apiAiAlarmService.resyncFailedData();
        
        if ((Boolean) result.getOrDefault("success", false)) {
            return R.ok(result, "重新同步成功");
        } else {
            return R.fail(result.get("message").toString());
        }
    }

    /**
     * 清理过期数据
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:remove')")
    @Log(title = "AI报警数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean-expired")
    public R<Map<String, Object>> cleanExpiredData(
            @RequestParam(defaultValue = "90") int retentionDays) {
        
        if (retentionDays < 30) {
            return R.fail("保留天数不能少于30天");
        }
        
        Map<String, Object> result = apiAiAlarmService.cleanExpiredData(retentionDays);
        
        if ((Boolean) result.getOrDefault("success", false)) {
            return R.ok(result, "清理成功");
        } else {
            return R.fail(result.get("message").toString());
        }
    }

    /**
     * 统计指定时间范围内的报警数量
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:query')")
    @GetMapping("/count")
    public R<Long> countByTimeRange(
            @RequestParam String startTime,
            @RequestParam String endTime) {
        
        LocalDateTime start = LocalDateTime.parse(startTime + " 00:00:00", 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime end = LocalDateTime.parse(endTime + " 23:59:59", 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        Long count = apiAiAlarmService.countByTimeRange(start, end);
        return R.ok(count);
    }

    /**
     * 获取最新的同步时间
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:query')")
    @GetMapping("/latest-sync-time")
    public R<LocalDateTime> getLatestSyncTime() {
        LocalDateTime latestSyncTime = apiAiAlarmService.getLatestSyncTime();
        return R.ok(latestSyncTime);
    }

    /**
     * 测试AI报警系统连接
     */
//    @PreAuthorize("@ss.hasPermi('lxbi:ai-alarm:query')")
    @GetMapping("/test-connection")
    public R<String> testConnection() {
        try {
            // 这里可以调用AI报警系统的健康检查接口
            return R.ok("连接正常");
        } catch (Exception e) {
            return R.fail("连接失败: " + e.getMessage());
        }
    }
}
