package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaBaseStationRealTimeData;
import org.apache.ibatis.annotations.Mapper;

/**
 * 基站实时数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface KafkaBaseStationRealTimeDataMapper
{
    /**
     * 查询基站实时数据
     * 
     * @param id 基站实时数据主键
     * @return 基站实时数据
     */
    public KafkaBaseStationRealTimeData selectKafkaBaseStationRealTimeDataById(Long id);

    /**
     * 查询基站实时数据列表
     * 
     * @param kafkaBaseStationRealTimeData 基站实时数据
     * @return 基站实时数据集合
     */
    public List<KafkaBaseStationRealTimeData> selectKafkaBaseStationRealTimeDataList(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData);

    /**
     * 新增基站实时数据
     * 
     * @param kafkaBaseStationRealTimeData 基站实时数据
     * @return 结果
     */
    public int insertKafkaBaseStationRealTimeData(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData);

    /**
     * 修改基站实时数据
     * 
     * @param kafkaBaseStationRealTimeData 基站实时数据
     * @return 结果
     */
    public int updateKafkaBaseStationRealTimeData(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData);

    /**
     * 删除基站实时数据
     * 
     * @param id 基站实时数据主键
     * @return 结果
     */
    public int deleteKafkaBaseStationRealTimeDataById(Long id);

    /**
     * 批量删除基站实时数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaBaseStationRealTimeDataByIds(Long[] ids);
}
