package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 支护月计划对象 plan_support_monthly
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanSupportMonthly extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 工作面ID */
    private Long workingFaceId;

    /** 采场ID */
    private Long stopeId;

    /** 锚网支护米数 */
    @Excel(name = "锚网支护米数", isStatistics = true)
    private BigDecimal boltMeshSupportMeter;

    /** 喷浆支护米数 */
    @Excel(name = "喷浆支护米数", isStatistics = true)
    private BigDecimal shotcreteSupportMeter;

    /** 计划月份 */
    @Excel(name = "计划月份", sort = 1)
    private String planDate;

    @Excel(name = "锚网支护方量")
    private BigDecimal boltMeshSupportVolume;

    @Excel(name = "喷浆支护方量")
    private BigDecimal shotcreteSupportVolume;

    @Excel(name = "支护方量合计")
    private BigDecimal supportVolume;

    @Excel(name = "支护米数合计")
    private BigDecimal supportMeter;

}
