package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import com.ruoyi.common.core.table.TableColumnDataBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 选矿数据月报表格VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableConfig(code = "mineral_monthly_report", name = "选矿月报", description = "选矿数据月报统计表格")
public class MonthlyMineralReportTableVo extends TableColumnDataBase {

    @TableHeader(label = "序号", order = 1, width = 5)
    private String serialNumber;

    @TableHeader(label = "指标项", order = 2, parentPath = {"名称"}, colMergeGroup = {"name"})
    private String name;

    @TableHeader(label = "指标明细", order = 3, parentPath = {"名称"}, colMergeGroup = {"name"})
    private String subName;

    @TableHeader(label = "单位", order = 4, width = 5)
    private String unit;

    @TableHeader(label = "月计划", order = 5)
    private BigDecimal monthlyPlan;

    @TableHeader(label = "月完成", order = 6)
    private BigDecimal monthlyCompleted;

    @TableHeader(label = "月完成率", order = 7)
    private String monthlyCompletionRate;

    @TableHeader(label = "年度预算计划", order = 8)
    private BigDecimal yearlyBudgetPlan;

    @TableHeader(label = "年度计划", order = 9)
    private BigDecimal yearlyPlan;

    @TableHeader(label = "年度完成", order = 10)
    private BigDecimal yearlyCompleted;

    @TableHeader(label = "年度完成率", order = 11)
    private String yearlyCompletionRate;

    @TableHeader(label = "累计超欠", order = 12)
    private BigDecimal cumulativeVariance;
}
