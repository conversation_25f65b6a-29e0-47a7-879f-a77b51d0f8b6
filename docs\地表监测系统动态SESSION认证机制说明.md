# 地表监测系统动态SESSION认证机制说明

## 概述

地表监测系统采用动态SESSION cookie认证机制，需要正确处理响应头中的Set-Cookie，并在后续请求中使用最新的SESSION值。同时支持xtoken头的传递。

## 认证机制特点

### 1. 双重认证方式

**SESSION Cookie**：
- 格式：`Cookie: SESSION=xxxxxxxxxx`
- 来源：登录响应的Set-Cookie头
- 特点：动态更新，必须使用最新值

**xtoken Header**：
- 格式：`xtoken: xxxxxxxxxx`
- 来源：登录响应体或其他接口
- 特点：可选，如果有则必须传递

### 2. 动态SESSION更新

**关键要求**：
- 接口不是一定返回Set-Cookie
- 当收到Set-Cookie时，必须在下次请求时使用新的SESSION
- 登录时要保存传过来的Cookie
- token则传一个头xtoken

## 实现方案

### 1. 缓存结构

**Redis缓存键**：
```java
private static final String SESSION_CACHE_KEY = "surface_monitoring_session_token";
private static final String XTOKEN_CACHE_KEY = "surface_monitoring_xtoken";
```

**内存备份**：
```java
private String sessionToken;
private String xToken;
```

### 2. 登录流程

**登录请求**：
```java
POST /web/login
Content-Type: application/json

{
  "yhm": "admin",
  "mm": "Lxky.123"
}
```

**响应处理**：
```java
// 1. 从Set-Cookie头提取SESSION
String sessionToken = extractSessionFromCookies(response.getHeaders());

// 2. 从响应体提取xtoken（如果有）
String xToken = extractXTokenFromResponse(responseBody);

// 3. 缓存认证信息
if (sessionToken != null) {
    redisTemplate.opsForValue().set(SESSION_CACHE_KEY, sessionToken, tokenCacheTime, TimeUnit.SECONDS);
    if (xToken != null) {
        redisTemplate.opsForValue().set(XTOKEN_CACHE_KEY, xToken, tokenCacheTime, TimeUnit.SECONDS);
    }
}
```

### 3. Set-Cookie解析

**解析方法**：
```java
private String extractSessionFromCookies(HttpHeaders headers) {
    List<String> setCookieHeaders = headers.get("Set-Cookie");
    if (setCookieHeaders != null) {
        for (String setCookie : setCookieHeaders) {
            if (setCookie.startsWith("SESSION=")) {
                // 提取SESSION值：SESSION=xxxxxxxxx; Path=/; HttpOnly
                String sessionValue = setCookie.substring("SESSION=".length());
                int semicolonIndex = sessionValue.indexOf(';');
                if (semicolonIndex > 0) {
                    sessionValue = sessionValue.substring(0, semicolonIndex);
                }
                return sessionValue;
            }
        }
    }
    return null;
}
```

**Set-Cookie示例**：
```
Set-Cookie: SESSION=YjhlMjE4ZTAtNzBkNi00YWE4LWI5ZjYtMzE2NzQ5NzJkNzAw; Path=/; HttpOnly
```

### 4. API调用流程

**请求头设置**：
```java
// 获取认证信息
Map<String, String> authInfo = getAuthInfo();

// 添加SESSION cookie
if (authInfo.containsKey("session")) {
    headers.add("Cookie", "SESSION=" + authInfo.get("session"));
}

// 添加xtoken头（如果有）
if (authInfo.containsKey("xtoken")) {
    headers.add("xtoken", authInfo.get("xtoken"));
}
```

**响应处理**：
```java
// 检查响应头中是否有新的Set-Cookie
updateAuthInfoFromResponse(response.getHeaders());

// 如果有新SESSION，更新缓存
private void updateAuthInfoFromResponse(HttpHeaders headers) {
    String newSession = extractSessionFromCookies(headers);
    if (newSession != null && !newSession.equals(this.sessionToken)) {
        log.info("检测到新的SESSION，更新缓存");
        this.sessionToken = newSession;
        redisTemplate.opsForValue().set(SESSION_CACHE_KEY, newSession, tokenCacheTime, TimeUnit.SECONDS);
    }
}
```

## 核心方法实现

### 1. 获取认证信息

```java
public Map<String, String> getAuthInfo() {
    Map<String, String> authInfo = new HashMap<>();
    
    // 从缓存获取SESSION和xtoken
    String cachedSession = redisTemplate.opsForValue().get(SESSION_CACHE_KEY);
    String cachedXToken = redisTemplate.opsForValue().get(XTOKEN_CACHE_KEY);
    
    if (StringUtils.hasText(cachedSession)) {
        authInfo.put("session", cachedSession);
        if (StringUtils.hasText(cachedXToken)) {
            authInfo.put("xtoken", cachedXToken);
        }
        return authInfo;
    }
    
    // 缓存失效，重新登录
    Map<String, Object> loginResult = performLogin();
    if ((Boolean) loginResult.getOrDefault("success", false)) {
        String newSession = (String) loginResult.get("session");
        String newXToken = (String) loginResult.get("xtoken");
        
        if (newSession != null) {
            redisTemplate.opsForValue().set(SESSION_CACHE_KEY, newSession, tokenCacheTime, TimeUnit.SECONDS);
            authInfo.put("session", newSession);
            
            if (StringUtils.hasText(newXToken)) {
                redisTemplate.opsForValue().set(XTOKEN_CACHE_KEY, newXToken, tokenCacheTime, TimeUnit.SECONDS);
                authInfo.put("xtoken", newXToken);
            }
        }
    }
    
    return authInfo;
}
```

### 2. 执行登录

```java
private Map<String, Object> performLogin() {
    Map<String, Object> result = new HashMap<>();
    
    try {
        // 发送登录请求
        ResponseEntity<String> response = restTemplate.exchange(
            loginUrl, HttpMethod.POST, requestEntity, String.class);
        
        if (response.getStatusCode() == HttpStatus.OK) {
            // 解析Set-Cookie获取SESSION
            String sessionToken = extractSessionFromCookies(response.getHeaders());
            
            // 解析响应体获取xtoken（如果有）
            String xToken = extractXTokenFromResponse(response.getBody());
            
            if (sessionToken != null) {
                result.put("success", true);
                result.put("session", sessionToken);
                if (xToken != null) {
                    result.put("xtoken", xToken);
                }
            }
        }
    } catch (Exception e) {
        result.put("success", false);
        result.put("message", "登录异常: " + e.getMessage());
    }
    
    return result;
}
```

### 3. 动态更新SESSION

```java
private void updateAuthInfoFromResponse(HttpHeaders headers) {
    try {
        String newSession = extractSessionFromCookies(headers);
        if (newSession != null && !newSession.equals(this.sessionToken)) {
            log.info("检测到新的SESSION，更新缓存");
            
            // 更新内存
            this.sessionToken = newSession;
            
            // 更新Redis缓存
            redisTemplate.opsForValue().set(SESSION_CACHE_KEY, newSession, tokenCacheTime, TimeUnit.SECONDS);
        }
    } catch (Exception e) {
        log.error("更新认证信息失败", e);
    }
}
```

## 使用示例

### 1. 登录获取认证信息

```java
// 登录并获取认证信息
Map<String, Object> loginResult = surfaceMonitoringExternalService.login();

// 响应示例
{
  "success": true,
  "message": "登录成功",
  "session": "YjhlMjE4ZTAtNzBkNi00YWE4LWI5ZjYtMzE2NzQ5NzJkNzAw",
  "xtoken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 2. API调用自动处理认证

```java
// API调用会自动处理SESSION和xtoken
SurfaceMonitoringApiResponse response = surfaceMonitoringExternalService.getLatest7DayMpptShiftingTotal();

// 请求头自动包含：
// Cookie: SESSION=YjhlMjE4ZTAtNzBkNi00YWE4LWI5ZjYtMzE2NzQ5NzJkNzAw
// xtoken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 3. 动态SESSION更新

```http
# 某个API响应包含新的Set-Cookie
HTTP/1.1 200 OK
Set-Cookie: SESSION=新的SESSION值; Path=/; HttpOnly
Content-Type: application/json

{
  "result": true,
  "data": [...]
}
```

```java
// 系统自动检测并更新SESSION
// 下次请求会使用新的SESSION值
```

## 监控和调试

### 1. Redis缓存检查

```bash
# 检查SESSION缓存
redis-cli GET surface_monitoring_session_token

# 检查xtoken缓存
redis-cli GET surface_monitoring_xtoken

# 查看过期时间
redis-cli TTL surface_monitoring_session_token
redis-cli TTL surface_monitoring_xtoken
```

### 2. 日志监控

**关键日志**：
```
INFO  - 从Set-Cookie中提取到SESSION: YjhlMjE4ZT...
INFO  - 检测到新的SESSION，更新缓存
DEBUG - 从缓存中获取到地表监测SESSION token
WARN  - 认证失败，清除缓存的认证信息
```

### 3. 手动测试

```bash
# 清除缓存测试重新登录
redis-cli DEL surface_monitoring_session_token
redis-cli DEL surface_monitoring_xtoken

# 调用API触发重新登录
curl -X GET "http://localhost:8080/lxbi/stat/surface-monitoring/overview"
```

## 错误处理

### 1. 401未授权处理

```java
if (response.getStatusCode() == HttpStatus.UNAUTHORIZED) {
    log.warn("认证失败，清除缓存的认证信息");
    redisTemplate.delete(SESSION_CACHE_KEY);
    redisTemplate.delete(XTOKEN_CACHE_KEY);
    this.sessionToken = null;
    this.xToken = null;
}
```

### 2. Set-Cookie解析失败

```java
try {
    String sessionToken = extractSessionFromCookies(headers);
    if (sessionToken == null) {
        log.warn("响应头中未找到SESSION cookie");
    }
} catch (Exception e) {
    log.error("解析Set-Cookie失败", e);
}
```

### 3. 缓存操作失败

```java
try {
    redisTemplate.opsForValue().set(SESSION_CACHE_KEY, sessionToken, tokenCacheTime, TimeUnit.SECONDS);
} catch (Exception e) {
    log.warn("缓存SESSION失败，将直接使用获取的SESSION", e);
    // 即使缓存失败，也可以使用内存中的SESSION
}
```

## 配置说明

### application.yml

```yaml
surface:
  monitoring:
    api:
      base-url: http://10.10.22.24:19093/busgateway
      timeout: 30000
      token-cache-time: 3600  # SESSION缓存时间：1小时
```

## 总结

地表监测系统的动态SESSION认证机制具有以下特点：

1. **动态更新**: 自动检测和更新Set-Cookie中的SESSION
2. **双重认证**: 支持SESSION cookie和xtoken header
3. **智能缓存**: Redis缓存 + 内存备份的双重保障
4. **自动处理**: API调用时自动添加认证头
5. **错误恢复**: 401错误时自动清除缓存重新登录
6. **完整日志**: 详细的认证过程日志记录

这套机制确保了与地表监测系统的稳定通信，能够正确处理动态SESSION更新的复杂认证场景。

## 编译状态

✅ **编译成功**
- **编译时间**: 20.3秒
- **编译文件**: 548个源文件
- **状态**: BUILD SUCCESS

所有动态SESSION认证功能都已正确实现并通过编译验证。
