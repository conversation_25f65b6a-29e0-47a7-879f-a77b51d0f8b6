package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 选矿整体月计划导入对象
 */
@Data
@ExcelImportTemplate(
        key = "plan_mineral_monthly",
        name = "选矿整体月计划导入",
        description = "用于导入选矿整体月计划数据",
        sheetName = "选矿月计划"
)
public class PlanMineralMonthlyImport {

    /**
     * 计划月份
     */
    @ExcelProperty(value = "计划月份", index = 0)
    @ExcelRequired(message = "计划月份不能为空")
    @ExcelSelected(prompt = "请输入计划月份，格式：yyyyMM，例如：202501")
    private String planDate;

    /**
     * 原矿处理量
     */
    @ExcelProperty(value = "原矿处理量", index = 1)
    @ExcelSelected(prompt = "请输入原矿处理量")
    private BigDecimal rawOreProcessingVolume;

    /**
     * 干选量
     */
    @ExcelProperty(value = "干选量", index = 2)
    @ExcelSelected(prompt = "请输入干选量")
    private BigDecimal drySeparationVolume;

    /**
     * 入磨量
     */
    @ExcelProperty(value = "入磨量", index = 3)
    @ExcelSelected(prompt = "请输入入磨量")
    private BigDecimal grindingFeedVolume;

    /**
     * 原矿品位-TFe
     */
    @ExcelProperty(value = "原矿品位-TFe(%)", index = 4)
    @ExcelSelected(prompt = "请输入原矿品位TFe，单位：%")
    private BigDecimal rawOreGradeTfe;

    /**
     * 精矿品位-TFe
     */
    @ExcelProperty(value = "精矿品位-TFe(%)", index = 5)
    @ExcelSelected(prompt = "请输入精矿品位TFe，单位：%")
    private BigDecimal concentrateGrade;

    /**
     * 尾矿品位-TFe
     */
    @ExcelProperty(value = "尾矿品位-TFe(%)", index = 6)
    @ExcelSelected(prompt = "请输入尾矿品位TFe，单位：%")
    private BigDecimal tailingGradeTfe;

    /**
     * 精矿量
     */
    @ExcelProperty(value = "精矿量", index = 7)
    @ExcelSelected(prompt = "请输入精矿量")
    private BigDecimal concentrateVolume;
}
