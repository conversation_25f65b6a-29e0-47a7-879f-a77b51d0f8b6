<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.PlanBackfillingMonthlyMapper">
    
    <resultMap type="PlanBackfillingMonthly" id="PlanBackfillingMonthlyResult">
        <result property="id"    column="id"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="fillingVolume"    column="filling_volume"    />
        <result property="planDate"    column="plan_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPlanBackfillingMonthlyVo">
        select pbm.*, bpd.project_department_name, bwf.working_face_name, bs.stope_name
        from plan_backfilling_monthly pbm
        left join base_project_department bpd on pbm.project_department_id = bpd.project_department_id
        left join base_working_face bwf on pbm.working_face_id = bwf.working_face_id
        left join base_stope bs on pbm.stope_id = bs.stope_id
    </sql>

    <select id="selectPlanBackfillingMonthlyList" parameterType="PlanBackfillingMonthly" resultType="com.ruoyi.lxbi.domain.response.PlanBackfillingMonthlyVo">
        <include refid="selectPlanBackfillingMonthlyVo"/>
        <where>
            <if test="projectDepartmentId != null "> and pbm.project_department_id = #{projectDepartmentId}</if>
            <if test="workingFaceId != null "> and pbm.working_face_id = #{workingFaceId}</if>
            <if test="stopeId != null "> and pbm.stope_id = #{stopeId}</if>
            <if test="planDate != null  and planDate != ''"> and pbm.plan_date = #{planDate}</if>
        </where>
    </select>

    <select id="selectPlanBackfillingMonthlyListForBatch" parameterType="PlanBackfillingMonthly" resultMap="PlanBackfillingMonthlyResult">
        select id, project_department_id, working_face_id, stope_id, filling_volume, plan_date, create_by, create_time, update_by, update_time from plan_backfilling_monthly
        <where>
            <if test="projectDepartmentId != null "> and project_department_id = #{projectDepartmentId}</if>
            <if test="workingFaceId != null "> and working_face_id = #{workingFaceId}</if>
            <if test="stopeId != null "> and stope_id = #{stopeId}</if>
            <if test="planDate != null  and planDate != ''"> and plan_date = #{planDate}</if>
        </where>
    </select>
    
    <select id="selectPlanBackfillingMonthlyById" parameterType="Long" resultMap="PlanBackfillingMonthlyResult">
        <include refid="selectPlanBackfillingMonthlyVo"/>
        where pbm.id = #{id}
    </select>

    <insert id="insertPlanBackfillingMonthly" parameterType="PlanBackfillingMonthly" useGeneratedKeys="true" keyProperty="id">
        insert into plan_backfilling_monthly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="workingFaceId != null">working_face_id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="fillingVolume != null">filling_volume,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="workingFaceId != null">#{workingFaceId},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="fillingVolume != null">#{fillingVolume},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePlanBackfillingMonthly" parameterType="PlanBackfillingMonthly">
        update plan_backfilling_monthly
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="workingFaceId != null">working_face_id = #{workingFaceId},</if>
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="fillingVolume != null">filling_volume = #{fillingVolume},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanBackfillingMonthlyById" parameterType="Long">
        delete from plan_backfilling_monthly where id = #{id}
    </delete>

    <delete id="deletePlanBackfillingMonthlyByIds" parameterType="String">
        delete from plan_backfilling_monthly where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertPlanBackfillingMonthly" parameterType="java.util.List">
        insert into plan_backfilling_monthly (project_department_id, working_face_id, stope_id, filling_volume, plan_date, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectDepartmentId}, #{item.workingFaceId}, #{item.stopeId}, #{item.fillingVolume}, #{item.planDate}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>