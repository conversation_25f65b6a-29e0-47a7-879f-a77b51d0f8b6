package com.ruoyi.web.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 隐患统计数据DTO
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class HiddenTroubleStatisticsData {

    /** 待整改隐患数量 */
    @JsonProperty("notCorrectionCount")
    private Long notCorrectionCount = 0L;

    /** 被驳回隐患数量 */
    @JsonProperty("rejectCount")
    private Long rejectCount = 0L;

    /** 待复查隐患数量 */
    @JsonProperty("notReviewedCount")
    private Long notReviewedCount = 0L;

    /** 已整改隐患数量 */
    @JsonProperty("downCount")
    private Long downCount = 0L;

    /** 超期隐患数量 */
    @JsonProperty("overdueCount")
    private Long overdueCount = 0L;

    /**
     * 计算总隐患数量
     */
    public Long getTotalCount() {
        return (notCorrectionCount != null ? notCorrectionCount : 0L) +
               (rejectCount != null ? rejectCount : 0L) +
               (notReviewedCount != null ? notReviewedCount : 0L) +
               (downCount != null ? downCount : 0L) +
               (overdueCount != null ? overdueCount : 0L);
    }
}
