<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaSafeMonitorErrorInfoMapper">
    
    <resultMap type="KafkaSafeMonitorErrorInfo" id="KafkaSafeMonitorErrorInfoResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="monitoringPointCode"    column="monitoring_point_code"    />
        <result property="sensorTypeName"    column="sensor_type_name"    />
        <result property="monitoringPointLocation"    column="monitoring_point_location"    />
        <result property="monitoringPointUnit"    column="monitoring_point_unit"    />
        <result property="abnormalType"    column="abnormal_type"    />
        <result property="abnormalStartTime"    column="abnormal_start_time"    />
        <result property="abnormalEndTime"    column="abnormal_end_time"    />
        <result property="abnormalMaxValue"    column="abnormal_max_value"    />
        <result property="abnormalMaxTime"    column="abnormal_max_time"    />
        <result property="abnormalMinValue"    column="abnormal_min_value"    />
        <result property="abnormalMinTime"    column="abnormal_min_time"    />
        <result property="abnormalAverageValue"    column="abnormal_average_value"    />
        <result property="abnormalReason"    column="abnormal_reason"    />
        <result property="handlingMeasures"    column="handling_measures"    />
        <result property="dataTime"    column="data_time"    />
    </resultMap>

    <sql id="selectKafkaSafeMonitorErrorInfoVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, mine_name, data_upload_time, monitoring_point_code, sensor_type_name, monitoring_point_location, monitoring_point_unit, abnormal_type, abnormal_start_time, abnormal_end_time, abnormal_max_value, abnormal_max_time, abnormal_min_value, abnormal_min_time, abnormal_average_value, abnormal_reason, handling_measures, data_time from kafka_safe_monitor_error_info
    </sql>

    <select id="selectKafkaSafeMonitorErrorInfoList" parameterType="KafkaSafeMonitorErrorInfo" resultMap="KafkaSafeMonitorErrorInfoResult">
        <include refid="selectKafkaSafeMonitorErrorInfoVo"/>
        <where>  
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="params.beginDataUploadTime != null and params.beginDataUploadTime != '' and params.endDataUploadTime != null and params.endDataUploadTime != ''"> and data_upload_time between #{params.beginDataUploadTime}::date and #{params.endDataUploadTime}::date</if>
            <if test="monitoringPointCode != null  and monitoringPointCode != ''"> and monitoring_point_code = #{monitoringPointCode}</if>
            <if test="sensorTypeName != null  and sensorTypeName != ''"> and sensor_type_name like concat('%', #{sensorTypeName}, '%')</if>
            <if test="monitoringPointLocation != null  and monitoringPointLocation != ''"> and monitoring_point_location = #{monitoringPointLocation}</if>
            <if test="monitoringPointUnit != null  and monitoringPointUnit != ''"> and monitoring_point_unit = #{monitoringPointUnit}</if>
            <if test="abnormalType != null  and abnormalType != ''"> and abnormal_type = #{abnormalType}</if>
            <if test="params.beginAbnormalStartTime != null and params.beginAbnormalStartTime != '' and params.endAbnormalStartTime != null and params.endAbnormalStartTime != ''"> and abnormal_start_time between #{params.beginAbnormalStartTime}::date and #{params.endAbnormalStartTime}::date</if>
            <if test="params.beginAbnormalEndTime != null and params.beginAbnormalEndTime != '' and params.endAbnormalEndTime != null and params.endAbnormalEndTime != ''"> and abnormal_end_time between #{params.beginAbnormalEndTime}::date and #{params.endAbnormalEndTime}::date</if>
            <if test="abnormalMaxValue != null "> and abnormal_max_value = #{abnormalMaxValue}</if>
            <if test="params.beginAbnormalMaxTime != null and params.beginAbnormalMaxTime != '' and params.endAbnormalMaxTime != null and params.endAbnormalMaxTime != ''"> and abnormal_max_time between #{params.beginAbnormalMaxTime}::date and #{params.endAbnormalMaxTime}::date</if>
            <if test="abnormalMinValue != null "> and abnormal_min_value = #{abnormalMinValue}</if>
            <if test="params.beginAbnormalMinTime != null and params.beginAbnormalMinTime != '' and params.endAbnormalMinTime != null and params.endAbnormalMinTime != ''"> and abnormal_min_time between #{params.beginAbnormalMinTime}::date and #{params.endAbnormalMinTime}::date</if>
            <if test="abnormalAverageValue != null "> and abnormal_average_value = #{abnormalAverageValue}</if>
            <if test="abnormalReason != null  and abnormalReason != ''"> and abnormal_reason = #{abnormalReason}</if>
            <if test="handlingMeasures != null  and handlingMeasures != ''"> and handling_measures = #{handlingMeasures}</if>
            <if test="params.beginDataTime != null and params.beginDataTime != '' and params.endDataTime != null and params.endDataTime != ''"> and data_time between #{params.beginDataTime}::date and #{params.endDataTime}::date</if>
        </where>
    </select>
    
    <select id="selectKafkaSafeMonitorErrorInfoById" parameterType="Long" resultMap="KafkaSafeMonitorErrorInfoResult">
        <include refid="selectKafkaSafeMonitorErrorInfoVo"/>
        where id = #{id}
    </select>

    <!-- 统计环境安全报警位置分布 -->
    <select id="selectLocationDistributionStats" resultType="java.util.Map">
        SELECT
            COALESCE(p.monitoring_point_installation_location, '未知位置') as location_name,
            COUNT(e.id) as alarm_count
        FROM kafka_safe_monitor_error_info e
        LEFT JOIN kafka_monitoring_point_basic_info p
            ON e.monitoring_point_code = p.monitoring_point_code
        WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND e.abnormal_start_time >= #{startDate}::date
            </if>
            <if test="endDate != null and endDate != ''">
                AND e.abnormal_start_time &lt;= #{endDate}::date + INTERVAL '1 day' - INTERVAL '1 second'
            </if>
            AND e.monitoring_point_code IS NOT NULL
            AND e.monitoring_point_code != ''
        GROUP BY p.monitoring_point_installation_location
        HAVING COUNT(e.id) > 0
        ORDER BY alarm_count DESC
    </select>

    <!-- 按日期统计环境安全报警数量趋势 -->
    <select id="selectAlarmTrendByDate" resultType="java.util.Map">
        SELECT
            DATE(e.abnormal_start_time) as trend_date,
            COUNT(e.id) as alarm_count,
            COUNT(CASE WHEN e.abnormal_end_time IS NOT NULL THEN 1 END) as end_event_count
        FROM kafka_safe_monitor_error_info e
        WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND e.abnormal_start_time >= #{startDate}::date
            </if>
            <if test="endDate != null and endDate != ''">
                AND e.abnormal_start_time &lt;= #{endDate}::date + INTERVAL '1 day' - INTERVAL '1 second'
            </if>
            AND e.abnormal_start_time IS NOT NULL
        GROUP BY DATE(e.abnormal_start_time)
        ORDER BY trend_date
    </select>

    <!-- 按小时统计环境安全报警数量趋势 -->
    <select id="selectAlarmTrendByHour" resultType="java.util.Map">
        SELECT
            DATE(e.abnormal_start_time) as trend_date,
            EXTRACT(HOUR FROM e.abnormal_start_time) as trend_hour,
            COUNT(e.id) as alarm_count,
            COUNT(CASE WHEN e.abnormal_end_time IS NOT NULL THEN 1 END) as end_event_count
        FROM kafka_safe_monitor_error_info e
        WHERE 1=1
            <if test="startDate != null and startDate != ''">
                AND e.abnormal_start_time >= #{startDate}::date
            </if>
            <if test="endDate != null and endDate != ''">
                AND e.abnormal_start_time &lt;= #{endDate}::date + INTERVAL '1 day' - INTERVAL '1 second'
            </if>
            AND e.abnormal_start_time IS NOT NULL
        GROUP BY DATE(e.abnormal_start_time), EXTRACT(HOUR FROM e.abnormal_start_time)
        ORDER BY trend_date, trend_hour
    </select>

    <insert id="insertKafkaSafeMonitorErrorInfo" parameterType="KafkaSafeMonitorErrorInfo" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_safe_monitor_error_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null and fileEncoding != ''">file_encoding,</if>
            <if test="mineCode != null and mineCode != ''">mine_code,</if>
            <if test="mineName != null and mineName != ''">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="monitoringPointCode != null and monitoringPointCode != ''">monitoring_point_code,</if>
            <if test="sensorTypeName != null and sensorTypeName != ''">sensor_type_name,</if>
            <if test="monitoringPointLocation != null">monitoring_point_location,</if>
            <if test="monitoringPointUnit != null">monitoring_point_unit,</if>
            <if test="abnormalType != null">abnormal_type,</if>
            <if test="abnormalStartTime != null">abnormal_start_time,</if>
            <if test="abnormalEndTime != null">abnormal_end_time,</if>
            <if test="abnormalMaxValue != null">abnormal_max_value,</if>
            <if test="abnormalMaxTime != null">abnormal_max_time,</if>
            <if test="abnormalMinValue != null">abnormal_min_value,</if>
            <if test="abnormalMinTime != null">abnormal_min_time,</if>
            <if test="abnormalAverageValue != null">abnormal_average_value,</if>
            <if test="abnormalReason != null">abnormal_reason,</if>
            <if test="handlingMeasures != null">handling_measures,</if>
            <if test="dataTime != null">data_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null and fileEncoding != ''">#{fileEncoding},</if>
            <if test="mineCode != null and mineCode != ''">#{mineCode},</if>
            <if test="mineName != null and mineName != ''">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="monitoringPointCode != null and monitoringPointCode != ''">#{monitoringPointCode},</if>
            <if test="sensorTypeName != null and sensorTypeName != ''">#{sensorTypeName},</if>
            <if test="monitoringPointLocation != null">#{monitoringPointLocation},</if>
            <if test="monitoringPointUnit != null">#{monitoringPointUnit},</if>
            <if test="abnormalType != null">#{abnormalType},</if>
            <if test="abnormalStartTime != null">#{abnormalStartTime},</if>
            <if test="abnormalEndTime != null">#{abnormalEndTime},</if>
            <if test="abnormalMaxValue != null">#{abnormalMaxValue},</if>
            <if test="abnormalMaxTime != null">#{abnormalMaxTime},</if>
            <if test="abnormalMinValue != null">#{abnormalMinValue},</if>
            <if test="abnormalMinTime != null">#{abnormalMinTime},</if>
            <if test="abnormalAverageValue != null">#{abnormalAverageValue},</if>
            <if test="abnormalReason != null">#{abnormalReason},</if>
            <if test="handlingMeasures != null">#{handlingMeasures},</if>
            <if test="dataTime != null">#{dataTime},</if>
         </trim>
    </insert>

    <update id="updateKafkaSafeMonitorErrorInfo" parameterType="KafkaSafeMonitorErrorInfo">
        update kafka_safe_monitor_error_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null and fileEncoding != ''">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null and mineCode != ''">mine_code = #{mineCode},</if>
            <if test="mineName != null and mineName != ''">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="monitoringPointCode != null and monitoringPointCode != ''">monitoring_point_code = #{monitoringPointCode},</if>
            <if test="sensorTypeName != null and sensorTypeName != ''">sensor_type_name = #{sensorTypeName},</if>
            <if test="monitoringPointLocation != null">monitoring_point_location = #{monitoringPointLocation},</if>
            <if test="monitoringPointUnit != null">monitoring_point_unit = #{monitoringPointUnit},</if>
            <if test="abnormalType != null">abnormal_type = #{abnormalType},</if>
            <if test="abnormalStartTime != null">abnormal_start_time = #{abnormalStartTime},</if>
            <if test="abnormalEndTime != null">abnormal_end_time = #{abnormalEndTime},</if>
            <if test="abnormalMaxValue != null">abnormal_max_value = #{abnormalMaxValue},</if>
            <if test="abnormalMaxTime != null">abnormal_max_time = #{abnormalMaxTime},</if>
            <if test="abnormalMinValue != null">abnormal_min_value = #{abnormalMinValue},</if>
            <if test="abnormalMinTime != null">abnormal_min_time = #{abnormalMinTime},</if>
            <if test="abnormalAverageValue != null">abnormal_average_value = #{abnormalAverageValue},</if>
            <if test="abnormalReason != null">abnormal_reason = #{abnormalReason},</if>
            <if test="handlingMeasures != null">handling_measures = #{handlingMeasures},</if>
            <if test="dataTime != null">data_time = #{dataTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaSafeMonitorErrorInfoById" parameterType="Long">
        delete from kafka_safe_monitor_error_info where id = #{id}
    </delete>

    <delete id="deleteKafkaSafeMonitorErrorInfoByIds" parameterType="String">
        delete from kafka_safe_monitor_error_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>