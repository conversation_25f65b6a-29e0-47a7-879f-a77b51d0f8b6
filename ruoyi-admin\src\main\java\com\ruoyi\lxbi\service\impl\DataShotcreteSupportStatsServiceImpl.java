package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.response.DataSupportTypeTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportTypeDepartmentWithPlanStats;
import com.ruoyi.lxbi.mapper.DataShotcreteSupportStatsMapper;
import com.ruoyi.lxbi.service.IDataShotcreteSupportStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 喷浆支护数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class DataShotcreteSupportStatsServiceImpl implements IDataShotcreteSupportStatsService {
    @Autowired
    private DataShotcreteSupportStatsMapper dataShotcreteSupportStatsMapper;

    /**
     * 查询喷浆支护总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 喷浆支护总体统计数据集合（含计划量）
     */
    @Override
    public List<DataSupportTypeTotalWithPlanStats> selectTotalWithPlanStatsList(DataSupportStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataShotcreteSupportStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataShotcreteSupportStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataShotcreteSupportStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认为月统计
            return dataShotcreteSupportStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询喷浆支护项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 喷浆支护项目部门统计数据集合（含计划量）
     */
    @Override
    public List<DataSupportTypeDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataSupportStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataShotcreteSupportStatsMapper.selectDailyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataShotcreteSupportStatsMapper.selectWeeklyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataShotcreteSupportStatsMapper.selectYearlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认为月统计
            return dataShotcreteSupportStatsMapper.selectMonthlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }
}
