package com.ruoyi.lxbi.controller.stat.sec;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.admin.service.IPersonnelSafetyStatService;
import com.ruoyi.lxbi.domain.vo.*;
import com.ruoyi.lxbi.domain.ApiAiAlarm;
import com.ruoyi.lxbi.utils.DateRangeCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 人员安全统计Controller
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@RestController
@RequestMapping("/sec/personnelSafetyStat")
public class PersonnelSafetyStatController extends BaseController {
    
    @Autowired
    private IPersonnelSafetyStatService personnelSafetyStatService;

    /**
     * 获取人员安全概览数据
     * 包括：下井人次、区域超时、教育培训、违规行为
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/overview")
    public R<PersonnelSafetyOverviewVO> getOverview(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取人员安全概览数据，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证视图类型参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            PersonnelSafetyOverviewVO overview = personnelSafetyStatService.getOverviewStatistics(viewType, startDate, endDate);
            return R.ok(overview);
        } catch (Exception e) {
            log.error("获取人员安全概览数据失败", e);
            return R.fail("获取概览数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取主要超时组分布统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/timeoutGroupDistribution")
    public R<List<TimeoutGroupDistributionVO>> getTimeoutGroupDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取主要超时组分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<TimeoutGroupDistributionVO> distribution = personnelSafetyStatService.getTimeoutGroupDistribution(viewType, startDate, endDate);
            return R.ok(distribution);
        } catch (Exception e) {
            log.error("获取主要超时组分布统计失败", e);
            return R.fail("获取超时组分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取区域超时人员名单
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/areaTimeoutPersonnel")
    @Log(title = "区域超时人员名单", businessType = BusinessType.OTHER)
    public R<List<AreaTimeoutPersonnelVO>> getAreaTimeoutPersonnel(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取区域超时人员名单，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<AreaTimeoutPersonnelVO> personnelList = personnelSafetyStatService.getAreaTimeoutPersonnel(viewType, startDate, endDate);
            return R.ok(personnelList);
        } catch (Exception e) {
            log.error("获取区域超时人员名单失败", e);
            return R.fail("获取超时人员名单失败: " + e.getMessage());
        }
    }

    /**
     * 获取来救数量趋势统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/rescueQuantityTrend")
    @Log(title = "来救数量趋势统计", businessType = BusinessType.OTHER)
    public R<List<RescueQuantityTrendVO>> getRescueQuantityTrend(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取来救数量趋势统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            // 对于按日查询，如果天数少于7天，向前推到7天
            if ("daily".equals(viewType)) {
                startDate = DateRangeCalculator.ensureMinimumDaysForDaily(startDate, endDate, 7);
            }

            List<RescueQuantityTrendVO> trendData = personnelSafetyStatService.getRescueQuantityTrend(viewType, startDate, endDate);
            return R.ok(trendData);
        } catch (Exception e) {
            log.error("获取来救数量趋势统计失败", e);
            return R.fail("获取来救数量趋势统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取人员安全综合统计数据（用于仪表板）
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/dashboard")
    @Log(title = "人员安全综合统计数据", businessType = BusinessType.OTHER)
    public R<PersonnelSafetyDashboardVO> getDashboardData(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取人员安全综合统计数据，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            PersonnelSafetyDashboardVO dashboardData = personnelSafetyStatService.getDashboardData(viewType, startDate, endDate);
            return R.ok(dashboardData);
        } catch (Exception e) {
            log.error("获取人员安全综合统计数据失败", e);
            return R.fail("获取人员安全综合统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取人员安全小结统计
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/safetySummary")
    @Log(title = "人员安全小结统计", businessType = BusinessType.OTHER)
    public R<PersonnelSafetySummaryVO> getSafetySummary(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取人员安全小结统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证周期参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            PersonnelSafetySummaryVO safetySummary = personnelSafetyStatService.getSafetySummary(viewType, startDate, endDate);
            return R.ok(safetySummary);
        } catch (Exception e) {
            log.error("获取人员安全小结统计失败", e);
            return R.fail("获取人员安全小结统计失败: " + e.getMessage());
        }
    }

    /**
     * 生成人员安全小结文本描述
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/safetySummaryText")
    @Log(title = "人员安全小结文本生成", businessType = BusinessType.OTHER)
    public R<PersonnelSafetySummaryTextVO> generateSafetySummaryText(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("生成人员安全小结文本，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证视图类型参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            PersonnelSafetySummaryTextVO summaryText = personnelSafetyStatService.generateSafetySummaryText(viewType, startDate, endDate);
            return R.ok(summaryText);
        } catch (Exception e) {
            log.error("生成人员安全小结文本失败", e);
            return R.fail("生成人员安全小结文本失败: " + e.getMessage());
        }
    }

    /**
     * 获取人员安全简化概览
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/simpleOverview")
    @Log(title = "人员安全简化概览", businessType = BusinessType.OTHER)
    public R<PersonnelSafetySimpleOverviewVO> getSimpleOverview(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取人员安全简化概览，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证视图类型参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            PersonnelSafetySimpleOverviewVO simpleOverview = personnelSafetyStatService.getSimpleOverview(viewType, startDate, endDate);
            return R.ok(simpleOverview);
        } catch (Exception e) {
            log.error("获取人员安全简化概览失败", e);
            return R.fail("获取人员安全简化概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取井下人员分布统计
     * 按区域统计井下人员分布情况
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @Anonymous
    @GetMapping("/underground-distribution")
    public R<List<UndergroundPersonnelDistributionVO>> getUndergroundPersonnelDistribution(
            @RequestParam(defaultValue = "daily") String viewType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("获取井下人员分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 验证视图类型参数
            if (!DateRangeCalculator.isValidViewType(viewType)) {
                return R.fail("无效的视图类型，支持的类型: daily, weekly, monthly");
            }

            // 设置默认日期范围或根据viewType计算日期范围
            if (startDate == null || endDate == null) {
                Map<String, String> calculatedDates = DateRangeCalculator.calculateDateRangeByViewType(viewType, startDate, endDate);
                startDate = calculatedDates.get("startDate");
                endDate = calculatedDates.get("endDate");
            }

            List<UndergroundPersonnelDistributionVO> distribution = personnelSafetyStatService.getUndergroundPersonnelDistribution(viewType, startDate, endDate);
            return R.ok(distribution);
        } catch (Exception e) {
            log.error("获取井下人员分布统计失败", e);
            return R.fail("获取井下人员分布统计失败: " + e.getMessage());
        }
    }

}
