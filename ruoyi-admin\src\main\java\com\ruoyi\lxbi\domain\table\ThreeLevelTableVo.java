package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import lombok.Data;

/**
 * 三层表头示例VO
 */
@Data
@TableConfig(code = "three_level_table", name = "三层表头示例", description = "演示三层表头功能")
public class ThreeLevelTableVo {
    
    @TableHeader(label = "R11", order = 1, parentPath = {"R1", "R1"}, enableRowMerge = true)
    private String a1;
    
    @TableHeader(label = "R12", order = 2, parentPath = {"R1", "R1"}, enableRowMerge = true, relateColumn = "a1")
    private String a2;
    
    @TableHeader(label = "R1", order = 3, parentPath = {"R1"}, enableRowMerge = true, relateColumn = "a2")
    private String b;
    
    @TableHeader(label = "R2", order = 4, enableRowMerge = true, relateColumn = "b")
    private String c;
    
    @TableHeader(label = "R31", order = 5, parentPath = {"R3"}, enableRowMerge = true, relateColumn = "c")
    private String d;
    
    @TableHeader(label = "R32", order = 6, parentPath = {"R3"})
    private String e;
}
