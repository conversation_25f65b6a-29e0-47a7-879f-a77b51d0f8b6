package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.response.DataSupportTypeTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportTypeDepartmentWithPlanStats;
import com.ruoyi.lxbi.mapper.DataBoltMeshSupportStatsMapper;
import com.ruoyi.lxbi.service.IDataBoltMeshSupportStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 锚网支护数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class DataBoltMeshSupportStatsServiceImpl implements IDataBoltMeshSupportStatsService {
    @Autowired
    private DataBoltMeshSupportStatsMapper dataBoltMeshSupportStatsMapper;

    /**
     * 查询锚网支护总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 锚网支护总体统计数据集合（含计划量）
     */
    @Override
    public List<DataSupportTypeTotalWithPlanStats> selectTotalWithPlanStatsList(DataSupportStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataBoltMeshSupportStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataBoltMeshSupportStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataBoltMeshSupportStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认为月统计
            return dataBoltMeshSupportStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询锚网支护项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 锚网支护项目部门统计数据集合（含计划量）
     */
    @Override
    public List<DataSupportTypeDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataSupportStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataBoltMeshSupportStatsMapper.selectDailyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataBoltMeshSupportStatsMapper.selectWeeklyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataBoltMeshSupportStatsMapper.selectYearlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认为月统计
            return dataBoltMeshSupportStatsMapper.selectMonthlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }
}
