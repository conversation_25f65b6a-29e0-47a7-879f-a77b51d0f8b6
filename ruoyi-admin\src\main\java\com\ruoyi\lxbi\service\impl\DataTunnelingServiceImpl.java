package com.ruoyi.lxbi.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.DataTunnelingMapper;
import com.ruoyi.lxbi.domain.DataTunneling;
import com.ruoyi.lxbi.domain.request.DataTunnelingBatchDto;
import com.ruoyi.lxbi.domain.response.DataTunnelingVo;
import com.ruoyi.lxbi.service.IDataTunnelingService;

/**
 * 掘进数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class DataTunnelingServiceImpl implements IDataTunnelingService 
{
    @Autowired
    private DataTunnelingMapper dataTunnelingMapper;

    /**
     * 查询掘进数据
     * 
     * @param id 掘进数据主键
     * @return 掘进数据
     */
    @Override
    public DataTunneling selectDataTunnelingById(Long id)
    {
        return dataTunnelingMapper.selectDataTunnelingById(id);
    }

    /**
     * 查询掘进数据列表
     *
     * @param dataTunneling 掘进数据
     * @return 掘进数据
     */
    @Override
    public List<DataTunnelingVo> selectDataTunnelingList(DataTunneling dataTunneling)
    {
        return dataTunnelingMapper.selectDataTunnelingList(dataTunneling);
    }

    /**
     * 新增掘进数据
     * 
     * @param dataTunneling 掘进数据
     * @return 结果
     */
    @Override
    public int insertDataTunneling(DataTunneling dataTunneling)
    {
        dataTunneling.setCreateTime(DateUtils.getNowDate());
        return dataTunnelingMapper.insertDataTunneling(dataTunneling);
    }

    /**
     * 修改掘进数据
     * 
     * @param dataTunneling 掘进数据
     * @return 结果
     */
    @Override
    public int updateDataTunneling(DataTunneling dataTunneling)
    {
        dataTunneling.setUpdateTime(DateUtils.getNowDate());
        return dataTunnelingMapper.updateDataTunneling(dataTunneling);
    }

    /**
     * 批量删除掘进数据
     * 
     * @param ids 需要删除的掘进数据主键
     * @return 结果
     */
    @Override
    public int deleteDataTunnelingByIds(Long[] ids)
    {
        return dataTunnelingMapper.deleteDataTunnelingByIds(ids);
    }

    /**
     * 删除掘进数据信息
     *
     * @param id 掘进数据主键
     * @return 结果
     */
    @Override
    public int deleteDataTunnelingById(Long id)
    {
        return dataTunnelingMapper.deleteDataTunnelingById(id);
    }

    /**
     * 根据作业日期和项目部门查询掘进数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 掘进数据集合
     */
    @Override
    public List<DataTunnelingVo> selectDataTunnelingByOperationDateAndProject(Date operationDate, Long projectDepartmentId) {
        return dataTunnelingMapper.selectDataTunnelingByOperationDateAndProject(operationDate, projectDepartmentId);
    }

    /**
     * 批量保存掘进数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDataTunneling(List<DataTunnelingBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个日期和项目部的数据
        Date operationDate = batchDataList.get(0).getOperationDate();
        Long projectDepartmentId = batchDataList.get(0).getProjectDepartmentId();
        if (operationDate == null) {
            throw new ServiceException("作业日期不能为空");
        }
        if (projectDepartmentId == null) {
            throw new ServiceException("项目部门不能为空");
        }

        boolean allSameDate = batchDataList.stream()
                .allMatch(data -> operationDate.equals(data.getOperationDate())
                    && projectDepartmentId.equals(data.getProjectDepartmentId()));
        if (!allSameDate) {
            throw new ServiceException("批量数据必须是同一个作业日期和项目部门");
        }

        // 查询该日期和项目部的所有现有数据
        List<DataTunnelingVo> existingDataList = selectDataTunnelingByOperationDateAndProject(operationDate, projectDepartmentId);
        Map<Long, DataTunnelingVo> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(
                        DataTunnelingVo::getId,
                        data -> data
                ));

        // 分类处理数据
        List<DataTunneling> toInsert = new ArrayList<>();
        List<DataTunneling> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>();

        // 处理传入的数据
        for (DataTunnelingBatchDto batchData : batchDataList) {
            if (Boolean.TRUE.equals(batchData.getIsNew())) {
                // 新增数据
                DataTunneling newData = new DataTunneling();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if (batchData.getId() != null && existingDataMap.containsKey(batchData.getId())) {
                // 更新现有数据（根据ID判断）
                DataTunneling updateData = new DataTunneling();
                copyProperties(batchData, updateData);
                updateData.setId(batchData.getId());
                updateData.setUpdateBy(SecurityUtils.getUsername());
                updateData.setUpdateTime(DateUtils.getNowDate());
                toUpdate.add(updateData);

                // 从现有数据中移除，剩下的就是要删除的
                existingDataMap.remove(batchData.getId());
            }
        }

        // 剩余的现有数据需要删除
        toDelete.addAll(existingDataMap.keySet());

        int result = 0;

        // 执行删除
        if (!toDelete.isEmpty()) {
            result += dataTunnelingMapper.deleteDataTunnelingByIds(toDelete.toArray(new Long[0]));
        }

        // 执行新增
        if (!toInsert.isEmpty()) {
            result += dataTunnelingMapper.batchInsertDataTunneling(toInsert);
        }

        // 执行更新
        for (DataTunneling updateData : toUpdate) {
            result += dataTunnelingMapper.updateDataTunneling(updateData);
        }

        return result;
    }

    /**
     * 复制属性
     */
    private void copyProperties(DataTunnelingBatchDto source, DataTunneling target) {
        target.setOperationDate(source.getOperationDate());
        target.setProjectDepartmentId(source.getProjectDepartmentId());
        target.setWorkingFaceId(source.getWorkingFaceId());
        target.setStopeId(source.getStopeId());
        target.setWorkingPeriodId(source.getWorkingPeriodId());
        target.setTunnelingLength(source.getTunnelingLength());
        target.setTunnelingVolume(source.getTunnelingVolume());
        target.setWorkContent(source.getWorkContent());
        target.setRemarks(source.getRemarks());
    }
}
