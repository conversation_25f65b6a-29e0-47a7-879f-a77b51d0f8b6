<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaSubstationBasicInfoMapper">
    
    <resultMap type="KafkaSubstationBasicInfo" id="KafkaSubstationBasicInfoResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="substationCode"    column="substation_code"    />
        <result property="substationInstallationLocation"    column="substation_installation_location"    />
        <result property="xCoordinate"    column="x_coordinate"    />
        <result property="yCoordinate"    column="y_coordinate"    />
        <result property="zCoordinate"    column="z_coordinate"    />
    </resultMap>

    <sql id="selectKafkaSubstationBasicInfoVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, mine_name, data_upload_time, substation_code, substation_installation_location, x_coordinate, y_coordinate, z_coordinate from kafka_substation_basic_info
    </sql>

    <select id="selectKafkaSubstationBasicInfoList" parameterType="KafkaSubstationBasicInfo" resultMap="KafkaSubstationBasicInfoResult">
        <include refid="selectKafkaSubstationBasicInfoVo"/>
        <where>  
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="params.beginDataUploadTime != null and params.beginDataUploadTime != '' and params.endDataUploadTime != null and params.endDataUploadTime != ''"> and data_upload_time between #{params.beginDataUploadTime}::date and #{params.endDataUploadTime}::date</if>
            <if test="substationCode != null  and substationCode != ''"> and substation_code = #{substationCode}</if>
            <if test="substationInstallationLocation != null  and substationInstallationLocation != ''"> and substation_installation_location = #{substationInstallationLocation}</if>
            <if test="xCoordinate != null "> and x_coordinate = #{xCoordinate}</if>
            <if test="yCoordinate != null "> and y_coordinate = #{yCoordinate}</if>
            <if test="zCoordinate != null "> and z_coordinate = #{zCoordinate}</if>
        </where>
    </select>
    
    <select id="selectKafkaSubstationBasicInfoById" parameterType="Long" resultMap="KafkaSubstationBasicInfoResult">
        <include refid="selectKafkaSubstationBasicInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertKafkaSubstationBasicInfo" parameterType="KafkaSubstationBasicInfo" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_substation_basic_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null">file_encoding,</if>
            <if test="mineCode != null">mine_code,</if>
            <if test="mineName != null">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="substationCode != null">substation_code,</if>
            <if test="substationInstallationLocation != null">substation_installation_location,</if>
            <if test="xCoordinate != null">x_coordinate,</if>
            <if test="yCoordinate != null">y_coordinate,</if>
            <if test="zCoordinate != null">z_coordinate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null">#{fileEncoding},</if>
            <if test="mineCode != null">#{mineCode},</if>
            <if test="mineName != null">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="substationCode != null">#{substationCode},</if>
            <if test="substationInstallationLocation != null">#{substationInstallationLocation},</if>
            <if test="xCoordinate != null">#{xCoordinate},</if>
            <if test="yCoordinate != null">#{yCoordinate},</if>
            <if test="zCoordinate != null">#{zCoordinate},</if>
         </trim>
    </insert>

    <update id="updateKafkaSubstationBasicInfo" parameterType="KafkaSubstationBasicInfo">
        update kafka_substation_basic_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="mineName != null">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="substationCode != null">substation_code = #{substationCode},</if>
            <if test="substationInstallationLocation != null">substation_installation_location = #{substationInstallationLocation},</if>
            <if test="xCoordinate != null">x_coordinate = #{xCoordinate},</if>
            <if test="yCoordinate != null">y_coordinate = #{yCoordinate},</if>
            <if test="zCoordinate != null">z_coordinate = #{zCoordinate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaSubstationBasicInfoById" parameterType="Long">
        delete from kafka_substation_basic_info where id = #{id}
    </delete>

    <delete id="deleteKafkaSubstationBasicInfoByIds" parameterType="String">
        delete from kafka_substation_basic_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>