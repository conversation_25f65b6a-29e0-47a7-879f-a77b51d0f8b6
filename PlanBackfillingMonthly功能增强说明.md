# PlanBackfillingMonthly功能增强说明

## 概述

参照PlanDthMonthlyController的实现，为PlanBackfillingMonthlyController添加了关联字段的name字段输出、导出功能修改和批量保存月计划的接口实现。

## 修改内容

### 1. 创建PlanBackfillingMonthlyVo类

**文件路径**: `ruoyi-admin/src/main/java/com/ruoyi/lxbi/domain/response/PlanBackfillingMonthlyVo.java`

**功能**: 继承PlanBackfillingMonthly，添加关联字段的名称

```java
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanBackfillingMonthlyVo extends PlanBackfillingMonthly {

    /** 项目部门ID - 不导出 */
    @Excel(name = "项目部门ID", isExport = false)
    private Long projectDepartmentId;

    /** 工作面ID - 不导出 */
    @Excel(name = "工作面ID", isExport = false)
    private Long workingFaceId;

    /** 采场ID - 不导出 */
    @Excel(name = "采场ID", isExport = false)
    private Long stopeId;

    /** 项目部门名称 */
    @Excel(name = "项目部门名称", sort = 1, mergeByValue = true)
    private String projectDepartmentName;

    /** 工作面名称 */
    @Excel(name = "工作面名称", sort = 2, mergeByValue = true)
    private String workingFaceName;

    /** 采场名称 */
    @Excel(name = "采场名称", sort = 3, mergeByValue = true)
    private String stopeName;
}
```

### 2. 创建PlanBackfillingMonthlyBatchDto类

**文件路径**: `ruoyi-admin/src/main/java/com/ruoyi/lxbi/domain/request/PlanBackfillingMonthlyBatchDto.java`

**功能**: 用于批量操作的数据传输对象

```java
@Data
public class PlanBackfillingMonthlyBatchDto {
    private Long id;
    private String planDate;
    private Long projectDepartmentId;
    private Long workingFaceId;
    private Long stopeId;
    private BigDecimal fillingVolume;
    private String remark;
    private String operationType; // add, edit
}
```

### 3. 修改PlanBackfillingMonthlyController

**新增功能**:
- 关联查询：list和export方法返回包含名称字段的VO
- 批量保存：新增batch接口支持批量增删改

**关键修改**:
```java
// 新增import
import com.ruoyi.lxbi.domain.request.PlanBackfillingMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanBackfillingMonthlyVo;
import com.ruoyi.common.core.domain.R;

// 修改list方法返回类型
public TableDataInfo list(PlanBackfillingMonthly planBackfillingMonthly) {
    startPage();
    List<PlanBackfillingMonthlyVo> list = planBackfillingMonthlyService.selectPlanBackfillingMonthlyList(planBackfillingMonthly);
    return getDataTable(list);
}

// 修改export方法
public void export(HttpServletResponse response, PlanBackfillingMonthly planBackfillingMonthly) {
    List<PlanBackfillingMonthlyVo> list = planBackfillingMonthlyService.selectPlanBackfillingMonthlyList(planBackfillingMonthly);
    ExcelUtil<PlanBackfillingMonthlyVo> util = new ExcelUtil<PlanBackfillingMonthlyVo>(PlanBackfillingMonthlyVo.class);
    util.exportExcel(response, list, "充填月计划数据");
}

// 新增批量保存接口
@PostMapping("/batch")
public R<String> batchSave(@RequestBody List<PlanBackfillingMonthlyBatchDto> batchDataList) {
    try {
        int result = planBackfillingMonthlyService.batchSavePlanBackfillingMonthly(batchDataList);
        return R.ok("批量操作成功，共处理 " + result + " 条数据");
    } catch (Exception e) {
        return R.fail("批量操作失败：" + e.getMessage());
    }
}
```

### 4. 修改Service层

#### IPlanBackfillingMonthlyService接口
**新增方法**:
```java
// 修改返回类型
public List<PlanBackfillingMonthlyVo> selectPlanBackfillingMonthlyList(PlanBackfillingMonthly planBackfillingMonthly);

// 新增批量保存方法
public int batchSavePlanBackfillingMonthly(List<PlanBackfillingMonthlyBatchDto> batchDataList);
```

#### PlanBackfillingMonthlyServiceImpl实现类
**核心功能**:
- **关联查询**: 调用Mapper的关联查询方法
- **批量保存**: 实现完整的批量增删改逻辑

**批量保存逻辑**:
```java
@Override
@Transactional
public int batchSavePlanBackfillingMonthly(List<PlanBackfillingMonthlyBatchDto> batchDataList) {
    // 1. 验证数据（同一月份）
    // 2. 查询现有数据
    // 3. 分类处理：新增、更新、删除
    // 4. 执行批量操作
    // 5. 返回处理结果
}
```

### 5. 修改Mapper层

#### PlanBackfillingMonthlyMapper接口
**新增方法**:
```java
// 修改返回类型
public List<PlanBackfillingMonthlyVo> selectPlanBackfillingMonthlyList(PlanBackfillingMonthly planBackfillingMonthly);

// 新增方法
public List<PlanBackfillingMonthly> selectPlanBackfillingMonthlyListForBatch(PlanBackfillingMonthly planBackfillingMonthly);
public int batchInsertPlanBackfillingMonthly(List<PlanBackfillingMonthly> planBackfillingMonthlyList);
```

### 6. 修改XML映射文件

#### 关联查询SQL
```xml
<sql id="selectPlanBackfillingMonthlyVo">
    select pbm.*, bpd.project_department_name, bwf.working_face_name, bs.stope_name
    from plan_backfilling_monthly pbm
    left join base_project_department bpd on pbm.project_department_id = bpd.project_department_id
    left join base_working_face bwf on pbm.working_face_id = bwf.working_face_id
    left join base_stope bs on pbm.stope_id = bs.stope_id
</sql>
```

#### 主查询方法
```xml
<select id="selectPlanBackfillingMonthlyList" parameterType="PlanBackfillingMonthly" resultType="com.ruoyi.lxbi.domain.response.PlanBackfillingMonthlyVo">
    <include refid="selectPlanBackfillingMonthlyVo"/>
    <where>  
        <if test="projectDepartmentId != null "> and pbm.project_department_id = #{projectDepartmentId}</if>
        <if test="workingFaceId != null "> and pbm.working_face_id = #{workingFaceId}</if>
        <if test="stopeId != null "> and pbm.stope_id = #{stopeId}</if>
        <if test="planDate != null  and planDate != ''"> and pbm.plan_date = #{planDate}</if>
    </where>
</select>
```

#### 批量操作相关SQL
```xml
<!-- 批量查询（用于批量操作） -->
<select id="selectPlanBackfillingMonthlyListForBatch" parameterType="PlanBackfillingMonthly" resultMap="PlanBackfillingMonthlyResult">
    select id, project_department_id, working_face_id, stope_id, filling_volume, plan_date, create_by, create_time, update_by, update_time from plan_backfilling_monthly
    <where>  
        <!-- 查询条件... -->
    </where>
</select>

<!-- 批量插入 -->
<insert id="batchInsertPlanBackfillingMonthly" parameterType="java.util.List">
    insert into plan_backfilling_monthly (project_department_id, working_face_id, stope_id, filling_volume, plan_date, create_by, create_time, update_by, update_time)
    values
    <foreach collection="list" item="item" separator=",">
        (#{item.projectDepartmentId}, #{item.workingFaceId}, #{item.stopeId}, #{item.fillingVolume}, #{item.planDate}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
    </foreach>
</insert>
```

### 7. 优化Excel导出

**移除父类Excel注解**:
```java
// PlanBackfillingMonthly.java 中移除@Excel注解
/** 项目部门ID */
private Long projectDepartmentId;

/** 工作面ID */
private Long workingFaceId;

/** 采场ID */
private Long stopeId;
```

**VO中重新定义**:
- ID字段设置为不导出（`isExport = false`）
- 名称字段设置为优先显示并支持合并（`mergeByValue = true`）

## 功能特点

### 1. 关联查询
- **自动关联**: 查询时自动关联项目部门、工作面、采场信息
- **LEFT JOIN**: 使用左连接，确保数据完整性
- **字段映射**: 自动映射名称字段到VO

### 2. Excel导出优化
- **ID字段隐藏**: 所有ID字段设置为不导出
- **名称字段优先**: 名称字段排在前列
- **层次化合并**: 支持按部门、工作面、采场进行层次化单元格合并

### 3. 批量保存功能
- **数据验证**: 验证批量数据必须为同一月份
- **智能处理**: 自动识别新增、更新、删除操作
- **事务保证**: 使用@Transactional确保数据一致性
- **错误处理**: 完善的异常处理和错误信息返回

### 4. 操作类型
- **add**: 新增数据
- **edit**: 更新现有数据
- **自动删除**: 未在传入数据中的现有数据会被自动删除

## 接口使用示例

### 查询接口
```javascript
// 分页查询（返回包含名称字段）
GET /plan/planBackfillingMonthly/list

// Excel导出（包含名称字段，支持合并）
POST /plan/planBackfillingMonthly/export
```

### 批量保存接口
```javascript
POST /plan/planBackfillingMonthly/batch
Content-Type: application/json

[
    {
        "planDate": "2025-01",
        "projectDepartmentId": 1,
        "workingFaceId": 2,
        "stopeId": 3,
        "fillingVolume": 100.5,
        "remark": "正常充填",
        "operationType": "add"
    },
    {
        "id": 10,
        "planDate": "2025-01",
        "projectDepartmentId": 1,
        "workingFaceId": 2,
        "stopeId": 4,
        "fillingVolume": 120.0,
        "remark": "更新数据",
        "operationType": "edit"
    }
]
```

### 返回数据格式
```json
{
    "id": 1,
    "projectDepartmentId": 1,
    "workingFaceId": 2,
    "stopeId": 3,
    "projectDepartmentName": "项目部门A",
    "workingFaceName": "工作面1",
    "stopeName": "采场1",
    "fillingVolume": 100.5,
    "planDate": "2025-01",
    "remark": "正常充填"
}
```

## 总结

通过这次修改，PlanBackfillingMonthlyController现在具备了：

1. ✅ **完整的关联查询**: 自动获取项目部门、工作面、采场名称
2. ✅ **优化的Excel导出**: 显示名称字段，支持层次化合并
3. ✅ **批量保存功能**: 支持同一月份数据的批量增删改
4. ✅ **完整实现链路**: 从Controller到Mapper的完整实现
5. ✅ **参考标准**: 完全参照PlanDthMonthlyController的实现方式

这些功能大大提升了充填月计划管理的效率和用户体验，特别是批量操作功能可以显著减少用户的操作步骤。
