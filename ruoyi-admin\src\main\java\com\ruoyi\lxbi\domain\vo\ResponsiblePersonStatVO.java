package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 责任人隐患统计VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResponsiblePersonStatVO {
    
    /**
     * 责任人
     */
    private String person;
    
    /**
     * 隐患总数
     */
    private Long totalCount;
    
    /**
     * 已完成数
     */
    private Long completedCount;
    
    /**
     * 超期数
     */
    private Long overdueCount;
    
    /**
     * 完成率
     */
    private BigDecimal completionRate;
}
