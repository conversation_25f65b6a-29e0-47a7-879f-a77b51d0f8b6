package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;

/**
 * 项目部门配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface IBaseProjectDepartmentService 
{
    /**
     * 查询项目部门配置
     * 
     * @param projectDepartmentId 项目部门配置主键
     * @return 项目部门配置
     */
    public BaseProjectDepartment selectBaseProjectDepartmentByProjectDepartmentId(Long projectDepartmentId);

    /**
     * 查询项目部门配置列表
     * 
     * @param baseProjectDepartment 项目部门配置
     * @return 项目部门配置集合
     */
    public List<BaseProjectDepartment> selectBaseProjectDepartmentList(BaseProjectDepartment baseProjectDepartment);

    /**
     * 新增项目部门配置
     * 
     * @param baseProjectDepartment 项目部门配置
     * @return 结果
     */
    public int insertBaseProjectDepartment(BaseProjectDepartment baseProjectDepartment);

    /**
     * 修改项目部门配置
     * 
     * @param baseProjectDepartment 项目部门配置
     * @return 结果
     */
    public int updateBaseProjectDepartment(BaseProjectDepartment baseProjectDepartment);

    /**
     * 批量删除项目部门配置
     * 
     * @param projectDepartmentIds 需要删除的项目部门配置主键集合
     * @return 结果
     */
    public int deleteBaseProjectDepartmentByProjectDepartmentIds(Long[] projectDepartmentIds);

    /**
     * 删除项目部门配置信息
     * 
     * @param projectDepartmentId 项目部门配置主键
     * @return 结果
     */
    public int deleteBaseProjectDepartmentByProjectDepartmentId(Long projectDepartmentId);
}
