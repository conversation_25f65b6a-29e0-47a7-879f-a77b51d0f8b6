package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.BaseOrePass;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.domain.BaseWorkingPeriod;
import com.ruoyi.lxbi.domain.DataOrepassOperation;
import com.ruoyi.lxbi.domain.excel.DataOrepassOperationImport;
import com.ruoyi.lxbi.service.IBaseOrePassService;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;
import com.ruoyi.lxbi.service.IBaseWorkingPeriodService;
import com.ruoyi.lxbi.service.IDataOrepassOperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 溜井运行数据导入处理器
 *
 * <AUTHOR>
 */
@Component
public class DataOrepassOperationImportHandler extends ExcelImportHandler<DataOrepassOperationImport> {

    @Autowired
    private IBaseWorkingPeriodService baseWorkingPeriodService;

    @Autowired
    private IBaseProjectDepartmentService baseProjectDepartmentService;

    @Autowired
    private IBaseOrePassService baseOrePassService;

    @Autowired
    private IDataOrepassOperationService dataOrepassOperationService;

    @Override
    protected Class<DataOrepassOperationImport> getEntityClass() {
        return DataOrepassOperationImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置作业时段选项
        List<ExcelOptionInfo> workingPeriods = new ArrayList<>();
        BaseWorkingPeriod queryParam = new BaseWorkingPeriod();
        queryParam.setStatus(1L); // 只查询启用状态的作业时段
        List<BaseWorkingPeriod> periodList = baseWorkingPeriodService.selectBaseWorkingPeriodList(queryParam);

        for (BaseWorkingPeriod period : periodList) {
            workingPeriods.add(new ExcelOptionInfo(
                    period.getWorkingPeriodId(),
                    period.getWorkingPeriodName()
            ));
        }
        context.setOptions("workingPeriod", workingPeriods);

        // 设置项目部门选项
        List<ExcelOptionInfo> projectDepartments = new ArrayList<>();
        BaseProjectDepartment deptQueryParam = new BaseProjectDepartment();
        List<BaseProjectDepartment> deptList = baseProjectDepartmentService.selectBaseProjectDepartmentList(deptQueryParam);

        for (BaseProjectDepartment dept : deptList) {
            projectDepartments.add(new ExcelOptionInfo(
                    dept.getProjectDepartmentId(),
                    dept.getProjectDepartmentName()
            ));
        }
        context.setOptions("projectDepartment", projectDepartments);

        // 设置溜井选项
        List<ExcelOptionInfo> orePasses = new ArrayList<>();
        BaseOrePass orePassQueryParam = new BaseOrePass();
        List<BaseOrePass> orePassList = baseOrePassService.selectBaseOrePassList(orePassQueryParam);

        for (BaseOrePass orePass : orePassList) {
            orePasses.add(new ExcelOptionInfo(
                    orePass.getOrePassId(),
                    orePass.getOrePassName()
            ));
        }
        context.setOptions("orePass", orePasses);
    }

    @Override
    protected void validateData(ExcelDataInfo<DataOrepassOperationImport> dataInfo, ExcelImportContext context) {
        DataOrepassOperationImport data = dataInfo.getData();

        // 验证作业日期不能为未来日期
        if (data.getOperationDate() != null && data.getOperationDate().after(new Date())) {
            dataInfo.addError("operationDate", "作业日期不能是未来日期");
        }

        // 验证溜放趟数
        if (data.getTrips() != null && data.getTrips() < 0) {
            dataInfo.addError("trips", "溜放趟数不能为负数");
        }

        // 验证溜放矿石量
        if (data.getOreTons() != null && data.getOreTons() < 0) {
            dataInfo.addError("oreTons", "溜放矿石量不能为负数");
        }
    }

    @Override
    protected void saveData(DataOrepassOperationImport data, ExcelImportContext context) {
        // 转换为实体对象
        DataOrepassOperation entity = new DataOrepassOperation();
        entity.setProjectDepartmentId(data.getProjectDepartmentId());
        entity.setOrePassId(data.getOrePassId());
        entity.setWorkingPeriodId(data.getWorkingPeriodId());
        entity.setOperationDate(data.getOperationDate());
        entity.setTrips(data.getTrips());
        entity.setOreTons(data.getOreTons());
        
        // 保存到数据库
        dataOrepassOperationService.insertDataOrepassOperation(entity);
    }

    @Override
    public List<DataOrepassOperationImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("溜井运行数据验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("溜井运行数据导入完成，总行数: " + ctx.getTotalRows());
    }
}
