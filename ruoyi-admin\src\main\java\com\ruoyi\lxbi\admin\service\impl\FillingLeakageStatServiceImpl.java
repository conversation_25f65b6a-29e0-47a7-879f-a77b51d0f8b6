package com.ruoyi.lxbi.admin.service.impl;

import com.ruoyi.lxbi.admin.service.IFillingLeakageStatService;
import com.ruoyi.lxbi.domain.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 充填漏浆检测统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class FillingLeakageStatServiceImpl implements IFillingLeakageStatService {

    /**
     * 获取充填漏浆检测概览统计
     */
    @Override
    public FillingLeakageOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate) {
        try {
            log.info("获取充填漏浆检测概览统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // TODO: 实际实现时需要从数据库查询真实数据
            // 这里先返回模拟数据
            FillingLeakageOverviewVO overview = new FillingLeakageOverviewVO();
            overview.setTotalAlarmCount(200L);
            overview.setAreaIntrusionCount(200L);
            overview.setTemperatureDifferenceAlarmCount(200L);
            overview.setStartDate(startDate);
            overview.setEndDate(endDate);
            overview.setPeriod(viewType);

            return overview;
        } catch (Exception e) {
            log.error("获取充填漏浆检测概览统计失败", e);
            throw new RuntimeException("获取充填漏浆检测概览统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取充填漏浆检测趋势统计
     */
    @Override
    public List<FillingLeakageTrendVO> getAlarmTrend(String viewType, String startDate, String endDate) {
        try {
            log.info("获取充填漏浆检测趋势统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            List<FillingLeakageTrendVO> trendList = new ArrayList<>();

            // 根据viewType返回不同维度的趋势数据
            switch (viewType.toLowerCase()) {
                case "daily":
                    // 日维度：X轴为小时（0-23小时）
                    for (int hour = 0; hour < 24; hour++) {
                        FillingLeakageTrendVO trend = new FillingLeakageTrendVO();
                        trend.setDate(startDate + " " + String.format("%02d:00:00", hour));
                        trend.setShortDate(String.format("%02d:00", hour));

                        // 模拟小时级别的报警数量分布
                        Long alarmCount = generateHourlyAlarmCount(hour);
                        trend.setAlarmTrendCount(alarmCount);
                        trend.setAreaIntrusionCount((long) (alarmCount * 0.6)); // 60%为区域入侵
                        trend.setTemperatureDifferenceCount((long) (alarmCount * 0.4)); // 40%为温差异常

                        trendList.add(trend);
                    }
                    break;

                case "weekly":
                    // 周维度：X轴为日期（7天）
                    LocalDate weekStart = LocalDate.parse(startDate);
                    for (int day = 0; day < 7; day++) {
                        LocalDate currentDate = weekStart.plusDays(day);
                        FillingLeakageTrendVO trend = new FillingLeakageTrendVO();
                        trend.setDate(currentDate.toString());
                        trend.setShortDate(currentDate.format(DateTimeFormatter.ofPattern("MM-dd")));

                        // 模拟日级别的报警数量分布
                        Long alarmCount = generateDailyAlarmCount(day);
                        trend.setAlarmTrendCount(alarmCount);
                        trend.setAreaIntrusionCount((long) (alarmCount * 0.65));
                        trend.setTemperatureDifferenceCount((long) (alarmCount * 0.35));

                        trendList.add(trend);
                    }
                    break;

                case "monthly":
                    // 月维度：X轴为日期（按月内日期）
                    LocalDate monthStart = LocalDate.parse(startDate);
                    LocalDate monthEnd = LocalDate.parse(endDate);
                    LocalDate currentDate = monthStart;

                    while (!currentDate.isAfter(monthEnd)) {
                        FillingLeakageTrendVO trend = new FillingLeakageTrendVO();
                        trend.setDate(currentDate.toString());
                        trend.setShortDate(currentDate.format(DateTimeFormatter.ofPattern("MM-dd")));

                        // 模拟日级别的报警数量分布
                        Long alarmCount = generateDailyAlarmCount(currentDate.getDayOfMonth() % 7);
                        trend.setAlarmTrendCount(alarmCount);
                        trend.setAreaIntrusionCount((long) (alarmCount * 0.7));
                        trend.setTemperatureDifferenceCount((long) (alarmCount * 0.3));

                        trendList.add(trend);
                        currentDate = currentDate.plusDays(1);
                    }
                    break;

                default:
                    // 默认使用原有逻辑
                    List<String> dateList = generateDateSequence(viewType, startDate, endDate);
                    for (int i = 0; i < dateList.size(); i++) {
                        String date = dateList.get(i);
                        FillingLeakageTrendVO trend = new FillingLeakageTrendVO();
                        trend.setDate(date);
                        trend.setShortDate(formatShortDate(date));

                        int baseValue = 100 - (i * 10);
                        trend.setAlarmTrendCount((long) Math.max(baseValue, 20));
                        trend.setAreaIntrusionCount((long) Math.max(baseValue - 5, 15));
                        trend.setTemperatureDifferenceCount((long) Math.max(baseValue - 10, 10));

                        trendList.add(trend);
                    }
            }

            return trendList;
        } catch (Exception e) {
            log.error("获取充填漏浆检测趋势统计失败", e);
            throw new RuntimeException("获取充填漏浆检测趋势统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取充填漏浆检测位置分布统计 (雷达图数据)
     */
    @Override
    public List<FillingLeakageLocationDistributionVO> getLocationDistribution(String viewType, String startDate, String endDate) {
        try {
            log.info("获取充填漏浆检测位置分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // TODO: 实际实现时需要从数据库查询真实数据
            // 这里先返回模拟数据
            List<FillingLeakageLocationDistributionVO> distributionList = new ArrayList<>();
            
            // 模拟雷达图数据 - 报警点位分布
            // 根据图片中的雷达图布局，模拟各个深度层级的报警点
            distributionList.add(new FillingLeakageLocationDistributionVO("一号回风井井口", "-992m", 208L, 0.0, 1.0, "LOC_001"));
            distributionList.add(new FillingLeakageLocationDistributionVO("", "-960m", 230L, -0.8, 0.6, "LOC_002"));
            distributionList.add(new FillingLeakageLocationDistributionVO("", "-960-840m", 100L, -1.0, 0.0, "LOC_003"));
            distributionList.add(new FillingLeakageLocationDistributionVO("", "-840m", 119L, -0.8, -0.6, "LOC_004"));
            distributionList.add(new FillingLeakageLocationDistributionVO("", "-480m", 200L, 0.0, -1.0, "LOC_005"));
            distributionList.add(new FillingLeakageLocationDistributionVO("", "-1130m", 110L, 0.8, -0.6, "LOC_006"));
            distributionList.add(new FillingLeakageLocationDistributionVO("", "-1060m", 135L, 1.0, 0.0, "LOC_007"));
            distributionList.add(new FillingLeakageLocationDistributionVO("", "-1020-1060m", 71L, 0.8, 0.6, "LOC_008"));
            distributionList.add(new FillingLeakageLocationDistributionVO("", "-1020m", 140L, 0.6, 0.8, "LOC_009"));
            distributionList.add(new FillingLeakageLocationDistributionVO("", "-1200m", 1200L, 0.2, 1.0, "LOC_010"));

            return distributionList;
        } catch (Exception e) {
            log.error("获取充填漏浆检测位置分布统计失败", e);
            throw new RuntimeException("获取充填漏浆检测位置分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 根据视图类型生成日期序列
     * 
     * @param viewType 视图类型（day/week/month）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期序列
     */
    private List<String> generateDateSequence(String viewType, String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            // 根据视图类型确定步长
            int stepDays = 1;
            switch (viewType.toLowerCase()) {
                case "week":
                    stepDays = 7;
                    break;
                case "month":
                    stepDays = 30;
                    break;
                default: // day
                    stepDays = 1;
                    break;
            }
            
            LocalDate current = start;
            while (!current.isAfter(end) && dateList.size() < 6) { // 限制为6个点，符合图片中的趋势图
                dateList.add(current.toString());
                current = current.plusDays(stepDays);
            }
            
            // 如果没有生成任何日期，至少返回结束日期
            if (dateList.isEmpty()) {
                dateList.add(endDate);
            }
            
        } catch (Exception e) {
            log.warn("生成日期序列失败，使用默认日期: {}", e.getMessage());
            // 如果解析失败，返回最近6天的日期
            LocalDate today = LocalDate.now();
            for (int i = 5; i >= 0; i--) {
                dateList.add(today.minusDays(i).toString());
            }
        }
        
        return dateList;
    }

    /**
     * 格式化短日期
     *
     * @param date 完整日期
     * @return 短日期格式 (MM-dd)
     */
    private String formatShortDate(String date) {
        try {
            LocalDate localDate = LocalDate.parse(date);
            return localDate.format(DateTimeFormatter.ofPattern("MM-dd"));
        } catch (Exception e) {
            log.warn("格式化短日期失败: {}", date);
            return date;
        }
    }

    /**
     * 生成小时级别的模拟报警数量
     * 模拟一天中不同时段的报警分布规律
     */
    private Long generateHourlyAlarmCount(int hour) {
        Random random = new Random();
        // 工作时间(8-18点)报警较多，夜间较少
        if (hour >= 8 && hour <= 18) {
            return (long) (15 + random.nextInt(10)); // 15-25个报警
        } else if (hour >= 19 && hour <= 23) {
            return (long) (8 + random.nextInt(7)); // 8-15个报警
        } else {
            return (long) (2 + random.nextInt(6)); // 2-8个报警
        }
    }

    /**
     * 生成日级别的模拟报警数量
     * 模拟工作日和周末的报警分布差异
     */
    private Long generateDailyAlarmCount(int dayIndex) {
        Random random = new Random();
        // 周末(0=周日, 6=周六)报警较少，工作日较多
        if (dayIndex == 0 || dayIndex == 6) { // 周末
            return (long) (50 + random.nextInt(30)); // 50-80个报警
        } else { // 工作日
            return (long) (80 + random.nextInt(40)); // 80-120个报警
        }
    }
}
