package com.ruoyi.common.utils.excel;

import cn.idev.excel.metadata.Head;
import cn.idev.excel.write.handler.SheetWriteHandler;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.common.annotation.ExcelSelected;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.CellReference;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Excel增强处理器
 * 支持时间格式设置和隐藏表格下拉选项
 */
public class ExcelSelectedHandler implements SheetWriteHandler {

    /**
     * 字段选项映射
     */
    private final Map<String, List<ExcelOptionInfo>> optionsMap;
    private final Map<String, String> optionSourceFormulaMap = new HashMap<>();
    private static final String HIDDEN_SHEET_PREFIX = "_opt_";
    private static final int MAX_SHEET_NAME_LENGTH = 31;
    private static final Pattern INVALID_SHEET_CHARS_PATTERN = Pattern.compile("[\\\\/?*\\[\\]:]");


    /**
     * 最大行数
     */
    private final int maxRows;

    public ExcelSelectedHandler(Map<String, List<ExcelOptionInfo>> optionsMap,
                                int maxRows) {
        this.optionsMap = optionsMap;
        this.maxRows = maxRows;
    }

    private String sanitizeSheetName(String name) {
        String sanitized = INVALID_SHEET_CHARS_PATTERN.matcher(name).replaceAll("_");
        // Ensure prefix + sanitized name fits within Excel's limit
        int availableLength = MAX_SHEET_NAME_LENGTH - HIDDEN_SHEET_PREFIX.length();
        if (sanitized.length() > availableLength) {
            sanitized = sanitized.substring(0, availableLength);
        }
        return HIDDEN_SHEET_PREFIX + sanitized;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet currentSheet = writeSheetHolder.getSheet();
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        DataValidationHelper helper = currentSheet.getDataValidationHelper();
        for (int i = 0; i < writeSheetHolder.getExcelWriteHeadProperty().getHeadMap().values().size(); i++) {
            Sheet sheet = writeSheetHolder.getSheet();
            CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
            cellStyle.setDataFormat((short) 49);
            sheet.setDefaultColumnStyle(i, cellStyle);
        }

        for (Head head : writeSheetHolder.getExcelWriteHeadProperty().getHeadMap().values()) {
            Field field = head.getField();
            if (field == null) continue;
            ExcelSelected annotation = field.getAnnotation(ExcelSelected.class);
            if (annotation == null) continue;

            String optionKey = StringUtils.isBlank(annotation.optionKey()) ? field.getName() : annotation.optionKey();
            List<ExcelOptionInfo> options = optionsMap.get(optionKey);

            DataValidationConstraint constraint;
            if (options != null && !options.isEmpty() && annotation.link() == ExcelSelected.FieldLinkType.LABEL) {
                if (options.size() <= 20) {
                    String[] optionArray = options.stream()
                            .map(ExcelOptionInfo::getLabel)
                            .toArray(String[]::new);
                    constraint = helper.createExplicitListConstraint(optionArray);
                } else {
                    String formulaRange;
                    if (optionSourceFormulaMap.containsKey(optionKey)) {
                        formulaRange = optionSourceFormulaMap.get(optionKey);
                    } else {
                        String hiddenSheetName = sanitizeSheetName(optionKey);
                        Sheet hiddenSheet = workbook.getSheet(hiddenSheetName);

                        if (hiddenSheet == null) {
                            hiddenSheet = workbook.createSheet(hiddenSheetName);
                            // Populate the first column (A) of this new hidden sheet
                            for (int i = 0; i < options.size(); i++) {
                                Row row = hiddenSheet.getRow(i);
                                if (row == null) {
                                    row = hiddenSheet.createRow(i);
                                }
                                Cell cell = row.createCell(0); // Column A
                                cell.setCellValue(options.get(i).getLabel());
                            }
                            // Hide this newly created sheet
                            int hiddenSheetIndex = workbook.getSheetIndex(hiddenSheet);
                            if (hiddenSheetIndex != -1) {
                                workbook.setSheetHidden(hiddenSheetIndex, true);
                            }
                        }
                        // Formula always refers to column A of the specific hidden sheet
                        String colStr = CellReference.convertNumToColString(0); // Column A
                        formulaRange = String.format("'%s'!$%s$1:$%s$%d", hiddenSheetName, colStr, colStr, options.size());
                        optionSourceFormulaMap.put(optionKey, formulaRange);
                    }
                    constraint = helper.createFormulaListConstraint(formulaRange);
                }
            } else if (StringUtils.isNotBlank(annotation.prompt()) || StringUtils.isNotBlank(annotation.error())) {
                constraint = helper.createTextLengthConstraint(DataValidationConstraint.OperatorType.BETWEEN, "0", "32767");
            } else {
                continue;
            }

            int mainSheetColIndex = head.getColumnIndex();
            CellRangeAddressList range = new CellRangeAddressList(1, maxRows, mainSheetColIndex, mainSheetColIndex);

            DataValidation validation = helper.createValidation(constraint, range);
            validation.setEmptyCellAllowed(true);
            validation.setShowErrorBox(true);
            validation.createErrorBox("错误", annotation.error());

            if (StringUtils.isNotBlank(annotation.prompt())) {
                validation.setShowPromptBox(true);
                validation.createPromptBox("提示", annotation.prompt());
            }

            currentSheet.addValidationData(validation);
        }

    }

}
