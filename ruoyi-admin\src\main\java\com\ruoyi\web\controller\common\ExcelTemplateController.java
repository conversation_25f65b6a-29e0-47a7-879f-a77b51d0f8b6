package com.ruoyi.web.controller.common;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.excel.ExcelTemplateInfo;
import com.ruoyi.common.core.excel.ExcelImportService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Excel模板管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common/excel/template")
public class ExcelTemplateController extends BaseController {

    @Autowired
    private ExcelImportService excelImportService;

    /**
     * 获取模板列表（用于前端选择）
     */
    @GetMapping("/list")
    public R<List<TemplateOption>> getTemplateList() {
        Map<String, ExcelTemplateInfo> templates = excelImportService.getAllTemplates();

        List<TemplateOption> options = new ArrayList<>();
        for (ExcelTemplateInfo template : templates.values()) {
            TemplateOption option = new TemplateOption();
            option.setKey(template.getKey());
            option.setName(template.getName());
            option.setDescription(template.getDescription());
            option.setMaxRows(template.getMaxRows());
            options.add(option);
        }

        return R.ok(options);
    }

    /**
     * 模板选项DTO
     */
    @Data
    public static class TemplateOption {
        private String key;
        private String name;
        private String description;
        private Integer maxRows;
    }
}
