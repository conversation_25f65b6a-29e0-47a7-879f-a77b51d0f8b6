package com.ruoyi.lxbi.admin.service;

import com.ruoyi.lxbi.domain.vo.*;

import java.util.List;

/**
 * 设备安全统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IEquipmentSafetyStatService {

    /**
     * 获取设备安全概览统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 设备安全概览统计
     */
    EquipmentSafetyOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取设备状态分布统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 设备状态分布统计列表
     */
    List<EquipmentStatusDistributionVO> getStatusDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取设备报警趋势统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 设备报警趋势统计列表
     */
    List<EquipmentAlarmTrendVO> getAlarmTrend(String viewType, String startDate, String endDate);

    /**
     * 获取设备故障记录列表
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 设备故障记录列表
     */
    List<EquipmentFaultRecordVO> getFaultRecords(String viewType, String startDate, String endDate);

    /**
     * 获取设备系统分布统计 (雷达图数据)
     *
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 设备系统分布统计列表
     */
    List<EquipmentSystemDistributionVO> getSystemDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取设备监测次数统计
     *
     * @param viewType 视图类型 (daily/weekly/monthly)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 设备监测次数统计列表
     */
    List<EquipmentMonitoringFrequencyVO> getMonitoringFrequency(String viewType, String startDate, String endDate);
}
