package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.domain.response.BaseStopeVo;

/**
 * 采场配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface IBaseStopeService 
{
    /**
     * 查询采场配置
     * 
     * @param stopeId 采场配置主键
     * @return 采场配置
     */
    public BaseStope selectBaseStopeByStopeId(Long stopeId);

    /**
     * 查询采场配置列表
     * 
     * @param baseStope 采场配置
     * @return 采场配置集合
     */
    public List<BaseStopeVo> selectBaseStopeList(BaseStope baseStope);
    /**
     * 查询采场配置列表
     *
     * @param baseStope 采场配置
     * @return 采场配置集合
     */
    public List<BaseStope> selectBaseStopeListAll(BaseStope baseStope);

    /**
     * 新增采场配置
     * 
     * @param baseStope 采场配置
     * @return 结果
     */
    public int insertBaseStope(BaseStope baseStope);

    /**
     * 修改采场配置
     * 
     * @param baseStope 采场配置
     * @return 结果
     */
    public int updateBaseStope(BaseStope baseStope);

    /**
     * 批量删除采场配置
     * 
     * @param stopeIds 需要删除的采场配置主键集合
     * @return 结果
     */
    public int deleteBaseStopeByStopeIds(Long[] stopeIds);

    /**
     * 删除采场配置信息
     * 
     * @param stopeId 采场配置主键
     * @return 结果
     */
    public int deleteBaseStopeByStopeId(Long stopeId);
}
