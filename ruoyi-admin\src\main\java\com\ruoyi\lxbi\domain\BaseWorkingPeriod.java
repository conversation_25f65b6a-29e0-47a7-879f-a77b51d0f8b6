package com.ruoyi.lxbi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;
import java.util.Date;

/**
 * 作业时段配置对象 base_working_period
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseWorkingPeriod extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 作业时段ID */
    private Long workingPeriodId;

    /** 作业时段名称 */
    @Excel(name = "作业时段名称")
    private String workingPeriodName;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 作业时段开始时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "作业时段开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 作业时段结束时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "作业时段结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 是否删除 */
    private Long isDelete;

}
