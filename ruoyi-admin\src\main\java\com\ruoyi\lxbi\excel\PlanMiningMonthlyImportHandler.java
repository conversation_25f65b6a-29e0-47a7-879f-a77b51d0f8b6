package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.domain.PlanMiningMonthly;
import com.ruoyi.lxbi.domain.excel.PlanMiningMonthlyImport;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;
import com.ruoyi.lxbi.service.IPlanMiningMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 采矿整体月计划导入处理器
 *
 * <AUTHOR>
 */
@Component
public class PlanMiningMonthlyImportHandler extends ExcelImportHandler<PlanMiningMonthlyImport> {

    @Autowired
    private IBaseProjectDepartmentService baseProjectDepartmentService;

    @Autowired
    private IPlanMiningMonthlyService planMiningMonthlyService;

    @Override
    protected Class<PlanMiningMonthlyImport> getEntityClass() {
        return PlanMiningMonthlyImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置项目部门选项
        List<ExcelOptionInfo> projectDepartments = new ArrayList<>();
        BaseProjectDepartment deptQueryParam = new BaseProjectDepartment();
        List<BaseProjectDepartment> deptList = baseProjectDepartmentService.selectBaseProjectDepartmentList(deptQueryParam);

        for (BaseProjectDepartment dept : deptList) {
            projectDepartments.add(new ExcelOptionInfo(
                    dept.getProjectDepartmentId(),
                    dept.getProjectDepartmentName()
            ));
        }
        context.setOptions("projectDepartment", projectDepartments);
    }

    @Override
    protected void validateData(ExcelDataInfo<PlanMiningMonthlyImport> dataInfo, ExcelImportContext context) {
        PlanMiningMonthlyImport data = dataInfo.getData();

        // 验证计划月份格式
        if (data.getPlanDate() != null) {
            Pattern pattern = Pattern.compile("^\\d{4}\\d{2}$");
            if (!pattern.matcher(data.getPlanDate()).matches()) {
                dataInfo.addError("planDate", "计划月份格式不正确，应为yyyyMM格式");
            }
        }

        // 验证掘进米数
        if (data.getDriftMeter() != null && data.getDriftMeter().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("driftMeter", "掘进米数不能为负数");
        }

        // 验证原矿量
        if (data.getRawOreVolume() != null && data.getRawOreVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("rawOreVolume", "原矿量不能为负数");
        }

        // 验证支护米数
        if (data.getSupportMeter() != null && data.getSupportMeter().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("supportMeter", "支护米数不能为负数");
        }

        // 验证充填量
        if (data.getFillingVolume() != null && data.getFillingVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("fillingVolume", "充填量不能为负数");
        }

        // 验证潜孔米数
        if (data.getDthMeter() != null && data.getDthMeter().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("dthMeter", "潜孔米数不能为负数");
        }

        // 验证中深孔米数
        if (data.getDeepHoleMeter() != null && data.getDeepHoleMeter().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("deepHoleMeter", "中深孔米数不能为负数");
        }

        // 验证出矿量
        if (data.getOreOutputVolume() != null && data.getOreOutputVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("oreOutputVolume", "出矿量不能为负数");
        }
    }

    @Override
    protected void saveData(PlanMiningMonthlyImport data, ExcelImportContext context) {
        // 转换为实体对象
        PlanMiningMonthly entity = new PlanMiningMonthly();
        entity.setProjectDepartmentId(data.getProjectDepartmentId());
        entity.setDriftMeter(data.getDriftMeter());
        entity.setRawOreVolume(data.getRawOreVolume());
        entity.setSupportMeter(data.getSupportMeter());
        entity.setFillingVolume(data.getFillingVolume());
        entity.setDthMeter(data.getDthMeter());
        entity.setDeepHoleMeter(data.getDeepHoleMeter());
        entity.setOreOutputVolume(data.getOreOutputVolume());
        entity.setPlanDate(data.getPlanDate());
        
        // 保存到数据库
        planMiningMonthlyService.insertPlanMiningMonthly(entity);
    }

    @Override
    public List<PlanMiningMonthlyImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("采矿整体月计划验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("采矿整体月计划导入完成，总行数: " + ctx.getTotalRows());
    }
}
