package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.response.DataSupportTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportDepartmentWithPlanStats;

import java.util.List;

/**
 * 支护数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IDataSupportStatsService {

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    public List<DataSupportTotalWithPlanStats> selectTotalWithPlanStatsList(DataSupportStatsRequest request, String viewType);

    /**
     * 查询项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合（含计划量）
     */
    public List<DataSupportDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataSupportStatsRequest request, String viewType);
}
