# 地表监测接口补全说明

## 概述

基于新网关平台的OpenAPI文档，我们补全了地表监测系统的接口，提供了更加完整和实用的功能。

## 新增接口列表

### 1. 监测站点管理

#### 1.1 获取监测站点列表
```
GET /lxbi/stat/surface-monitoring/stations
```
**功能**: 获取所有监测站点的基本信息
**返回**: 站点列表，包含站点ID、名称、位置、状态等信息

#### 1.2 获取监测站点实时状态
```
GET /lxbi/stat/surface-monitoring/stations/status
```
**功能**: 获取所有监测站点的实时运行状态
**返回**: 站点状态列表，包含通信状态、偏移量、报警状态等

#### 1.3 获取指定监测站详细数据
```
GET /lxbi/stat/surface-monitoring/station/{stationId}/details
```
**参数**:
- `stationId`: 站点ID
- `viewType`: 视图类型 (daily/weekly/monthly)
- `startDate`: 开始日期 (可选)
- `endDate`: 结束日期 (可选)

**功能**: 获取指定站点的详细信息，包括基本信息、当前状态、历史趋势、报警事件和统计数据

### 2. 数据分析

#### 2.1 获取监测数据历史趋势
```
GET /lxbi/stat/surface-monitoring/data-trend
```
**参数**:
- `stationId`: 站点ID (可选，为空时获取所有站点)
- `viewType`: 视图类型 (daily/weekly/monthly)
- `startDate`: 开始日期 (可选)
- `endDate`: 结束日期 (可选)

**功能**: 获取监测数据的历史趋势，包括偏移量变化、环境参数等

### 3. 报警管理

#### 3.1 获取报警事件列表
```
GET /lxbi/stat/surface-monitoring/alarms
```
**参数**:
- `stationId`: 站点ID (可选)
- `viewType`: 视图类型 (daily/weekly/monthly)
- `startDate`: 开始日期 (可选)
- `endDate`: 结束日期 (可选)

**功能**: 获取报警事件列表，支持按站点和时间范围筛选

### 4. 系统监控

#### 4.1 获取系统健康状态
```
GET /lxbi/stat/surface-monitoring/system-health
```
**功能**: 获取整个地表监测系统的健康状态，包括站点统计、性能指标、系统状态等

## 新增VO类

### 1. SurfaceMonitoringStationVO
监测站点基本信息，包含：
- 站点ID、名称、编号
- 地理位置（经纬度）
- 网关信息
- 通信状态
- 设备状态

### 2. SurfaceMonitoringStationStatusVO
监测站点实时状态，包含：
- 通信状态、定位状态
- 当前偏移量（X、Y、H）
- 报警状态和等级
- 数据质量评分
- 在线时长

### 3. SurfaceMonitoringStationDetailVO
监测站点详细信息，包含：
- 站点基本信息
- 当前状态
- 历史数据趋势
- 最近报警事件
- 统计信息

### 4. SurfaceMonitoringDataTrendVO
监测数据趋势，包含：
- 时间点信息
- 偏移量数据
- 环境参数（温度、湿度）
- 设备参数（电池电压、信号强度）
- 报警信息

### 5. SurfaceMonitoringAlarmVO
报警事件信息，包含：
- 报警基本信息（类型、等级、时间）
- 触发条件（触发值、阈值）
- 偏移量数据
- 处理状态和处理信息
- 确认状态

### 6. SurfaceMonitoringSystemHealthVO
系统健康状态，包含：
- 系统整体评分和状态
- 站点统计（总数、在线数、故障数）
- 系统性能指标
- 各站点健康摘要

## 数据结构示例

### 监测站点列表响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "wgbh": "GW001",
      "cphm": "监测站点1",
      "clbz": "STATION_1",
      "clbh": "ST0001",
      "wxdwjd": 116.401,
      "wxdwwd": 39.901,
      "wgip": "*************",
      "txzt": "在线",
      "clzt": 1,
      "drzt": 1,
      "wxsl": 9,
      "statusDesc": "正常运行",
      "isOnline": true
    }
  ]
}
```

### 站点状态响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "stationId": 1,
      "stationName": "监测站点1",
      "stationCode": "ST0001",
      "communicationStatus": "在线",
      "positioningStatus": "已定位",
      "satelliteCount": 9,
      "currentXOffset": 5.23,
      "currentYOffset": 3.45,
      "currentHOffset": 2.67,
      "totalOffset": 11.35,
      "alarmStatus": "正常",
      "alarmLevel": "低",
      "lastUpdateTime": "2025-08-29 15:30:25",
      "dataQualityScore": 95,
      "isOnline": true,
      "onlineDuration": 1440
    }
  ]
}
```

### 数据趋势响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "timePoint": "00:00",
      "shortTime": "00:00",
      "stationId": 1,
      "stationName": "监测站点1",
      "xOffset": 5.23,
      "yOffset": 3.45,
      "hOffset": 2.67,
      "totalOffset": 11.35,
      "offsetRate": 0.125,
      "satelliteCount": 9,
      "dataQuality": 95,
      "temperature": 25.3,
      "humidity": 65.2,
      "batteryVoltage": 12.45,
      "signalStrength": 85,
      "hasAlarm": false,
      "alarmLevel": "低"
    }
  ]
}
```

### 系统健康状态响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "overallHealthScore": 95,
    "systemStatus": "优秀",
    "totalStations": 5,
    "onlineStations": 4,
    "offlineStations": 1,
    "faultStations": 0,
    "systemOnlineRate": 80.0,
    "dataIntegrityRate": 98.5,
    "todayDataCollections": 3500,
    "todayAlarmCount": 8,
    "unhandledAlarmCount": 2,
    "highLevelAlarmCount": 1,
    "networkStatus": "正常",
    "databaseStatus": "正常",
    "apiConnectionStatus": "正常",
    "performanceMetrics": {
      "avgResponseTime": 250,
      "dataProcessingSpeed": 850.5,
      "memoryUsage": 65.2,
      "cpuUsage": 35.8,
      "diskUsage": 45.3,
      "networkUsage": 25.7
    }
  }
}
```

## 接口使用场景

### 1. 监控大屏
- 使用系统健康状态接口获取整体概览
- 使用站点状态接口显示各站点实时状态
- 使用数据趋势接口绘制趋势图表

### 2. 站点详情页面
- 使用站点详细数据接口获取完整信息
- 展示站点基本信息、实时状态、历史趋势

### 3. 报警管理
- 使用报警事件列表接口管理报警
- 支持按站点和时间筛选报警事件

### 4. 数据分析
- 使用数据趋势接口进行历史数据分析
- 支持不同时间维度的数据查看

## 技术特点

### 1. 统一的接口设计
- 所有接口都支持viewType参数（daily/weekly/monthly）
- 统一的日期范围参数（startDate/endDate）
- 一致的响应格式

### 2. 灵活的数据查询
- 支持按站点筛选
- 支持时间范围查询
- 支持不同时间维度的数据聚合

### 3. 完整的数据模型
- 涵盖站点管理、状态监控、数据分析、报警管理等各个方面
- 数据结构清晰，便于前端使用

### 4. 良好的扩展性
- 模块化的VO设计
- 易于添加新的字段和功能
- 支持未来的功能扩展

## 总结

通过补全地表监测接口，我们现在拥有了一套完整的地表监测数据管理系统，包括：

1. **6个新增接口**：覆盖站点管理、数据分析、报警管理、系统监控
2. **6个新增VO类**：完整的数据模型设计
3. **统一的接口规范**：一致的参数和响应格式
4. **丰富的功能特性**：支持多维度数据查询和分析

这些接口为地表监测系统的前端开发和数据可视化提供了强有力的支持，能够满足各种业务场景的需求。
