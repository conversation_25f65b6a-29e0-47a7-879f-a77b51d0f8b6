package com.ruoyi.lxbi.admin.service;

import java.util.List;
import com.ruoyi.lxbi.admin.domain.KafkaPeopleLocationInfo;

/**
 * 区域基本信息数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IKafkaPeopleLocationInfoService 
{
    /**
     * 查询区域基本信息数据
     * 
     * @param id 区域基本信息数据主键
     * @return 区域基本信息数据
     */
    public KafkaPeopleLocationInfo selectKafkaPeopleLocationInfoById(Long id);

    /**
     * 查询区域基本信息数据列表
     * 
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 区域基本信息数据集合
     */
    public List<KafkaPeopleLocationInfo> selectKafkaPeopleLocationInfoList(KafkaPeopleLocationInfo kafkaPeopleLocationInfo);

    /**
     * 新增区域基本信息数据
     * 
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 结果
     */
    public int insertKafkaPeopleLocationInfo(KafkaPeopleLocationInfo kafkaPeopleLocationInfo);

    /**
     * 修改区域基本信息数据
     * 
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 结果
     */
    public int updateKafkaPeopleLocationInfo(KafkaPeopleLocationInfo kafkaPeopleLocationInfo);

    /**
     * 批量删除区域基本信息数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaPeopleLocationInfoByIds(Long[] ids);

    /**
     * 删除区域基本信息数据信息
     * 
     * @param id 区域基本信息数据主键
     * @return 结果
     */
    public int deleteKafkaPeopleLocationInfoById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     * 根据区域编码和煤矿编码唯一性，如果存在则更新，不存在则新增
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaPeopleLocationInfo parseKafkaMessage(String kafkaMessage);

    /**
     * 根据区域编码查询区域信息
     * 
     * @param areaCode 区域编码
     * @return 区域基本信息数据
     */
    public KafkaPeopleLocationInfo selectByAreaCode(String areaCode);

    /**
     * 根据区域编码和煤矿编码查询区域信息
     * 
     * @param areaCode 区域编码
     * @param mineCode 煤矿编码
     * @return 区域基本信息数据
     */
    public KafkaPeopleLocationInfo selectByAreaCodeAndMineCode(String areaCode, String mineCode);

    /**
     * 查询所有有效的区域信息
     * 
     * @return 区域基本信息数据集合
     */
    public List<KafkaPeopleLocationInfo> selectAllValidAreas();

    /**
     * 根据区域类型查询区域信息
     * 
     * @param areaType 区域类型
     * @return 区域基本信息数据集合
     */
    public List<KafkaPeopleLocationInfo> selectByAreaType(String areaType);

    /**
     * 统计区域总数
     * 
     * @return 区域总数
     */
    public Long countTotalAreas();

    /**
     * 统计指定煤矿的区域数量
     * 
     * @param mineCode 煤矿编码
     * @return 区域数量
     */
    public Long countAreasByMineCode(String mineCode);
}
