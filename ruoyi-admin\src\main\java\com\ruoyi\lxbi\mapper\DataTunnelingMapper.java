package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import com.ruoyi.lxbi.domain.DataTunneling;
import com.ruoyi.lxbi.domain.response.DataTunnelingVo;
import org.apache.ibatis.annotations.Param;

/**
 * 掘进数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface DataTunnelingMapper 
{
    /**
     * 查询掘进数据
     * 
     * @param id 掘进数据主键
     * @return 掘进数据
     */
    public DataTunneling selectDataTunnelingById(Long id);

    /**
     * 查询掘进数据列表
     *
     * @param dataTunneling 掘进数据
     * @return 掘进数据集合
     */
    public List<DataTunnelingVo> selectDataTunnelingList(DataTunneling dataTunneling);

    /**
     * 新增掘进数据
     * 
     * @param dataTunneling 掘进数据
     * @return 结果
     */
    public int insertDataTunneling(DataTunneling dataTunneling);

    /**
     * 修改掘进数据
     * 
     * @param dataTunneling 掘进数据
     * @return 结果
     */
    public int updateDataTunneling(DataTunneling dataTunneling);

    /**
     * 删除掘进数据
     * 
     * @param id 掘进数据主键
     * @return 结果
     */
    public int deleteDataTunnelingById(Long id);

    /**
     * 批量删除掘进数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataTunnelingByIds(Long[] ids);

    /**
     * 根据作业日期和项目部门查询掘进数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 掘进数据集合
     */
    public List<DataTunnelingVo> selectDataTunnelingByOperationDateAndProject(@Param("operationDate") Date operationDate, @Param("projectDepartmentId") Long projectDepartmentId);

    /**
     * 批量新增掘进数据
     *
     * @param dataTunnelingList 掘进数据列表
     * @return 结果
     */
    public int batchInsertDataTunneling(List<DataTunneling> dataTunnelingList);
}
