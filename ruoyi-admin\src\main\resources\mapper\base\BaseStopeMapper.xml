<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.BaseStopeMapper">
    
    <resultMap type="BaseStope" id="BaseStopeResult">
        <result property="stopeId"    column="stope_id"    />
        <result property="stopeName"    column="stope_name"    />
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectBaseStopeVo">
        select bs.*, bws.working_face_name
        from base_stope bs left join base_working_face bws on bs.working_face_id = bws.working_face_id
    </sql>

    <select id="selectBaseStopeList" parameterType="BaseStope" resultType="com.ruoyi.lxbi.domain.response.BaseStopeVo">
        <include refid="selectBaseStopeVo"/>
        <where>  
            <if test="stopeName != null  and stopeName != ''"> and bs.stope_name like concat('%', #{stopeName}, '%')</if>
            <if test="workingFaceId != null "> and bs.working_face_id = #{workingFaceId}</if>
            <if test="status != null "> and bs.status = #{status}</if>
            <if test="updateBy != null  and updateBy != ''"> and bs.update_by = #{updateBy}</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and bs.start_time between #{params.beginStartTime}::date and #{params.endStartTime}::date</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and bs.end_time between #{params.beginEndTime}::date and #{params.endEndTime}::date</if>
            and bs.is_delete = 0
        </where>
    </select>

    <select id="selectBaseStopeListAll" parameterType="BaseStope" resultType="com.ruoyi.lxbi.domain.response.BaseStopeVo">
        select stope_id, stope_name, working_face_id, status, create_by, create_time, update_by, update_time, start_time, end_time, is_delete from base_stope
        <where>
            <if test="stopeName != null  and stopeName != ''"> and stope_name like concat('%', #{stopeName}, '%')</if>
            <if test="workingFaceId != null "> and working_face_id = #{workingFaceId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and start_time between #{params.beginStartTime}::date and #{params.endStartTime}::date</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and end_time between #{params.beginEndTime}::date and #{params.endEndTime}::date</if>
            and is_delete = 0
        </where>
    </select>

    <select id="selectBaseStopeByStopeId" parameterType="Long" resultMap="BaseStopeResult">
        <include refid="selectBaseStopeVo"/>
        where stope_id = #{stopeId}
    </select>
    <select id="getBaseStopeByStopeIds" resultType="java.lang.Integer">
        select count(1) as count from base_stope where stope_id =
        <foreach item="stopeId" collection="array" open="(" separator="," close=")">
            #{stopeId}
        </foreach>
        and is_delete = 0 and status = 1;
    </select>

    <insert id="insertBaseStope" parameterType="BaseStope" useGeneratedKeys="true" keyProperty="stopeId">
        insert into base_stope
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stopeName != null">stope_name,</if>
            <if test="workingFaceId != null">working_face_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stopeName != null">#{stopeName},</if>
            <if test="workingFaceId != null">#{workingFaceId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updateBaseStope" parameterType="BaseStope">
        update base_stope
        <trim prefix="SET" suffixOverrides=",">
            <if test="stopeName != null">stope_name = #{stopeName},</if>
            <if test="workingFaceId != null">working_face_id = #{workingFaceId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where stope_id = #{stopeId}
    </update>

    <delete id="deleteBaseStopeByStopeId" parameterType="Long">
        update base_stope set is_delete=1 where stope_id = #{stopeId}
    </delete>

    <delete id="deleteBaseStopeByStopeIds" parameterType="String">
        update base_stope set is_delete=1 where stope_id in
        <foreach item="stopeId" collection="array" open="(" separator="," close=")">
            #{stopeId}
        </foreach>
    </delete>
</mapper>