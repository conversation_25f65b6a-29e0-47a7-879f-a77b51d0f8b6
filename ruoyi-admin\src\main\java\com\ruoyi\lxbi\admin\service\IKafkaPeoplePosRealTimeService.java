package com.ruoyi.lxbi.admin.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaPeoplePosRealTime;

/**
 * 井下作业人员实时数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
public interface IKafkaPeoplePosRealTimeService 
{
    /**
     * 查询井下作业人员实时数据
     * 
     * @param id 井下作业人员实时数据主键
     * @return 井下作业人员实时数据
     */
    public KafkaPeoplePosRealTime selectKafkaPeoplePosRealTimeById(Long id);

    /**
     * 查询井下作业人员实时数据列表
     * 
     * @param kafkaPeoplePosRealTime 井下作业人员实时数据
     * @return 井下作业人员实时数据集合
     */
    public List<KafkaPeoplePosRealTime> selectKafkaPeoplePosRealTimeList(KafkaPeoplePosRealTime kafkaPeoplePosRealTime);

    /**
     * 新增井下作业人员实时数据
     * 
     * @param kafkaPeoplePosRealTime 井下作业人员实时数据
     * @return 结果
     */
    public int insertKafkaPeoplePosRealTime(KafkaPeoplePosRealTime kafkaPeoplePosRealTime);

    /**
     * 修改井下作业人员实时数据
     * 
     * @param kafkaPeoplePosRealTime 井下作业人员实时数据
     * @return 结果
     */
    public int updateKafkaPeoplePosRealTime(KafkaPeoplePosRealTime kafkaPeoplePosRealTime);

    /**
     * 批量删除井下作业人员实时数据
     * 
     * @param ids 需要删除的井下作业人员实时数据主键集合
     * @return 结果
     */
    public int deleteKafkaPeoplePosRealTimeByIds(Long[] ids);

    /**
     * 删除井下作业人员实时数据信息
     * 
     * @param id 井下作业人员实时数据主键
     * @return 结果
     */
    public int deleteKafkaPeoplePosRealTimeById(Long id);

    /**
     * 处理Kafka消息
     * 
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);
}
