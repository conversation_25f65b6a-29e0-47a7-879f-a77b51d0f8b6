package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataIronConcentrate;
import com.ruoyi.lxbi.service.IDataIronConcentrateService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 铁精粉生产数据Controller
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@RestController
@RequestMapping("/data/concentrate")
public class DataIronConcentrateController extends BaseController {
    @Autowired
    private IDataIronConcentrateService dataIronConcentrateService;

    /**
     * 查询铁精粉生产数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:concentrate:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataIronConcentrate dataIronConcentrate) {
        startPage();
        List<DataIronConcentrate> list = dataIronConcentrateService.selectDataIronConcentrateList(dataIronConcentrate);
        return getDataTable(list);
    }

    /**
     * 导出铁精粉生产数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:concentrate:export')")
    @Log(title = "铁精粉生产数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataIronConcentrate dataIronConcentrate) {
        List<DataIronConcentrate> list = dataIronConcentrateService.selectDataIronConcentrateList(dataIronConcentrate);
        ExcelUtil<DataIronConcentrate> util = new ExcelUtil<DataIronConcentrate>(DataIronConcentrate.class);
        util.exportExcel(response, list, "铁精粉生产数据数据");
    }

    /**
     * 获取铁精粉生产数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:concentrate:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataIronConcentrateService.selectDataIronConcentrateById(id));
    }

    /**
     * 新增铁精粉生产数据
     */
    @PreAuthorize("@ss.hasPermi('data:concentrate:add')")
    @Log(title = "铁精粉生产数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataIronConcentrate dataIronConcentrate)
    {
        return toAjax(dataIronConcentrateService.insertDataIronConcentrate(dataIronConcentrate));
    }

    /**
     * 修改铁精粉生产数据
     */
    @PreAuthorize("@ss.hasPermi('data:concentrate:edit')")
    @Log(title = "铁精粉生产数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataIronConcentrate dataIronConcentrate)
    {
        return toAjax(dataIronConcentrateService.updateDataIronConcentrate(dataIronConcentrate));
    }

    /**
     * 删除铁精粉生产数据
     */
    @PreAuthorize("@ss.hasPermi('data:concentrate:remove')")
    @Log(title = "铁精粉生产数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataIronConcentrateService.deleteDataIronConcentrateByIds(ids));
    }
}
