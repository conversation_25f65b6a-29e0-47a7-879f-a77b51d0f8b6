package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.PlanDeepHoleMonthlyMapper;
import com.ruoyi.lxbi.domain.PlanDeepHoleMonthly;
import com.ruoyi.lxbi.domain.request.PlanDeepHoleMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanDeepHoleMonthlyVo;
import com.ruoyi.lxbi.service.IPlanDeepHoleMonthlyService;

/**
 * 中深孔月计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class PlanDeepHoleMonthlyServiceImpl implements IPlanDeepHoleMonthlyService 
{
    @Autowired
    private PlanDeepHoleMonthlyMapper planDeepHoleMonthlyMapper;

    /**
     * 查询中深孔月计划
     * 
     * @param id 中深孔月计划主键
     * @return 中深孔月计划
     */
    @Override
    public PlanDeepHoleMonthly selectPlanDeepHoleMonthlyById(Long id)
    {
        return planDeepHoleMonthlyMapper.selectPlanDeepHoleMonthlyById(id);
    }

    /**
     * 查询中深孔月计划列表
     *
     * @param planDeepHoleMonthly 中深孔月计划
     * @return 中深孔月计划
     */
    @Override
    public List<PlanDeepHoleMonthlyVo> selectPlanDeepHoleMonthlyList(PlanDeepHoleMonthly planDeepHoleMonthly)
    {
        return planDeepHoleMonthlyMapper.selectPlanDeepHoleMonthlyList(planDeepHoleMonthly);
    }

    /**
     * 新增中深孔月计划
     * 
     * @param planDeepHoleMonthly 中深孔月计划
     * @return 结果
     */
    @Override
    public int insertPlanDeepHoleMonthly(PlanDeepHoleMonthly planDeepHoleMonthly)
    {
        planDeepHoleMonthly.setCreateTime(DateUtils.getNowDate());
        return planDeepHoleMonthlyMapper.insertPlanDeepHoleMonthly(planDeepHoleMonthly);
    }

    /**
     * 修改中深孔月计划
     * 
     * @param planDeepHoleMonthly 中深孔月计划
     * @return 结果
     */
    @Override
    public int updatePlanDeepHoleMonthly(PlanDeepHoleMonthly planDeepHoleMonthly)
    {
        planDeepHoleMonthly.setUpdateTime(DateUtils.getNowDate());
        return planDeepHoleMonthlyMapper.updatePlanDeepHoleMonthly(planDeepHoleMonthly);
    }

    /**
     * 批量删除中深孔月计划
     * 
     * @param ids 需要删除的中深孔月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanDeepHoleMonthlyByIds(Long[] ids)
    {
        return planDeepHoleMonthlyMapper.deletePlanDeepHoleMonthlyByIds(ids);
    }

    /**
     * 删除中深孔月计划信息
     *
     * @param id 中深孔月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanDeepHoleMonthlyById(Long id)
    {
        return planDeepHoleMonthlyMapper.deletePlanDeepHoleMonthlyById(id);
    }

    /**
     * 批量保存中深孔月计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePlanDeepHoleMonthly(List<PlanDeepHoleMonthlyBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个月份的数据
        String planMonth = batchDataList.get(0).getPlanMonth();
        if (StringUtils.isBlank(planMonth)) {
            throw new ServiceException("计划月份不能为空");
        }
        // 验证是否同一个日期和项目部的数据
        Long projectDepartmentId = batchDataList.get(0).getProjectDepartmentId();
        if (projectDepartmentId == null) {
            throw new ServiceException("项目部门不能为空");
        }
        boolean allSameDateAndProject = batchDataList.stream()
                .allMatch(data -> planMonth.equals(data.getPlanMonth())
                        && projectDepartmentId.equals(data.getProjectDepartmentId()));
        if (!allSameDateAndProject) {
            throw new ServiceException("批量数据必须是同一个计划月份和项目部门");
        }

        // 查询该月份的现有数据
        PlanDeepHoleMonthly queryParam = new PlanDeepHoleMonthly();
        queryParam.setPlanDate(planMonth);
        queryParam.setProjectDepartmentId(projectDepartmentId);
        List<PlanDeepHoleMonthlyVo> existingDataList = planDeepHoleMonthlyMapper.selectPlanDeepHoleMonthlyList(queryParam);
        Map<Long, PlanDeepHoleMonthly> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(PlanDeepHoleMonthly::getId, data -> data));

        List<PlanDeepHoleMonthly> toInsert = new ArrayList<>();
        List<PlanDeepHoleMonthly> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (PlanDeepHoleMonthlyBatchDto batchData : batchDataList) {
            if ("add".equals(batchData.getOperationType())) {
                // 新增数据
                PlanDeepHoleMonthly newData = new PlanDeepHoleMonthly();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if ("edit".equals(batchData.getOperationType()) && batchData.getId() != null) {
                // 更新现有数据
                if (existingDataMap.containsKey(batchData.getId())) {
                    PlanDeepHoleMonthly updateData = new PlanDeepHoleMonthly();
                    copyProperties(batchData, updateData);
                    updateData.setId(batchData.getId());
                    updateData.setUpdateBy(SecurityUtils.getUsername());
                    updateData.setUpdateTime(DateUtils.getNowDate());
                    toUpdate.add(updateData);
                    toDelete.remove(batchData.getId());
                }
            }
        }

        int totalProcessed = 0;

        // 执行批量操作
        if (!toInsert.isEmpty()) {
            for (PlanDeepHoleMonthly data : toInsert) {
                totalProcessed += planDeepHoleMonthlyMapper.insertPlanDeepHoleMonthly(data);
            }
        }

        if (!toUpdate.isEmpty()) {
            for (PlanDeepHoleMonthly data : toUpdate) {
                totalProcessed += planDeepHoleMonthlyMapper.updatePlanDeepHoleMonthly(data);
            }
        }

        if (!toDelete.isEmpty()) {
            totalProcessed += planDeepHoleMonthlyMapper.deletePlanDeepHoleMonthlyByIds(toDelete.toArray(new Long[0]));
        }

        return totalProcessed;
    }

    /**
     * 复制属性
     */
    private void copyProperties(PlanDeepHoleMonthlyBatchDto source, PlanDeepHoleMonthly target) {
        target.setPlanDate(source.getPlanMonth());
        target.setProjectDepartmentId(source.getProjectDepartmentId());
        target.setWorkingFaceId(source.getWorkingFaceId());
        target.setStopeId(source.getStopeId());
        target.setDeepHoleMeter(source.getDeepHoleMeter());
        if (StringUtils.isNotBlank(source.getRemark())) {
            target.setRemark(source.getRemark());
        }
    }
}
