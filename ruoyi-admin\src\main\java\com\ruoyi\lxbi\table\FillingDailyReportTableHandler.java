package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.domain.PlanBackfillingMonthly;
import com.ruoyi.lxbi.domain.PlanStopeMonthly;
import com.ruoyi.lxbi.domain.request.DataFillingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataFillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataFillingStopeStats;
import com.ruoyi.lxbi.domain.response.PlanBackfillingMonthlyVo;
import com.ruoyi.lxbi.domain.response.PlanStopeMonthlyVo;
import com.ruoyi.lxbi.domain.table.FillingDailyReportTableVo;
import com.ruoyi.lxbi.service.IBaseStopeService;
import com.ruoyi.lxbi.service.IDataFillingStatsService;
import com.ruoyi.lxbi.service.IPlanBackfillingMonthlyService;
import com.ruoyi.lxbi.service.IPlanStopeMonthlyService;
import com.ruoyi.lxbi.table.params.FillingDailyReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 充填数据日报表格处理器
 */
@Component
public class FillingDailyReportTableHandler extends BaseTableHandler<FillingDailyReportTableVo, FillingDailyReportQueryParams> {

    @Autowired
    private IDataFillingStatsService dataFillingStatsService;

    @Autowired
    private IPlanBackfillingMonthlyService planBackfillingMonthlyService;

    @Autowired
    private IBaseStopeService baseStopeService;

    @Autowired
    private IPlanStopeMonthlyService planStopeMonthlyService;

    @Override
    public List<FillingDailyReportTableVo> queryTableData(FillingDailyReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date operationDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取充填数据
        FillingResult fillingResult = getFillingData(operationDate);

        // 构建表格数据
        return buildTableData(fillingResult, operationDate);
    }

    /**
     * 获取充填数据
     */
    private FillingResult getFillingData(Date operationDate) {
        // 获取财务月的开始日期到查询日期的累计数据
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(operationDate);

        DataFillingStatsRequest request = new DataFillingStatsRequest();
        request.setStartDate(financialMonthStart);
        request.setEndDate(operationDate);

        // 获取充填总体统计数据
        List<DataFillingTotalWithPlanStats> fillingTotalStats = dataFillingStatsService.selectTotalWithPlanStatsList(request, "daily");
        List<DataFillingTotalWithPlanStats> fillingDailyStats = fillingTotalStats.stream()
                .filter(stats -> FinancialDateUtils.isSameDay(stats.getOperationDate(), operationDate))
                .collect(Collectors.toList());

        // 计算充填总体月累计
        BigDecimal fillingTotalMonthlyAccumulated = fillingTotalStats.stream()
                .filter(stats -> stats.getTotalSlurryVolume() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSlurryVolume()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算充填总体日产量
        BigDecimal fillingTotalDailyOutput = fillingDailyStats.stream()
                .filter(stats -> stats.getTotalSlurryVolume() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalSlurryVolume()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 获取充填月计划
        BigDecimal fillingTotalMonthlyPlan = getFillingMonthlyPlan(operationDate);

        // 获取充填采场统计数据
        List<DataFillingStopeStats> fillingStopeStats = dataFillingStatsService.selectStopeStatsList(request, "daily");

        // 获取财务月开始到查询日期的采场月累计数据
        DataFillingStatsRequest monthlyRequest = new DataFillingStatsRequest();
        monthlyRequest.setStartDate(financialMonthStart);
        monthlyRequest.setEndDate(operationDate);
        List<DataFillingStopeStats> fillingStopeMonthlyStats = dataFillingStatsService.selectStopeStatsList(monthlyRequest, "daily");


        return new FillingResult(fillingTotalDailyOutput, fillingTotalMonthlyAccumulated, fillingTotalMonthlyPlan,
                fillingStopeStats, fillingStopeMonthlyStats);
    }

    /**
     * 获取充填月计划
     */
    private BigDecimal getFillingMonthlyPlan(Date operationDate) {
        // 计算财务月
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        // 获取充填月度计划
        PlanBackfillingMonthly planQuery = new PlanBackfillingMonthly();
        planQuery.setPlanDate(financialMonth);
        List<PlanBackfillingMonthlyVo> fillingPlans = planBackfillingMonthlyService.selectPlanBackfillingMonthlyList(planQuery);

        // 汇总充填计划
        return fillingPlans.stream()
                .filter(plan -> plan.getFillingVolume() != null)
                .map(PlanBackfillingMonthly::getFillingVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 构建表格数据
     */
    private List<FillingDailyReportTableVo> buildTableData(FillingResult fillingResult, Date operationDate) {
        List<FillingDailyReportTableVo> result = new ArrayList<>();

        // 添加充填总计行
        FillingDailyReportTableVo summary = new FillingDailyReportTableVo();
        summary.setBold(List.of("serialNumber", "name", "monthlyPlan", "dailyOutput", "monthlyAccumulated", "completionRate", "monthlyOverUnder"));
        summary.setSerialNumber("总计");
        summary.setName("充填");
        summary.setSubName("充填");
        summary.setUnit("m³");
        summary.setMonthlyPlan(fillingResult.getTotalMonthlyPlan());
        summary.setDailyOutput(fillingResult.getTotalDailyOutput());
        summary.setMonthlyAccumulated(fillingResult.getTotalMonthlyAccumulated());

        // 计算总完成率
        if (fillingResult.getTotalMonthlyPlan().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal rate = fillingResult.getTotalMonthlyAccumulated()
                    .divide(fillingResult.getTotalMonthlyPlan(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            summary.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
            summary.setMonthlyOverUnder(fillingResult.getTotalMonthlyAccumulated()
                    .subtract(fillingResult.getTotalMonthlyPlan()));
        } else {
            summary.setCompletionRate("　");
            summary.setMonthlyOverUnder(null);
        }

        result.add(summary);

        // 添加充填采场明细
        buildFillingStopeData(result, fillingResult, operationDate);

        return result;
    }

    /**
     * 构建充填采场明细数据
     */
    private void buildFillingStopeData(List<FillingDailyReportTableVo> result, FillingResult fillingResult, Date operationDate) {
        // 获取所有涉及的采场ID
        Set<Long> allStopeIds = new HashSet<>();

        // 从日统计数据中获取采场ID
        fillingResult.getStopeStats().forEach(stats -> {
            if (stats.getStopeId() != null) {
                allStopeIds.add(stats.getStopeId());
            }
        });

        // 从月统计数据中获取采场ID
        fillingResult.getStopeMonthlyStats().forEach(stats -> {
            if (stats.getStopeId() != null) {
                allStopeIds.add(stats.getStopeId());
            }
        });

        // 按采场ID分组日统计数据
        Map<Long, DataFillingStopeStats> dailyStatsMap = fillingResult.getStopeStats().stream()
                .filter(stats -> stats.getStopeId() != null)
                .collect(Collectors.toMap(
                        DataFillingStopeStats::getStopeId,
                        stats -> stats,
                        (existing, replacement) -> {
                            // 如果有重复，累加充填量
                            existing.setTotalSlurryVolume((existing.getTotalSlurryVolume() != null ? existing.getTotalSlurryVolume() : 0.0) +
                                    (replacement.getTotalSlurryVolume() != null ? replacement.getTotalSlurryVolume() : 0.0));
                            return existing;
                        }
                ));

        // 按采场ID汇总月累计数据
        Map<Long, BigDecimal> monthlyAccumulatedMap = fillingResult.getStopeMonthlyStats().stream()
                .filter(stats -> stats.getStopeId() != null && stats.getTotalSlurryVolume() != null)
                .collect(Collectors.groupingBy(
                        DataFillingStopeStats::getStopeId,
                        Collectors.summingDouble(stats -> stats.getTotalSlurryVolume())
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> BigDecimal.valueOf(entry.getValue())
                ));

        // 获取采场月计划数据
        Map<Long, BigDecimal> stopeMonthlyPlans = getStopeMonthlyPlans(operationDate);

        int subSerialNumber = 1;
        for (Long stopeId : allStopeIds) {
            DataFillingStopeStats dailyStats = dailyStatsMap.get(stopeId);
            BigDecimal dailyOutput = dailyStats != null && dailyStats.getTotalSlurryVolume() != null ?
                    BigDecimal.valueOf(dailyStats.getTotalSlurryVolume()) : BigDecimal.ZERO;
            BigDecimal monthlyAccumulated = monthlyAccumulatedMap.getOrDefault(stopeId, BigDecimal.ZERO);
            BigDecimal monthlyPlan = stopeMonthlyPlans.getOrDefault(stopeId, BigDecimal.ZERO);

            FillingDailyReportTableVo vo = new FillingDailyReportTableVo();
            vo.setSerialNumber(String.valueOf(subSerialNumber++));
            vo.setName(getStopeDisplayName(stopeId, dailyStats));
            vo.setSubName(getStopeDisplayName(stopeId, dailyStats));
            vo.setUnit("m³");
            vo.setMonthlyPlan(monthlyPlan);
            vo.setDailyOutput(dailyOutput);
            vo.setMonthlyAccumulated(monthlyAccumulated);

            // 计算完成率
            if (monthlyPlan.compareTo(BigDecimal.ZERO) > 0 && monthlyAccumulated.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = monthlyAccumulated.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                vo.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
                vo.setMonthlyOverUnder(monthlyAccumulated.subtract(monthlyPlan));
            } else {
                vo.setCompletionRate("　");
                vo.setMonthlyOverUnder(null);
            }

            result.add(vo);
        }
    }

    /**
     * 获取采场月计划数据
     */
    private Map<Long, BigDecimal> getStopeMonthlyPlans(Date operationDate) {
        // 计算财务月
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        // 获取采场月度计划
        PlanStopeMonthly planQuery = new PlanStopeMonthly();
        planQuery.setPlanDate(financialMonth);
        List<PlanStopeMonthlyVo> stopePlans = planStopeMonthlyService.selectPlanStopeMonthlyList(planQuery);

        // 按采场ID汇总计划（这里假设采场月计划中有充填相关的字段）
        // 注意：PlanStopeMonthly中目前只有oreOutput字段，可能需要扩展或使用其他计划表
        Map<Long, BigDecimal> result = new HashMap<>();
        for (PlanStopeMonthlyVo plan : stopePlans) {
            if (plan.getStopeId() != null && plan.getOreOutput() != null) {
                try {
                    BigDecimal planValue = new BigDecimal(plan.getOreOutput());
                    result.put(plan.getStopeId(), planValue);
                } catch (NumberFormatException e) {
                    // 如果转换失败，跳过这条记录
                    System.err.println("采场计划数据转换失败，采场ID: " + plan.getStopeId() + ", 值: " + plan.getOreOutput());
                }
            }
        }

        return result;
    }

    /**
     * 获取采场显示名称
     */
    private String getStopeDisplayName(Long stopeId, DataFillingStopeStats stats) {
        // 优先使用统计数据中的采场名称
        if (stats != null && stats.getStopeName() != null && !stats.getStopeName().trim().isEmpty()) {
            return stats.getStopeName();
        }

        // 从采场基础数据服务获取采场名称
        try {
            BaseStope stope = baseStopeService.selectBaseStopeByStopeId(stopeId);
            if (stope != null && stope.getStopeName() != null && !stope.getStopeName().trim().isEmpty()) {
                return stope.getStopeName();
            }
        } catch (Exception e) {
            // 查询失败时记录日志，但不影响整体流程
            System.err.println("获取采场名称失败，采场ID: " + stopeId + ", 错误: " + e.getMessage());
        }

        // 最后使用默认格式
        return "采场-" + stopeId;
    }

    /**
     * 充填结果包装类
     */
    private static class FillingResult {
        private final BigDecimal totalDailyOutput;
        private final BigDecimal totalMonthlyAccumulated;
        private final BigDecimal totalMonthlyPlan;
        private final List<DataFillingStopeStats> stopeStats;
        private final List<DataFillingStopeStats> stopeMonthlyStats;

        public FillingResult(BigDecimal totalDailyOutput,
                           BigDecimal totalMonthlyAccumulated,
                           BigDecimal totalMonthlyPlan,
                           List<DataFillingStopeStats> stopeStats,
                           List<DataFillingStopeStats> stopeMonthlyStats) {
            this.totalDailyOutput = totalDailyOutput;
            this.totalMonthlyAccumulated = totalMonthlyAccumulated;
            this.totalMonthlyPlan = totalMonthlyPlan;
            this.stopeStats = stopeStats;
            this.stopeMonthlyStats = stopeMonthlyStats;
        }

        public BigDecimal getTotalDailyOutput() {
            return totalDailyOutput;
        }

        public BigDecimal getTotalMonthlyAccumulated() {
            return totalMonthlyAccumulated;
        }

        public BigDecimal getTotalMonthlyPlan() {
            return totalMonthlyPlan;
        }

        public List<DataFillingStopeStats> getStopeStats() {
            return stopeStats;
        }

        public List<DataFillingStopeStats> getStopeMonthlyStats() {
            return stopeMonthlyStats;
        }
    }
}
