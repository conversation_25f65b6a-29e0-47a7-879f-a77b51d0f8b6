package com.ruoyi.lxbi.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 铁精粉生产数据统计请求参数
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
public class DataIronConcentrateStatsRequest {
    /**
     * 统计类型：daily-日统计, weekly-周统计, monthly-月统计, yearly-年统计
     */
    private String statsType;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
}
