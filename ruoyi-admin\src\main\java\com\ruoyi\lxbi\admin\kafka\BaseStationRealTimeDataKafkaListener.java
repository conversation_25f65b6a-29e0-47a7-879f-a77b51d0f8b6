package com.ruoyi.lxbi.admin.kafka;

import com.ruoyi.lxbi.service.IKafkaBaseStationRealTimeDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

/**
 * 基站实时数据Kafka监听器
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Component
public class BaseStationRealTimeDataKafkaListener {

    @Autowired
    private IKafkaBaseStationRealTimeDataService kafkaBaseStationRealTimeDataService;

    /**
     * 监听基站实时数据主题
     * 
     * @param record Kafka消息记录
     * @param ack 手动确认
     */
    @KafkaListener(topics = "PeoplePos_Station_RealTIme", groupId = "lxbi-consumer-base-station-realtime")
    public void handleBaseStationRealTimeData(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try {
            String message = record.value();
            log.info("接收到基站实时数据Kafka消息，主题: {}, 分区: {}, 偏移量: {}", 
                record.topic(), record.partition(), record.offset());
            log.debug("消息内容: {}", message);

            // 处理消息
            boolean success = kafkaBaseStationRealTimeDataService.processKafkaMessage(message);
            
            if (success) {
                log.info("基站实时数据消息处理成功，偏移量: {}", record.offset());
                // 手动确认消息
                if (ack != null) {
                    ack.acknowledge();
                }
            } else {
                log.error("基站实时数据消息处理失败，偏移量: {}, 消息: {}", record.offset(), message);

            }

        } catch (Exception e) {
            log.error("处理基站实时数据Kafka消息异常，偏移量: {}", record.offset(), e);

        }
    }

}
