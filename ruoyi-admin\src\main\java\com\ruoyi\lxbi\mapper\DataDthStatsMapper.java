package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalStats;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataDrillingPeriodStats;
import com.ruoyi.lxbi.domain.response.DataDrillingDepartmentStats;
import com.ruoyi.lxbi.domain.response.DataDrillingDepartmentWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataDrillingStopeStats;
import com.ruoyi.lxbi.domain.response.DataDrillingWorkingFaceStats;

/**
 * 潜孔施工数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface DataDthStatsMapper 
{
    // ========== 总体统计查询方法 ==========
    
    /**
     * 查询总体统计数据列表 (日)
     */
    public List<DataDrillingTotalStats> selectDailyTotalStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表 (周)
     */
    public List<DataDrillingTotalStats> selectWeeklyTotalStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表 (月)
     */
    public List<DataDrillingTotalStats> selectMonthlyTotalStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表 (年)
     */
    public List<DataDrillingTotalStats> selectYearlyTotalStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 总体统计查询方法（含计划量） ==========

    /**
     * 查询总体统计数据列表（含计划量） (日)
     */
    public List<DataDrillingTotalWithPlanStats> selectDailyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (周)
     */
    public List<DataDrillingTotalWithPlanStats> selectWeeklyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (月)
     */
    public List<DataDrillingTotalWithPlanStats> selectMonthlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (年)
     */
    public List<DataDrillingTotalWithPlanStats> selectYearlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 班次统计查询方法 ==========
    
    /**
     * 查询班次统计数据列表 (日)
     */
    public List<DataDrillingPeriodStats> selectDailyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (周)
     */
    public List<DataDrillingPeriodStats> selectWeeklyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (月)
     */
    public List<DataDrillingPeriodStats> selectMonthlyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (年)
     */
    public List<DataDrillingPeriodStats> selectYearlyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 项目部门统计查询方法 ==========
    
    /**
     * 查询项目部门统计数据列表 (日)
     */
    public List<DataDrillingDepartmentStats> selectDailyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (周)
     */
    public List<DataDrillingDepartmentStats> selectWeeklyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (月)
     */
    public List<DataDrillingDepartmentStats> selectMonthlyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (年)
     */
    public List<DataDrillingDepartmentStats> selectYearlyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 采场统计查询方法 ==========
    
    /**
     * 查询采场统计数据列表 (日)
     */
    public List<DataDrillingStopeStats> selectDailyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询采场统计数据列表 (周)
     */
    public List<DataDrillingStopeStats> selectWeeklyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询采场统计数据列表 (月)
     */
    public List<DataDrillingStopeStats> selectMonthlyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询采场统计数据列表 (年)
     */
    public List<DataDrillingStopeStats> selectYearlyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 工作面统计查询方法 ==========
    
    /**
     * 查询工作面统计数据列表 (日)
     */
    public List<DataDrillingWorkingFaceStats> selectDailyWorkingFaceStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询工作面统计数据列表 (周)
     */
    public List<DataDrillingWorkingFaceStats> selectWeeklyWorkingFaceStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询工作面统计数据列表 (月)
     */
    public List<DataDrillingWorkingFaceStats> selectMonthlyWorkingFaceStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询工作面统计数据列表 (年)
     */
    public List<DataDrillingWorkingFaceStats> selectYearlyWorkingFaceStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 项目部门统计查询方法（含计划量） ==========

    /**
     * 查询项目部门统计数据列表（含计划量） (日)
     */
    public List<DataDrillingDepartmentWithPlanStats> selectDailyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表（含计划量） (周)
     */
    public List<DataDrillingDepartmentWithPlanStats> selectWeeklyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表（含计划量） (月)
     */
    public List<DataDrillingDepartmentWithPlanStats> selectMonthlyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表（含计划量） (年)
     */
    public List<DataDrillingDepartmentWithPlanStats> selectYearlyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

}
