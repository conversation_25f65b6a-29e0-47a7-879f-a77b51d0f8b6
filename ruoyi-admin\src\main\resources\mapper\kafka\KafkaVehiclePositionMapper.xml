<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaVehiclePositionMapper">
    
    <resultMap type="KafkaVehiclePosition" id="KafkaVehiclePositionResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="dataGenerateTime"    column="data_generate_time"    />
        <result property="vehicleIdentificationCard"    column="vehicle_identification_card"    />
        <result property="inOrOutMineFlag"    column="in_or_out_mine_flag"    />
        <result property="inMineTime"    column="in_mine_time"    />
        <result property="outMineTime"    column="out_mine_time"    />
        <result property="areaCode"    column="area_code"    />
        <result property="enterAreaTime"    column="enter_area_time"    />
        <result property="stationCode"    column="station_code"    />
        <result property="enterStationTime"    column="enter_station_time"    />
        <result property="stationXCoordinate"    column="station_x_coordinate"    />
        <result property="stationYCoordinate"    column="station_y_coordinate"    />
        <result property="stationZCoordinate"    column="station_z_coordinate"    />
    </resultMap>

    <sql id="selectKafkaVehiclePositionVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, data_generate_time, vehicle_identification_card, in_or_out_mine_flag, in_mine_time, out_mine_time, area_code, enter_area_time, station_code, enter_station_time, station_x_coordinate, station_y_coordinate, station_z_coordinate from kafka_vehicle_position
    </sql>

    <select id="selectKafkaVehiclePositionList" parameterType="KafkaVehiclePosition" resultMap="KafkaVehiclePositionResult">
        <include refid="selectKafkaVehiclePositionVo"/>
        <where>  
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="params.beginDataGenerateTime != null and params.beginDataGenerateTime != '' and params.endDataGenerateTime != null and params.endDataGenerateTime != ''"> and data_generate_time between #{params.beginDataGenerateTime}::date and #{params.endDataGenerateTime}::date</if>
            <if test="vehicleIdentificationCard != null  and vehicleIdentificationCard != ''"> and vehicle_identification_card = #{vehicleIdentificationCard}</if>
            <if test="inOrOutMineFlag != null  and inOrOutMineFlag != ''"> and in_or_out_mine_flag = #{inOrOutMineFlag}</if>
            <if test="params.beginInMineTime != null and params.beginInMineTime != '' and params.endInMineTime != null and params.endInMineTime != ''"> and in_mine_time between #{params.beginInMineTime}::date and #{params.endInMineTime}::date</if>
            <if test="params.beginOutMineTime != null and params.beginOutMineTime != '' and params.endOutMineTime != null and params.endOutMineTime != ''"> and out_mine_time between #{params.beginOutMineTime}::date and #{params.endOutMineTime}::date</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="params.beginEnterAreaTime != null and params.beginEnterAreaTime != '' and params.endEnterAreaTime != null and params.endEnterAreaTime != ''"> and enter_area_time between #{params.beginEnterAreaTime}::date and #{params.endEnterAreaTime}::date</if>
            <if test="stationCode != null  and stationCode != ''"> and station_code = #{stationCode}</if>
            <if test="params.beginEnterStationTime != null and params.beginEnterStationTime != '' and params.endEnterStationTime != null and params.endEnterStationTime != ''"> and enter_station_time between #{params.beginEnterStationTime}::date and #{params.endEnterStationTime}::date</if>
            <if test="stationXCoordinate != null "> and station_x_coordinate = #{stationXCoordinate}</if>
            <if test="stationYCoordinate != null "> and station_y_coordinate = #{stationYCoordinate}</if>
            <if test="stationZCoordinate != null "> and station_z_coordinate = #{stationZCoordinate}</if>
        </where>
    </select>
    
    <select id="selectKafkaVehiclePositionById" parameterType="Long" resultMap="KafkaVehiclePositionResult">
        <include refid="selectKafkaVehiclePositionVo"/>
        where id = #{id}
    </select>

    <insert id="insertKafkaVehiclePosition" parameterType="KafkaVehiclePosition" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_vehicle_position
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null and fileEncoding != ''">file_encoding,</if>
            <if test="mineCode != null and mineCode != ''">mine_code,</if>
            <if test="dataGenerateTime != null">data_generate_time,</if>
            <if test="vehicleIdentificationCard != null and vehicleIdentificationCard != ''">vehicle_identification_card,</if>
            <if test="inOrOutMineFlag != null">in_or_out_mine_flag,</if>
            <if test="inMineTime != null">in_mine_time,</if>
            <if test="outMineTime != null">out_mine_time,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="enterAreaTime != null">enter_area_time,</if>
            <if test="stationCode != null">station_code,</if>
            <if test="enterStationTime != null">enter_station_time,</if>
            <if test="stationXCoordinate != null">station_x_coordinate,</if>
            <if test="stationYCoordinate != null">station_y_coordinate,</if>
            <if test="stationZCoordinate != null">station_z_coordinate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null and fileEncoding != ''">#{fileEncoding},</if>
            <if test="mineCode != null and mineCode != ''">#{mineCode},</if>
            <if test="dataGenerateTime != null">#{dataGenerateTime},</if>
            <if test="vehicleIdentificationCard != null and vehicleIdentificationCard != ''">#{vehicleIdentificationCard},</if>
            <if test="inOrOutMineFlag != null">#{inOrOutMineFlag},</if>
            <if test="inMineTime != null">#{inMineTime},</if>
            <if test="outMineTime != null">#{outMineTime},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="enterAreaTime != null">#{enterAreaTime},</if>
            <if test="stationCode != null">#{stationCode},</if>
            <if test="enterStationTime != null">#{enterStationTime},</if>
            <if test="stationXCoordinate != null">#{stationXCoordinate},</if>
            <if test="stationYCoordinate != null">#{stationYCoordinate},</if>
            <if test="stationZCoordinate != null">#{stationZCoordinate},</if>
         </trim>
    </insert>

    <update id="updateKafkaVehiclePosition" parameterType="KafkaVehiclePosition">
        update kafka_vehicle_position
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null and fileEncoding != ''">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null and mineCode != ''">mine_code = #{mineCode},</if>
            <if test="dataGenerateTime != null">data_generate_time = #{dataGenerateTime},</if>
            <if test="vehicleIdentificationCard != null and vehicleIdentificationCard != ''">vehicle_identification_card = #{vehicleIdentificationCard},</if>
            <if test="inOrOutMineFlag != null">in_or_out_mine_flag = #{inOrOutMineFlag},</if>
            <if test="inMineTime != null">in_mine_time = #{inMineTime},</if>
            <if test="outMineTime != null">out_mine_time = #{outMineTime},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="enterAreaTime != null">enter_area_time = #{enterAreaTime},</if>
            <if test="stationCode != null">station_code = #{stationCode},</if>
            <if test="enterStationTime != null">enter_station_time = #{enterStationTime},</if>
            <if test="stationXCoordinate != null">station_x_coordinate = #{stationXCoordinate},</if>
            <if test="stationYCoordinate != null">station_y_coordinate = #{stationYCoordinate},</if>
            <if test="stationZCoordinate != null">station_z_coordinate = #{stationZCoordinate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaVehiclePositionById" parameterType="Long">
        delete from kafka_vehicle_position where id = #{id}
    </delete>

    <delete id="deleteKafkaVehiclePositionByIds" parameterType="String">
        delete from kafka_vehicle_position where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>