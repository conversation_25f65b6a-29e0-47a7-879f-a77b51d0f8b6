package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 人员安全小结VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonnelSafetySummaryVO {
    
    /**
     * 当前井下作业人员数
     */
    private Long currentUndergroundPersonnel;
    
    /**
     * 检测到违规行为数
     */
    private Long detectedViolations;
    
    /**
     * 人员求救报警数
     */
    private Long personnelDistressAlarms;
    
    /**
     * 存在超时人员数
     */
    private Long timeoutPersonnel;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 统计周期
     */
    private String period;
}
