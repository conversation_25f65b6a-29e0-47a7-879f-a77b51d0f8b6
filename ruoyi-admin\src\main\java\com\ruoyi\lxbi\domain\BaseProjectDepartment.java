package com.ruoyi.lxbi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;
import java.util.Date;

/**
 * 项目部门配置对象 base_project_department
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseProjectDepartment extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 项目部门名称 */
    @Excel(name = "项目部门名称")
    private String projectDepartmentName;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 项目部门开始时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "项目部门开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 项目部门结束时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "项目部门结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 是否删除 */
    private Long isDelete;

}
