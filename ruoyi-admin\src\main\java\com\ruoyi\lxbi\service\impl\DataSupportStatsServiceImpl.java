package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataSupportStatsRequest;
import com.ruoyi.lxbi.domain.response.DataSupportTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportDepartmentWithPlanStats;
import com.ruoyi.lxbi.mapper.DataSupportStatsMapper;
import com.ruoyi.lxbi.service.IDataSupportStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 支护数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class DataSupportStatsServiceImpl implements IDataSupportStatsService {
    @Autowired
    private DataSupportStatsMapper dataSupportStatsMapper;

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    @Override
    public List<DataSupportTotalWithPlanStats> selectTotalWithPlanStatsList(DataSupportStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataSupportStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataSupportStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataSupportStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认为月统计
            return dataSupportStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合（含计划量）
     */
    @Override
    public List<DataSupportDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataSupportStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataSupportStatsMapper.selectDailyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataSupportStatsMapper.selectWeeklyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataSupportStatsMapper.selectYearlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            // 默认为月统计
            return dataSupportStatsMapper.selectMonthlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }
}
