package com.ruoyi.lxbi.domain.request;

import com.ruoyi.lxbi.domain.DataCrushingOperation;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 破碎数据批量操作DTO
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataCrushingOperationBatchDto extends DataCrushingOperation {
    
    /** 是否为新增数据 */
    private Boolean isNew;
    
    /** 作业时段名称 */
    private String workingPeriodName;
}
