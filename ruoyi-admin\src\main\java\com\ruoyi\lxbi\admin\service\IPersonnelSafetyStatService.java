package com.ruoyi.lxbi.admin.service;

import com.ruoyi.lxbi.domain.vo.*;
import com.ruoyi.lxbi.domain.ApiAiAlarm;

import java.util.List;

/**
 * 人员安全统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IPersonnelSafetyStatService {

    /**
     * 获取人员安全概览统计
     * 包括：下井人次、区域超时、教育培训、违规行为
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 人员安全概览统计数据
     */
    PersonnelSafetyOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取主要超时组分布统计
     * 用于饼图显示各组别的超时情况分布
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 主要超时组分布统计数据
     */
    List<TimeoutGroupDistributionVO> getTimeoutGroupDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取区域超时人员名单
     * 显示具体的超时人员信息
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 区域超时人员名单
     */
    List<AreaTimeoutPersonnelVO> getAreaTimeoutPersonnel(String viewType, String startDate, String endDate);

    /**
     * 获取来救数量趋势统计
     * 用于折线图显示救援和培训数量的变化趋势
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 来救数量趋势统计数据
     */
    List<RescueQuantityTrendVO> getRescueQuantityTrend(String viewType, String startDate, String endDate);

    /**
     * 获取人员安全综合统计数据（用于仪表板）
     * 包含所有人员安全相关的统计数据
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 人员安全综合统计数据
     */
    PersonnelSafetyDashboardVO getDashboardData(String viewType, String startDate, String endDate);

    /**
     * 获取人员安全小结统计
     * 包括：当前井下作业人员、检测到违规行为、人员求救报警、存在超时人员等
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 人员安全小结统计数据
     */
    PersonnelSafetySummaryVO getSafetySummary(String viewType, String startDate, String endDate);

    /**
     * 生成人员安全小结文本描述
     * 根据统计数据生成文字说明
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 人员安全小结文本数据
     */
    PersonnelSafetySummaryTextVO generateSafetySummaryText(String viewType, String startDate, String endDate);

    /**
     * 获取人员安全简化概览
     * 包括：下井人数、违规行为、求救数
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 人员安全简化概览数据
     */
    PersonnelSafetySimpleOverviewVO getSimpleOverview(String viewType, String startDate, String endDate);

    /**
     * 获取人员违规行为详细列表（基于AI报警数据）
     *
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 违规行为详细列表
     */
    List<ApiAiAlarm> getViolationDetailList(String startDate, String endDate);

    /**
     * 获取井下人员分布统计
     * 按区域统计井下人员分布情况
     *
     * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 井下人员分布统计数据
     */
    List<UndergroundPersonnelDistributionVO> getUndergroundPersonnelDistribution(String viewType, String startDate, String endDate);
}
