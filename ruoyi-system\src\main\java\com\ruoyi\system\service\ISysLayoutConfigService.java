package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysLayoutConfig;

/**
 * 布局配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface ISysLayoutConfigService 
{
    /**
     * 查询布局配置
     * 
     * @param id 布局配置主键
     * @return 布局配置
     */
    public SysLayoutConfig selectSysLayoutConfigById(Long id);

    /**
     * 查询布局配置列表
     * 
     * @param sysLayoutConfig 布局配置
     * @return 布局配置集合
     */
    public List<SysLayoutConfig> selectSysLayoutConfigList(SysLayoutConfig sysLayoutConfig);

    /**
     * 新增布局配置
     * 
     * @param sysLayoutConfig 布局配置
     * @return 结果
     */
    public int insertSysLayoutConfig(SysLayoutConfig sysLayoutConfig);

    /**
     * 修改布局配置
     * 
     * @param sysLayoutConfig 布局配置
     * @return 结果
     */
    public int updateSysLayoutConfig(SysLayoutConfig sysLayoutConfig);

    /**
     * 批量删除布局配置
     * 
     * @param ids 需要删除的布局配置主键集合
     * @return 结果
     */
    public int deleteSysLayoutConfigByIds(Long[] ids);

    /**
     * 删除布局配置信息
     * 
     * @param id 布局配置主键
     * @return 结果
     */
    public int deleteSysLayoutConfigById(Long id);
}
