package com.ruoyi.lxbi.controller;

import java.util.List;
import java.util.Map;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.format.annotation.DateTimeFormat;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.lxbi.admin.domain.SurfaceMonitoringData;
import com.ruoyi.lxbi.admin.service.ISurfaceMonitoringDataService;
import com.ruoyi.lxbi.admin.service.SurfaceMonitoringExternalService;
import lombok.extern.slf4j.Slf4j;

/**
 * 地表监测数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Slf4j
@RestController
@RequestMapping("/lxbi/surfaceMonitoring")
public class SurfaceMonitoringDataController extends BaseController {
    
    @Autowired
    private ISurfaceMonitoringDataService surfaceMonitoringDataService;

    @Autowired
    private SurfaceMonitoringExternalService surfaceMonitoringExternalService;

    /**
     * 查询地表监测数据列表
     */
    @PreAuthorize("@ss.hasPermi('lxbi:surfaceMonitoring:list')")
    @GetMapping("/list")
    public TableDataInfo list(SurfaceMonitoringData surfaceMonitoringData) {
        startPage();
        List<SurfaceMonitoringData> list = surfaceMonitoringDataService.selectSurfaceMonitoringDataList(surfaceMonitoringData);
        return getDataTable(list);
    }

    /**
     * 导出地表监测数据列表
     */
    @PreAuthorize("@ss.hasPermi('lxbi:surfaceMonitoring:export')")
    @Log(title = "地表监测数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SurfaceMonitoringData surfaceMonitoringData) {
        List<SurfaceMonitoringData> list = surfaceMonitoringDataService.selectSurfaceMonitoringDataList(surfaceMonitoringData);
        ExcelUtil<SurfaceMonitoringData> util = new ExcelUtil<SurfaceMonitoringData>(SurfaceMonitoringData.class);
        util.exportExcel(list, "地表监测数据", "地表监测数据");
    }

    /**
     * 获取地表监测数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('lxbi:surfaceMonitoring:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(surfaceMonitoringDataService.selectSurfaceMonitoringDataById(id));
    }

    /**
     * 新增地表监测数据
     */
    @PreAuthorize("@ss.hasPermi('lxbi:surfaceMonitoring:add')")
    @Log(title = "地表监测数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SurfaceMonitoringData surfaceMonitoringData) {
        return toAjax(surfaceMonitoringDataService.insertSurfaceMonitoringData(surfaceMonitoringData));
    }

    /**
     * 修改地表监测数据
     */
    @PreAuthorize("@ss.hasPermi('lxbi:surfaceMonitoring:edit')")
    @Log(title = "地表监测数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SurfaceMonitoringData surfaceMonitoringData) {
        return toAjax(surfaceMonitoringDataService.updateSurfaceMonitoringData(surfaceMonitoringData));
    }

    /**
     * 删除地表监测数据
     */
    @PreAuthorize("@ss.hasPermi('lxbi:surfaceMonitoring:remove')")
    @Log(title = "地表监测数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(surfaceMonitoringDataService.deleteSurfaceMonitoringDataByIds(ids));
    }

    /**
     * 获取地表监测统计数据
     */
    @Anonymous
    @GetMapping("/statistics")
    @Log(title = "地表监测统计", businessType = BusinessType.OTHER)
    public AjaxResult getStatistics() {
        try {
            log.info("获取地表监测统计数据");
            Map<String, Object> statistics = surfaceMonitoringDataService.getStatistics();
            return AjaxResult.success("获取统计数据成功").put("data", statistics);
        } catch (Exception e) {
            log.error("获取地表监测统计数据失败", e);
            return error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 同步第三方地表监测数据
     */
    @Anonymous
    @PostMapping("/sync")
    @Log(title = "地表监测数据同步", businessType = BusinessType.OTHER)
    public AjaxResult syncData() {
        try {
            log.info("开始同步第三方地表监测数据");
            Map<String, Object> result = surfaceMonitoringDataService.syncSurfaceMonitoringData();
            
            if ((Boolean) result.get("success")) {
                return AjaxResult.success("同步成功").put("data", result);
            } else {
                return error("同步失败: " + result.get("message"));
            }
        } catch (Exception e) {
            log.error("同步第三方地表监测数据失败", e);
            return error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 测试第三方API连接
     */
    @Anonymous
    @PostMapping("/testConnection")
    @Log(title = "测试API连接", businessType = BusinessType.OTHER)
    public AjaxResult testConnection() {
        try {
            log.info("测试第三方地表监测API连接");
            Map<String, Object> result = surfaceMonitoringExternalService.testConnection();
            
            if ((Boolean) result.get("success")) {
                return AjaxResult.success("连接测试成功").put("data", result);
            } else {
                return error("连接测试失败: " + result.get("message"));
            }
        } catch (Exception e) {
            log.error("测试第三方API连接失败", e);
            return error("连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取API健康状态
     */
    @Anonymous
    @GetMapping("/health")
    public AjaxResult getHealthStatus() {
        try {
            log.info("获取第三方地表监测API健康状态");
            Map<String, Object> status = surfaceMonitoringExternalService.getHealthStatus();
            return AjaxResult.success("获取健康状态成功").put("data", status);
        } catch (Exception e) {
            log.error("获取API健康状态失败", e);
            return error("获取健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 按日期范围查询地表监测数据
     */
    @Anonymous
    @GetMapping("/dateRange")
    public AjaxResult getByDateRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            log.info("按日期范围查询地表监测数据: {} 到 {}", startDate, endDate);
            List<SurfaceMonitoringData> list = surfaceMonitoringDataService.selectByDateRange(startDate, endDate);
            return AjaxResult.success("查询成功").put("data", list);
        } catch (Exception e) {
            log.error("按日期范围查询地表监测数据失败", e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按基站名称查询地表监测数据
     */
    @Anonymous
    @GetMapping("/station/{stationName}")
    public AjaxResult getByStationName(@PathVariable("stationName") String stationName) {
        try {
            log.info("按基站名称查询地表监测数据: {}", stationName);
            List<SurfaceMonitoringData> list = surfaceMonitoringDataService.selectByStationName(stationName);
            return AjaxResult.success("查询成功").put("data", list);
        } catch (Exception e) {
            log.error("按基站名称查询地表监测数据失败", e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新的监测数据
     */
    @Anonymous
    @GetMapping("/latest")
    public AjaxResult getLatestData(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            log.info("获取最新的监测数据，限制数量: {}", limit);
            List<SurfaceMonitoringData> list = surfaceMonitoringDataService.selectLatestData(limit);
            return AjaxResult.success("查询成功").put("data", list);
        } catch (Exception e) {
            log.error("获取最新监测数据失败", e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取偏移异常数据
     */
    @Anonymous
    @GetMapping("/abnormal")
    public AjaxResult getAbnormalData(@RequestParam(defaultValue = "10.0") Double threshold) {
        try {
            log.info("获取偏移异常数据，阈值: {}", threshold);
            List<SurfaceMonitoringData> list = surfaceMonitoringDataService.selectAbnormalOffsetData(threshold);
            return AjaxResult.success("查询成功").put("data", list);
        } catch (Exception e) {
            log.error("获取偏移异常数据失败", e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 按日期获取统计信息
     */
    @Anonymous
    @GetMapping("/statistics/{date}")
    public AjaxResult getStatisticsByDate(@PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        try {
            log.info("按日期获取统计信息: {}", date);
            Map<String, Object> statistics = surfaceMonitoringDataService.getStatisticsByDate(date);
            return AjaxResult.success("查询成功").put("data", statistics);
        } catch (Exception e) {
            log.error("按日期获取统计信息失败", e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试处理单条API数据
     */
    @Anonymous
    @PostMapping("/test/processApiData")
    public AjaxResult testProcessApiData(@RequestBody Map<String, Object> apiData) {
        try {
            log.info("测试处理单条API数据");
            boolean result = surfaceMonitoringDataService.processApiData(apiData);
            
            Map<String, Object> response = new java.util.HashMap<>();
            response.put("success", result);
            response.put("message", result ? "处理成功" : "处理失败");
            response.put("apiData", apiData);
            
            return AjaxResult.success("测试完成").put("data", response);
        } catch (Exception e) {
            log.error("测试处理API数据失败", e);
            return error("测试失败: " + e.getMessage());
        }
    }
}
