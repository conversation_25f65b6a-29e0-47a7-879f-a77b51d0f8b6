package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 高频发隐患位置VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LocationFrequencyVO {
    
    /**
     * 位置名称
     */
    private String location;
    
    /**
     * 隐患数量
     */
    private Long count;
    
    /**
     * 占比百分比
     */
    private BigDecimal percentage;
}
