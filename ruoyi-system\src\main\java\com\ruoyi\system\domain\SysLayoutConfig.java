package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 布局配置对象 sys_layout_config
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysLayoutConfig extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 布局唯一标识（业务级名称） */
    @Excel(name = "布局唯一标识", readConverterExp = "业=务级名称")
    private String layoutName;

    /** 布局类型: dashboard仪表板/page页面/custom自定义 */
    @Excel(name = "布局类型: dashboard仪表板/page页面/custom自定义")
    private String layoutType;

    /** 布局JSON配置（JSONB支持索引查询） */
    private String configJson;

}
