package com.ruoyi.lxbi.mapper;

import com.ruoyi.lxbi.domain.response.DataSupportTypeTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportTypeDepartmentWithPlanStats;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 锚网支护数据统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface DataBoltMeshSupportStatsMapper {

    /**
     * 查询日锚网支护总体统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日锚网支护总体统计数据集合（含计划量）
     */
    public List<DataSupportTypeTotalWithPlanStats> selectDailyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询周锚网支护总体统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 周锚网支护总体统计数据集合（含计划量）
     */
    public List<DataSupportTypeTotalWithPlanStats> selectWeeklyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询月锚网支护总体统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 月锚网支护总体统计数据集合（含计划量）
     */
    public List<DataSupportTypeTotalWithPlanStats> selectMonthlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询年锚网支护总体统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 年锚网支护总体统计数据集合（含计划量）
     */
    public List<DataSupportTypeTotalWithPlanStats> selectYearlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询日锚网支护项目部门统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日锚网支护项目部门统计数据集合（含计划量）
     */
    public List<DataSupportTypeDepartmentWithPlanStats> selectDailyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询周锚网支护项目部门统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 周锚网支护项目部门统计数据集合（含计划量）
     */
    public List<DataSupportTypeDepartmentWithPlanStats> selectWeeklyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询月锚网支护项目部门统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 月锚网支护项目部门统计数据集合（含计划量）
     */
    public List<DataSupportTypeDepartmentWithPlanStats> selectMonthlyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询年锚网支护项目部门统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 年锚网支护项目部门统计数据集合（含计划量）
     */
    public List<DataSupportTypeDepartmentWithPlanStats> selectYearlyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
