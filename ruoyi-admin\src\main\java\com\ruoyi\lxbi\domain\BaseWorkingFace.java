package com.ruoyi.lxbi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;
import java.util.Date;

/**
 * 中段-工作面配置对象 base_working_face
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseWorkingFace extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 工作面ID */
    private Long workingFaceId;

    /** 工作面名称 */
    @Excel(name = "工作面名称")
    private String workingFaceName;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 工作面开始时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "工作面开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 工作面结束时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "工作面结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 是否删除 */
    private Long isDelete;

}
