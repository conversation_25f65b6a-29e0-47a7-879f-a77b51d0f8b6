package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DataTunnelingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataTunnelingDepartmentWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingStopeStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingWorkingFaceStats;

import java.util.List;

/**
 * 掘进数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IDataTunnelingStatsService {

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    public List<DataTunnelingTotalWithPlanStats> selectTotalWithPlanStatsList(DataTunnelingStatsRequest request, String viewType);

    /**
     * 查询项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合（含计划量）
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataTunnelingStatsRequest request, String viewType);

    /**
     * 查询重点工程总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 重点工程总体统计数据集合（含计划量）
     */
    public List<DataTunnelingTotalWithPlanStats> selectPriorityProjectTotalWithPlanStatsList(DataTunnelingStatsRequest request, String viewType);

    /**
     * 查询重点工程项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 重点工程项目部门统计数据集合（含计划量）
     */
    public List<DataTunnelingDepartmentWithPlanStats> selectPriorityProjectDepartmentWithPlanStatsList(DataTunnelingStatsRequest request, String viewType);

    /**
     * 查询重点工程掘进数据采场统计列表
     *
     * @param request 查询请求
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 重点工程掘进数据采场统计集合
     */
    List<DataTunnelingStopeStats> selectPriorityProjectStopeStatsList(DataTunnelingStatsRequest request, String viewType);

    /**
     * 查询重点工程掘进数据工作面统计列表
     *
     * @param request 查询请求
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 重点工程掘进数据工作面统计集合
     */
    List<DataTunnelingWorkingFaceStats> selectPriorityProjectWorkingFaceStatsList(DataTunnelingStatsRequest request, String viewType);
}
