package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 主要超时组分布VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TimeoutGroupDistributionVO {
    
    /**
     * 组别名称
     */
    private String groupName;
    
    /**
     * 超时次数
     */
    private Long timeoutCount;
    
    /**
     * 占比百分比
     */
    private BigDecimal percentage;
}
