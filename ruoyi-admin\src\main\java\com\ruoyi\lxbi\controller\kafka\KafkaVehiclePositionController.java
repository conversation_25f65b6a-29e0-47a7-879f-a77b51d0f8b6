package com.ruoyi.lxbi.controller.kafka;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.KafkaVehiclePosition;
import com.ruoyi.lxbi.service.IKafkaVehiclePositionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 车辆定位数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@RestController
@RequestMapping("/kafka/VehiclePosition")
public class KafkaVehiclePositionController extends BaseController {
    @Autowired
    private IKafkaVehiclePositionService kafkaVehiclePositionService;

    /**
     * 查询车辆定位数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehiclePosition:list')")
    @GetMapping("/list")
    public TableDataInfo list(KafkaVehiclePosition kafkaVehiclePosition) {
        startPage();
        List<KafkaVehiclePosition> list = kafkaVehiclePositionService.selectKafkaVehiclePositionList(kafkaVehiclePosition);
        return getDataTable(list);
    }

    /**
     * 导出车辆定位数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehiclePosition:export')")
    @Log(title = "车辆定位数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KafkaVehiclePosition kafkaVehiclePosition) {
        List<KafkaVehiclePosition> list = kafkaVehiclePositionService.selectKafkaVehiclePositionList(kafkaVehiclePosition);
        ExcelUtil<KafkaVehiclePosition> util = new ExcelUtil<KafkaVehiclePosition>(KafkaVehiclePosition.class);
        util.exportExcel(response, list, "车辆定位数据数据");
    }

    /**
     * 获取车辆定位数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehiclePosition:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kafkaVehiclePositionService.selectKafkaVehiclePositionById(id));
    }

    /**
     * 新增车辆定位数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehiclePosition:add')")
    @Log(title = "车辆定位数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KafkaVehiclePosition kafkaVehiclePosition)
    {
        return toAjax(kafkaVehiclePositionService.insertKafkaVehiclePosition(kafkaVehiclePosition));
    }

    /**
     * 修改车辆定位数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehiclePosition:edit')")
    @Log(title = "车辆定位数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KafkaVehiclePosition kafkaVehiclePosition)
    {
        return toAjax(kafkaVehiclePositionService.updateKafkaVehiclePosition(kafkaVehiclePosition));
    }

    /**
     * 删除车辆定位数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:VehiclePosition:remove')")
    @Log(title = "车辆定位数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kafkaVehiclePositionService.deleteKafkaVehiclePositionByIds(ids));
    }
}
