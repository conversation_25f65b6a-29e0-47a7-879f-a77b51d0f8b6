# 充填漏浆检测统计接口说明

## 概述

本文档说明了充填漏浆检测统计模块的接口设计和实现，该模块提供充填漏浆检测相关的统计分析功能，包括报警概览、趋势分析和位置分布等统计数据。

## 接口列表

### 1. 充填漏浆检测概览统计

**接口地址**: `GET /lxbi/stat/filling-leakage/overview`

**功能描述**: 获取充填漏浆检测的总体概览数据

**请求参数**:
- `viewType`: 视图类型 (day/week/month)
- `startDate`: 开始日期 (yyyy-MM-dd)
- `endDate`: 结束日期 (yyyy-MM-dd)

**响应数据**:
```json
{
  "totalAlarmCount": 200,
  "areaIntrusionCount": 200,
  "temperatureDifferenceAlarmCount": 200,
  "startDate": "2025-08-20",
  "endDate": "2025-08-25",
  "period": "day"
}
```

### 2. 充填漏浆检测趋势统计

**接口地址**: `GET /lxbi/stat/filling-leakage/alarm-trend`

**功能描述**: 获取充填漏浆检测趋势数据，用于折线图展示

**响应数据**:
```json
[
  {
    "date": "2025-08-20",
    "shortDate": "08-20",
    "alarmTrendCount": 100,
    "areaIntrusionCount": 95,
    "temperatureDifferenceCount": 90
  },
  {
    "date": "2025-08-21",
    "shortDate": "08-21",
    "alarmTrendCount": 90,
    "areaIntrusionCount": 85,
    "temperatureDifferenceCount": 80
  }
]
```

### 3. 充填漏浆检测位置分布统计

**接口地址**: `GET /lxbi/stat/filling-leakage/location-distribution`

**功能描述**: 获取充填漏浆检测位置分布数据，用于雷达图展示

**响应数据**:
```json
[
  {
    "locationName": "一号回风井井口",
    "depthLevel": "-992m",
    "alarmPointCount": 208,
    "xCoordinate": 0.0,
    "yCoordinate": 1.0,
    "locationCode": "LOC_001"
  },
  {
    "locationName": "",
    "depthLevel": "-960m",
    "alarmPointCount": 230,
    "xCoordinate": -0.8,
    "yCoordinate": 0.6,
    "locationCode": "LOC_002"
  },
  {
    "locationName": "",
    "depthLevel": "-1200m",
    "alarmPointCount": 1200,
    "xCoordinate": 0.2,
    "yCoordinate": 1.0,
    "locationCode": "LOC_010"
  }
]
```

## 数据结构说明

### VO类设计

#### 1. FillingLeakageOverviewVO
- **用途**: 充填漏浆检测概览数据
- **字段**: 报警事件总数、区域入侵数量、温差报警数量

#### 2. FillingLeakageTrendVO
- **用途**: 充填漏浆检测趋势数据（折线图）
- **字段**: 日期、报警趋势数量、区域入侵数量、温差报警数量

#### 3. FillingLeakageLocationDistributionVO
- **用途**: 充填漏浆检测位置分布数据（雷达图）
- **字段**: 位置名称、深度标识、报警点数量、坐标信息

## 业务逻辑

### 1. 数据统计维度

**时间维度**:
- 日统计: 按天统计充填漏浆检测数据
- 周统计: 按周统计充填漏浆检测数据
- 月统计: 按月统计充填漏浆检测数据

**空间维度**:
- 按深度层级统计
- 按井口位置统计
- 按报警点分布统计

### 2. 统计指标

**基础指标**:
- 报警事件总数: 所有类型报警的总数量
- 区域入侵数量: 区域入侵类型的报警数量
- 温差报警数量: 温度差异类型的报警数量

**趋势分析指标**:
- 报警趋势变化: 时间序列上的报警数量变化
- 下降趋势: 模拟数据显示报警数量呈下降趋势
- 多指标对比: 同时展示多种类型报警的趋势

**位置分析指标**:
- 深度分布: 不同深度层级的报警分布
- 空间坐标: 雷达图上的位置坐标
- 报警密度: 各位置的报警点密度

### 3. 数据展示

**概览卡片**:
- 报警事件总数: 200起
- 区域入侵: 200起
- 温差报警: 200起

**趋势图展示**:
- 折线图显示报警数量的时间变化
- 呈现下降趋势，表明检测效果良好
- 支持多指标同时展示

**雷达图展示**:
- 显示不同深度层级的报警分布
- 坐标范围: X和Y坐标均在[-1.0, 1.0]范围内
- 报警点数量通过数值大小体现

## 模拟数据说明

### 1. 概览数据
- 报警事件总数: 200起
- 区域入侵数量: 200起
- 温差报警数量: 200起

### 2. 趋势数据特点
- **下降趋势**: 从100开始，每个时间点减少10
- **最小值保护**: 最小值不低于20，确保数据合理性
- **多指标关联**: 区域入侵和温差报警数量与总趋势相关

### 3. 位置分布数据
包含以下深度层级:
- **-992m**: 一号回风井井口 (208个报警点)
- **-960m**: 230个报警点
- **-960-840m**: 100个报警点
- **-840m**: 119个报警点
- **-480m**: 200个报警点
- **-1130m**: 110个报警点
- **-1060m**: 135个报警点
- **-1020-1060m**: 71个报警点
- **-1020m**: 140个报警点
- **-1200m**: 1200个报警点

### 4. 雷达图坐标设计
- **坐标系**: 标准化坐标系，范围[-1.0, 1.0]
- **位置分布**: 均匀分布在雷达图的各个方向
- **数据映射**: 报警点数量映射到雷达图的数值轴

## 扩展接入点

### 1. 充填漏浆检测系统接入
```java
// 可接入的数据源
- 充填漏浆监测系统
- 温度传感器网络
- 区域入侵检测系统
- 井下环境监控系统
```

### 2. 数据接入方式
- **实时数据**: 通过Kafka队列接收传感器数据
- **定时同步**: 定时从监测系统同步数据
- **API接口**: 通过REST API获取检测数据

### 3. 数据库设计建议
```sql
-- 充填漏浆检测记录表
CREATE TABLE filling_leakage_detection (
    id BIGSERIAL PRIMARY KEY,
    detection_point_code VARCHAR(50),
    location_name VARCHAR(100),
    depth_level VARCHAR(20),
    alarm_type VARCHAR(50),
    alarm_time TIMESTAMP,
    temperature_value DECIMAL(5,2),
    threshold_value DECIMAL(5,2),
    x_coordinate DECIMAL(10,6),
    y_coordinate DECIMAL(10,6),
    alarm_status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 检测点基础信息表
CREATE TABLE detection_point_info (
    id BIGSERIAL PRIMARY KEY,
    point_code VARCHAR(50) UNIQUE,
    point_name VARCHAR(100),
    location_name VARCHAR(100),
    depth_level VARCHAR(20),
    x_coordinate DECIMAL(10,6),
    y_coordinate DECIMAL(10,6),
    point_type VARCHAR(50),
    status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 报警阈值配置表
CREATE TABLE alarm_threshold_config (
    id BIGSERIAL PRIMARY KEY,
    alarm_type VARCHAR(50),
    threshold_value DECIMAL(10,2),
    threshold_unit VARCHAR(20),
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 测试说明

### 1. 单元测试
- 所有Service方法的功能测试
- 不同视图类型的测试
- 数据完整性和一致性测试
- 雷达图坐标范围验证

### 2. 接口测试
```bash
# 测试概览统计
curl -X GET "http://localhost:8080/lxbi/stat/filling-leakage/overview?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试趋势统计
curl -X GET "http://localhost:8080/lxbi/stat/filling-leakage/alarm-trend?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试位置分布
curl -X GET "http://localhost:8080/lxbi/stat/filling-leakage/location-distribution?viewType=day&startDate=2025-08-20&endDate=2025-08-25"
```

### 3. 数据验证
- 趋势数据的下降趋势验证
- 雷达图坐标范围验证 ([-1.0, 1.0])
- 深度层级数据的合理性验证
- 报警点总数的一致性验证

## 后续开发计划

### 1. 数据接入
- 接入真实的充填漏浆检测系统
- 建立温度传感器数据流
- 实现区域入侵自动检测

### 2. 功能增强
- 报警阈值动态配置
- 预警机制建立
- 历史数据分析

### 3. 可视化优化
- 3D井下结构展示
- 实时数据刷新
- 移动端监控界面

## 业务价值

### 1. 安全管理提升
- **实时监控**: 及时发现充填漏浆问题
- **预警机制**: 提前预警潜在风险
- **位置定位**: 精确定位问题发生位置

### 2. 运营效率优化
- **趋势分析**: 了解检测效果变化趋势
- **资源配置**: 基于数据优化检测点布局
- **维护计划**: 制定针对性的维护计划

### 3. 技术创新支持
- **数据驱动**: 基于数据进行决策
- **智能分析**: 支持机器学习算法应用
- **系统集成**: 与其他安全系统集成

## 总结

充填漏浆检测统计模块提供了完整的充填漏浆监控和分析功能，通过多维度的统计分析，帮助管理人员及时了解充填作业状况，预防漏浆事故，提高充填作业安全性。

当前实现使用模拟数据，完美对应了界面图片中的所有功能模块，为后续接入真实检测数据预留了扩展接口，可以根据实际业务需求进行数据源的接入和功能的扩展。
