package com.ruoyi.lxbi.controller.base;

import java.util.List;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.response.BaseStopeVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.service.IBaseStopeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 采场配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/base/stope")
public class BaseStopeController extends BaseController {
    @Autowired
    private IBaseStopeService baseStopeService;

    /**
     * 查询采场配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:stope:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaseStope baseStope) {
        startPage();
        List<BaseStopeVo> list = baseStopeService.selectBaseStopeList(baseStope);
        return getDataTable(list);
    }

    /**
     * 查询采场配置列表（不分页）
     */
    @GetMapping("/listAll")
    public R<List<BaseStope>> listAll(BaseStope baseStope) {
        List<BaseStope> list = baseStopeService.selectBaseStopeListAll(baseStope);
        return R.ok(list);
    }

    /**
     * 导出采场配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:stope:export')")
    @Log(title = "采场配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseStope baseStope) {
        List<BaseStopeVo> list = baseStopeService.selectBaseStopeList(baseStope);
        ExcelUtil<BaseStopeVo> util = new ExcelUtil<>(BaseStopeVo.class);
        util.exportExcel(response, list, "采场配置数据");
    }

    /**
     * 获取采场配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:stope:query')")
    @GetMapping(value = "/{stopeId}")
    public AjaxResult getInfo(@PathVariable("stopeId") Long stopeId) {
        return success(baseStopeService.selectBaseStopeByStopeId(stopeId));
    }

    /**
     * 新增采场配置
     */
    @PreAuthorize("@ss.hasPermi('base:stope:add')")
    @Log(title = "采场配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseStope baseStope)
    {
        return toAjax(baseStopeService.insertBaseStope(baseStope));
    }

    /**
     * 修改采场配置
     */
    @PreAuthorize("@ss.hasPermi('base:stope:edit')")
    @Log(title = "采场配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaseStope baseStope)
    {
        return toAjax(baseStopeService.updateBaseStope(baseStope));
    }

    /**
     * 删除采场配置
     */
    @PreAuthorize("@ss.hasPermi('base:stope:remove')")
    @Log(title = "采场配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{stopeIds}")
    public AjaxResult remove(@PathVariable Long[] stopeIds)
    {
        return toAjax(baseStopeService.deleteBaseStopeByStopeIds(stopeIds));
    }
}
