package com.ruoyi.lxbi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 采场月度计划对象 plan_stope_monthly
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanStopeMonthly extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 采场ID */
    private Long stopeId;

    /** 计划日期 */
    @Excel(name = "计划日期" , sort = 1, mergeByValue = true)
    private String planDate;

    /** 出矿量 */
    @Excel(name = "出矿量")
    private String oreOutput;

}
