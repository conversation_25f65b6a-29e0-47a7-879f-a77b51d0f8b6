package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 支护数据项目部门统计对象（含计划量） - 统一日/周/月/年项目部门统计
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
public class DataSupportDepartmentWithPlanStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 周开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekStartDate;

    /**
     * 周结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekEndDate;

    /**
     * 项目部门ID
     */
    @Excel(name = "项目部门ID")
    private Long projectDepartmentId;

    /**
     * 项目部门名称
     */
    @Excel(name = "项目部门名称")
    private String projectDepartmentName;

    /**
     * 总支护长度
     */
    @Excel(name = "总支护长度")
    private Double totalSupportLength;

    /**
     * 总支护方量
     */
    @Excel(name = "总支护方量")
    private Double totalSupportVolume;

    /**
     * 计划支护长度
     */
    @Excel(name = "计划支护长度")
    private Double planSupportLength;

    /**
     * 计划支护方量
     */
    @Excel(name = "计划支护方量")
    private Double planSupportVolume;
}
