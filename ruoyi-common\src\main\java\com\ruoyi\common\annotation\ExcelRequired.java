package com.ruoyi.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Excel必填字段注解
 * 用于标识Excel导入时的必填字段
 * 
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelRequired {
    
    /**
     * 错误消息
     */
    String message() default "该字段为必填项";
    
    /**
     * 是否启用必填验证
     */
    boolean enabled() default true;

}
