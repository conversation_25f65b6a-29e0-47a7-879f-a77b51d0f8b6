package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanDthMonthly;
import com.ruoyi.lxbi.domain.request.PlanDthMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanDthMonthlyVo;

/**
 * 潜孔月计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IPlanDthMonthlyService 
{
    /**
     * 查询潜孔月计划
     * 
     * @param id 潜孔月计划主键
     * @return 潜孔月计划
     */
    public PlanDthMonthly selectPlanDthMonthlyById(Long id);

    /**
     * 查询潜孔月计划列表
     *
     * @param planDthMonthly 潜孔月计划
     * @return 潜孔月计划集合
     */
    public List<PlanDthMonthlyVo> selectPlanDthMonthlyList(PlanDthMonthly planDthMonthly);

    /**
     * 新增潜孔月计划
     * 
     * @param planDthMonthly 潜孔月计划
     * @return 结果
     */
    public int insertPlanDthMonthly(PlanDthMonthly planDthMonthly);

    /**
     * 修改潜孔月计划
     * 
     * @param planDthMonthly 潜孔月计划
     * @return 结果
     */
    public int updatePlanDthMonthly(PlanDthMonthly planDthMonthly);

    /**
     * 批量删除潜孔月计划
     * 
     * @param ids 需要删除的潜孔月计划主键集合
     * @return 结果
     */
    public int deletePlanDthMonthlyByIds(Long[] ids);

    /**
     * 删除潜孔月计划信息
     *
     * @param id 潜孔月计划主键
     * @return 结果
     */
    public int deletePlanDthMonthlyById(Long id);

    /**
     * 批量保存潜孔月计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    public int batchSavePlanDthMonthly(List<PlanDthMonthlyBatchDto> batchDataList);
}
