package com.ruoyi.lxbi.admin.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Date;
import java.text.SimpleDateFormat;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.web.service.HiddenTroubleExternalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.admin.mapper.ApiHiddenTroubleRecordMapper;
import com.ruoyi.lxbi.admin.domain.ApiHiddenTroubleRecord;
import com.ruoyi.lxbi.admin.service.IApiHiddenTroubleRecordService;
import lombok.extern.slf4j.Slf4j;

/**
 * 隐患数据记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Slf4j
@Service
public class ApiHiddenTroubleRecordServiceImpl implements IApiHiddenTroubleRecordService
{
    @Autowired
    private ApiHiddenTroubleRecordMapper apiHiddenTroubleRecordMapper;

    @Autowired
    private HiddenTroubleExternalService hiddenTroubleExternalService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询隐患数据记录
     * 
     * @param id 隐患数据记录主键
     * @return 隐患数据记录
     */
    @Override
    public ApiHiddenTroubleRecord selectApiHiddenTroubleRecordById(Long id)
    {
        return apiHiddenTroubleRecordMapper.selectApiHiddenTroubleRecordById(id);
    }

    /**
     * 查询隐患数据记录列表
     * 
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 隐患数据记录
     */
    @Override
    public List<ApiHiddenTroubleRecord> selectApiHiddenTroubleRecordList(ApiHiddenTroubleRecord apiHiddenTroubleRecord)
    {
        return apiHiddenTroubleRecordMapper.selectApiHiddenTroubleRecordList(apiHiddenTroubleRecord);
    }

    /**
     * 新增隐患数据记录
     * 
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    @Override
    public int insertApiHiddenTroubleRecord(ApiHiddenTroubleRecord apiHiddenTroubleRecord)
    {
        apiHiddenTroubleRecord.setCreateTime(DateUtils.getNowDate());
        return apiHiddenTroubleRecordMapper.insertApiHiddenTroubleRecord(apiHiddenTroubleRecord);
    }

    /**
     * 修改隐患数据记录
     * 
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    @Override
    public int updateApiHiddenTroubleRecord(ApiHiddenTroubleRecord apiHiddenTroubleRecord)
    {
        apiHiddenTroubleRecord.setUpdateTime(DateUtils.getNowDate());
        return apiHiddenTroubleRecordMapper.updateApiHiddenTroubleRecord(apiHiddenTroubleRecord);
    }

    /**
     * 批量删除隐患数据记录
     * 
     * @param ids 需要删除的隐患数据记录主键
     * @return 结果
     */
    @Override
    public int deleteApiHiddenTroubleRecordByIds(Long[] ids)
    {
        return apiHiddenTroubleRecordMapper.deleteApiHiddenTroubleRecordByIds(ids);
    }

    /**
     * 删除隐患数据记录信息
     *
     * @param id 隐患数据记录主键
     * @return 结果
     */
    @Override
    public int deleteApiHiddenTroubleRecordById(Long id)
    {
        return apiHiddenTroubleRecordMapper.deleteApiHiddenTroubleRecordById(id);
    }

    /**
     * 根据通知编号查询隐患数据记录
     *
     * @param noticeNumber 通知编号
     * @return 隐患数据记录
     */
    @Override
    public ApiHiddenTroubleRecord selectByNoticeNumber(String noticeNumber)
    {
        return apiHiddenTroubleRecordMapper.selectByNoticeNumber(noticeNumber);
    }

    /**
     * 根据通知编号和隐患日期查询隐患数据记录
     *
     * @param noticeNumber 通知编号
     * @param troubleDate 隐患日期
     * @return 隐患数据记录
     */
    @Override
    public ApiHiddenTroubleRecord selectByNoticeNumberAndDate(String noticeNumber, Date troubleDate)
    {
        return apiHiddenTroubleRecordMapper.selectByNoticeNumberAndDate(noticeNumber, troubleDate);
    }

    /**
     * UPSERT操作（根据通知编号插入或更新）
     *
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    @Override
    public int upsertApiHiddenTroubleRecord(ApiHiddenTroubleRecord apiHiddenTroubleRecord)
    {
        // 检查是否已存在（基于通知编号和隐患日期的复合唯一性）
        ApiHiddenTroubleRecord existingRecord = selectByNoticeNumberAndDate(
            apiHiddenTroubleRecord.getNoticeNumber(),
            apiHiddenTroubleRecord.getTroubleDate()
        );
        boolean isUpdate = existingRecord != null;

        // 设置时间字段
        Date now = DateUtils.getNowDate();
        if (isUpdate) {
            // 更新操作：保留原创建时间，更新修改时间
            apiHiddenTroubleRecord.setCreateTime(existingRecord.getCreateTime());
            apiHiddenTroubleRecord.setCreateBy(existingRecord.getCreateBy());
        } else {
            // 新增操作：设置创建时间
            apiHiddenTroubleRecord.setCreateTime(now);
            if (!StringUtils.hasText(apiHiddenTroubleRecord.getCreateBy())) {
                apiHiddenTroubleRecord.setCreateBy("api-system");
            }
        }

        // 设置更新时间
        apiHiddenTroubleRecord.setUpdateTime(now);
        if (!StringUtils.hasText(apiHiddenTroubleRecord.getUpdateBy())) {
            apiHiddenTroubleRecord.setUpdateBy("api-system");
        }

        // 执行UPSERT操作
        try {
            int result = apiHiddenTroubleRecordMapper.upsertApiHiddenTroubleRecord(apiHiddenTroubleRecord);

            if (result > 0) {
                String operation = isUpdate ? "更新" : "新增";
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String dateStr = apiHiddenTroubleRecord.getTroubleDate() != null ?
                    dateFormat.format(apiHiddenTroubleRecord.getTroubleDate()) : "null";
                log.debug("{}隐患数据成功，通知编号: {}, 隐患日期: {}",
                    operation, apiHiddenTroubleRecord.getNoticeNumber(), dateStr);
            }

            return result;
        } catch (Exception e) {
            log.error("UPSERT隐患数据失败，通知编号: {}, 错误: {}",
                apiHiddenTroubleRecord.getNoticeNumber(), e.getMessage());
            log.debug("失败的数据详情: 位置={}, 责任人={}, 部门={}, 检查人={}",
                apiHiddenTroubleRecord.getTroubleLocation(),
                apiHiddenTroubleRecord.getResponsiblePerson(),
                apiHiddenTroubleRecord.getResponsibleDepartment(),
                apiHiddenTroubleRecord.getInspector());
            return 0;
        }
    }

    /**
     * 处理第三方API隐患数据（插入或更新）
     *
     * @param apiData 第三方API数据
     * @return 处理结果
     */
    @Override
    public boolean processApiData(Map<String, Object> apiData)
    {
        try {
            log.debug("开始处理第三方API隐患数据");

            // 解析API数据
            ApiHiddenTroubleRecord record = parseApiData(apiData);
            if (record == null) {
                log.warn("解析第三方API数据失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(record.getNoticeNumber())) {
                log.warn("通知编号为空，跳过处理");
                return false;
            }

            if (record.getTroubleDate() == null) {
                log.warn("隐患日期为空，跳过处理，通知编号: {}", record.getNoticeNumber());
                return false;
            }

            // 验证和修复必填字段
            if (!StringUtils.hasText(record.getTroubleLocation())) {
                record.setTroubleLocation("");
                log.warn("隐患位置为空，设置为默认值: 未指定位置，通知编号: {}", record.getNoticeNumber());
            }

            if (!StringUtils.hasText(record.getResponsiblePerson())) {
                record.setResponsiblePerson("");
                log.warn("责任人为空，设置为默认值: 未指定，通知编号: {}", record.getNoticeNumber());
            }

            if (!StringUtils.hasText(record.getResponsibleDepartment())) {
                record.setResponsibleDepartment("");
                log.warn("责任部门为空，设置为默认值: 未指定部门，通知编号: {}", record.getNoticeNumber());
            }

            if (!StringUtils.hasText(record.getInspector())) {
                record.setInspector("");
                log.warn("检查人为空，设置为默认值: 未指定，通知编号: {}", record.getNoticeNumber());
            }

            // 执行UPSERT操作
            int result = upsertApiHiddenTroubleRecord(record);

            if (result > 0) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String dateStr = record.getTroubleDate() != null ? dateFormat.format(record.getTroubleDate()) : "null";
                log.info("成功处理隐患数据，通知编号: {}, 隐患日期: {}, 责任人: {}, 部门: {}",
                    record.getNoticeNumber(), dateStr, record.getResponsiblePerson(), record.getResponsibleDepartment());
                return true;
            } else {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String dateStr = record.getTroubleDate() != null ? dateFormat.format(record.getTroubleDate()) : "null";
                log.warn("处理隐患数据失败，通知编号: {}, 隐患日期: {}", record.getNoticeNumber(), dateStr);
                return false;
            }

        } catch (Exception e) {
            log.error("处理第三方API隐患数据异常", e);
            return false;
        }
    }

    /**
     * 批量处理第三方API隐患数据
     *
     * @param apiDataList 第三方API数据列表
     * @return 处理结果统计
     */
    @Override
    public Map<String, Object> batchProcessApiData(List<Map<String, Object>> apiDataList)
    {
        Map<String, Object> result = new HashMap<>();
        int totalCount = apiDataList.size();
        int successCount = 0;
        int failCount = 0;

        log.info("开始批量处理隐患数据，总数: {}", totalCount);

        for (Map<String, Object> apiData : apiDataList) {
            try {
                boolean success = processApiData(apiData);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                failCount++;
                log.warn("批量处理中单条数据失败", e);
            }
        }

        result.put("totalCount", totalCount);
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("message", String.format("批量处理完成，总数: %d, 成功: %d, 失败: %d", totalCount, successCount, failCount));

        log.info("批量处理隐患数据完成，总数: {}, 成功: {}, 失败: {}", totalCount, successCount, failCount);
        return result;
    }

    /**
     * 解析第三方API数据为实体对象
     *
     * @param apiData 第三方API数据
     * @return 解析后的实体对象
     */
    @Override
    public ApiHiddenTroubleRecord parseApiData(Map<String, Object> apiData)
    {
        try {
            ApiHiddenTroubleRecord record = new ApiHiddenTroubleRecord();

            // 基本信息映射（处理必填字段的空值情况）
            record.setNoticeNumber(getStringValueWithDefault(apiData, "hiddenTroubleNumber", ""));
            record.setResponsiblePerson(getStringValueWithDefault(apiData, "rectificationUserName", ""));
            record.setTroubleLocation(getStringValueWithDefault(apiData, "hiddenTroubleLocationDescribe", ""));
            record.setResponsibleDepartment(getStringValueWithDefault(apiData, "rectificationDepartmentName", ""));
            record.setTroubleGrade(getStringValueWithDefault(apiData, "hiddenTroubleGrade", "0"));
            record.setInspector(getStringValueWithDefault(apiData, "checkUserName", ""));
            record.setReviewer(getStringValue(apiData, "reexaminationUserName"));

            // 状态映射
            Object statusObj = apiData.get("status");
            if (statusObj != null) {
                record.setStatus(Long.valueOf(statusObj.toString()));
            }

            // 日期字段处理
            String checkTimeStr = getStringValue(apiData, "checkTime");
            if (StringUtils.hasText(checkTimeStr)) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    record.setTroubleDate(dateFormat.parse(checkTimeStr));
                } catch (Exception e) {
                    log.warn("解析检查时间失败: {}", checkTimeStr);
                }
            }

            String deadlineTimeStr = getStringValue(apiData, "deadlineTime");
            if (StringUtils.hasText(deadlineTimeStr)) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    record.setRectificationDeadline(dateFormat.parse(deadlineTimeStr));
                } catch (Exception e) {
                    log.warn("解析整改期限失败: {}", deadlineTimeStr);
                }
            }

            // 描述信息
            record.setTroubleDescription(getStringValue(apiData, "rectificationOpinion"));

            // 存储原始数据
            try {
                record.setOriginalData(objectMapper.writeValueAsString(apiData));
            } catch (Exception e) {
                log.warn("序列化原始数据失败", e);
            }

            return record;

        } catch (Exception e) {
            log.error("解析第三方API数据失败", e);
            return null;
        }
    }

    /**
     * 获取隐患统计数据
     *
     * @return 统计结果
     */
    @Override
    public Map<String, Object> getStatistics()
    {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 按状态统计
            List<Map<String, Object>> statusStats = apiHiddenTroubleRecordMapper.countByStatus();
            statistics.put("statusStats", statusStats);

            // 按部门统计
            List<Map<String, Object>> departmentStats = apiHiddenTroubleRecordMapper.countByDepartment();
            statistics.put("departmentStats", departmentStats);

            // 按等级统计
            List<Map<String, Object>> gradeStats = apiHiddenTroubleRecordMapper.countByGrade();
            statistics.put("gradeStats", gradeStats);

            // 计算总数
            int totalCount = 0;
            for (Map<String, Object> stat : statusStats) {
                totalCount += ((Number) stat.get("count")).intValue();
            }
            statistics.put("totalCount", totalCount);

            log.info("获取隐患统计数据成功，总数: {}", totalCount);

        } catch (Exception e) {
            log.error("获取隐患统计数据失败", e);
        }

        return statistics;
    }

    /**
     * 同步第三方隐患数据
     *
     * @return 同步结果
     */
    @Override
    public Map<String, Object> syncHiddenTroubleData()
    {
        return syncHiddenTroubleDataByDateRange(null, null);
    }

    /**
     * 按日期范围同步第三方隐患数据
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 同步结果
     */
    @Override
    public Map<String, Object> syncHiddenTroubleDataByDateRange(String startDate, String endDate)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            String dateRangeStr = (startDate != null && endDate != null) ?
                startDate + " 到 " + endDate : "全部数据";
            log.info("开始按日期范围同步第三方隐患数据，日期范围: {}", dateRangeStr);

            // 定义所有需要同步的状态：0:待整改 1:已驳回 2:已整改 3:待复查 4:已超期
            String[] states = {"0", "1", "2", "3", "4"};

            List<Map<String, Object>> allRecords = new ArrayList<>();
            Map<String, Integer> stateCountMap = new HashMap<>();

            // 循环调用每个状态的数据
            for (String state : states) {
                try {
                    log.info("正在同步状态为 {} 的隐患数据", getStateDescription(state));

                    // 调用第三方API获取指定状态和日期范围的隐患列表数据
                    Map<String, Object> apiResponse = hiddenTroubleExternalService.getStatisticsPage(1, -1, state, startDate, endDate);

                    if (apiResponse != null && "0".equals(String.valueOf(apiResponse.get("code")))) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> data = (Map<String, Object>) apiResponse.get("data");

                        if (data != null && data.get("records") != null) {
                            @SuppressWarnings("unchecked")
                            List<Map<String, Object>> records = (List<Map<String, Object>>) data.get("records");

                            allRecords.addAll(records);
                            stateCountMap.put(state, records.size());

                            log.info("状态 {} ({}) 的隐患数据获取成功，数量: {}",
                                state, getStateDescription(state), records.size());
                        } else {
                            stateCountMap.put(state, 0);
                            log.info("状态 {} ({}) 的隐患数据为空", state, getStateDescription(state));
                        }
                    } else {
                        stateCountMap.put(state, 0);
                        log.warn("调用状态 {} ({}) 的API失败，响应: {}",
                            state, getStateDescription(state), apiResponse);
                    }

                } catch (Exception e) {
                    stateCountMap.put(state, 0);
                    log.error("同步状态 {} ({}) 的数据异常", state, getStateDescription(state), e);
                }
            }

            // 批量处理所有获取到的数据
            if (!allRecords.isEmpty()) {
                Map<String, Object> batchResult = batchProcessApiData(allRecords);

                result.put("success", true);
                result.put("syncTime", new Date());
                result.put("dateRange", dateRangeStr);
                result.put("stateCountMap", stateCountMap);
                result.putAll(batchResult);

                log.info("按日期范围同步第三方隐患数据完成，日期范围: {}, 各状态数量: {}, 处理结果: {}",
                    dateRangeStr, stateCountMap, batchResult);
            } else {
                result.put("success", false);
                result.put("message", "所有状态的第三方API都返回空数据");
                result.put("dateRange", dateRangeStr);
                result.put("stateCountMap", stateCountMap);
                log.warn("所有状态的第三方API都返回空数据，日期范围: {}", dateRangeStr);
            }

        } catch (Exception e) {
            log.error("按日期范围同步第三方隐患数据异常，日期范围: {} 到 {}", startDate, endDate, e);
            result.put("success", false);
            result.put("message", "同步异常: " + e.getMessage());
            result.put("dateRange", (startDate != null && endDate != null) ?
                startDate + " 到 " + endDate : "全部数据");
        }

        return result;
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取字符串值（带默认值）
     */
    private String getStringValueWithDefault(Map<String, Object> data, String key, String defaultValue) {
        Object value = data.get(key);
        if (value == null || value.toString().trim().isEmpty()) {
            return defaultValue;
        }
        return value.toString().trim();
    }

    /**
     * 获取状态描述
     */
    private String getStateDescription(String state) {
        switch (state) {
            case "0": return "待整改";
            case "1": return "已驳回";
            case "2": return "已整改";
            case "3": return "待复查";
            case "4": return "已超期";
            default: return "未知状态";
        }
    }
}
