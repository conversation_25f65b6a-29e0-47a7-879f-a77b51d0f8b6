package com.ruoyi.lxbi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 出矿数据对象 data_mucking_out
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataMuckingOut extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 出矿数据ID */
    private Long id;

    /** 作业日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "作业日期", sort = 1, dateFormat = "yyyy-MM-dd", mergeByValue = true)
    private Date operationDate;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 采场ID */
    private Long stopeId;

    /** 作业时段ID */
    private Long workingPeriodId;

    /** 出矿吨数 */
    @Excel(name = "出矿吨数")
    private Double tons;

}
