package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.lxbi.domain.response.DateCrushingOperationStats;
import com.ruoyi.lxbi.domain.response.DateCrushingOperationPeriodStats;

/**
 * 破碎操作数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface DateCrushingOperationStatsMapper 
{
    /**
     * 查询统计数据列表 (日)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据集合
     */
    public List<DateCrushingOperationStats> selectDailyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询统计数据列表 (周)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据集合
     */
    public List<DateCrushingOperationStats> selectWeeklyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询统计数据列表 (月)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据集合
     */
    public List<DateCrushingOperationStats> selectMonthlyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 查询班次统计数据列表 (日)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 班次统计数据集合
     */
    public List<DateCrushingOperationPeriodStats> selectDailyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (周)
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 班次统计数据集合
     */
    public List<DateCrushingOperationPeriodStats> selectWeeklyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (月)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 班次统计数据集合
     */
    public List<DateCrushingOperationPeriodStats> selectMonthlyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询统计数据列表 (年)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据集合
     */
    public List<DateCrushingOperationStats> selectYearlyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (年)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 班次统计数据集合
     */
    public List<DateCrushingOperationPeriodStats> selectYearlyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

}
