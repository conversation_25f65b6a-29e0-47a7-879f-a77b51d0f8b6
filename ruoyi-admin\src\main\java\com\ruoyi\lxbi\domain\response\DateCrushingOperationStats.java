package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 破碎操作数据统计对象 - 统一日/周/月统计
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class DateCrushingOperationStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /**
     * 周开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekStartDate;

    /**
     * 周结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date weekEndDate;

    /**
     * 总操作时间
     */
    @Excel(name = "总操作时间")
    private Double totalOperationTime;

    /**
     * 总故障时间
     */
    @Excel(name = "总故障时间")
    private Double totalFaultTime;

    /**
     * 故障次数
     */
    @Excel(name = "故障次数")
    private Long faultCount;

    /**
     * 总破碎量
     */
    @Excel(name = "总破碎量")
    private Double totalCrushingVolume;
}
