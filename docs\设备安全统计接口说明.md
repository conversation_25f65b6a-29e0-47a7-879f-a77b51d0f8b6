# 设备安全统计接口说明

## 概述

本文档说明了设备安全统计模块的接口设计和实现，该模块提供设备安全相关的统计分析功能，包括设备概览、状态分布、报警趋势、故障记录和系统分布等统计数据。

## 接口列表

### 1. 设备安全概览统计

**接口地址**: `GET /lxbi/stat/equipment-safety/overview`

**功能描述**: 获取设备安全的总体概览数据

**请求参数**:
- `viewType`: 视图类型 (day/week/month)
- `startDate`: 开始日期 (yyyy-MM-dd)
- `endDate`: 结束日期 (yyyy-MM-dd)

**响应数据**:
```json
{
  "totalEquipmentCount": 200,
  "runningEquipmentCount": 120,
  "faultEquipmentCount": 20,
  "alarmEquipmentCount": 30,
  "startDate": "2025-08-20",
  "endDate": "2025-08-25",
  "period": "day"
}
```

### 2. 设备状态分布统计

**接口地址**: `GET /lxbi/stat/equipment-safety/status-distribution`

**功能描述**: 获取设备状态分布数据，用于饼图展示

**响应数据**:
```json
[
  {
    "statusName": "已处置",
    "equipmentCount": 100,
    "percentage": 33.33,
    "statusCode": "DISPOSED"
  },
  {
    "statusName": "未处置",
    "equipmentCount": 150,
    "percentage": 50.00,
    "statusCode": "UNDISPOSED"
  },
  {
    "statusName": "无需处置",
    "equipmentCount": 50,
    "percentage": 16.67,
    "statusCode": "NO_NEED"
  }
]
```

### 3. 设备报警趋势统计

**接口地址**: `GET /lxbi/stat/equipment-safety/alarm-trend`

**功能描述**: 获取设备报警趋势数据，用于柱状图展示

**响应数据**:
```json
[
  {
    "date": "水泵房",
    "shortDate": "水泵房",
    "alarmCount": 8,
    "faultCount": 7,
    "maintenanceCount": 6
  },
  {
    "date": "中央变电所",
    "shortDate": "中央变电所",
    "alarmCount": 7,
    "faultCount": 6,
    "maintenanceCount": 5
  }
]
```

### 4. 设备故障记录列表

**接口地址**: `GET /lxbi/stat/equipment-safety/fault-records`

**功能描述**: 获取设备故障记录列表

**响应数据**:
```json
[
  {
    "serialNumber": 1,
    "equipmentName": "电机车1#",
    "faultDuration": "12.5H",
    "faultReason": "设备损坏...",
    "equipmentCode": "EQ001",
    "faultStartTime": "2025-08-25 08:00:00",
    "faultEndTime": "2025-08-25 20:30:00",
    "faultStatus": "已修复"
  }
]
```

### 5. 设备系统分布统计

**接口地址**: `GET /lxbi/stat/equipment-safety/system-distribution`

**功能描述**: 获取设备系统分布数据，用于雷达图展示

**响应数据**:
```json
[
  {
    "systemName": "主井",
    "equipmentCount": 100,
    "normalCount": 80,
    "faultCount": 15,
    "maintenanceCount": 5,
    "systemCode": "MAIN_SHAFT"
  },
  {
    "systemName": "破碎系统",
    "equipmentCount": 140,
    "normalCount": 120,
    "faultCount": 15,
    "maintenanceCount": 5,
    "systemCode": "CRUSHING_SYSTEM"
  }
]
```

## 数据结构说明

### VO类设计

#### 1. EquipmentSafetyOverviewVO
- **用途**: 设备安全概览数据
- **字段**: 总设备数、运行设备数、故障设备数、报警设备数

#### 2. EquipmentStatusDistributionVO
- **用途**: 设备状态分布数据（饼图）
- **字段**: 状态名称、设备数量、占比百分比、状态代码

#### 3. EquipmentAlarmTrendVO
- **用途**: 设备报警趋势数据（柱状图）
- **字段**: 日期、报警数量、故障数量、维修数量

#### 4. EquipmentFaultRecordVO
- **用途**: 设备故障记录数据
- **字段**: 序号、设备名称、故障时长、故障原因、故障状态

#### 5. EquipmentSystemDistributionVO
- **用途**: 设备系统分布数据（雷达图）
- **字段**: 系统名称、设备总数、正常数量、故障数量、维修数量

## 业务逻辑

### 1. 数据统计维度

**时间维度**:
- 日统计: 按天统计设备状态
- 周统计: 按周统计设备状态
- 月统计: 按月统计设备状态

**设备维度**:
- 按设备类型统计
- 按设备系统统计
- 按设备状态统计

### 2. 统计指标

**设备状态指标**:
- 总设备数: 系统中所有设备的总数
- 运行设备数: 正常运行的设备数量
- 故障设备数: 发生故障的设备数量
- 报警设备数: 产生报警的设备数量

**故障分析指标**:
- 故障时长: 设备故障持续时间
- 故障原因: 设备故障的具体原因
- 修复状态: 故障设备的修复进度

### 3. 数据展示

**饼图展示**:
- 设备状态分布
- 已处置/未处置/无需处置比例

**柱状图展示**:
- 各系统设备报警趋势
- 报警/故障/维修数量对比

**雷达图展示**:
- 各系统设备分布情况
- 多维度设备状态对比

**列表展示**:
- 设备故障记录详情
- 故障设备排行榜

## 模拟数据说明

### 1. 概览数据
- 总设备数: 200台
- 运行设备数: 120台
- 故障设备数: 20台
- 设备报警数: 30台

### 2. 系统分布
包含以下主要系统:
- 主井系统
- 副井系统
- 破碎系统
- 排水系统
- 通风系统
- 供电系统

### 3. 故障记录
模拟了常见的设备故障:
- 电机车故障
- 破碎机故障
- 各类设备损坏

## 扩展接入点

### 1. 设备管理系统接入
```java
// 可接入的数据源
- 设备基础信息系统
- 设备状态监控系统
- 设备维修管理系统
- 设备报警系统
```

### 2. 数据接入方式
- **实时数据**: 通过Kafka队列接收设备状态变更
- **定时同步**: 定时从设备管理系统同步数据
- **API接口**: 通过REST API获取设备信息

### 3. 数据库设计建议
```sql
-- 设备基础信息表
CREATE TABLE equipment_info (
    id BIGSERIAL PRIMARY KEY,
    equipment_code VARCHAR(50) UNIQUE,
    equipment_name VARCHAR(100),
    system_code VARCHAR(50),
    equipment_type VARCHAR(50),
    status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 设备故障记录表
CREATE TABLE equipment_fault_record (
    id BIGSERIAL PRIMARY KEY,
    equipment_code VARCHAR(50),
    fault_start_time TIMESTAMP,
    fault_end_time TIMESTAMP,
    fault_reason TEXT,
    fault_status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 设备报警记录表
CREATE TABLE equipment_alarm_record (
    id BIGSERIAL PRIMARY KEY,
    equipment_code VARCHAR(50),
    alarm_type VARCHAR(50),
    alarm_level VARCHAR(20),
    alarm_time TIMESTAMP,
    handle_status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 测试说明

### 1. 单元测试
- 所有Service方法的功能测试
- 不同视图类型的测试
- 异常情况的测试

### 2. 接口测试
```bash
# 测试概览统计
curl -X GET "http://localhost:8080/lxbi/stat/equipment-safety/overview?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试状态分布
curl -X GET "http://localhost:8080/lxbi/stat/equipment-safety/status-distribution?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试报警趋势
curl -X GET "http://localhost:8080/lxbi/stat/equipment-safety/alarm-trend?viewType=day&startDate=2025-08-20&endDate=2025-08-25"
```

### 3. 性能测试
- 大数据量下的查询性能
- 并发访问测试
- 缓存效果测试

## 后续开发计划

### 1. 数据接入
- 接入真实的设备管理系统
- 建立设备状态监控数据流
- 实现设备故障自动检测

### 2. 功能增强
- 设备预警功能
- 设备维护计划管理
- 设备性能分析

### 3. 可视化优化
- 实时数据刷新
- 交互式图表
- 移动端适配

## 总结

设备安全统计模块提供了完整的设备安全监控和分析功能，通过多维度的统计分析，帮助管理人员及时了解设备运行状况，预防设备故障，提高设备管理效率。

当前实现使用模拟数据，为后续接入真实设备数据预留了扩展接口，可以根据实际业务需求进行数据源的接入和功能的扩展。
