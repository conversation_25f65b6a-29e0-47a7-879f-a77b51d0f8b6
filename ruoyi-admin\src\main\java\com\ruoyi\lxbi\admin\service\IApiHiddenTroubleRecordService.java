package com.ruoyi.lxbi.admin.service;

import java.util.List;
import com.ruoyi.lxbi.admin.domain.ApiHiddenTroubleRecord;

/**
 * 隐患数据记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
public interface IApiHiddenTroubleRecordService 
{
    /**
     * 查询隐患数据记录
     * 
     * @param id 隐患数据记录主键
     * @return 隐患数据记录
     */
    public ApiHiddenTroubleRecord selectApiHiddenTroubleRecordById(Long id);

    /**
     * 查询隐患数据记录列表
     * 
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 隐患数据记录集合
     */
    public List<ApiHiddenTroubleRecord> selectApiHiddenTroubleRecordList(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * 新增隐患数据记录
     * 
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    public int insertApiHiddenTroubleRecord(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * 修改隐患数据记录
     * 
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    public int updateApiHiddenTroubleRecord(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * 批量删除隐患数据记录
     * 
     * @param ids 需要删除的隐患数据记录主键集合
     * @return 结果
     */
    public int deleteApiHiddenTroubleRecordByIds(Long[] ids);

    /**
     * 删除隐患数据记录信息
     *
     * @param id 隐患数据记录主键
     * @return 结果
     */
    public int deleteApiHiddenTroubleRecordById(Long id);

    /**
     * 根据通知编号查询隐患数据记录
     *
     * @param noticeNumber 通知编号
     * @return 隐患数据记录
     */
    public ApiHiddenTroubleRecord selectByNoticeNumber(String noticeNumber);

    /**
     * 根据通知编号和隐患日期查询隐患数据记录
     *
     * @param noticeNumber 通知编号
     * @param troubleDate 隐患日期
     * @return 隐患数据记录
     */
    public ApiHiddenTroubleRecord selectByNoticeNumberAndDate(String noticeNumber, java.util.Date troubleDate);

    /**
     * 处理第三方API隐患数据（插入或更新）
     *
     * @param apiData 第三方API数据
     * @return 处理结果
     */
    public boolean processApiData(java.util.Map<String, Object> apiData);

    /**
     * 批量处理第三方API隐患数据
     *
     * @param apiDataList 第三方API数据列表
     * @return 处理结果统计
     */
    public java.util.Map<String, Object> batchProcessApiData(java.util.List<java.util.Map<String, Object>> apiDataList);

    /**
     * UPSERT操作（根据通知编号插入或更新）
     *
     * @param apiHiddenTroubleRecord 隐患数据记录
     * @return 结果
     */
    public int upsertApiHiddenTroubleRecord(ApiHiddenTroubleRecord apiHiddenTroubleRecord);

    /**
     * 解析第三方API数据为实体对象
     *
     * @param apiData 第三方API数据
     * @return 解析后的实体对象
     */
    public ApiHiddenTroubleRecord parseApiData(java.util.Map<String, Object> apiData);

    /**
     * 获取隐患统计数据
     *
     * @return 统计结果
     */
    public java.util.Map<String, Object> getStatistics();

    /**
     * 同步第三方隐患数据
     *
     * @return 同步结果
     */
    public java.util.Map<String, Object> syncHiddenTroubleData();

    /**
     * 按日期范围同步第三方隐患数据
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 同步结果
     */
    public java.util.Map<String, Object> syncHiddenTroubleDataByDateRange(String startDate, String endDate);
}
