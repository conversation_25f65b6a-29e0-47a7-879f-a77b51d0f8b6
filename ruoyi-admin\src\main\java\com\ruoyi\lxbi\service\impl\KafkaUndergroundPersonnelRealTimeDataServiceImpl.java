package com.ruoyi.lxbi.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaUndergroundPersonnelRealTimeDataMapper;
import com.ruoyi.lxbi.domain.KafkaUndergroundPersonnelRealTimeData;
import com.ruoyi.lxbi.service.IKafkaUndergroundPersonnelRealTimeDataService;

/**
 * 井下作业人员实时数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class KafkaUndergroundPersonnelRealTimeDataServiceImpl implements IKafkaUndergroundPersonnelRealTimeDataService
{
    @Autowired
    private KafkaUndergroundPersonnelRealTimeDataMapper kafkaUndergroundPersonnelRealTimeDataMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询井下作业人员实时数据
     * 
     * @param id 井下作业人员实时数据主键
     * @return 井下作业人员实时数据
     */
    @Override
    public KafkaUndergroundPersonnelRealTimeData selectKafkaUndergroundPersonnelRealTimeDataById(Long id)
    {
        return kafkaUndergroundPersonnelRealTimeDataMapper.selectKafkaUndergroundPersonnelRealTimeDataById(id);
    }

    /**
     * 查询井下作业人员实时数据列表
     * 
     * @param kafkaUndergroundPersonnelRealTimeData 井下作业人员实时数据
     * @return 井下作业人员实时数据
     */
    @Override
    public List<KafkaUndergroundPersonnelRealTimeData> selectKafkaUndergroundPersonnelRealTimeDataList(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData)
    {
        return kafkaUndergroundPersonnelRealTimeDataMapper.selectKafkaUndergroundPersonnelRealTimeDataList(kafkaUndergroundPersonnelRealTimeData);
    }

    /**
     * 新增井下作业人员实时数据
     * 
     * @param kafkaUndergroundPersonnelRealTimeData 井下作业人员实时数据
     * @return 结果
     */
    @Override
    public int insertKafkaUndergroundPersonnelRealTimeData(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData)
    {
        kafkaUndergroundPersonnelRealTimeData.setCreateTime(DateUtils.getNowDate());
        return kafkaUndergroundPersonnelRealTimeDataMapper.insertKafkaUndergroundPersonnelRealTimeData(kafkaUndergroundPersonnelRealTimeData);
    }

    /**
     * 修改井下作业人员实时数据
     * 
     * @param kafkaUndergroundPersonnelRealTimeData 井下作业人员实时数据
     * @return 结果
     */
    @Override
    public int updateKafkaUndergroundPersonnelRealTimeData(KafkaUndergroundPersonnelRealTimeData kafkaUndergroundPersonnelRealTimeData)
    {
        kafkaUndergroundPersonnelRealTimeData.setUpdateTime(DateUtils.getNowDate());
        return kafkaUndergroundPersonnelRealTimeDataMapper.updateKafkaUndergroundPersonnelRealTimeData(kafkaUndergroundPersonnelRealTimeData);
    }

    /**
     * 批量删除井下作业人员实时数据
     * 
     * @param ids 需要删除的井下作业人员实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaUndergroundPersonnelRealTimeDataByIds(Long[] ids)
    {
        return kafkaUndergroundPersonnelRealTimeDataMapper.deleteKafkaUndergroundPersonnelRealTimeDataByIds(ids);
    }

    /**
     * 删除井下作业人员实时数据信息
     *
     * @param id 井下作业人员实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaUndergroundPersonnelRealTimeDataById(Long id)
    {
        return kafkaUndergroundPersonnelRealTimeDataMapper.deleteKafkaUndergroundPersonnelRealTimeDataById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka井下作业人员实时数据消息");

            // 解析Kafka消息
            KafkaUndergroundPersonnelRealTimeData personnelData = parseKafkaMessage(kafkaMessage);
            if (personnelData == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(personnelData.getPersonnelCardCode())) {
                log.warn("人员卡编码为空，跳过处理");
                return false;
            }

            // 执行插入操作
            int result = insertKafkaUndergroundPersonnelRealTimeData(personnelData);

            if (result > 0) {
                log.info("成功插入井下作业人员实时数据，人员卡编码: {}, 姓名: {}",
                    personnelData.getPersonnelCardCode(), personnelData.getName());
                return true;
            } else {
                log.warn("插入井下作业人员实时数据失败，人员卡编码: {}",
                    personnelData.getPersonnelCardCode());
                return false;
            }

        } catch (Exception e) {
            log.error("处理Kafka井下作业人员实时数据消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaUndergroundPersonnelRealTimeData parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaUndergroundPersonnelRealTimeData personnelData = new KafkaUndergroundPersonnelRealTimeData();

            // 基础信息
            personnelData.setFileEncoding(getStringValue(jsonNode, "文件前缀"));
            personnelData.setMineCode(getStringValue(jsonNode, "煤矿编码"));
            personnelData.setMineName(getStringValue(jsonNode, "矿井名称"));
            personnelData.setDataUploadTime(getDateValue(jsonNode, "数据上传时间"));
            personnelData.setPersonnelCardCode(getStringValue(jsonNode, "人员卡编码"));
            personnelData.setName(getStringValue(jsonNode, "姓名"));
            personnelData.setEntryAndExitFlag(getStringValue(jsonNode, "出入井标志位"));
            personnelData.setEntryTime(getDateValue(jsonNode, "入井时刻"));
            personnelData.setExitTime(getDateValue(jsonNode, "出井时刻"));
            personnelData.setAreaCode(getStringValue(jsonNode, "区域编码"));
            personnelData.setEntryCurrentAreaTime(getDateValue(jsonNode, "进入当前区域时刻"));
            personnelData.setBaseStationCode(getStringValue(jsonNode, "基站编码"));
            personnelData.setEntryCurrentBaseStationTime(getDateValue(jsonNode, "进入当前所处基站时刻"));
            personnelData.setLaborOrganizationMethod(getStringValue(jsonNode, "劳动组织方式"));
            personnelData.setDistanceFromBaseStation(getBigDecimalValue(jsonNode, "距离基站距离"));
            personnelData.setPersonnelWorkStatus(getStringValue(jsonNode, "人员工作状态"));
            personnelData.setIsMineLeader(getStringValue(jsonNode, "是否矿领导"));
            personnelData.setIsSpecialPersonnel(getStringValue(jsonNode, "是否特种人员"));
            personnelData.setTrajectoryBaseStationTimeCollection(getStringValue(jsonNode, "行进轨迹基站，时间集合"));

            // 默认值
            personnelData.setIsDeleted(0L);
            personnelData.setCreateTime(DateUtils.getNowDate());
            personnelData.setUpdateTime(DateUtils.getNowDate());

            return personnelData;

        } catch (Exception e) {
            log.error("解析Kafka井下作业人员实时数据消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null && !node.isNull() ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private java.util.Date getDateValue(JsonNode jsonNode, String fieldName) {
        try {
            String dateStr = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            }
        } catch (Exception e) {
            log.warn("解析日期字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }

    /**
     * 从JsonNode中获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(JsonNode jsonNode, String fieldName) {
        try {
            String value = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(value)) {
                return new BigDecimal(value);
            }
        } catch (Exception e) {
            log.warn("解析BigDecimal字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }
}
