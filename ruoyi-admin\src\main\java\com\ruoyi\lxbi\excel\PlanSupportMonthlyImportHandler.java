package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.domain.BaseWorkingFace;
import com.ruoyi.lxbi.domain.PlanSupportMonthly;
import com.ruoyi.lxbi.domain.excel.PlanSupportMonthlyImport;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;
import com.ruoyi.lxbi.service.IBaseStopeService;
import com.ruoyi.lxbi.service.IBaseWorkingFaceService;
import com.ruoyi.lxbi.service.IPlanSupportMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 支护月计划导入处理器
 *
 * <AUTHOR>
 */
@Component
public class PlanSupportMonthlyImportHandler extends ExcelImportHandler<PlanSupportMonthlyImport> {

    @Autowired
    private IBaseProjectDepartmentService baseProjectDepartmentService;

    @Autowired
    private IBaseWorkingFaceService baseWorkingFaceService;

    @Autowired
    private IBaseStopeService baseStopeService;

    @Autowired
    private IPlanSupportMonthlyService planSupportMonthlyService;

    @Override
    protected Class<PlanSupportMonthlyImport> getEntityClass() {
        return PlanSupportMonthlyImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置项目部门选项
        List<ExcelOptionInfo> projectDepartments = new ArrayList<>();
        BaseProjectDepartment deptQueryParam = new BaseProjectDepartment();
        List<BaseProjectDepartment> deptList = baseProjectDepartmentService.selectBaseProjectDepartmentList(deptQueryParam);

        for (BaseProjectDepartment dept : deptList) {
            projectDepartments.add(new ExcelOptionInfo(
                    dept.getProjectDepartmentId(),
                    dept.getProjectDepartmentName()
            ));
        }
        context.setOptions("projectDepartment", projectDepartments);

        // 设置工作面选项
        List<ExcelOptionInfo> workingFaces = new ArrayList<>();
        BaseWorkingFace faceQueryParam = new BaseWorkingFace();
        List<BaseWorkingFace> faceList = baseWorkingFaceService.selectBaseWorkingFaceList(faceQueryParam);

        for (BaseWorkingFace face : faceList) {
            workingFaces.add(new ExcelOptionInfo(
                    face.getWorkingFaceId(),
                    face.getWorkingFaceName()
            ));
        }
        context.setOptions("workingFace", workingFaces);

        // 设置采场选项
        List<ExcelOptionInfo> stopes = new ArrayList<>();
        BaseStope stopeQueryParam = new BaseStope();
        List<BaseStope> stopeList = baseStopeService.selectBaseStopeListAll(stopeQueryParam);

        for (BaseStope stope : stopeList) {
            stopes.add(new ExcelOptionInfo(
                    stope.getStopeId(),
                    stope.getStopeName()
            ));
        }
        context.setOptions("stope", stopes);

    }

    @Override
    protected void validateData(ExcelDataInfo<PlanSupportMonthlyImport> dataInfo, ExcelImportContext context) {
        PlanSupportMonthlyImport data = dataInfo.getData();

        // 验证计划月份格式
        if (data.getPlanDate() != null) {
            Pattern pattern = Pattern.compile("^\\d{4}\\d{2}$");
            if (!pattern.matcher(data.getPlanDate()).matches()) {
                dataInfo.addError("planDate", "计划月份格式不正确，应为yyyyMM格式");
            }
        }

        // 验证锚网支护米数
        if (data.getBoltMeshSupportMeter() != null && data.getBoltMeshSupportMeter().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("boltMeshSupportMeter", "锚网支护米数不能为负数");
        }

        // 验证喷浆支护米数
        if (data.getShotcreteSupportMeter() != null && data.getShotcreteSupportMeter().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("shotcreteSupportMeter", "喷浆支护米数不能为负数");
        }
        if (data.getBoltMeshSupportVolume() != null && data.getBoltMeshSupportVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("boltMeshSupportVolume", "锚网支护方量不能为负数");
        }

        if (data.getShotcreteSupportVolume() != null && data.getShotcreteSupportVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("shotcreteSupportVolume", "喷浆支护方量不能为负数");
        }
        if (data.getSupportMeter() != null && data.getSupportMeter().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("supportMeter", "支护米数合计不能为负数");
        }
        if (data.getSupportVolume() != null && data.getSupportVolume().compareTo(BigDecimal.ZERO) < 0) {
            dataInfo.addError("supportVolume", "支护方量合计不能为负数");
        }

    }

    @Override
    protected void saveData(PlanSupportMonthlyImport data, ExcelImportContext context) {
        // 转换为实体对象
        PlanSupportMonthly entity = new PlanSupportMonthly();
        entity.setProjectDepartmentId(data.getProjectDepartmentId());
        entity.setWorkingFaceId(data.getWorkingFaceId());
        entity.setStopeId(data.getStopeId());
        entity.setBoltMeshSupportMeter(data.getBoltMeshSupportMeter());
        entity.setShotcreteSupportMeter(data.getShotcreteSupportMeter());
        entity.setBoltMeshSupportVolume(data.getBoltMeshSupportVolume());
        entity.setShotcreteSupportVolume(data.getShotcreteSupportVolume());
        entity.setSupportVolume(data.getSupportVolume());
        entity.setSupportMeter(data.getSupportMeter());
        entity.setPlanDate(data.getPlanDate());
        
        // 保存到数据库
        planSupportMonthlyService.insertPlanSupportMonthly(entity);
    }

    @Override
    public List<PlanSupportMonthlyImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("支护月计划验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("支护月计划导入完成，总行数: " + ctx.getTotalRows());
    }
}
