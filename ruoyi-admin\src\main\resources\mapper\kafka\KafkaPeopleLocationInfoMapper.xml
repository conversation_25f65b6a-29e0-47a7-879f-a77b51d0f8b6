<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.admin.mapper.KafkaPeopleLocationInfoMapper">
    
    <resultMap type="KafkaPeopleLocationInfo" id="KafkaPeopleLocationInfoResult">
        <result property="id"    column="id"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="mineName"    column="mine_name"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
        <result property="areaType"    column="area_type"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaApprovedPersonnel"    column="area_approved_personnel"    />
        <result property="areaName"    column="area_name"    />
        <result property="status"    column="status"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKafkaPeopleLocationInfoVo">
        select id, mine_code, mine_name, data_upload_time, area_type, area_code, area_approved_personnel, area_name, status, is_deleted, create_by, create_time, update_by, update_time, remark from kafka_people_location_info
    </sql>

    <select id="selectKafkaPeopleLocationInfoList" parameterType="KafkaPeopleLocationInfo" resultMap="KafkaPeopleLocationInfoResult">
        <include refid="selectKafkaPeopleLocationInfoVo"/>
        <where>  
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="mineName != null  and mineName != ''"> and mine_name like concat('%', #{mineName}, '%')</if>
            <if test="dataUploadTime != null "> and data_upload_time = #{dataUploadTime}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaApprovedPersonnel != null "> and area_approved_personnel = #{areaApprovedPersonnel}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
        </where>
        order by data_upload_time desc
    </select>
    
    <select id="selectKafkaPeopleLocationInfoById" parameterType="Long" resultMap="KafkaPeopleLocationInfoResult">
        <include refid="selectKafkaPeopleLocationInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectByAreaCode" parameterType="String" resultMap="KafkaPeopleLocationInfoResult">
        <include refid="selectKafkaPeopleLocationInfoVo"/>
        where area_code = #{areaCode} and is_deleted = 0
        order by data_upload_time desc
        limit 1
    </select>

    <select id="selectByAreaCodeAndMineCode" resultMap="KafkaPeopleLocationInfoResult">
        <include refid="selectKafkaPeopleLocationInfoVo"/>
        where area_code = #{areaCode} and mine_code = #{mineCode} and is_deleted = 0
        order by data_upload_time desc
        limit 1
    </select>

    <select id="selectAllValidAreas" resultMap="KafkaPeopleLocationInfoResult">
        <include refid="selectKafkaPeopleLocationInfoVo"/>
        where is_deleted = 0 and status = 1
        order by area_code
    </select>

    <select id="selectByAreaType" parameterType="String" resultMap="KafkaPeopleLocationInfoResult">
        <include refid="selectKafkaPeopleLocationInfoVo"/>
        where area_type = #{areaType} and is_deleted = 0
        order by area_code
    </select>

    <select id="countTotalAreas" resultType="Long">
        select count(*) from kafka_people_location_info where is_deleted = 0
    </select>

    <select id="countAreasByMineCode" parameterType="String" resultType="Long">
        select count(*) from kafka_people_location_info 
        where mine_code = #{mineCode} and is_deleted = 0
    </select>
        
    <insert id="insertKafkaPeopleLocationInfo" parameterType="KafkaPeopleLocationInfo" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_people_location_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mineCode != null">mine_code,</if>
            <if test="mineName != null">mine_name,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
            <if test="areaType != null">area_type,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaApprovedPersonnel != null">area_approved_personnel,</if>
            <if test="areaName != null">area_name,</if>
            <if test="status != null">status,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mineCode != null">#{mineCode},</if>
            <if test="mineName != null">#{mineName},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
            <if test="areaType != null">#{areaType},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaApprovedPersonnel != null">#{areaApprovedPersonnel},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="status != null">#{status},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKafkaPeopleLocationInfo" parameterType="KafkaPeopleLocationInfo">
        update kafka_people_location_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="mineName != null">mine_name = #{mineName},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
            <if test="areaType != null">area_type = #{areaType},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaApprovedPersonnel != null">area_approved_personnel = #{areaApprovedPersonnel},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaPeopleLocationInfoById" parameterType="Long">
        delete from kafka_people_location_info where id = #{id}
    </delete>

    <delete id="deleteKafkaPeopleLocationInfoByIds" parameterType="String">
        delete from kafka_people_location_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- PostgreSQL UPSERT 操作 - 使用表级唯一约束 -->
    <insert id="upsertKafkaPeopleLocationInfo" parameterType="KafkaPeopleLocationInfo">
        INSERT INTO kafka_people_location_info (
            mine_code, mine_name, data_upload_time, area_type, area_code,
            area_approved_personnel, area_name, status, is_deleted,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{mineCode}, #{mineName}, #{dataUploadTime}, #{areaType}, #{areaCode},
            #{areaApprovedPersonnel}, #{areaName}, #{status}, #{isDeleted},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
        ON CONFLICT (area_code, mine_code)
        DO UPDATE SET
            mine_name = EXCLUDED.mine_name,
            data_upload_time = EXCLUDED.data_upload_time,
            area_type = EXCLUDED.area_type,
            area_approved_personnel = EXCLUDED.area_approved_personnel,
            area_name = EXCLUDED.area_name,
            status = EXCLUDED.status,
            is_deleted = EXCLUDED.is_deleted,
            update_by = EXCLUDED.update_by,
            update_time = EXCLUDED.update_time,
            remark = EXCLUDED.remark
    </insert>
</mapper>
