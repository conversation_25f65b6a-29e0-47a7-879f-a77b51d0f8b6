package com.ruoyi.lxbi.domain.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 选矿销售月计划批量操作DTO
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Data
public class PlanMineralSaleMonthlyBatchDto {

    /** 主键ID */
    private Long id;

    /** 计划月份 */
    private String planMonth;

    /** 铁精粉量 */
    private BigDecimal ironConcentrateVolume;

    /** 选矿厂矿仓存矿销售 */
    private BigDecimal concentratorBinsStockVolume;

    /** 入措施井地表存矿销售 */
    private BigDecimal serviceShaftSurfaceStockVolume;

    /** 原矿品位-TFe */
    private BigDecimal rawOreGrade;

    /** 库存 */
    private BigDecimal stockVolume;

    /** 备注 */
    private String remark;

    /** 操作类型（新增、更新、删除） */
    private String operationType;

}
