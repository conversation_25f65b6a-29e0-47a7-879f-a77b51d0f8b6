package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 人员安全简化概览VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonnelSafetySimpleOverviewVO {
    
    /**
     * 下井人数
     */
    private Long downWellPersonnel;
    
    /**
     * 违规行为数
     */
    private Long violationBehavior;
    
    /**
     * 求救数
     */
    private Long distressCalls;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 统计周期
     */
    private String period;
}
