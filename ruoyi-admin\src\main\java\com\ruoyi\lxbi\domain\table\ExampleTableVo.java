package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import lombok.Data;

/**
 * 示例表格VO
 */
@Data
@TableConfig(code = "example_table", name = "示例表格", description = "演示通用表格功能")
public class ExampleTableVo {
    
    @TableHeader(label = "R1", order = 1, parentPath = {"R1"}, enableRowMerge = true, colMergeGroup = {"group1"})
    private String a;
    
    @TableHeader(label = "R1", order = 2, parentPath = {"R1"}, enableRowMerge = true, relateColumn = "a", colMergeGroup = {"group1","group2"})
    private String b;
    
    @TableHeader(label = "R2", order = 3, enableRowMerge = true, relateColumn = "b")
    private String c;
    
    @TableHeader(label = "R31", order = 4, parentPath = {"R3"}, enableRowMerge = true, relateColumn = "c", colMergeGroup = {"group2"})
    private String d;
    
    @TableHeader(label = "R32", order = 5, parentPath = {"R3"}, colMergeGroup = {"group2"})
    private String e;
}