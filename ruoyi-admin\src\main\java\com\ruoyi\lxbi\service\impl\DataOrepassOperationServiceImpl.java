package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.Date;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationVo;
import com.ruoyi.lxbi.domain.request.DataOrepassOperationBatchDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;
import com.ruoyi.lxbi.mapper.DataOrepassOperationMapper;
import com.ruoyi.lxbi.domain.DataOrepassOperation;
import com.ruoyi.lxbi.service.IDataOrepassOperationService;

/**
 * 溜井运行数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DataOrepassOperationServiceImpl implements IDataOrepassOperationService 
{
    @Autowired
    private DataOrepassOperationMapper dataOrepassOperationMapper;

    /**
     * 查询溜井运行数据
     * 
     * @param id 溜井运行数据主键
     * @return 溜井运行数据
     */
    @Override
    public DataOrepassOperation selectDataOrepassOperationById(Long id)
    {
        return dataOrepassOperationMapper.selectDataOrepassOperationById(id);
    }

    /**
     * 查询溜井运行数据列表
     *
     * @param dataOrepassOperation 溜井运行数据
     * @return 溜井运行数据
     */
    @Override
    public List<DataOrepassOperationVo> selectDataOrepassOperationList(DataOrepassOperation dataOrepassOperation)
    {
        return dataOrepassOperationMapper.selectDataOrepassOperationList(dataOrepassOperation);
    }

    /**
     * 新增溜井运行数据
     * 
     * @param dataOrepassOperation 溜井运行数据
     * @return 结果
     */
    @Override
    public int insertDataOrepassOperation(DataOrepassOperation dataOrepassOperation)
    {
        dataOrepassOperation.setCreateTime(DateUtils.getNowDate());
        return dataOrepassOperationMapper.insertDataOrepassOperation(dataOrepassOperation);
    }

    /**
     * 修改溜井运行数据
     * 
     * @param dataOrepassOperation 溜井运行数据
     * @return 结果
     */
    @Override
    public int updateDataOrepassOperation(DataOrepassOperation dataOrepassOperation)
    {
        dataOrepassOperation.setUpdateTime(DateUtils.getNowDate());
        return dataOrepassOperationMapper.updateDataOrepassOperation(dataOrepassOperation);
    }

    /**
     * 批量删除溜井运行数据
     * 
     * @param ids 需要删除的溜井运行数据主键
     * @return 结果
     */
    @Override
    public int deleteDataOrepassOperationByIds(Long[] ids)
    {
        return dataOrepassOperationMapper.deleteDataOrepassOperationByIds(ids);
    }

    /**
     * 删除溜井运行数据信息
     *
     * @param id 溜井运行数据主键
     * @return 结果
     */
    @Override
    public int deleteDataOrepassOperationById(Long id)
    {
        return dataOrepassOperationMapper.deleteDataOrepassOperationById(id);
    }

    /**
     * 根据作业日期和项目部门查询溜井运行数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 溜井运行数据集合
     */
    @Override
    public List<DataOrepassOperationVo> selectDataOrepassOperationByOperationDateAndProject(Date operationDate, Long projectDepartmentId)
    {
        return dataOrepassOperationMapper.selectDataOrepassOperationByOperationDateAndProject(operationDate, projectDepartmentId);
    }

    /**
     * 批量保存溜井运行数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDataOrepassOperation(List<DataOrepassOperationBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个日期和项目部的数据
        Date operationDate = batchDataList.get(0).getOperationDate();
        Long projectDepartmentId = batchDataList.get(0).getProjectDepartmentId();
        if (operationDate == null) {
            throw new ServiceException("作业日期不能为空");
        }
        if (projectDepartmentId == null) {
            throw new ServiceException("项目部门不能为空");
        }

        boolean allSameDateAndProject = batchDataList.stream()
                .allMatch(data -> operationDate.equals(data.getOperationDate())
                    && projectDepartmentId.equals(data.getProjectDepartmentId()));
        if (!allSameDateAndProject) {
            throw new ServiceException("批量数据必须是同一个作业日期和项目部门");
        }

        // 查询现有数据
        List<DataOrepassOperationVo> existingDataList = selectDataOrepassOperationByOperationDateAndProject(operationDate, projectDepartmentId);
        Map<Long, DataOrepassOperationVo> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(DataOrepassOperationVo::getId, data -> data));

        List<DataOrepassOperation> toInsert = new ArrayList<>();
        List<DataOrepassOperation> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (DataOrepassOperationBatchDto batchData : batchDataList) {
            if (Boolean.TRUE.equals(batchData.getIsNew())) {
                // 新增数据
                DataOrepassOperation newData = new DataOrepassOperation();
                BeanUtils.copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if (batchData.getId() != null && existingDataMap.containsKey(batchData.getId())) {
                // 更新现有数据（根据ID判断）
                DataOrepassOperation updateData = new DataOrepassOperation();
                BeanUtils.copyProperties(batchData, updateData);
                updateData.setId(batchData.getId());
                updateData.setUpdateBy(SecurityUtils.getUsername());
                updateData.setUpdateTime(DateUtils.getNowDate());
                toUpdate.add(updateData);

                // 从现有数据中移除，剩下的就是要删除的
                toDelete.remove(batchData.getId());
            }
        }

        int result = 0;

        // 执行删除
        if (!toDelete.isEmpty()) {
            result += dataOrepassOperationMapper.deleteDataOrepassOperationByIds(toDelete.toArray(new Long[0]));
        }

        // 执行新增
        if (!toInsert.isEmpty()) {
            result += dataOrepassOperationMapper.batchInsertDataOrepassOperation(toInsert);
        }

        // 执行更新
        for (DataOrepassOperation updateData : toUpdate) {
            result += dataOrepassOperationMapper.updateDataOrepassOperation(updateData);
        }

        return result;
    }
}
