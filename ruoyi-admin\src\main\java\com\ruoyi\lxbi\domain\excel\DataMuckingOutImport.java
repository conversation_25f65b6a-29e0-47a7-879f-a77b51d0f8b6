package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import lombok.Data;

import java.util.Date;

/**
 * 出矿数据导入对象
 */
@Data
@ExcelImportTemplate(
        key = "data_mucking_out",
        name = "出矿数据导入",
        description = "用于导入出矿数据",
        sheetName = "出矿数据"
)
public class DataMuckingOutImport {

    /**
     * 作业日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "作业日期", index = 0)
    @ExcelRequired(message = "作业日期不能为空")
    @ExcelSelected(prompt = "请输入作业日期，格式：yyyy-MM-dd，例如：2025-01-15")
    private Date operationDate;

    /**
     * 项目部门ID（存储值）
     */
    @ExcelSelected(
            optionKey = "projectDepartment",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long projectDepartmentId;

    /**
     * 项目部门名称（显示值）
     */
    @ExcelProperty(value = "项目部门", index = 1)
    @ExcelRequired(message = "项目部门不能为空")
    @ExcelSelected(
            optionKey = "projectDepartment",
            prompt = "请选择项目部门",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String projectDepartmentName;

    /**
     * 作业时段ID（存储值）
     */
    @ExcelSelected(
            optionKey = "workingPeriod",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long workingPeriodId;

    /**
     * 作业时段名称（显示值）
     */
    @ExcelProperty(value = "作业时段", index = 2)
    @ExcelRequired(message = "作业时段不能为空")
    @ExcelSelected(
            optionKey = "workingPeriod",
            prompt = "请选择作业时段",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String workingPeriodName;

    /**
     * 采场ID（存储值）
     */
    @ExcelSelected(
            optionKey = "stope",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long stopeId;

    /**
     * 采场名称（显示值）
     */
    @ExcelProperty(value = "采场", index = 3)
    @ExcelRequired(message = "采场不能为空")
    @ExcelSelected(
            optionKey = "stope",
            prompt = "请选择采场",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String stopeName;

    /**
     * 出矿吨数
     */
    @ExcelProperty(value = "出矿吨数", index = 4)
    @ExcelRequired(message = "出矿吨数不能为空")
    @ExcelSelected(prompt = "请输入出矿吨数")
    private Double tons;
}
