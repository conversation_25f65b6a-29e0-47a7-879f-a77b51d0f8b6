package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanMiningMonthly;
import com.ruoyi.lxbi.domain.response.PlanMiningMonthlyVo;

/**
 * 采矿整体月计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface PlanMiningMonthlyMapper 
{
    /**
     * 查询采矿整体月计划
     * 
     * @param id 采矿整体月计划主键
     * @return 采矿整体月计划
     */
    public PlanMiningMonthly selectPlanMiningMonthlyById(Long id);

    /**
     * 查询采矿整体月计划列表
     * 
     * @param planMiningMonthly 采矿整体月计划
     * @return 采矿整体月计划集合
     */
    public List<PlanMiningMonthlyVo> selectPlanMiningMonthlyList(PlanMiningMonthly planMiningMonthly);

    /**
     * 新增采矿整体月计划
     * 
     * @param planMiningMonthly 采矿整体月计划
     * @return 结果
     */
    public int insertPlanMiningMonthly(PlanMiningMonthly planMiningMonthly);

    /**
     * 修改采矿整体月计划
     * 
     * @param planMiningMonthly 采矿整体月计划
     * @return 结果
     */
    public int updatePlanMiningMonthly(PlanMiningMonthly planMiningMonthly);

    /**
     * 删除采矿整体月计划
     * 
     * @param id 采矿整体月计划主键
     * @return 结果
     */
    public int deletePlanMiningMonthlyById(Long id);

    /**
     * 批量删除采矿整体月计划
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanMiningMonthlyByIds(Long[] ids);
}
