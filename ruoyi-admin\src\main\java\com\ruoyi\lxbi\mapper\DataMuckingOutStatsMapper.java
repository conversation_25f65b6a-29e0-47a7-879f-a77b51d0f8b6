package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.lxbi.domain.response.DataMuckingOutTotalStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutPeriodStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutDepartmentStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStopeStats;

/**
 * 出矿数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface DataMuckingOutStatsMapper 
{
    // ========== 总体统计查询方法 ==========
    
    /**
     * 查询总体统计数据列表 (日)
     */
    public List<DataMuckingOutTotalStats> selectDailyTotalStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表 (周)
     */
    public List<DataMuckingOutTotalStats> selectWeeklyTotalStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表 (月)
     */
    public List<DataMuckingOutTotalStats> selectMonthlyTotalStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表 (年)
     */
    public List<DataMuckingOutTotalStats> selectYearlyTotalStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 总体统计查询方法（含计划量） ==========

    /**
     * 查询总体统计数据列表（含计划量） (日)
     */
    public List<DataMuckingOutTotalWithPlanStats> selectDailyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (周)
     */
    public List<DataMuckingOutTotalWithPlanStats> selectWeeklyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (月)
     */
    public List<DataMuckingOutTotalWithPlanStats> selectMonthlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (年)
     */
    public List<DataMuckingOutTotalWithPlanStats> selectYearlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 详细统计查询方法 ==========
    
    /**
     * 查询详细统计数据列表 (日)
     */
    public List<DataMuckingOutStats> selectDailyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询详细统计数据列表 (周)
     */
    public List<DataMuckingOutStats> selectWeeklyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询详细统计数据列表 (月)
     */
    public List<DataMuckingOutStats> selectMonthlyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询详细统计数据列表 (年)
     */
    public List<DataMuckingOutStats> selectYearlyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 班次统计查询方法 ==========
    
    /**
     * 查询班次统计数据列表 (日)
     */
    public List<DataMuckingOutPeriodStats> selectDailyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (周)
     */
    public List<DataMuckingOutPeriodStats> selectWeeklyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (月)
     */
    public List<DataMuckingOutPeriodStats> selectMonthlyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询班次统计数据列表 (年)
     */
    public List<DataMuckingOutPeriodStats> selectYearlyPeriodStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 项目部门统计查询方法 ==========
    
    /**
     * 查询项目部门统计数据列表 (日)
     */
    public List<DataMuckingOutDepartmentStats> selectDailyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (周)
     */
    public List<DataMuckingOutDepartmentStats> selectWeeklyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (月)
     */
    public List<DataMuckingOutDepartmentStats> selectMonthlyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询项目部门统计数据列表 (年)
     */
    public List<DataMuckingOutDepartmentStats> selectYearlyDepartmentStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ========== 采场统计查询方法 ==========
    
    /**
     * 查询采场统计数据列表 (日)
     */
    public List<DataMuckingOutStopeStats> selectDailyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询采场统计数据列表 (周)
     */
    public List<DataMuckingOutStopeStats> selectWeeklyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询采场统计数据列表 (月)
     */
    public List<DataMuckingOutStopeStats> selectMonthlyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询采场统计数据列表 (年)
     */
    public List<DataMuckingOutStopeStats> selectYearlyStopeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

}
