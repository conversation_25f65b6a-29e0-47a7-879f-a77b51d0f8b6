package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 月报数据表格VO
 */
@Data
@TableConfig(code = "mining_monthly_report", name = "生产月报", description = "生产数据月报统计表格")
public class MonthlyReportTableVo {
    
    @TableHeader(label = "序号", order = 1)
    private String serialNumber;
    
    @TableHeader(label = "名称", order = 2, enableRowMerge = true)
    private String name;
    
    @TableHeader(label = "单位", order = 3)
    private String unit;
    
    @TableHeader(label = "月计划", order = 4)
    private BigDecimal monthlyPlan;
    
    @TableHeader(label = "月完成", order = 5)
    private BigDecimal monthlyCompleted;
    
    @TableHeader(label = "月完成率", order = 6)
    private String monthlyCompletionRate;
    
    @TableHeader(label = "年度预算计划", order = 7)
    private BigDecimal yearlyBudgetPlan;
    
    @TableHeader(label = "年度计划", order = 8)
    private BigDecimal yearlyPlan;
    
    @TableHeader(label = "年度完成", order = 9)
    private BigDecimal yearlyCompleted;
    
    @TableHeader(label = "年度完成率", order = 10)
    private String yearlyCompletionRate;
    
    @TableHeader(label = "累计超欠", order = 11)
    private BigDecimal cumulativeVariance;
}
