package com.ruoyi.lxbi.mapper;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.domain.DataCrushingOperation;
import com.ruoyi.lxbi.domain.response.DataCrushingOperationVo;
import org.apache.ibatis.annotations.Param;

/**
 * 破碎数据管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface DataCrushingOperationMapper 
{
    /**
     * 查询破碎数据管理
     * 
     * @param id 破碎数据管理主键
     * @return 破碎数据管理
     */
    public DataCrushingOperation selectDataCrushingOperationById(Long id);

    /**
     * 查询破碎数据管理列表
     *
     * @param dataCrushingOperation 破碎数据管理
     * @return 破碎数据管理集合
     */
    public List<DataCrushingOperationVo> selectDataCrushingOperationList(DataCrushingOperation dataCrushingOperation);

    /**
     * 新增破碎数据管理
     * 
     * @param dataCrushingOperation 破碎数据管理
     * @return 结果
     */
    public int insertDataCrushingOperation(DataCrushingOperation dataCrushingOperation);

    /**
     * 修改破碎数据管理
     * 
     * @param dataCrushingOperation 破碎数据管理
     * @return 结果
     */
    public int updateDataCrushingOperation(DataCrushingOperation dataCrushingOperation);

    /**
     * 删除破碎数据管理
     * 
     * @param id 破碎数据管理主键
     * @return 结果
     */
    public int deleteDataCrushingOperationById(Long id);

    /**
     * 批量删除破碎数据管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataCrushingOperationByIds(Long[] ids);

    /**
     * 根据作业日期查询破碎数据列表
     *
     * @param operationDate 作业日期
     * @return 破碎数据集合
     */
    public List<DataCrushingOperationVo> selectDataCrushingOperationByOperationDate(@Param("operationDate") Date operationDate);

    /**
     * 批量新增破碎数据
     *
     * @param dataCrushingOperationList 破碎数据列表
     * @return 结果
     */
    public int batchInsertDataCrushingOperation(List<DataCrushingOperation> dataCrushingOperationList);
}
