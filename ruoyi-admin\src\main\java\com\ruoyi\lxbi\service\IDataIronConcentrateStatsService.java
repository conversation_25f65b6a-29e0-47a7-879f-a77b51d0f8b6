package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DataIronConcentrateStatsRequest;
import com.ruoyi.lxbi.domain.response.DataIronConcentrateTotalWithPlanStats;

import java.util.List;

/**
 * 铁精粉生产数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
public interface IDataIronConcentrateStatsService {

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    public List<DataIronConcentrateTotalWithPlanStats> selectTotalWithPlanStatsList(DataIronConcentrateStatsRequest request, String viewType);
}
