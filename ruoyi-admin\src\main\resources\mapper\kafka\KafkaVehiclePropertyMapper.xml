<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.KafkaVehiclePropertyMapper">
    
    <resultMap type="KafkaVehicleProperty" id="KafkaVehiclePropertyResult">
        <result property="id"    column="id"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fileEncoding"    column="file_encoding"    />
        <result property="mineCode"    column="mine_code"    />
        <result property="dataGenerationTime"    column="data_generation_time"    />
        <result property="vehicleLocationCardNumber"    column="vehicle_location_card_number"    />
        <result property="vehicleCode"    column="vehicle_code"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="department"    column="department"    />
        <result property="loadWeight"    column="load_weight"    />
        <result property="loadPersonnel"    column="load_personnel"    />
        <result property="vehicleStatus"    column="vehicle_status"    />
        <result property="factoryDate"    column="factory_date"    />
    </resultMap>

    <sql id="selectKafkaVehiclePropertyVo">
        select id, is_deleted, create_by, create_time, update_by, update_time, remark, file_encoding, mine_code, data_generation_time, vehicle_location_card_number, vehicle_code, vehicle_type, department, load_weight, load_personnel, vehicle_status, factory_date from kafka_vehicle_property
    </sql>

    <select id="selectKafkaVehiclePropertyList" parameterType="KafkaVehicleProperty" resultMap="KafkaVehiclePropertyResult">
        <include refid="selectKafkaVehiclePropertyVo"/>
        <where>  
            <if test="fileEncoding != null  and fileEncoding != ''"> and file_encoding = #{fileEncoding}</if>
            <if test="mineCode != null  and mineCode != ''"> and mine_code = #{mineCode}</if>
            <if test="params.beginDataGenerationTime != null and params.beginDataGenerationTime != '' and params.endDataGenerationTime != null and params.endDataGenerationTime != ''"> and data_generation_time between #{params.beginDataGenerationTime}::date and #{params.endDataGenerationTime}::date</if>
            <if test="vehicleLocationCardNumber != null  and vehicleLocationCardNumber != ''"> and vehicle_location_card_number = #{vehicleLocationCardNumber}</if>
            <if test="vehicleCode != null  and vehicleCode != ''"> and vehicle_code = #{vehicleCode}</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="department != null  and department != ''"> and department = #{department}</if>
            <if test="loadWeight != null "> and load_weight = #{loadWeight}</if>
            <if test="loadPersonnel != null "> and load_personnel = #{loadPersonnel}</if>
            <if test="vehicleStatus != null  and vehicleStatus != ''"> and vehicle_status = #{vehicleStatus}</if>
            <if test="params.beginFactoryDate != null and params.beginFactoryDate != '' and params.endFactoryDate != null and params.endFactoryDate != ''"> and factory_date between #{params.beginFactoryDate}::date and #{params.endFactoryDate}::date</if>
        </where>
    </select>
    
    <select id="selectKafkaVehiclePropertyById" parameterType="Long" resultMap="KafkaVehiclePropertyResult">
        <include refid="selectKafkaVehiclePropertyVo"/>
        where id = #{id}
    </select>

    <!-- 根据车辆定位卡号查询车辆属性数据 -->
    <select id="selectByVehicleLocationCardNumber" parameterType="String" resultMap="KafkaVehiclePropertyResult">
        <include refid="selectKafkaVehiclePropertyVo"/>
        where vehicle_location_card_number = #{vehicleLocationCardNumber}
        order by data_generation_time desc
        limit 1
    </select>

    <!-- 统计截止日期前的车辆总数 -->
    <select id="countVehiclesByEndDate" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT vehicle_code)
        FROM kafka_vehicle_property
        WHERE 1=1
            <if test="endDate != null and endDate != ''">
                AND data_generation_time &lt;= #{endDate}::date + INTERVAL '1 day' - INTERVAL '1 second'
            </if>
            AND vehicle_code IS NOT NULL
            AND vehicle_code != ''
    </select>

    <insert id="insertKafkaVehicleProperty" parameterType="KafkaVehicleProperty" useGeneratedKeys="true" keyProperty="id">
        insert into kafka_vehicle_property
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fileEncoding != null">file_encoding,</if>
            <if test="mineCode != null">mine_code,</if>
            <if test="dataGenerationTime != null">data_generation_time,</if>
            <if test="vehicleLocationCardNumber != null">vehicle_location_card_number,</if>
            <if test="vehicleCode != null">vehicle_code,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="department != null">department,</if>
            <if test="loadWeight != null">load_weight,</if>
            <if test="loadPersonnel != null">load_personnel,</if>
            <if test="vehicleStatus != null">vehicle_status,</if>
            <if test="factoryDate != null">factory_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileEncoding != null">#{fileEncoding},</if>
            <if test="mineCode != null">#{mineCode},</if>
            <if test="dataGenerationTime != null">#{dataGenerationTime},</if>
            <if test="vehicleLocationCardNumber != null">#{vehicleLocationCardNumber},</if>
            <if test="vehicleCode != null">#{vehicleCode},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="department != null">#{department},</if>
            <if test="loadWeight != null">#{loadWeight},</if>
            <if test="loadPersonnel != null">#{loadPersonnel},</if>
            <if test="vehicleStatus != null">#{vehicleStatus},</if>
            <if test="factoryDate != null">#{factoryDate},</if>
         </trim>
    </insert>

    <update id="updateKafkaVehicleProperty" parameterType="KafkaVehicleProperty">
        update kafka_vehicle_property
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileEncoding != null">file_encoding = #{fileEncoding},</if>
            <if test="mineCode != null">mine_code = #{mineCode},</if>
            <if test="dataGenerationTime != null">data_generation_time = #{dataGenerationTime},</if>
            <if test="vehicleLocationCardNumber != null">vehicle_location_card_number = #{vehicleLocationCardNumber},</if>
            <if test="vehicleCode != null">vehicle_code = #{vehicleCode},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="department != null">department = #{department},</if>
            <if test="loadWeight != null">load_weight = #{loadWeight},</if>
            <if test="loadPersonnel != null">load_personnel = #{loadPersonnel},</if>
            <if test="vehicleStatus != null">vehicle_status = #{vehicleStatus},</if>
            <if test="factoryDate != null">factory_date = #{factoryDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKafkaVehiclePropertyById" parameterType="Long">
        delete from kafka_vehicle_property where id = #{id}
    </delete>

    <delete id="deleteKafkaVehiclePropertyByIds" parameterType="String">
        delete from kafka_vehicle_property where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>