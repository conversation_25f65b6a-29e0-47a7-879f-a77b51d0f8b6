package com.ruoyi.lxbi.controller.base;

import java.util.List;

import com.ruoyi.common.core.domain.R;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.BaseWorkingPeriod;
import com.ruoyi.lxbi.service.IBaseWorkingPeriodService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 作业时段配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/base/period")
public class BaseWorkingPeriodController extends BaseController {
    @Autowired
    private IBaseWorkingPeriodService baseWorkingPeriodService;

    /**
     * 查询作业时段配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:period:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaseWorkingPeriod baseWorkingPeriod) {
        startPage();
        List<BaseWorkingPeriod> list = baseWorkingPeriodService.selectBaseWorkingPeriodList(baseWorkingPeriod);
        return getDataTable(list);
    }

    /**
     * 查询作业时段配置列表（不分页）
     */
    @GetMapping("/listAll")
    public R<List<BaseWorkingPeriod>> listAll(BaseWorkingPeriod baseWorkingPeriod) {
        startOrderBy();
        List<BaseWorkingPeriod> list = baseWorkingPeriodService.selectBaseWorkingPeriodList(baseWorkingPeriod);
        return R.ok(list);
    }

    /**
     * 导出作业时段配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:period:export')")
    @Log(title = "作业时段配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseWorkingPeriod baseWorkingPeriod) {
        List<BaseWorkingPeriod> list = baseWorkingPeriodService.selectBaseWorkingPeriodList(baseWorkingPeriod);
        ExcelUtil<BaseWorkingPeriod> util = new ExcelUtil<BaseWorkingPeriod>(BaseWorkingPeriod.class);
        util.exportExcel(response, list, "作业时段配置数据");
    }

    /**
     * 获取作业时段配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:period:query')")
    @GetMapping(value = "/{workingPeriodId}")
    public AjaxResult getInfo(@PathVariable("workingPeriodId") Long workingPeriodId) {
        return success(baseWorkingPeriodService.selectBaseWorkingPeriodByWorkingPeriodId(workingPeriodId));
    }

    /**
     * 新增作业时段配置
     */
    @PreAuthorize("@ss.hasPermi('base:period:add')")
    @Log(title = "作业时段配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseWorkingPeriod baseWorkingPeriod)
    {
        return toAjax(baseWorkingPeriodService.insertBaseWorkingPeriod(baseWorkingPeriod));
    }

    /**
     * 修改作业时段配置
     */
    @PreAuthorize("@ss.hasPermi('base:period:edit')")
    @Log(title = "作业时段配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaseWorkingPeriod baseWorkingPeriod)
    {
        return toAjax(baseWorkingPeriodService.updateBaseWorkingPeriod(baseWorkingPeriod));
    }

    /**
     * 删除作业时段配置
     */
    @PreAuthorize("@ss.hasPermi('base:period:remove')")
    @Log(title = "作业时段配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{workingPeriodIds}")
    public AjaxResult remove(@PathVariable Long[] workingPeriodIds)
    {
        return toAjax(baseWorkingPeriodService.deleteBaseWorkingPeriodByWorkingPeriodIds(workingPeriodIds));
    }
}
