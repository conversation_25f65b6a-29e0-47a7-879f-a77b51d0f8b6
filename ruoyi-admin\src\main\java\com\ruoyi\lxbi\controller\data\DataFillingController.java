package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataFilling;
import com.ruoyi.lxbi.domain.request.DataFillingBatchDto;
import com.ruoyi.lxbi.domain.response.DataFillingVo;
import com.ruoyi.lxbi.service.IDataFillingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 充填数据Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/data/filling")
public class DataFillingController extends BaseController {
    @Autowired
    private IDataFillingService dataFillingService;

    /**
     * 查询充填数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:filling:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataFilling dataFilling) {
        startPage();
        List<DataFillingVo> list = dataFillingService.selectDataFillingList(dataFilling);
        return getDataTable(list);
    }

    /**
     * 导出充填数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:filling:export')")
    @Log(title = "充填数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataFilling dataFilling) {
        List<DataFillingVo> list = dataFillingService.selectDataFillingList(dataFilling);
        ExcelUtil<DataFillingVo> util = new ExcelUtil<DataFillingVo>(DataFillingVo.class);
        util.exportExcel(response, list, "充填数据数据");
    }

    /**
     * 获取充填数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:filling:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataFillingService.selectDataFillingById(id));
    }

    /**
     * 新增充填数据
     */
    @PreAuthorize("@ss.hasPermi('data:filling:add')")
    @Log(title = "充填数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataFilling dataFilling)
    {
        return toAjax(dataFillingService.insertDataFilling(dataFilling));
    }

    /**
     * 修改充填数据
     */
    @PreAuthorize("@ss.hasPermi('data:filling:edit')")
    @Log(title = "充填数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataFilling dataFilling)
    {
        return toAjax(dataFillingService.updateDataFilling(dataFilling));
    }

    /**
     * 删除充填数据
     */
    @PreAuthorize("@ss.hasPermi('data:filling:remove')")
    @Log(title = "充填数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataFillingService.deleteDataFillingByIds(ids));
    }

    /**
     * 批量保存充填数据（增删改查）
     * 传入批量列表，验证是否同一个日期和项目部的数据，然后查询这个日期和项目部的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('data:filling:edit')")
    @Log(title = "充填数据批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<DataFillingBatchDto> batchDataList)
    {
        try {
            int result = dataFillingService.batchSaveDataFilling(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
