<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.PlanSupportMonthlyMapper">
    
    <resultMap type="PlanSupportMonthly" id="PlanSupportMonthlyResult">
        <result property="id"    column="id"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="boltMeshSupportMeter"    column="bolt_mesh_support_meter"    />
        <result property="shotcreteSupportMeter"    column="shotcrete_support_meter"    />
        <result property="boltMeshSupportVolume"    column="bolt_mesh_support_volume"    />
        <result property="shotcreteSupportVolume"    column="shotcrete_support_volume"    />
        <result property="supportVolume"    column="support_volume"    />
        <result property="supportMeter"    column="support_meter"    />
        <result property="remark"    column="remark"    />
        <result property="planDate"    column="plan_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPlanSupportMonthlyVo">
        select psm.*, bpd.project_department_name, bs.stope_name, bws.working_face_name from plan_support_monthly psm  left join base_project_department bpd on psm.project_department_id = bpd.project_department_id
        left join base_working_face bws on psm.working_face_id = bws.working_face_id
        left join base_stope bs on psm.stope_id = bs.stope_id
    </sql>

    <select id="selectPlanSupportMonthlyList" parameterType="PlanSupportMonthly" resultType="com.ruoyi.lxbi.domain.response.PlanSupportMonthlyVo">
        <include refid="selectPlanSupportMonthlyVo"/>
        <where>  
            <if test="projectDepartmentId != null "> and psm.project_department_id = #{projectDepartmentId}</if>
            <if test="workingFaceId != null "> and psm.working_face_id = #{workingFaceId}</if>
            <if test="stopeId != null "> and psm.stope_id = #{stopeId}</if>
            <if test="planDate != null and planDate != ''"> and psm.plan_date = #{planDate}</if>
        </where>
    </select>
    
    <select id="selectPlanSupportMonthlyById" parameterType="Long" resultType="com.ruoyi.lxbi.domain.response.PlanSupportMonthlyVo">
        <include refid="selectPlanSupportMonthlyVo"/>
        where psm.id = #{id}
    </select>

    <insert id="insertPlanSupportMonthly" parameterType="PlanSupportMonthly" useGeneratedKeys="true" keyProperty="id">
        insert into plan_support_monthly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="workingFaceId != null">working_face_id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="boltMeshSupportMeter != null">bolt_mesh_support_meter,</if>
            <if test="shotcreteSupportMeter != null">shotcrete_support_meter,</if>
            <if test="boltMeshSupportVolume != null">bolt_mesh_support_volume,</if>
            <if test="shotcreteSupportVolume != null">shotcrete_support_volume,</if>
            <if test="supportVolume != null">support_volume,</if>
            <if test="supportMeter != null">support_meter,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="workingFaceId != null">#{workingFaceId},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="boltMeshSupportMeter != null">#{boltMeshSupportMeter},</if>
            <if test="shotcreteSupportMeter != null">#{shotcreteSupportMeter},</if>
            <if test="boltMeshSupportVolume != null">#{boltMeshSupportVolume},</if>
            <if test="shotcreteSupportVolume != null">#{shotcreteSupportVolume},</if>
            <if test="supportVolume != null">#{supportVolume},</if>
            <if test="supportMeter != null">#{supportMeter},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePlanSupportMonthly" parameterType="PlanSupportMonthly">
        update plan_support_monthly
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="workingFaceId != null">working_face_id = #{workingFaceId},</if>
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="boltMeshSupportMeter != null">bolt_mesh_support_meter = #{boltMeshSupportMeter},</if>
            <if test="shotcreteSupportMeter != null">shotcrete_support_meter = #{shotcreteSupportMeter},</if>
            <if test="boltMeshSupportVolume != null">bolt_mesh_support_volume = #{boltMeshSupportVolume},</if>
            <if test="shotcreteSupportVolume != null">shotcrete_support_volume = #{shotcreteSupportVolume},</if>
            <if test="supportVolume != null">support_volume = #{supportVolume},</if>
            <if test="supportMeter != null">support_meter = #{supportMeter},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanSupportMonthlyById" parameterType="Long">
        delete from plan_support_monthly where id = #{id}
    </delete>

    <delete id="deletePlanSupportMonthlyByIds" parameterType="String">
        delete from plan_support_monthly where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>