# 报警事件趋势接口说明

## 概述

本文档说明了充填漏浆检测趋势统计接口的修改，实现了根据不同时间维度返回相应的X轴数据格式：
- **日维度**：X轴为小时（00:00-23:00）
- **周维度**：X轴为日期（7天的日期）
- **月维度**：X轴为日期（月内所有日期）

## 修改内容

### 1. Service实现类更新

**文件**: `FillingLeakageStatServiceImpl.java`

**主要修改**:
- 在`getAlarmTrend`方法中添加了`switch`语句，根据`viewType`返回不同维度的数据
- 新增了两个辅助方法：`generateHourlyAlarmCount`和`generateDailyAlarmCount`

### 2. 数据返回格式

#### 日维度 (daily)
```json
[
  {
    "date": "2025-08-25 00:00:00",
    "shortDate": "00:00",
    "alarmTrendCount": 5,
    "areaIntrusionCount": 3,
    "temperatureDifferenceCount": 2
  },
  {
    "date": "2025-08-25 01:00:00", 
    "shortDate": "01:00",
    "alarmTrendCount": 3,
    "areaIntrusionCount": 2,
    "temperatureDifferenceCount": 1
  },
  // ... 继续到23:00
]
```

#### 周维度 (weekly)
```json
[
  {
    "date": "2025-08-19",
    "shortDate": "08-19",
    "alarmTrendCount": 85,
    "areaIntrusionCount": 55,
    "temperatureDifferenceCount": 30
  },
  {
    "date": "2025-08-20",
    "shortDate": "08-20", 
    "alarmTrendCount": 92,
    "areaIntrusionCount": 60,
    "temperatureDifferenceCount": 32
  },
  // ... 7天数据
]
```

#### 月维度 (monthly)
```json
[
  {
    "date": "2025-08-01",
    "shortDate": "08-01",
    "alarmTrendCount": 78,
    "areaIntrusionCount": 55,
    "temperatureDifferenceCount": 23
  },
  {
    "date": "2025-08-02",
    "shortDate": "08-02",
    "alarmTrendCount": 95,
    "areaIntrusionCount": 67,
    "temperatureDifferenceCount": 28
  },
  // ... 整月数据
]
```

## 业务逻辑说明

### 1. 时间维度处理

**日维度 (daily)**:
- 生成24个数据点，对应0-23小时
- `shortDate`格式：`"HH:mm"`（如："08:00"）
- 适合查看一天内的报警分布规律

**周维度 (weekly)**:
- 生成7个数据点，对应一周的7天
- `shortDate`格式：`"MM-dd"`（如："08-25"）
- 适合查看一周内的报警趋势

**月维度 (monthly)**:
- 生成月内所有日期的数据点
- `shortDate`格式：`"MM-dd"`（如："08-01"）
- 适合查看月度报警趋势

### 2. 模拟数据规律

**小时级别分布**:
```java
// 工作时间(8-18点): 15-25个报警
// 晚间时间(19-23点): 8-15个报警  
// 夜间时间(0-7点): 2-8个报警
```

**日级别分布**:
```java
// 工作日: 80-120个报警
// 周末: 50-80个报警
```

**报警类型分布**:
- 区域入侵报警：占总数的60-70%
- 温差异常报警：占总数的30-40%

### 3. 前端图表配置

```javascript
// ECharts配置示例
const getChartOption = (data, viewType) => {
  const xAxisName = viewType === 'daily' ? '小时' : '日期';
  const tooltipUnit = viewType === 'daily' ? '时' : '日';
  
  return {
    title: {
      text: '充填漏浆报警事件趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        let result = `${params[0].name}${tooltipUnit}<br/>`;
        params.forEach(param => {
          result += `${param.seriesName}: ${param.value}<br/>`;
        });
        return result;
      }
    },
    legend: {
      data: ['总报警', '区域入侵', '温差异常'],
      bottom: 10
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.shortDate),
      name: xAxisName
    },
    yAxis: {
      type: 'value',
      name: '报警数量'
    },
    series: [
      {
        name: '总报警',
        type: 'line',
        data: data.map(item => item.alarmTrendCount),
        itemStyle: { color: '#ff6b6b' }
      },
      {
        name: '区域入侵',
        type: 'line', 
        data: data.map(item => item.areaIntrusionCount),
        itemStyle: { color: '#4ecdc4' }
      },
      {
        name: '温差异常',
        type: 'line',
        data: data.map(item => item.temperatureDifferenceCount), 
        itemStyle: { color: '#45b7d1' }
      }
    ]
  };
};
```

## 使用示例

### 1. 获取日维度趋势（24小时）

```bash
curl -X GET "http://localhost:8080/lxbi/stat/filling-leakage/alarm-trend?viewType=daily"
```

### 2. 获取周维度趋势（7天）

```bash
curl -X GET "http://localhost:8080/lxbi/stat/filling-leakage/alarm-trend?viewType=weekly"
```

### 3. 获取月维度趋势（整月）

```bash
curl -X GET "http://localhost:8080/lxbi/stat/filling-leakage/alarm-trend?viewType=monthly"
```

### 4. 指定日期范围

```bash
curl -X GET "http://localhost:8080/lxbi/stat/filling-leakage/alarm-trend?viewType=weekly&startDate=2025-08-19&endDate=2025-08-25"
```

## 扩展建议

### 1. 数据库实现

实际项目中，可以按以下SQL查询真实数据：

```sql
-- 日维度查询
SELECT 
    HOUR(alarm_time) as hour_value,
    COUNT(*) as alarm_count,
    SUM(CASE WHEN alarm_type = 'AREA_INTRUSION' THEN 1 ELSE 0 END) as area_intrusion_count,
    SUM(CASE WHEN alarm_type = 'TEMPERATURE_DIFF' THEN 1 ELSE 0 END) as temperature_diff_count
FROM filling_leakage_alarm 
WHERE DATE(alarm_time) = ?
GROUP BY HOUR(alarm_time)
ORDER BY hour_value;

-- 周/月维度查询  
SELECT 
    DATE(alarm_time) as alarm_date,
    COUNT(*) as alarm_count,
    SUM(CASE WHEN alarm_type = 'AREA_INTRUSION' THEN 1 ELSE 0 END) as area_intrusion_count,
    SUM(CASE WHEN alarm_type = 'TEMPERATURE_DIFF' THEN 1 ELSE 0 END) as temperature_diff_count
FROM filling_leakage_alarm 
WHERE alarm_time BETWEEN ? AND ?
GROUP BY DATE(alarm_time)
ORDER BY alarm_date;
```

### 2. 缓存优化

对于频繁查询的趋势数据，建议添加缓存：

```java
@Cacheable(value = "alarmTrend", key = "#viewType + '_' + #startDate + '_' + #endDate")
public List<FillingLeakageTrendVO> getAlarmTrend(String viewType, String startDate, String endDate) {
    // 实现逻辑
}
```

### 3. 实时数据支持

可以集成WebSocket实现实时数据推送：

```java
@EventListener
public void handleAlarmEvent(AlarmEvent event) {
    // 更新趋势数据
    // 推送给前端
    webSocketService.sendTrendUpdate(event);
}
```

## 总结

通过这次修改，充填漏浆检测趋势接口现在能够：

1. **智能适配**：根据时间维度自动调整X轴数据格式
2. **业务贴合**：模拟真实的报警分布规律
3. **易于扩展**：为后续接入真实数据预留了接口
4. **用户友好**：提供了清晰的时间标签和数据结构

这为前端图表展示提供了完整的数据支持，能够满足不同时间维度的报警趋势分析需求。
