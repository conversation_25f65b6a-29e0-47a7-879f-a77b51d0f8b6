package com.ruoyi.common.core.table;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.core.domain.TableConfigInfo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.TableConfigProcessor;
import com.ruoyi.common.utils.reflect.ReflectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 表格处理器抽象基类
 * @param <T> 实体类型
 * @param <P> 查询参数类型
 */
public abstract class BaseTableHandler<T, P> {
    
    @Autowired
    private TableConfigProcessor tableConfigProcessor;
    
    /**
     * 获取表格配置
     */
    public TableConfigInfo getTableConfig() {
        Class<T> entityClass = getEntityClass();
        return tableConfigProcessor.processTableConfig(entityClass);
    }
    
    /**
     * 获取表格代码
     */
    public String getTableCode() {
        Class<T> entityClass = getEntityClass();
        TableConfig tableConfig = entityClass.getAnnotation(TableConfig.class);
        if (tableConfig == null) {
            throw new RuntimeException("实体类必须使用@TableConfig注解");
        }
        return tableConfig.code();
    }
    
    /**
     * 查询表格数据（使用Map参数）
     */
    public List<T> queryTableData(Map<String, Object> params) {
        P typedParams = convertToParams(params);
        return queryTableData(typedParams);
    }

    /**
     * 查询表格数据（使用泛型参数对象）
     */
    public abstract List<T> queryTableData(P params);

    /**
     * 将Map转换为参数对象
     */
    protected P convertToParams(Map<String, Object> paramMap) {
        if (paramMap == null) {
            return null;
        }
        Class<P> paramClass = getParamClass();
        if (paramClass == null) {
            return null;
        }
        // 使用FastJSON进行转换
        String jsonStr = JSON.toJSONString(paramMap);
        return JSON.parseObject(jsonStr, paramClass);
    }

    /**
     * 获取实体类类型
     */
    protected Class<T> getEntityClass() {
        return ReflectUtils.getClassGenricType(getClass(), 0);
    }

    /**
     * 获取参数类类型
     */
    protected Class<P> getParamClass() {
        return ReflectUtils.getClassGenricType(getClass(), 1);
    }

}