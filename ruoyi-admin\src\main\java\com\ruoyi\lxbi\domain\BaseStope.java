package com.ruoyi.lxbi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 采场配置对象 base_stope
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseStope extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 采场ID */
    private Long stopeId;

    /** 采场名称 */
    @Excel(name = "采场名称")
    private String stopeName;

    /** 工作面ID */
    @Excel(name = "工作面ID")
    private Long workingFaceId;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 采场开始时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "采场开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 采场结束时间 */
    @JsonFormat(pattern ="yyyy-MM-dd")
    @Excel(name = "采场结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 是否删除 */
    private Long isDelete;

}
