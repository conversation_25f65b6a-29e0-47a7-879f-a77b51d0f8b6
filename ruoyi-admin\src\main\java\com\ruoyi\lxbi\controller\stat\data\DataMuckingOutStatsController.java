package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DataMuckingOutStatsRequest;
import com.ruoyi.lxbi.domain.response.DataMuckingOutTotalStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutPeriodStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutDepartmentStats;
import com.ruoyi.lxbi.domain.response.DataMuckingOutStopeStats;
import com.ruoyi.lxbi.service.IDataMuckingOutStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 出矿数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/data/stats/out")
public class DataMuckingOutStatsController {
    @Autowired
    private IDataMuckingOutStatsService dataMuckingOutStatsService;

    /**
     * 查询总体出矿量统计数据
     * 对应图表一：总体出矿量统计柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:out:01a')")
    @GetMapping("/01a")
    public R<List<DataMuckingOutTotalStats>> totalVolume(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                          @RequestParam(value = "startDate", required = false) String startDate,
                                                          @RequestParam(value = "endDate", required = false) String endDate) {
        DataMuckingOutStatsRequest request = new DataMuckingOutStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMuckingOutTotalStats> stats = dataMuckingOutStatsService.selectTotalStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询总体出矿量统计数据（含计划量）
     * 对应图表一：总体出矿量统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:out:02')")
    @GetMapping("/02")
    public R<List<DataMuckingOutTotalWithPlanStats>> totalVolumeWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                          @RequestParam(value = "startDate", required = false) String startDate,
                                                                          @RequestParam(value = "endDate", required = false) String endDate) {
        DataMuckingOutStatsRequest request = new DataMuckingOutStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMuckingOutTotalWithPlanStats> stats = dataMuckingOutStatsService.selectTotalWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询班次出矿量分布柱状图数据
     * 对应图表三：按班次分组的出矿量柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:out:01e')")
    @GetMapping("/01e")
    public R<List<DataMuckingOutPeriodStats>> volumePeriod(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                            @RequestParam(value = "startDate", required = false) String startDate,
                                                            @RequestParam(value = "endDate", required = false) String endDate) {
        DataMuckingOutStatsRequest request = new DataMuckingOutStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMuckingOutPeriodStats> stats = dataMuckingOutStatsService.selectPeriodStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询项目部门出矿量分布柱状图数据
     * 对应图表四：按项目部门分组的出矿量柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:out:01c')")
    @GetMapping("/01c")
    public R<List<DataMuckingOutDepartmentStats>> volumeDepartment(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                    @RequestParam(value = "startDate", required = false) String startDate,
                                                                    @RequestParam(value = "endDate", required = false) String endDate) {
        DataMuckingOutStatsRequest request = new DataMuckingOutStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMuckingOutDepartmentStats> stats = dataMuckingOutStatsService.selectDepartmentStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询采场出矿量分布柱状图数据
     * 对应图表五：按采场分组的出矿量柱状图
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:out:01b')")
    @GetMapping("/01b")
    public R<List<DataMuckingOutStopeStats>> volumeStope(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                          @RequestParam(value = "startDate", required = false) String startDate,
                                                          @RequestParam(value = "endDate", required = false) String endDate) {
        DataMuckingOutStatsRequest request = new DataMuckingOutStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataMuckingOutStopeStats> stats = dataMuckingOutStatsService.selectStopeStatsList(request, viewType);
        return R.ok(stats);
    }

}
