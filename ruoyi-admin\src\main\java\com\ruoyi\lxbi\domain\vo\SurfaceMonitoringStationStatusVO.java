package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 地表监测站点状态VO
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurfaceMonitoringStationStatusVO {

    /**
     * 站点ID
     */
    private Long stationId;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 站点编号
     */
    private String stationCode;

    /**
     * 通信状态
     */
    private String communicationStatus;

    /**
     * 定位状态
     */
    private String positioningStatus;

    /**
     * 卫星数量
     */
    private Integer satelliteCount;

    /**
     * 当前X偏移量(mm)
     */
    private BigDecimal currentXOffset;

    /**
     * 当前Y偏移量(mm)
     */
    private BigDecimal currentYOffset;

    /**
     * 当前H偏移量(mm)
     */
    private BigDecimal currentHOffset;

    /**
     * 总偏移量(mm)
     */
    private BigDecimal totalOffset;

    /**
     * 报警状态
     */
    private String alarmStatus;

    /**
     * 报警等级
     */
    private String alarmLevel;

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;

    /**
     * 数据质量评分
     */
    private Integer dataQualityScore;

    /**
     * 是否在线
     */
    private Boolean isOnline;

    /**
     * 在线时长(分钟)
     */
    private Long onlineDuration;
}
