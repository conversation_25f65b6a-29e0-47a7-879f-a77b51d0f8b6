package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 中深孔月计划导入对象
 */
@Data
@ExcelImportTemplate(
        key = "plan_deep_hole_monthly",
        name = "中深孔月计划导入",
        description = "用于导入中深孔月计划数据",
        sheetName = "中深孔月计划"
)
public class PlanDeepHoleMonthlyImport {

    /**
     * 计划月份
     */
    @ExcelProperty(value = "计划月份", index = 0)
    @ExcelRequired(message = "计划月份不能为空")
    @ExcelSelected(prompt = "请输入计划月份，格式：yyyyMM，例如：202501")
    private String planDate;

    /**
     * 项目部门ID（存储值）
     */
    @ExcelSelected(
            optionKey = "projectDepartment",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long projectDepartmentId;

    /**
     * 项目部门名称（显示值）
     */
    @ExcelProperty(value = "项目部门", index = 1)
    @ExcelRequired(message = "项目部门不能为空")
    @ExcelSelected(
            optionKey = "projectDepartment",
            prompt = "请选择项目部门",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String projectDepartmentName;

    /**
     * 工作面ID（存储值）
     */
    @ExcelSelected(
            optionKey = "workingFace",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long workingFaceId;

    /**
     * 工作面名称（显示值）
     */
    @ExcelProperty(value = "工作面", index = 2)
    @ExcelSelected(
            optionKey = "workingFace",
            prompt = "请选择工作面",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    @ExcelRequired(message = "工作面不能为空")
    private String workingFaceName;

    /**
     * 采场ID（存储值）
     */
    @ExcelSelected(
            optionKey = "stope",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long stopeId;

    /**
     * 采场名称（显示值）
     */
    @ExcelProperty(value = "采场", index = 3)
    @ExcelSelected(
            optionKey = "stope",
            prompt = "请选择采场",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    @ExcelRequired(message = "采场不能为空")
    private String stopeName;

    /**
     * 中深孔钻机米数
     */
    @ExcelProperty(value = "中深孔钻机米数", index = 4)
    @ExcelRequired(message = "中深孔钻机米数不能为空")
    @ExcelSelected(prompt = "请输入中深孔钻机米数")
    private BigDecimal deepHoleMeter;
}
