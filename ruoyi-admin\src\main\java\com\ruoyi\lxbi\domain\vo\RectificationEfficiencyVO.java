package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 隐患整改效率VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RectificationEfficiencyVO {
    
    /**
     * 部门名称
     */
    private String department;
    
    /**
     * 隐患总数
     */
    private Long totalCount;
    
    /**
     * 已完成数
     */
    private Long completedCount;
    
    /**
     * 平均完成天数
     */
    private BigDecimal avgCompletionDays;
    
    /**
     * 完成率
     */
    private BigDecimal completionRate;
}
