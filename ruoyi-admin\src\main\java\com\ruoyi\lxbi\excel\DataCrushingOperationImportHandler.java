package com.ruoyi.lxbi.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportContext;
import com.ruoyi.common.core.domain.excel.ExcelOptionInfo;
import com.ruoyi.common.core.excel.ExcelImportHandler;
import com.ruoyi.lxbi.domain.BaseWorkingPeriod;
import com.ruoyi.lxbi.domain.DataCrushingOperation;
import com.ruoyi.lxbi.domain.excel.DataCrushingOperationImport;
import com.ruoyi.lxbi.service.IBaseWorkingPeriodService;
import com.ruoyi.lxbi.service.IDataCrushingOperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 破碎数据导入处理器
 *
 * <AUTHOR>
 */
@Component
public class DataCrushingOperationImportHandler extends ExcelImportHandler<DataCrushingOperationImport> {

    @Autowired
    private IBaseWorkingPeriodService baseWorkingPeriodService;

    @Autowired
    private IDataCrushingOperationService dataCrushingOperationService;

    @Override
    protected Class<DataCrushingOperationImport> getEntityClass() {
        return DataCrushingOperationImport.class;
    }

    @Override
    protected void initContext(ExcelImportContext context) {
        // 设置作业时段选项
        List<ExcelOptionInfo> workingPeriods = new ArrayList<>();
        BaseWorkingPeriod queryParam = new BaseWorkingPeriod();
        queryParam.setStatus(1L); // 只查询启用状态的作业时段
        List<BaseWorkingPeriod> periodList = baseWorkingPeriodService.selectBaseWorkingPeriodList(queryParam);

        for (BaseWorkingPeriod period : periodList) {
            workingPeriods.add(new ExcelOptionInfo(
                    period.getWorkingPeriodId(),
                    period.getWorkingPeriodName()
            ));
        }
        context.setOptions("workingPeriod", workingPeriods);
    }

    @Override
    protected void validateData(ExcelDataInfo<DataCrushingOperationImport> dataInfo, ExcelImportContext context) {
        DataCrushingOperationImport data = dataInfo.getData();

        // 验证作业日期不能为未来日期
        if (data.getOperationDate() != null && data.getOperationDate().after(new Date())) {
            dataInfo.addError("operationDate", "作业日期不能是未来日期");
        }

        // 验证运行时间
        if (data.getOperationTime() != null) {
            if (data.getOperationTime() < 0 || data.getOperationTime() > 1440) {
                dataInfo.addError("operationTime", "运行时间必须在0-1440分钟之间");
            }
        }

        // 验证故障时长
        if (data.getFaultTime() != null) {
            if (data.getFaultTime() < 0 || data.getFaultTime() > 1440) {
                dataInfo.addError("faultTime", "故障时长必须在0-1440分钟之间");
            }
        }

        // 验证破碎量
        if (data.getCrushingVolume() != null && data.getCrushingVolume() < 0) {
            dataInfo.addError("crushingVolume", "破碎量不能为负数");
        }

        if (data.getFaultStartTime() != null && data.getFaultEndTime() == null) {
            dataInfo.addError("faultEndTime", "填写故障开始时间时，故障结束时间不能为空");
        }
        if (data.getFaultStartTime() == null && data.getFaultEndTime() != null) {
            dataInfo.addError("faultStartTime", "填写故障结束时间时，故障开始时间不能为空");
        }

        // 验证故障时间逻辑
        if (data.getFaultStartTime() != null && data.getFaultEndTime() != null) {
            // 如果填写了故障时间，故障时长应该大于0
            if (data.getFaultTime() == null || data.getFaultTime() <= 0) {
                dataInfo.addError("faultTime", "填写故障时间时，故障时长必须大于0");
            }
            if (data.getFaultEndTime().isBefore(data.getFaultStartTime())) {
                dataInfo.addError("faultEndTime", "故障结束时间必须大于故障开始时间");
            }
            if (data.getFaultTime() != null && !data.getFaultStartTime().plusMinutes(data.getFaultTime()).equals(data.getFaultEndTime())) {
                dataInfo.addError("faultTime", "故障时长与故障开始时间、结束时间不匹配");
            }
        }
    }

    @Override
    protected void saveData(DataCrushingOperationImport data, ExcelImportContext context) {
        // 转换为实体对象
        DataCrushingOperation entity = new DataCrushingOperation();
        entity.setWorkingPeriodId(data.getWorkingPeriodId());
        entity.setOperationDate(data.getOperationDate());
        entity.setOperationTime(data.getOperationTime());
        entity.setFaultTime(data.getFaultTime());
        entity.setFaultReason(data.getFaultReason());
        entity.setFaultStartTime(data.getFaultStartTime() != null ? data.getFaultStartTime().toString() : null);
        entity.setFaultEndTime(data.getFaultEndTime() != null ? data.getFaultEndTime().toString() : null);
        entity.setCrushingVolume(data.getCrushingVolume());
        
        // 保存到数据库
        dataCrushingOperationService.insertDataCrushingOperation(entity);
    }

    @Override
    public List<DataCrushingOperationImport> exampleData() {
        // 模板不包含示例数据，只有表头
        return new ArrayList<>();
    }

    @Override
    protected void afterAllValidated(ExcelImportContext ctx) {
        // 验证完成后的处理逻辑
        System.out.println("破碎数据验证完成，总行数: " + ctx.getTotalRows());
    }

    @Override
    protected void afterAllImported(ExcelImportContext ctx) {
        // 导入完成后的处理逻辑
        System.out.println("破碎数据导入完成，总行数: " + ctx.getTotalRows());
    }
}
