package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 设备数据管理对象 data_equipment
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataEquipment extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 设备数据ID */
    private Long id;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String equipmentNo;

    /** 编组数量 */
    @Excel(name = "编组数量")
    private Long mineCarsNumber;

    /** 运行列次 */
    @Excel(name = "运行列次")
    private BigDecimal numberOfRunningTrains;

    /** 运行时长 */
    @Excel(name = "运行时长")
    private Integer operationTime;

    /** 单次处理量 */
    private BigDecimal singleProcessingVolume;

    /** 总处理量 */
    @Excel(name = "总处理量")
    private BigDecimal totalProcessingVolume;

    /** 运行日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "运行日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /** 运行开始时间 */
    @Excel(name = "运行开始时间", width = 30)
    private LocalTime startTime;

    /** 运行结束时间 */
    @Excel(name = "运行结束时间", width = 30)
    private LocalTime endTime;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private Long equipmentType;

}
