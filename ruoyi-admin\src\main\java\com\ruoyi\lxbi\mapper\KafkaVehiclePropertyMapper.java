package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaVehicleProperty;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 车辆属性数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface KafkaVehiclePropertyMapper
{
    /**
     * 查询车辆属性数据
     *
     * @param id 车辆属性数据主键
     * @return 车辆属性数据
     */
    public KafkaVehicleProperty selectKafkaVehiclePropertyById(Long id);

    /**
     * 查询车辆属性数据列表
     *
     * @param kafkaVehicleProperty 车辆属性数据
     * @return 车辆属性数据集合
     */
    public List<KafkaVehicleProperty> selectKafkaVehiclePropertyList(KafkaVehicleProperty kafkaVehicleProperty);

    /**
     * 根据车辆定位卡号查询车辆属性数据
     *
     * @param vehicleLocationCardNumber 车辆定位卡号
     * @return 车辆属性数据
     */
    public KafkaVehicleProperty selectByVehicleLocationCardNumber(@Param("vehicleLocationCardNumber") String vehicleLocationCardNumber);

    /**
     * 统计截止日期前的车辆总数
     *
     * @param endDate 截止日期
     * @return 车辆总数
     */
    public Long countVehiclesByEndDate(@Param("endDate") String endDate);

    /**
     * 新增车辆属性数据
     * 
     * @param kafkaVehicleProperty 车辆属性数据
     * @return 结果
     */
    public int insertKafkaVehicleProperty(KafkaVehicleProperty kafkaVehicleProperty);

    /**
     * 修改车辆属性数据
     * 
     * @param kafkaVehicleProperty 车辆属性数据
     * @return 结果
     */
    public int updateKafkaVehicleProperty(KafkaVehicleProperty kafkaVehicleProperty);

    /**
     * 删除车辆属性数据
     * 
     * @param id 车辆属性数据主键
     * @return 结果
     */
    public int deleteKafkaVehiclePropertyById(Long id);

    /**
     * 批量删除车辆属性数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaVehiclePropertyByIds(Long[] ids);
}
