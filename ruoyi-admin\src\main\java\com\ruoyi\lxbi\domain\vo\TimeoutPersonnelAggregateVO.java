package com.ruoyi.lxbi.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 超时人员聚合统计VO
 * 用于接收SQL聚合查询的结果
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TimeoutPersonnelAggregateVO {
    
    /**
     * 人员卡编码
     */
    private String personCardCode;
    
    /**
     * 姓名
     */
    private String personName;
    
    /**
     * 区域编码
     */
    private String areaCode;
    
    /**
     * 超时次数
     */
    private Long timeoutCount;
    
    /**
     * 停留小时数
     */
    private Double stayHours;
}
