package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaSubstationBasicInfo;

/**
 * 分站基本信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface IKafkaSubstationBasicInfoService 
{
    /**
     * 查询分站基本信息
     * 
     * @param id 分站基本信息主键
     * @return 分站基本信息
     */
    public KafkaSubstationBasicInfo selectKafkaSubstationBasicInfoById(Long id);

    /**
     * 查询分站基本信息列表
     * 
     * @param kafkaSubstationBasicInfo 分站基本信息
     * @return 分站基本信息集合
     */
    public List<KafkaSubstationBasicInfo> selectKafkaSubstationBasicInfoList(KafkaSubstationBasicInfo kafkaSubstationBasicInfo);

    /**
     * 新增分站基本信息
     * 
     * @param kafkaSubstationBasicInfo 分站基本信息
     * @return 结果
     */
    public int insertKafkaSubstationBasicInfo(KafkaSubstationBasicInfo kafkaSubstationBasicInfo);

    /**
     * 修改分站基本信息
     * 
     * @param kafkaSubstationBasicInfo 分站基本信息
     * @return 结果
     */
    public int updateKafkaSubstationBasicInfo(KafkaSubstationBasicInfo kafkaSubstationBasicInfo);

    /**
     * 批量删除分站基本信息
     * 
     * @param ids 需要删除的分站基本信息主键集合
     * @return 结果
     */
    public int deleteKafkaSubstationBasicInfoByIds(Long[] ids);

    /**
     * 删除分站基本信息信息
     *
     * @param id 分站基本信息主键
     * @return 结果
     */
    public int deleteKafkaSubstationBasicInfoById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaSubstationBasicInfo parseKafkaMessage(String kafkaMessage);
}
