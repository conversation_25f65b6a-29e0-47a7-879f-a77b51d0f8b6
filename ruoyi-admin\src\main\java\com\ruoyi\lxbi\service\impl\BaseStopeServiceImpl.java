package com.ruoyi.lxbi.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.lxbi.domain.response.BaseStopeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.lxbi.mapper.BaseStopeMapper;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.service.IBaseStopeService;

/**
 * 采场配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Service
public class BaseStopeServiceImpl implements IBaseStopeService 
{
    @Autowired
    private BaseStopeMapper baseStopeMapper;

    /**
     * 查询采场配置
     * 
     * @param stopeId 采场配置主键
     * @return 采场配置
     */
    @Override
    public BaseStope selectBaseStopeByStopeId(Long stopeId)
    {
        return baseStopeMapper.selectBaseStopeByStopeId(stopeId);
    }

    /**
     * 查询采场配置列表
     * 
     * @param baseStope 采场配置
     * @return 采场配置
     */
    @Override
    public List<BaseStopeVo> selectBaseStopeList(BaseStope baseStope)
    {
        return baseStopeMapper.selectBaseStopeList(baseStope);
    }

    /**
     * 查询采场配置列表
     *
     * @param baseStope 采场配置
     * @return 采场配置
     */
    @Override
    public List<BaseStope> selectBaseStopeListAll(BaseStope baseStope)
    {
        return baseStopeMapper.selectBaseStopeListAll(baseStope);
    }

    /**
     * 新增采场配置
     * 
     * @param baseStope 采场配置
     * @return 结果
     */
    @Override
    public int insertBaseStope(BaseStope baseStope)
    {
        baseStope.setCreateTime(DateUtils.getNowDate());
        return baseStopeMapper.insertBaseStope(baseStope);
    }

    /**
     * 修改采场配置
     * 
     * @param baseStope 采场配置
     * @return 结果
     */
    @Override
    public int updateBaseStope(BaseStope baseStope)
    {
        baseStope.setUpdateTime(DateUtils.getNowDate());
        return baseStopeMapper.updateBaseStope(baseStope);
    }

    /**
     * 批量删除采场配置
     * 
     * @param stopeIds 需要删除的采场配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseStopeByStopeIds(Long[] stopeIds)
    {
        if (stopeIds == null || stopeIds.length == 0) {
            throw new ServiceException("删除采场配置失败，未选择数据");
        }
        // Check if any of the stopeIds are in use before deletion
        if (baseStopeMapper.getBaseStopeByStopeIds(stopeIds) > 0) {
            throw new ServiceException("存在使用中的采场配置，无法删除");
        }
        return baseStopeMapper.deleteBaseStopeByStopeIds(stopeIds);
    }

    /**
     * 删除采场配置信息
     * 
     * @param stopeId 采场配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseStopeByStopeId(Long stopeId)
    {
        return baseStopeMapper.deleteBaseStopeByStopeId(stopeId);
    }
}
