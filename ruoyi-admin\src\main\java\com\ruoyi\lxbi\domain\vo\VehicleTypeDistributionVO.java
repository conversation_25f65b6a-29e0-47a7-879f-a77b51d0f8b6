package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 车辆类型分布VO
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleTypeDistributionVO {
    
    /**
     * 车辆类型名称
     */
    private String vehicleTypeName;
    
    /**
     * 车辆数量
     */
    private Long vehicleCount;
    
    /**
     * 占比百分比
     */
    private BigDecimal percentage;
    
    /**
     * 车辆类型代码
     */
    private String vehicleTypeCode;
}
