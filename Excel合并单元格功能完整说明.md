# Excel合并单元格功能完整说明

## 功能概述

为Excel注解增加了基于相同值的层次化合并单元格功能。系统会自动按照Excel注解的sort值进行排序，然后按照层次结构进行嵌套合并：
- sort值越小，合并优先级越高，合并的单元格范围越大
- 在上级合并的基础上，进行下级字段的合并
- 形成层次化的合并结构，使Excel表格更加美观和易读

## 核心特性

### 1. 自动排序和层次化合并
- 系统会自动按照标记了`mergeByValue = true`的字段进行多级排序
- 按照sort值从小到大依次进行层次化合并
- 确保所有相同值都能被正确合并

### 2. 智能合并策略
- 只合并相邻行中值相同的单元格
- 每一级合并都在上一级合并的范围内进行
- 不会跨越上级合并边界，保持数据的逻辑层次

### 3. 多种数据类型支持
- 字符串类型、数字类型、日期类型、布尔类型、公式类型
- 支持null值的智能处理

### 4. 多Sheet支持
- 支持大数据量的多Sheet导出
- 每个Sheet内部独立进行层次化合并
- 保持跨Sheet的数据一致性

## 使用方法

### 1. 在VO类中配置注解

```java
@Data
public class ExampleVo {
    /** 第一级合并（sort=1，优先级最高，合并范围最大） */
    @Excel(name = "项目部门", sort = 1, mergeByValue = true)
    private String projectDepartmentName;

    /** 第二级合并（sort=2，在项目部门合并基础上合并） */
    @Excel(name = "工作面", sort = 2, mergeByValue = true)
    private String workingFaceName;

    /** 第三级合并（sort=3，在工作面合并基础上合并） */
    @Excel(name = "采场名称", sort = 3, mergeByValue = true)
    private String stopeName;

    /** 不合并的字段 */
    @Excel(name = "作业日期", sort = 4, dateFormat = "yyyy-MM-dd")
    private String operationDate;

    @Excel(name = "出矿量(吨)", sort = 5)
    private Double tons;
}
```

### 2. 在Controller中导出

```java
@PostMapping("/export")
public void export(HttpServletResponse response) {
    List<ExampleVo> list = getDataList();
    ExcelUtil<ExampleVo> util = new ExcelUtil<ExampleVo>(ExampleVo.class);
    util.exportExcel(response, list, "层次化合并示例");
}
```

## 注解属性说明

### mergeByValue
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否基于相同值进行层次化合并
- **使用场景**: 通常用于分组字段，如部门、类别、地区等

### sort
- **类型**: `int`
- **默认值**: `Integer.MAX_VALUE`
- **说明**: 合并优先级，值越小优先级越高
- **重要性**: 决定了合并的层次顺序

## 层次化合并逻辑

### 合并算法流程

```
1. 数据排序阶段：
   - 识别标记了mergeByValue=true的字段
   - 按照sort值对字段进行排序
   - 按照字段顺序对数据进行多级排序

2. 层次化合并阶段：
   - 从整个数据范围开始
   - 按sort值从小到大依次合并
   - 每一级在上一级的范围内进行合并
   - 形成嵌套的合并结构
```

### 合并层次示例

```
第一级合并（sort=1，项目部门）：
├── 项目部门A（6行合并）
└── 项目部门B（4行合并）

第二级合并（sort=2，在第一级基础上合并工作面）：
├── 项目部门A
│   ├── 工作面1（3行合并）
│   └── 工作面2（3行合并）
└── 项目部门B
    └── 工作面3（4行合并）

第三级合并（sort=3，在第二级基础上合并采场）：
├── 项目部门A
│   ├── 工作面1
│   │   ├── 采场1-1（1行）
│   │   ├── 采场1-2（1行）
│   │   └── 采场1-3（1行）
│   └── 工作面2
│       ├── 采场2-1（2行合并）
│       └── 采场2-2（1行）
└── 项目部门B
    └── 工作面3
        ├── 采场3-1（2行合并）
        └── 采场3-2（2行合并）
```

## 示例效果

### 原始数据（未排序）
```
项目部门B | 工作面3 | 采场3-1 | 2025-07-01 | 156.4
项目部门A | 工作面2 | 采场2-1 | 2025-07-01 | 88.9
项目部门A | 工作面1 | 采场1-1 | 2025-07-01 | 120.5
项目部门B | 工作面3 | 采场3-2 | 2025-07-02 | 178.2
项目部门A | 工作面1 | 采场1-2 | 2025-07-01 | 98.3
```

### 自动排序后的数据
```
项目部门A | 工作面1 | 采场1-1 | 2025-07-01 | 120.5
项目部门A | 工作面1 | 采场1-2 | 2025-07-01 | 98.3
项目部门A | 工作面2 | 采场2-1 | 2025-07-01 | 88.9
项目部门B | 工作面3 | 采场3-1 | 2025-07-01 | 156.4
项目部门B | 工作面3 | 采场3-2 | 2025-07-02 | 178.2
```

### 最终Excel合并效果
```
┌─────────────┬─────────┬──────────┬────────────┬────────┐
│ 项目部门A    │ 工作面1  │ 采场1-1   │ 2025-07-01 │ 120.5  │
│             │         │ 采场1-2   │ 2025-07-01 │ 98.3   │
│             │ 工作面2  │ 采场2-1   │ 2025-07-01 │ 88.9   │
├─────────────┼─────────┼──────────┼────────────┼────────┤
│ 项目部门B    │ 工作面3  │ 采场3-1   │ 2025-07-01 │ 156.4  │
│             │         │ 采场3-2   │ 2025-07-02 │ 178.2  │
└─────────────┴─────────┴──────────┴────────────┴────────┘
```

## 技术实现

### 核心算法

1. **字段收集与排序**
```java
// 收集mergeByValue=true的字段，按sort值排序
List<MergeFieldInfo> mergeFields = collectMergeFields();
mergeFields.sort((a, b) -> Integer.compare(a.excel.sort(), b.excel.sort()));
```

2. **数据排序**
```java
// 按照合并字段进行多级排序
list.sort((o1, o2) -> {
    for (SortFieldInfo sortField : sortFields) {
        int result = compareValues(getValue(o1, sortField), getValue(o2, sortField));
        if (result != 0) return result;
    }
    return 0;
});
```

3. **层次化合并**
```java
// 从整个数据范围开始，逐级进行层次化合并
List<MergeRange> currentRanges = [整个数据范围];
for (MergeFieldInfo field : mergeFields) {
    List<MergeRange> nextRanges = new ArrayList<>();
    for (MergeRange range : currentRanges) {
        List<MergeRange> subRanges = mergeColumnInRange(field, range);
        nextRanges.addAll(subRanges);
    }
    currentRanges = nextRanges;
}
```

### 关键类和方法

- **MergeFieldInfo**: 合并字段信息，包含字段、Excel注解和列索引
- **MergeRange**: 合并范围，表示起始行和结束行
- **sortDataForMerging()**: 数据排序方法
- **performHierarchicalMerge()**: 执行层次化合并
- **mergeColumnInRange()**: 在指定范围内合并列

## 注意事项

### 1. 自动排序
- 系统会自动排序，无需在查询时手动排序
- 排序优先级按照字段的sort值确定
- 支持多种数据类型的智能比较

### 2. 性能考虑
- 排序开销：系统会在导出前对数据进行排序（只执行一次）
- 合并开销：合并操作在数据填充完成后执行
- 建议在数据量较大时谨慎使用

### 3. 使用建议
- 通常用于分组字段，如部门、类别、地区等
- 按照数据的层次结构从左到右设置合并
- 不建议对经常变化的数值字段使用
- 合理设置sort值，确保合并的逻辑层次正确

### 4. 兼容性
- 该功能与现有的`needMerge`功能完全兼容
- 可以同时使用多种合并策略
- 完全向后兼容，不影响现有功能

## 测试验证

### 测试接口
系统提供了测试Controller：`ExcelMergeExampleController`

1. **基础功能测试**：`/example/merge/export`
   - 数据量：约10条
   - 用途：验证基本层次化合并功能

2. **大数据量测试**：`/example/merge/exportLarge`
   - 数据量：150条（跨多个Sheet）
   - 用途：验证多Sheet场景下的排序和合并功能

3. **调试测试**：`/example/merge/exportDebug`
   - 数据量：6条简化数据
   - 用途：便于调试和验证合并逻辑

### 验证要点
1. ✅ 数据按照sort值正确排序
2. ✅ 按照sort顺序进行层次化合并
3. ✅ 下级合并不跨越上级边界
4. ✅ 多Sheet一致性保持
5. ✅ 各种数据类型正确处理

## 常见问题解决

### Q1: 为什么前面的数据没有合并？
**A**: 通常是因为数据没有正确排序。现在系统会自动排序，确保相同值连续出现。

### Q2: 如何实现多级嵌套合并？
**A**: 使用不同的sort值，系统会按照sort值从小到大依次进行层次化合并。

### Q3: 合并的层次顺序如何控制？
**A**: 通过Excel注解的sort属性控制，sort值越小优先级越高，合并范围越大。

### Q4: 支持多少级合并？
**A**: 理论上支持无限级合并，实际使用中建议不超过5级以保持表格的可读性。

这个层次化合并功能特别适用于具有明确层次结构的数据，如组织架构、地理区域、产品分类等场景，能够大大提升Excel导出的视觉效果和专业性。
