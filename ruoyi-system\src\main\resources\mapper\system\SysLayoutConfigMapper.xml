<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysLayoutConfigMapper">
    
    <resultMap type="SysLayoutConfig" id="SysLayoutConfigResult">
        <result property="id"    column="id"    />
        <result property="layoutName"    column="layout_name"    />
        <result property="layoutType"    column="layout_type"    />
        <result property="configJson"    column="config_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysLayoutConfigVo">
        select id, layout_name, layout_type, config_json, create_by, create_time, update_by, update_time from sys_layout_config
    </sql>

    <select id="selectSysLayoutConfigList" parameterType="SysLayoutConfig" resultMap="SysLayoutConfigResult">
        <include refid="selectSysLayoutConfigVo"/>
        <where>  
            <if test="layoutName != null  and layoutName != ''"> and layout_name like concat('%', #{layoutName}, '%')</if>
            <if test="layoutType != null  and layoutType != ''"> and layout_type = #{layoutType}</if>
        </where>
    </select>
    
    <select id="selectSysLayoutConfigById" parameterType="Long" resultMap="SysLayoutConfigResult">
        <include refid="selectSysLayoutConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysLayoutConfig" parameterType="SysLayoutConfig" useGeneratedKeys="true" keyProperty="id">
        insert into sys_layout_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="layoutName != null and layoutName != ''">layout_name,</if>
            <if test="layoutType != null and layoutType != ''">layout_type,</if>
            <if test="configJson != null">config_json,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="layoutName != null and layoutName != ''">#{layoutName},</if>
            <if test="layoutType != null and layoutType != ''">#{layoutType},</if>
            <if test="configJson != null">#{configJson},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysLayoutConfig" parameterType="SysLayoutConfig">
        update sys_layout_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="layoutName != null and layoutName != ''">layout_name = #{layoutName},</if>
            <if test="layoutType != null and layoutType != ''">layout_type = #{layoutType},</if>
            <if test="configJson != null">config_json = #{configJson},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysLayoutConfigById" parameterType="Long">
        delete from sys_layout_config where id = #{id}
    </delete>

    <delete id="deleteSysLayoutConfigByIds" parameterType="String">
        delete from sys_layout_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>