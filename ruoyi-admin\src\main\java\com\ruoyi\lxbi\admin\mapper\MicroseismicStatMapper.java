package com.ruoyi.lxbi.admin.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.MapKey;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 微震统计 Mapper（基于 excel_microseismic_events 表）
 */
@Mapper
public interface MicroseismicStatMapper {

    /**
     * 统计微震事件总数
     */
    long countEvents(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计目标事件数
     */
    long countTargetEvents(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计设备事件总数
     */
    long sumDeviceEventTotal(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计微震监测设备数量
     */
    long countMicroseismicDevice(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计P波辐射能总和
     */
    BigDecimal sumPWaveRadiatedEnergy(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计S波辐射能总和
     */
    BigDecimal sumSWaveRadiatedEnergy(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计微震事件辐射能总和
     */
    BigDecimal sumRadiatedEnergy(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计各等级震级次数
     */
    List<Map<String, Object>> getMagnitudeLevelDistribution(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 获取微震散点图原始数据
     */

    List<Map<String, Object>> getScatterRawData(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
