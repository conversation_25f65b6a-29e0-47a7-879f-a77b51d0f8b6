package com.ruoyi.lxbi.admin.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 微震统计 Mapper（基于 excel_microseismic_events 表）
 */
@Mapper
public interface MicroseismicStatMapper {

    @Select("SELECT COUNT(*) FROM excel_microseismic_events " +
            "WHERE event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)")
    long countEvents(@Param("startDate") String startDate, @Param("endDate") String endDate);

    // 目标事件：依据业务暂定为 signal_type = 12，可按需调整
    @Select("SELECT COUNT(*) FROM excel_microseismic_events " +
            "WHERE event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE) " +
            "AND signal_type = 12")
    long countTargetEvents(@Param("startDate") String startDate, @Param("endDate") String endDate);

    // 设备事件总数：按被触发传感器个数求和
    @Select("SELECT COALESCE(SUM(triggered_sensor_count), 0) FROM excel_microseismic_events " +
            "WHERE event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)")
    long sumDeviceEventTotal(@Param("startDate") String startDate, @Param("endDate") String endDate);

    // 微震监测设备数量：按 server_id 去重计数
    @Select("SELECT COUNT(DISTINCT server_id) FROM excel_microseismic_events " +
            "WHERE event_date BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE) " +
            "AND server_id IS NOT NULL")
    long countMicroseismicDevice(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
