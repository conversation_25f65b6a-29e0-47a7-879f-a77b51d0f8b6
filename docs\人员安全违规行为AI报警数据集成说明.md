# 人员安全违规行为AI报警数据集成说明

## 概述

本文档说明了人员安全统计系统中违规行为数据的修改，从原来的模拟数据改为使用真实的AI报警系统数据。

## 修改内容

### 1. 依赖注入

**文件**: `PersonnelSafetyStatServiceImpl.java`

**新增依赖**:
```java
@Autowired
private IApiAiAlarmService apiAiAlarmService;
```

### 2. 违规行为数据源修改

**修改前（模拟数据）**:
```java
// 4. 违规行为 - 暂时使用模拟数据，后续可接入监控系统
overview.setViolationCount(2L);

// 2. 检测到违规行为数 - 暂时使用模拟数据（可以后续接入AI报警系统）
summary.setDetectedViolations(3L);

// 2. 违规行为数 - 暂时使用模拟数据（可以后续接入AI报警系统）
simpleOverview.setViolationBehavior(2L);
```

**修改后（真实数据）**:
```java
// 4. 违规行为 - 使用AI报警系统的真实数据
Long violationCount = getViolationCountFromAiAlarm(startDate, endDate);
overview.setViolationCount(violationCount);

// 2. 检测到违规行为数 - 使用AI报警系统的真实数据
Long detectedViolations = getViolationCountFromAiAlarm(startDate, endDate);
summary.setDetectedViolations(detectedViolations);

// 2. 违规行为数 - 使用AI报警系统的真实数据
Long violationBehavior = getViolationCountFromAiAlarm(startDate, endDate);
simpleOverview.setViolationBehavior(violationBehavior);
```

### 3. 新增核心方法

#### 3.1 获取违规行为数量

```java
/**
 * 从AI报警系统获取违规行为数量
 * 
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @return 违规行为数量
 */
private Long getViolationCountFromAiAlarm(String startDate, String endDate)
```

**功能**:
- 解析日期范围
- 查询AI报警数据
- 筛选人员违规相关报警
- 返回统计数量

#### 3.2 判断违规报警类型

```java
/**
 * 判断是否为人员违规相关的报警
 * 
 * @param alarm AI报警数据
 * @return 是否为人员违规报警
 */
private boolean isPersonnelViolationAlarm(ApiAiAlarm alarm)
```

**违规关键词**:
- 人员违规、PERSONNEL_VIOLATION、STAFF_VIOLATION
- 未佩戴安全帽、NO_HELMET、HELMET_VIOLATION
- 未穿安全服、NO_SAFETY_CLOTHING、CLOTHING_VIOLATION
- 区域入侵、AREA_INTRUSION、UNAUTHORIZED_ACCESS
- 人员超时、PERSONNEL_OVERTIME、STAFF_OVERTIME
- 违规操作、ILLEGAL_OPERATION、VIOLATION_OPERATION
- 安全违规、SAFETY_VIOLATION、SAFETY_BREACH
- 人员安全、PERSONNEL_SAFETY、STAFF_SAFETY
- 行为违规、BEHAVIOR_VIOLATION、CONDUCT_VIOLATION
- 作业违规、WORK_VIOLATION、OPERATION_VIOLATION

#### 3.3 获取违规详细列表

```java
/**
 * 获取人员违规行为详细列表（用于详细查询）
 * 
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @return 违规行为详细列表
 */
public List<ApiAiAlarm> getViolationDetailList(String startDate, String endDate)
```

### 4. 新增接口

**文件**: `PersonnelSafetyStatController.java`

**新增接口**: `GET /violation-details`

```java
/**
 * 获取人员违规行为详细列表（基于AI报警数据）
 * @param viewType 视图类型：daily(日), weekly(周), monthly(月)
 * @param startDate 开始日期 (格式: yyyy-MM-dd)
 * @param endDate 结束日期 (格式: yyyy-MM-dd)
 */
@Anonymous
@GetMapping("/violation-details")
public R<List<ApiAiAlarm>> getViolationDetails(...)
```

## 数据流程

### 1. 违规行为统计流程

```
用户请求统计数据
    ↓
PersonnelSafetyStatController
    ↓
PersonnelSafetyStatServiceImpl
    ↓
getViolationCountFromAiAlarm()
    ↓
ApiAiAlarmService.getByTimeRange()
    ↓
筛选人员违规相关报警
    ↓
返回统计数量
```

### 2. 违规判断逻辑

```
AI报警数据
    ↓
检查报警类型 (alarm_type)
    ↓
检查报警标题 (alarm_title)
    ↓
检查报警描述 (alarm_description)
    ↓
匹配违规关键词
    ↓
返回是否为违规报警
```

## 接口使用示例

### 1. 获取概览统计（包含违规数据）

```bash
# 获取今日人员安全概览
curl -X GET "http://localhost:8080/lxbi/stat/personnel-safety/overview?viewType=daily"

# 响应示例
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalPersonnel": 150,
    "timeoutPersonnel": 5,
    "rescueCount": 1,
    "violationCount": 8,  // 来自AI报警系统的真实数据
    "lastUpdateTime": "2025-08-28T10:30:00"
  }
}
```

### 2. 获取违规行为详细列表

```bash
# 获取今日违规行为详细列表
curl -X GET "http://localhost:8080/lxbi/stat/personnel-safety/violation-details?viewType=daily"

# 响应示例
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "externalId": "AI_001",
      "alarmType": "PERSONNEL_VIOLATION",
      "alarmLevel": "HIGH",
      "alarmTitle": "未佩戴安全帽",
      "alarmDescription": "检测到人员在作业区域未佩戴安全帽",
      "alarmTime": "2025-08-28T09:15:00",
      "deviceName": "摄像头001",
      "locationName": "1号工作面",
      "imageUrl": "http://example.com/alarm_image_001.jpg"
    },
    {
      "id": 2,
      "externalId": "AI_002", 
      "alarmType": "AREA_INTRUSION",
      "alarmLevel": "MEDIUM",
      "alarmTitle": "区域入侵",
      "alarmDescription": "检测到人员进入禁止区域",
      "alarmTime": "2025-08-28T10:20:00",
      "deviceName": "摄像头002",
      "locationName": "危险区域A"
    }
  ]
}
```

### 3. 获取安全摘要（包含违规数据）

```bash
# 获取本周安全摘要
curl -X GET "http://localhost:8080/lxbi/stat/personnel-safety/safetySummary?viewType=weekly"

# 响应示例
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalPersonnelCount": 1050,
    "detectedViolations": 25,  // 来自AI报警系统的真实数据
    "rescueEvents": 3,
    "safetyScore": 92.5,
    "summaryText": "本周共有1050人次下井作业，检测到25起违规行为，发生3起求救事件，整体安全状况良好。"
  }
}
```

## 数据准确性

### 1. 关键词匹配策略

**多语言支持**:
- 中文关键词：人员违规、未佩戴安全帽、区域入侵等
- 英文关键词：PERSONNEL_VIOLATION、NO_HELMET、AREA_INTRUSION等

**匹配范围**:
- 报警类型 (alarm_type)
- 报警标题 (alarm_title)  
- 报警描述 (alarm_description)

**匹配方式**:
- 大小写不敏感
- 包含匹配（contains）
- 支持多个关键词

### 2. 数据过滤

**时间过滤**:
- 根据alarm_time字段过滤
- 支持精确到秒的时间范围

**状态过滤**:
- 只统计未删除的数据 (is_deleted = false)
- 只统计同步成功的数据 (sync_status = 'SUCCESS')

### 3. 异常处理

**网络异常**:
- 捕获异常并记录日志
- 返回默认值0，避免系统崩溃

**数据异常**:
- 空值检查
- 格式验证
- 边界条件处理

## 性能优化

### 1. 数据库查询优化

**索引使用**:
- alarm_time索引：快速时间范围查询
- alarm_type索引：快速类型过滤
- 复合索引：(alarm_time, alarm_type)

**查询优化**:
- 使用时间范围查询减少数据量
- 在应用层进行关键词过滤
- 避免全表扫描

### 2. 缓存策略

**建议实现**:
```java
@Cacheable(value = "violationCount", key = "#startDate + '_' + #endDate")
public Long getViolationCountFromAiAlarm(String startDate, String endDate) {
    // 实现逻辑
}
```

**缓存配置**:
- 缓存时间：5分钟
- 缓存键：日期范围
- 自动失效机制

## 监控和日志

### 1. 关键日志

```java
log.info("从AI报警系统获取违规行为数量: {} (时间范围: {} - {})", violationCount, startDate, endDate);
log.error("从AI报警系统获取违规行为数量失败", e);
```

### 2. 监控指标

**业务指标**:
- 违规行为数量趋势
- 违规类型分布
- 处理时效统计

**技术指标**:
- 查询响应时间
- 数据获取成功率
- 异常发生频率

## 扩展建议

### 1. 违规类型细分

```java
// 按违规类型分类统计
Map<String, Long> violationTypeCount = alarms.stream()
    .filter(alarm -> isPersonnelViolationAlarm(alarm))
    .collect(Collectors.groupingBy(
        alarm -> getViolationType(alarm),
        Collectors.counting()
    ));
```

### 2. 违规严重程度

```java
// 按报警等级统计
Map<String, Long> violationLevelCount = alarms.stream()
    .filter(alarm -> isPersonnelViolationAlarm(alarm))
    .collect(Collectors.groupingBy(
        ApiAiAlarm::getAlarmLevel,
        Collectors.counting()
    ));
```

### 3. 实时报警推送

```java
// WebSocket实时推送违规报警
@EventListener
public void handleViolationAlarm(ApiAiAlarm alarm) {
    if (isPersonnelViolationAlarm(alarm)) {
        webSocketService.sendViolationAlert(alarm);
    }
}
```

## 总结

通过这次修改，人员安全统计系统的违规行为数据已经从模拟数据升级为基于AI报警系统的真实数据，具备以下特点：

1. **数据真实性**：直接从AI报警系统获取实时数据
2. **智能识别**：通过关键词匹配准确识别人员违规报警
3. **完整性**：提供统计数量和详细列表两种数据
4. **可扩展性**：支持多种违规类型和严重程度分类
5. **稳定性**：完善的异常处理和容错机制

这为人员安全管理提供了更加准确和及时的数据支持。
