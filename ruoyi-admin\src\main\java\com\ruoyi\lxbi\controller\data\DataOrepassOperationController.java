package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataOrepassOperation;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationVo;
import com.ruoyi.lxbi.domain.request.DataOrepassOperationBatchDto;
import com.ruoyi.lxbi.service.IDataOrepassOperationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 溜井运行数据Controller
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/data/orepass")
public class DataOrepassOperationController extends BaseController {
    @Autowired
    private IDataOrepassOperationService dataOrepassOperationService;

    /**
     * 查询溜井运行数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:orepass:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataOrepassOperation dataOrepassOperation) {
        startPage();
        List<DataOrepassOperationVo> list = dataOrepassOperationService.selectDataOrepassOperationList(dataOrepassOperation);
        return getDataTable(list);
    }

    /**
     * 导出溜井运行数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:orepass:list')")
    @Log(title = "溜井运行数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataOrepassOperation dataOrepassOperation) {
        List<DataOrepassOperationVo> list = dataOrepassOperationService.selectDataOrepassOperationList(dataOrepassOperation);
        ExcelUtil<DataOrepassOperationVo> util = new ExcelUtil<DataOrepassOperationVo>(DataOrepassOperationVo.class);
        util.exportExcel(response, list, "溜井运行数据数据");
    }

    /**
     * 获取溜井运行数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:orepass:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataOrepassOperationService.selectDataOrepassOperationById(id));
    }

    /**
     * 新增溜井运行数据
     */
    @PreAuthorize("@ss.hasPermi('data:orepass:add')")
    @Log(title = "溜井运行数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataOrepassOperation dataOrepassOperation)
    {
        return toAjax(dataOrepassOperationService.insertDataOrepassOperation(dataOrepassOperation));
    }

    /**
     * 修改溜井运行数据
     */
    @PreAuthorize("@ss.hasPermi('data:orepass:edit')")
    @Log(title = "溜井运行数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataOrepassOperation dataOrepassOperation)
    {
        return toAjax(dataOrepassOperationService.updateDataOrepassOperation(dataOrepassOperation));
    }

    /**
     * 删除溜井运行数据
     */
    @PreAuthorize("@ss.hasPermi('data:orepass:remove')")
    @Log(title = "溜井运行数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataOrepassOperationService.deleteDataOrepassOperationByIds(ids));
    }

    /**
     * 批量保存溜井运行数据（增删改查）
     * 传入批量列表，验证是否同一个日期和项目部的数据，然后查询这个日期和项目部的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('data:orepass:edit')")
    @Log(title = "溜井运行数据批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<DataOrepassOperationBatchDto> batchDataList)
    {
        try {
            int result = dataOrepassOperationService.batchSaveDataOrepassOperation(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
