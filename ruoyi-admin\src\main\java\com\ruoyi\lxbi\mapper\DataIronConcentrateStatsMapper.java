package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.lxbi.domain.response.DataIronConcentrateTotalWithPlanStats;

/**
 * 铁精粉生产数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public interface DataIronConcentrateStatsMapper 
{
    // ========== 总体统计查询方法（含计划量） ==========
    
    /**
     * 查询总体统计数据列表（含计划量） (日)
     */
    public List<DataIronConcentrateTotalWithPlanStats> selectDailyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (周)
     */
    public List<DataIronConcentrateTotalWithPlanStats> selectWeeklyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (月)
     */
    public List<DataIronConcentrateTotalWithPlanStats> selectMonthlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询总体统计数据列表（含计划量） (年)
     */
    public List<DataIronConcentrateTotalWithPlanStats> selectYearlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
