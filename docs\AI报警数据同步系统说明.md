# AI报警数据同步系统说明

## 概述

本系统实现了AI报警数据的自动同步和管理功能，包括定时同步、手动同步、数据存储、查询统计等完整功能。

## 系统架构

### 1. 数据库设计

**表名**: `api_ai_alarm`

**主要字段**:
- 基本信息：ID、外部ID、报警类型、等级、状态等
- 时间信息：报警时间、创建时间、更新时间等
- 位置信息：位置名称、坐标信息等
- 设备信息：设备ID、名称、类型等
- 处理信息：处理人、处理时间、状态等
- 扩展字段：原始数据(JSON)、扩展数据等
- 同步信息：同步时间、状态、来源等

### 2. 核心组件

```
AiAlarmSyncTask (定时任务)
    ↓
IApiAiAlarmService (业务服务)
    ↓
AiAlarmExternalService (外部API调用)
    ↓
ApiAiAlarmMapper (数据访问)
    ↓
PostgreSQL Database
```

## 功能特性

### 1. 定时同步

**执行时间**: 每天凌晨2点
**同步范围**: 前一天的全部AI报警数据
**实现方式**: Spring @Scheduled注解

```java
@Scheduled(cron = "0 0 2 * * ?")
public void syncPreviousDayData()
```

### 2. 手动同步

**接口地址**: `POST /lxbi/ai-alarm/sync`
**参数**:
- `startDate`: 开始日期 (可选)
- `endDate`: 结束日期 (可选)

**使用示例**:
```bash
# 同步指定日期范围
curl -X POST "http://localhost:8080/lxbi/ai-alarm/sync?startDate=2025-08-25&endDate=2025-08-25"

# 同步前一天数据（默认）
curl -X POST "http://localhost:8080/lxbi/ai-alarm/sync"
```

### 3. 数据查询

**查询接口**: `GET /lxbi/ai-alarm/list`
**支持条件**:
- 按报警类型查询
- 按报警等级查询
- 按设备ID查询
- 按时间范围查询

### 4. 数据统计

**统计接口**: `GET /lxbi/ai-alarm/sync-statistics`
**统计内容**:
- 总数据量
- 成功/失败数量
- 自动/手动同步数量
- 最新同步时间

### 5. 数据清理

**清理接口**: `DELETE /lxbi/ai-alarm/clean-expired`
**清理策略**: 
- 定时清理：每周日凌晨3点
- 默认保留90天数据
- 支持自定义保留天数

## 接口文档

### 1. 手动同步接口

```http
POST /lxbi/ai-alarm/sync
Content-Type: application/json

参数:
- startDate: 开始日期 (yyyy-MM-dd, 可选)
- endDate: 结束日期 (yyyy-MM-dd, 可选)

响应:
{
  "code": 200,
  "msg": "同步成功",
  "data": {
    "success": true,
    "totalCount": 150,
    "successCount": 148,
    "insertCount": 120,
    "updateCount": 28,
    "failureCount": 2,
    "syncTime": "2025-08-28T10:30:00"
  }
}
```

### 2. 数据查询接口

```http
GET /lxbi/ai-alarm/list?alarmType=FIRE&startTime=2025-08-25&endTime=2025-08-28

响应:
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "externalId": "AI_001",
      "alarmType": "FIRE",
      "alarmLevel": "HIGH",
      "alarmTitle": "火灾报警",
      "alarmTime": "2025-08-25T14:30:00",
      "deviceName": "烟感器001",
      "locationName": "1号厂房"
    }
  ],
  "total": 1
}
```

### 3. 同步统计接口

```http
GET /lxbi/ai-alarm/sync-statistics

响应:
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "totalCount": 1500,
    "successCount": 1485,
    "failedCount": 15,
    "autoSyncCount": 1200,
    "manualSyncCount": 300,
    "latestSyncTime": "2025-08-28T02:00:00"
  }
}
```

### 4. 数据清理接口

```http
DELETE /lxbi/ai-alarm/clean-expired?retentionDays=90

响应:
{
  "code": 200,
  "msg": "清理成功",
  "data": {
    "success": true,
    "deletedCount": 500,
    "cutoffTime": "2025-05-30T00:00:00",
    "message": "清理完成，删除 500 条过期数据"
  }
}
```

## 定时任务配置

### 1. 数据同步任务

```java
// 每天凌晨2点执行
@Scheduled(cron = "0 0 2 * * ?")
public void syncPreviousDayData()
```

### 2. 数据清理任务

```java
// 每周日凌晨3点执行
@Scheduled(cron = "0 0 3 * * SUN")
public void cleanExpiredData()
```

### 3. 失败重试任务

```java
// 每小时30分执行
@Scheduled(cron = "0 30 * * * ?")
public void resyncFailedData()
```

## 数据流程

### 1. 同步流程

```
1. 登录AI报警系统获取Token
2. 调用API获取指定日期的报警数据
3. 数据格式转换和验证
4. 检查数据是否已存在（根据external_id）
5. 新数据插入，已存在数据更新
6. 记录同步结果和统计信息
```

### 2. 数据转换

```java
// API原始数据 → 实体对象
Map<String, Object> apiData → ApiAiAlarm entity

// 关键字段映射
- id → externalId
- alarm_type → alarmType
- alarm_time → alarmTime
- device_id → deviceId
- 完整数据 → rawData (JSON)
```

### 3. 错误处理

```java
// 同步失败处理
- 记录失败原因
- 设置sync_status = 'FAILED'
- 定时重试机制
- 错误日志记录
```

## 监控和运维

### 1. 日志监控

```bash
# 查看同步日志
tail -f logs/ruoyi-admin.log | grep "AI报警数据"

# 关键日志内容
- 同步开始/完成时间
- 同步数据量统计
- 错误信息和异常堆栈
```

### 2. 数据库监控

```sql
-- 查看同步状态统计
SELECT sync_status, sync_source, COUNT(*) 
FROM api_ai_alarm 
GROUP BY sync_status, sync_source;

-- 查看最近同步情况
SELECT DATE(sync_time) as sync_date, 
       COUNT(*) as total_count,
       SUM(CASE WHEN sync_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count
FROM api_ai_alarm 
WHERE sync_time >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(sync_time)
ORDER BY sync_date DESC;
```

### 3. 性能优化

**索引优化**:
- `external_id` 唯一索引
- `alarm_time` 时间索引
- `sync_time` 同步时间索引
- 复合索引：`(alarm_time, alarm_type)`

**批量处理**:
- 批量插入/更新
- 分页查询大数据量
- 异步处理非关键操作

## 部署配置

### 1. 数据库配置

```sql
-- 创建数据库表
\i sql/postgres/create_api_ai_alarm_table.sql
```

### 2. 应用配置

```yaml
# application.yml
external:
  api:
    ai-alarm:
      base-url: http://10.10.30.50:9000
      login-path: /login/
      alarm-path: /alarm
      account: api
      password: 123456
      enabled: true

# 定时任务配置
spring:
  task:
    scheduling:
      pool:
        size: 5
```

### 3. 权限配置

```sql
-- 添加菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('AI报警数据', 2000, 1, 'ai-alarm', 'lxbi/ai-alarm/index', 'C', '0', '0', 'lxbi:ai-alarm:list', 'alarm', 'admin', NOW(), 'admin', NOW(), 'AI报警数据菜单');
```

## 故障排查

### 1. 常见问题

**同步失败**:
- 检查网络连接
- 验证API账号密码
- 查看错误日志

**数据重复**:
- 检查external_id唯一性
- 验证更新逻辑

**性能问题**:
- 检查数据库索引
- 优化查询条件
- 调整批量大小

### 2. 调试方法

```bash
# 手动触发同步测试
curl -X POST "http://localhost:8080/lxbi/ai-alarm/sync?startDate=2025-08-28&endDate=2025-08-28"

# 查看同步统计
curl -X GET "http://localhost:8080/lxbi/ai-alarm/sync-statistics"

# 测试连接
curl -X GET "http://localhost:8080/lxbi/ai-alarm/test-connection"
```

## 总结

AI报警数据同步系统提供了完整的数据同步、存储、查询和管理功能，具备以下特点：

1. **自动化**: 定时同步，无需人工干预
2. **可靠性**: 失败重试，数据完整性保证
3. **灵活性**: 支持手动同步和多种查询方式
4. **可维护性**: 完善的日志和监控机制
5. **扩展性**: 支持原始数据存储，便于后续扩展

系统已经过充分测试，可以稳定运行在生产环境中。
