package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DataFillingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataFillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataFillingStopeStats;
import com.ruoyi.lxbi.service.IDataFillingStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 充填数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/data/stats/filling")
public class DataFillingStatsController {
    @Autowired
    private IDataFillingStatsService dataFillingStatsService;

    /**
     * 查询总体充填统计数据（含计划量）
     * 对应图表一：总体充填统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:filling:01a')")
    @GetMapping("/01a")
    public R<List<DataFillingTotalWithPlanStats>> totalFillingWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                        @RequestParam(value = "startDate", required = false) String startDate,
                                                                        @RequestParam(value = "endDate", required = false) String endDate) {
        DataFillingStatsRequest request = new DataFillingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataFillingTotalWithPlanStats> stats = dataFillingStatsService.selectTotalWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

    /**
     * 查询按采场分组的充填统计数据（含计划量）
     * 对应图表二：按采场分组的充填统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:filling:01b')")
    @GetMapping("/01b")
    public R<List<DataFillingStopeStats>> stopeFillingWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                               @RequestParam(value = "startDate", required = false) String startDate,
                                                               @RequestParam(value = "endDate", required = false) String endDate) {
        DataFillingStatsRequest request = new DataFillingStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataFillingStopeStats> stats = dataFillingStatsService.selectStopeStatsList(request, viewType);
        return R.ok(stats);
    }

}
