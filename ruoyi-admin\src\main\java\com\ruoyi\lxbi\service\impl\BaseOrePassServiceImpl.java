package com.ruoyi.lxbi.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.lxbi.mapper.BaseOrePassMapper;
import com.ruoyi.lxbi.domain.BaseOrePass;
import com.ruoyi.lxbi.service.IBaseOrePassService;

/**
 * 溜井配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Service
public class BaseOrePassServiceImpl implements IBaseOrePassService 
{
    @Autowired
    private BaseOrePassMapper baseOrePassMapper;

    /**
     * 查询溜井配置
     * 
     * @param orePassId 溜井配置主键
     * @return 溜井配置
     */
    @Override
    public BaseOrePass selectBaseOrePassByOrePassId(Long orePassId)
    {
        return baseOrePassMapper.selectBaseOrePassByOrePassId(orePassId);
    }

    /**
     * 查询溜井配置列表
     * 
     * @param baseOrePass 溜井配置
     * @return 溜井配置
     */
    @Override
    public List<BaseOrePass> selectBaseOrePassList(BaseOrePass baseOrePass)
    {
        return baseOrePassMapper.selectBaseOrePassList(baseOrePass);
    }

    /**
     * 新增溜井配置
     * 
     * @param baseOrePass 溜井配置
     * @return 结果
     */
    @Override
    public int insertBaseOrePass(BaseOrePass baseOrePass)
    {
        baseOrePass.setCreateTime(DateUtils.getNowDate());
        return baseOrePassMapper.insertBaseOrePass(baseOrePass);
    }

    /**
     * 修改溜井配置
     * 
     * @param baseOrePass 溜井配置
     * @return 结果
     */
    @Override
    public int updateBaseOrePass(BaseOrePass baseOrePass)
    {
        baseOrePass.setUpdateTime(DateUtils.getNowDate());
        return baseOrePassMapper.updateBaseOrePass(baseOrePass);
    }

    /**
     * 批量删除溜井配置
     * 
     * @param orePassIds 需要删除的溜井配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseOrePassByOrePassIds(Long[] orePassIds)
    {
        if (orePassIds == null || orePassIds.length == 0) {
            throw new ServiceException("删除溜井配置失败，未选择数据");
        }
        if (baseOrePassMapper.getBaseOrePassByOrePassIds(orePassIds) > 0) {
            throw new ServiceException("存在使用中的溜井配置，无法删除");
        }
        return baseOrePassMapper.deleteBaseOrePassByOrePassIds(orePassIds);
    }

    /**
     * 删除溜井配置信息
     * 
     * @param orePassId 溜井配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseOrePassByOrePassId(Long orePassId)
    {
        return baseOrePassMapper.deleteBaseOrePassByOrePassId(orePassId);
    }
}
