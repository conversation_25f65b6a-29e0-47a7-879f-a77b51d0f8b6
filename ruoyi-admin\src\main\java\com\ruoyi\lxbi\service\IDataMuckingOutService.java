package com.ruoyi.lxbi.service;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.domain.DataMuckingOut;
import com.ruoyi.lxbi.domain.response.DataMuckingOutVo;
import com.ruoyi.lxbi.domain.request.DataMuckingOutBatchDto;

/**
 * 出矿数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IDataMuckingOutService 
{
    /**
     * 查询出矿数据
     * 
     * @param id 出矿数据主键
     * @return 出矿数据
     */
    public DataMuckingOut selectDataMuckingOutById(Long id);

    /**
     * 查询出矿数据列表
     *
     * @param dataMuckingOut 出矿数据
     * @return 出矿数据集合
     */
    public List<DataMuckingOutVo> selectDataMuckingOutList(DataMuckingOut dataMuckingOut);

    /**
     * 新增出矿数据
     * 
     * @param dataMuckingOut 出矿数据
     * @return 结果
     */
    public int insertDataMuckingOut(DataMuckingOut dataMuckingOut);

    /**
     * 修改出矿数据
     * 
     * @param dataMuckingOut 出矿数据
     * @return 结果
     */
    public int updateDataMuckingOut(DataMuckingOut dataMuckingOut);

    /**
     * 批量删除出矿数据
     * 
     * @param ids 需要删除的出矿数据主键集合
     * @return 结果
     */
    public int deleteDataMuckingOutByIds(Long[] ids);

    /**
     * 删除出矿数据信息
     *
     * @param id 出矿数据主键
     * @return 结果
     */
    public int deleteDataMuckingOutById(Long id);

    /**
     * 根据作业日期和项目部门查询出矿数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 出矿数据集合
     */
    public List<DataMuckingOutVo> selectDataMuckingOutByOperationDateAndProject(Date operationDate, Long projectDepartmentId);

    /**
     * 批量保存出矿数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    public int batchSaveDataMuckingOut(List<DataMuckingOutBatchDto> batchDataList);
}
