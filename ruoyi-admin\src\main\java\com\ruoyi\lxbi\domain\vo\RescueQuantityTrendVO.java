package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 来救数量趋势VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RescueQuantityTrendVO {
    
    /**
     * 日期（YYYY-MM-DD）
     */
    private String date;
    
    /**
     * 短日期（MM-DD）
     */
    private String shortDate;
    
    /**
     * 周标签（YYYY年第XX周）
     */
    private String weekLabel;
    
    /**
     * 月标签（YYYY年MM月）
     */
    private String monthLabel;
    
    /**
     * 救援数量
     */
    private Long rescueCount;
    
    /**
     * 培训数量
     */
    private Long trainingCount;
    
    /**
     * 救援数量百分比
     */
    private BigDecimal rescuePercentage;
    
    /**
     * 培训数量百分比
     */
    private BigDecimal trainingPercentage;
}
