package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaMonitoringPointBasicInfo;

/**
 * 测点基本信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface IKafkaMonitoringPointBasicInfoService 
{
    /**
     * 查询测点基本信息
     * 
     * @param id 测点基本信息主键
     * @return 测点基本信息
     */
    public KafkaMonitoringPointBasicInfo selectKafkaMonitoringPointBasicInfoById(Long id);

    /**
     * 查询测点基本信息列表
     * 
     * @param kafkaMonitoringPointBasicInfo 测点基本信息
     * @return 测点基本信息集合
     */
    public List<KafkaMonitoringPointBasicInfo> selectKafkaMonitoringPointBasicInfoList(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo);

    /**
     * 新增测点基本信息
     * 
     * @param kafkaMonitoringPointBasicInfo 测点基本信息
     * @return 结果
     */
    public int insertKafkaMonitoringPointBasicInfo(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo);

    /**
     * 修改测点基本信息
     * 
     * @param kafkaMonitoringPointBasicInfo 测点基本信息
     * @return 结果
     */
    public int updateKafkaMonitoringPointBasicInfo(KafkaMonitoringPointBasicInfo kafkaMonitoringPointBasicInfo);

    /**
     * 批量删除测点基本信息
     * 
     * @param ids 需要删除的测点基本信息主键集合
     * @return 结果
     */
    public int deleteKafkaMonitoringPointBasicInfoByIds(Long[] ids);

    /**
     * 删除测点基本信息信息
     *
     * @param id 测点基本信息主键
     * @return 结果
     */
    public int deleteKafkaMonitoringPointBasicInfoById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaMonitoringPointBasicInfo parseKafkaMessage(String kafkaMessage);
}
