-- 修复 kafka_people_location_info 表的唯一约束问题
-- PostgreSQL 修复脚本

-- 1. 检查当前表结构和约束
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'kafka_people_location_info'::regclass;

-- 2. 检查当前索引
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'kafka_people_location_info';

-- 3. 删除可能存在的旧索引（如果存在）
DROP INDEX IF EXISTS idx_kpli_area_mine_unique;

-- 4. 检查是否有重复数据
SELECT 
    area_code, 
    mine_code, 
    COUNT(*) as count
FROM kafka_people_location_info 
WHERE is_deleted = 0
GROUP BY area_code, mine_code 
HAVING COUNT(*) > 1;

-- 5. 如果有重复数据，保留最新的记录，删除旧的
WITH ranked_data AS (
    SELECT 
        id,
        area_code,
        mine_code,
        ROW_NUMBER() OVER (
            PARTITION BY area_code, mine_code 
            ORDER BY data_upload_time DESC, id DESC
        ) as rn
    FROM kafka_people_location_info 
    WHERE is_deleted = 0
)
UPDATE kafka_people_location_info 
SET is_deleted = 1, 
    update_time = CURRENT_TIMESTAMP,
    update_by = 'system_cleanup'
WHERE id IN (
    SELECT id FROM ranked_data WHERE rn > 1
);

-- 6. 创建正确的唯一约束
-- 方法1: 使用唯一索引（推荐）
CREATE UNIQUE INDEX idx_kpli_area_mine_unique 
ON kafka_people_location_info(area_code, mine_code) 
WHERE is_deleted = 0;

-- 方法2: 如果上面的索引创建失败，使用表级约束
-- 注意：这种方法需要先删除表中的重复数据
-- ALTER TABLE kafka_people_location_info 
-- ADD CONSTRAINT uk_area_mine_code UNIQUE (area_code, mine_code);

-- 7. 验证约束是否创建成功
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'kafka_people_location_info'
AND indexname = 'idx_kpli_area_mine_unique';

-- 8. 测试UPSERT操作
-- 插入测试数据
INSERT INTO kafka_people_location_info (
    mine_code, mine_name, data_upload_time, area_type, area_code,
    area_approved_personnel, area_name, status, is_deleted,
    create_by, create_time, update_by, update_time
) VALUES (
    'TEST001', '测试煤矿', CURRENT_TIMESTAMP, '测试区', 'TESTAREA001',
    10, '测试区域', 1, 0,
    'test_user', CURRENT_TIMESTAMP, 'test_user', CURRENT_TIMESTAMP
)
ON CONFLICT (area_code, mine_code) 
DO UPDATE SET
    mine_name = EXCLUDED.mine_name,
    data_upload_time = EXCLUDED.data_upload_time,
    area_type = EXCLUDED.area_type,
    area_approved_personnel = EXCLUDED.area_approved_personnel,
    area_name = EXCLUDED.area_name,
    status = EXCLUDED.status,
    is_deleted = EXCLUDED.is_deleted,
    update_by = EXCLUDED.update_by,
    update_time = EXCLUDED.update_time;

-- 9. 验证测试数据
SELECT * FROM kafka_people_location_info 
WHERE area_code = 'TESTAREA001' AND mine_code = 'TEST001';

-- 10. 清理测试数据
DELETE FROM kafka_people_location_info 
WHERE area_code = 'TESTAREA001' AND mine_code = 'TEST001';

-- 11. 最终验证
SELECT 'kafka_people_location_info 唯一约束修复完成' as status;
