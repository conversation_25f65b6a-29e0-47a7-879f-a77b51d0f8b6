package com.ruoyi.lxbi.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.lxbi.domain.DataMineHoisting;
import com.ruoyi.lxbi.domain.request.DataMineHoistingBatchDto;
import com.ruoyi.lxbi.domain.response.DataMineHoistingVo;
import com.ruoyi.lxbi.mapper.DataMineHoistingMapper;
import com.ruoyi.lxbi.service.IDataMineHoistingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 矿井提升数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DataMineHoistingServiceImpl implements IDataMineHoistingService {
    @Autowired
    private DataMineHoistingMapper dataMineHoistingMapper;

    /**
     * 查询矿井提升数据
     *
     * @param id 矿井提升数据主键
     * @return 矿井提升数据
     */
    @Override
    public DataMineHoisting selectDataMineHoistingById(Long id) {
        return dataMineHoistingMapper.selectDataMineHoistingById(id);
    }

    /**
     * 查询矿井提升数据列表
     *
     * @param dataMineHoisting 矿井提升数据
     * @return 矿井提升数据
     */
    @Override
    public List<DataMineHoistingVo> selectDataMineHoistingList(DataMineHoisting dataMineHoisting) {
        return dataMineHoistingMapper.selectDataMineHoistingList(dataMineHoisting);
    }

    /**
     * 新增矿井提升数据
     *
     * @param dataMineHoisting 矿井提升数据
     * @return 结果
     */
    @Override
    public int insertDataMineHoisting(DataMineHoisting dataMineHoisting) {
        dataMineHoisting.setCreateTime(DateUtils.getNowDate());
        return dataMineHoistingMapper.insertDataMineHoisting(dataMineHoisting);
    }

    /**
     * 修改矿井提升数据
     *
     * @param dataMineHoisting 矿井提升数据
     * @return 结果
     */
    @Override
    public int updateDataMineHoisting(DataMineHoisting dataMineHoisting) {
        dataMineHoisting.setUpdateTime(DateUtils.getNowDate());
        return dataMineHoistingMapper.updateDataMineHoisting(dataMineHoisting);
    }

    /**
     * 批量删除矿井提升数据
     *
     * @param ids 需要删除的矿井提升数据主键
     * @return 结果
     */
    @Override
    public int deleteDataMineHoistingByIds(Long[] ids) {
        return dataMineHoistingMapper.deleteDataMineHoistingByIds(ids);
    }

    /**
     * 删除矿井提升数据信息
     *
     * @param id 矿井提升数据主键
     * @return 结果
     */
    @Override
    public int deleteDataMineHoistingById(Long id) {
        return dataMineHoistingMapper.deleteDataMineHoistingById(id);
    }

    /**
     * 根据作业日期查询矿井提升数据列表
     *
     * @param operationDate 作业日期
     * @return 矿井提升数据集合
     */
    @Override
    public List<DataMineHoistingVo> selectDataMineHoistingByOperationDate(Date operationDate) {
        return dataMineHoistingMapper.selectDataMineHoistingByOperationDate(operationDate);
    }

    /**
     * 批量保存矿井提升数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDataMineHoisting(List<DataMineHoistingBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个日期的数据
        Date operationDate = batchDataList.getFirst().getOperationDate();
        if (operationDate == null) {
            throw new ServiceException("作业日期不能为空");
        }

        boolean allSameDate = batchDataList.stream()
                .allMatch(data -> operationDate.equals(data.getOperationDate()));
        if (!allSameDate) {
            throw new ServiceException("批量数据必须是同一个作业日期");
        }

        // 查询该日期的所有现有数据
        List<DataMineHoistingVo> existingDataList = selectDataMineHoistingByOperationDate(operationDate);
        Map<Long, DataMineHoistingVo> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(
                        DataMineHoistingVo::getId,
                        data -> data
                ));

        // 分类处理数据
        List<DataMineHoisting> toInsert = new ArrayList<>();
        List<DataMineHoisting> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>();

        // 处理传入的数据
        for (DataMineHoistingBatchDto batchData : batchDataList) {
            if (Boolean.TRUE.equals(batchData.getIsNew())) {
                // 新增数据
                DataMineHoisting newData = new DataMineHoisting();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if (batchData.getId() != null && existingDataMap.containsKey(batchData.getId())) {
                // 更新现有数据（根据ID判断）
                DataMineHoisting updateData = new DataMineHoisting();
                copyProperties(batchData, updateData);
                updateData.setId(batchData.getId());
                updateData.setUpdateBy(SecurityUtils.getUsername());
                updateData.setUpdateTime(DateUtils.getNowDate());
                toUpdate.add(updateData);

                // 从现有数据中移除，剩下的就是要删除的
                existingDataMap.remove(batchData.getId());
            }
        }

        // 剩余的现有数据需要删除
        toDelete.addAll(existingDataMap.keySet());

        int result = 0;

        // 执行删除
        if (!toDelete.isEmpty()) {
            result += dataMineHoistingMapper.deleteDataMineHoistingByIds(toDelete.toArray(new Long[0]));
        }

        // 执行新增
        if (!toInsert.isEmpty()) {
            result += dataMineHoistingMapper.batchInsertDataMineHoisting(toInsert);
        }

        // 执行更新
        for (DataMineHoisting updateData : toUpdate) {
            result += dataMineHoistingMapper.updateDataMineHoisting(updateData);
        }

        return result;
    }

    /**
     * 复制属性
     */
    private void copyProperties(DataMineHoistingBatchDto source, DataMineHoisting target) {
        target.setWorkingPeriodId(source.getWorkingPeriodId());
        target.setOperationDate(source.getOperationDate());
        target.setOperationTime(source.getOperationTime());
        target.setFaultTime(source.getFaultTime());
        target.setBuckets(source.getBuckets());
        target.setWeight(source.getWeight());
        target.setFaultReason(source.getFaultReason());
        target.setFaultStartTime(source.getFaultStartTime());
        target.setFaultEndTime(source.getFaultEndTime());
    }
}
