package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备作业率统计对象
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
@Data
public class DataEquipmentUtilizationStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 设备类型ID
     */
    private Long equipmentType;

    /**
     * 设备类型名称
     */
    @Excel(name = "设备类型")
    private String equipmentTypeName;

    /**
     * 统计开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 统计结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 实际工作时间（分钟）
     */
    @Excel(name = "实际工作时间(分钟)")
    private Integer actualWorkTime;

    /**
     * 总时间（分钟）
     */
    @Excel(name = "总时间(分钟)")
    private Integer totalTime;

    /**
     * 作业率（百分比）
     */
    @Excel(name = "作业率(%)")
    private BigDecimal utilizationRate;

    /**
     * 统计时间类型（week/month/year）
     */
    private String timeType;

}
