package com.ruoyi.lxbi.admin.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.admin.mapper.KafkaPeopleLocationInfoMapper;
import com.ruoyi.lxbi.admin.domain.KafkaPeopleLocationInfo;
import com.ruoyi.lxbi.admin.service.IKafkaPeopleLocationInfoService;
import lombok.extern.slf4j.Slf4j;

/**
 * 区域基本信息数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class KafkaPeopleLocationInfoServiceImpl implements IKafkaPeopleLocationInfoService 
{
    @Autowired
    private KafkaPeopleLocationInfoMapper kafkaPeopleLocationInfoMapper;

    /**
     * 查询区域基本信息数据
     * 
     * @param id 区域基本信息数据主键
     * @return 区域基本信息数据
     */
    @Override
    public KafkaPeopleLocationInfo selectKafkaPeopleLocationInfoById(Long id)
    {
        return kafkaPeopleLocationInfoMapper.selectKafkaPeopleLocationInfoById(id);
    }

    /**
     * 查询区域基本信息数据列表
     * 
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 区域基本信息数据
     */
    @Override
    public List<KafkaPeopleLocationInfo> selectKafkaPeopleLocationInfoList(KafkaPeopleLocationInfo kafkaPeopleLocationInfo)
    {
        return kafkaPeopleLocationInfoMapper.selectKafkaPeopleLocationInfoList(kafkaPeopleLocationInfo);
    }

    /**
     * 新增区域基本信息数据
     * 
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 结果
     */
    @Override
    public int insertKafkaPeopleLocationInfo(KafkaPeopleLocationInfo kafkaPeopleLocationInfo)
    {
        kafkaPeopleLocationInfo.setCreateTime(DateUtils.getNowDate());
        return kafkaPeopleLocationInfoMapper.insertKafkaPeopleLocationInfo(kafkaPeopleLocationInfo);
    }

    /**
     * 修改区域基本信息数据
     * 
     * @param kafkaPeopleLocationInfo 区域基本信息数据
     * @return 结果
     */
    @Override
    public int updateKafkaPeopleLocationInfo(KafkaPeopleLocationInfo kafkaPeopleLocationInfo)
    {
        kafkaPeopleLocationInfo.setUpdateTime(DateUtils.getNowDate());
        return kafkaPeopleLocationInfoMapper.updateKafkaPeopleLocationInfo(kafkaPeopleLocationInfo);
    }

    /**
     * 批量删除区域基本信息数据
     * 
     * @param ids 需要删除的数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeopleLocationInfoByIds(Long[] ids)
    {
        return kafkaPeopleLocationInfoMapper.deleteKafkaPeopleLocationInfoByIds(ids);
    }

    /**
     * 删除区域基本信息数据信息
     * 
     * @param id 区域基本信息数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeopleLocationInfoById(Long id)
    {
        return kafkaPeopleLocationInfoMapper.deleteKafkaPeopleLocationInfoById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage) {
        try {
            log.debug("开始处理Kafka区域基本信息消息");

            // 解析Kafka消息
            KafkaPeopleLocationInfo locationInfo = parseKafkaMessage(kafkaMessage);
            if (locationInfo == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(locationInfo.getAreaCode())) {
                log.warn("区域编码为空，跳过处理");
                return false;
            }

            if (!StringUtils.hasText(locationInfo.getMineCode())) {
                log.warn("煤矿编码为空，区域编码: {}", locationInfo.getAreaCode());
                return false;
            }

            // 设置默认值
            locationInfo.setCreateTime(DateUtils.getNowDate());
            locationInfo.setUpdateTime(DateUtils.getNowDate());
            locationInfo.setCreateBy("kafka_system");
            locationInfo.setUpdateBy("kafka_system");
            
            if (locationInfo.getStatus() == null) {
                locationInfo.setStatus(1L);
            }
            if (locationInfo.getIsDeleted() == null) {
                locationInfo.setIsDeleted(0L);
            }

            // 尝试使用UPSERT操作，如果失败则使用传统的查询-插入/更新方式
            int result = 0;
            try {
                result = kafkaPeopleLocationInfoMapper.upsertKafkaPeopleLocationInfo(locationInfo);
                log.info("使用UPSERT处理区域基本信息记录成功，区域编码: {}, 煤矿编码: {}, 区域名称: {}",
                        locationInfo.getAreaCode(), locationInfo.getMineCode(), locationInfo.getAreaName());
            } catch (Exception e) {
                log.warn("UPSERT操作失败，尝试使用传统方式处理，区域编码: {}, 煤矿编码: {}",
                        locationInfo.getAreaCode(), locationInfo.getMineCode(), e);

                // 备用方案：先查询是否存在，然后决定插入或更新
                KafkaPeopleLocationInfo existing = kafkaPeopleLocationInfoMapper
                        .selectByAreaCodeAndMineCode(locationInfo.getAreaCode(), locationInfo.getMineCode());

                if (existing != null) {
                    // 更新现有记录
                    locationInfo.setId(existing.getId());
                    locationInfo.setCreateBy(existing.getCreateBy());
                    locationInfo.setCreateTime(existing.getCreateTime());
                    result = kafkaPeopleLocationInfoMapper.updateKafkaPeopleLocationInfo(locationInfo);
                    log.info("更新区域基本信息记录成功，区域编码: {}, 煤矿编码: {}",
                            locationInfo.getAreaCode(), locationInfo.getMineCode());
                } else {
                    // 插入新记录
                    result = kafkaPeopleLocationInfoMapper.insertKafkaPeopleLocationInfo(locationInfo);
                    log.info("插入区域基本信息记录成功，区域编码: {}, 煤矿编码: {}",
                            locationInfo.getAreaCode(), locationInfo.getMineCode());
                }
            }

            return result > 0;

        } catch (Exception e) {
            log.error("处理Kafka区域基本信息消息失败: {}", kafkaMessage, e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     */
    @Override
    public KafkaPeopleLocationInfo parseKafkaMessage(String kafkaMessage) {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            log.debug("解析JSON字符串: {}", jsonStr);

            // 使用Jackson解析JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonStr);

            KafkaPeopleLocationInfo locationInfo = new KafkaPeopleLocationInfo();

            // 基本信息
            locationInfo.setMineCode(getStringValue(jsonNode, "煤矿编码"));
            locationInfo.setMineName(getStringValue(jsonNode, "矿井名称"));
            
            // 时间字段处理
            locationInfo.setDataUploadTime(getDateValue(jsonNode, "数据上传时间"));

            // 区域信息
            locationInfo.setAreaType(getStringValue(jsonNode, "区域类型"));
            locationInfo.setAreaCode(getStringValue(jsonNode, "区域编码"));
            locationInfo.setAreaName(getStringValue(jsonNode, "区域名称"));
            
            // 数值字段处理
            locationInfo.setAreaApprovedPersonnel(getLongValue(jsonNode, "区域核定人数"));

            // 默认值
            locationInfo.setStatus(1L);
            locationInfo.setIsDeleted(0L);

            return locationInfo;

        } catch (Exception e) {
            log.error("解析Kafka消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 根据区域编码查询区域信息
     */
    @Override
    public KafkaPeopleLocationInfo selectByAreaCode(String areaCode) {
        return kafkaPeopleLocationInfoMapper.selectByAreaCode(areaCode);
    }

    /**
     * 根据区域编码和煤矿编码查询区域信息
     */
    @Override
    public KafkaPeopleLocationInfo selectByAreaCodeAndMineCode(String areaCode, String mineCode) {
        return kafkaPeopleLocationInfoMapper.selectByAreaCodeAndMineCode(areaCode, mineCode);
    }

    /**
     * 查询所有有效的区域信息
     */
    @Override
    public List<KafkaPeopleLocationInfo> selectAllValidAreas() {
        return kafkaPeopleLocationInfoMapper.selectAllValidAreas();
    }

    /**
     * 根据区域类型查询区域信息
     */
    @Override
    public List<KafkaPeopleLocationInfo> selectByAreaType(String areaType) {
        return kafkaPeopleLocationInfoMapper.selectByAreaType(areaType);
    }

    /**
     * 统计区域总数
     */
    @Override
    public Long countTotalAreas() {
        return kafkaPeopleLocationInfoMapper.countTotalAreas();
    }

    /**
     * 统计指定煤矿的区域数量
     */
    @Override
    public Long countAreasByMineCode(String mineCode) {
        return kafkaPeopleLocationInfoMapper.countAreasByMineCode(mineCode);
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return (node != null && !node.isNull()) ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取长整型值
     */
    private Long getLongValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        if (node != null && !node.isNull()) {
            try {
                return node.asLong();
            } catch (Exception e) {
                log.warn("解析长整型字段 {} 失败: {}", fieldName, node.asText());
                return null;
            }
        }
        return null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private Date getDateValue(JsonNode jsonNode, String fieldName) {
        String dateStr = getStringValue(jsonNode, fieldName);
        if (!StringUtils.hasText(dateStr)) {
            return null;
        }

        try {
            // 尝试多种日期格式
            String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd'T'HH:mm:ss",
                "yyyy-MM-dd HH:mm:ss.SSS",
                "yyyy-MM-dd'T'HH:mm:ss.SSS",
                "yyyy-MM-dd"
            };

            for (String pattern : patterns) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    return sdf.parse(dateStr);
                } catch (Exception ignored) {
                    // 继续尝试下一个格式
                }
            }

            log.warn("无法解析日期字段 {}: {}", fieldName, dateStr);
            return null;
        } catch (Exception e) {
            log.warn("解析日期字段 {} 失败: {}", fieldName, dateStr, e);
            return null;
        }
    }
}
