package com.ruoyi.common.core.excel;

import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.core.domain.excel.ExcelTemplateInfo;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Excel导入处理器注册表
 * 
 * <AUTHOR>
 */
@Component
public class ExcelImportRegistry implements ApplicationContextAware, InitializingBean {
    
    private ApplicationContext applicationContext;
    
    /** 处理器映射 key -> handler */
    private final Map<String, ExcelImportHandler<?>> handlerMap = new ConcurrentHashMap<>();
    
    /** 模板信息缓存 key -> templateInfo */
    private final Map<String, ExcelTemplateInfo> templateCache = new ConcurrentHashMap<>();
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        registerHandlers();
    }
    
    /**
     * 注册所有处理器
     */
    private void registerHandlers() {
        Map<String, ExcelImportHandler> handlers = applicationContext.getBeansOfType(ExcelImportHandler.class);
        
        for (ExcelImportHandler<?> handler : handlers.values()) {
            Class<?> entityClass = handler.getEntityClass();
            ExcelImportTemplate template = entityClass.getAnnotation(ExcelImportTemplate.class);
            
            if (template != null && template.enabled()) {
                String key = template.key();
                handlerMap.put(key, handler);
                
                // 缓存模板信息
                ExcelTemplateInfo templateInfo = handler.getTemplateInfo();
                templateCache.put(key, templateInfo);
            }
        }
    }
    
    /**
     * 获取处理器
     */
    public ExcelImportHandler<?> getHandler(String key) {
        return handlerMap.get(key);
    }
    
    /**
     * 获取模板信息
     */
    public ExcelTemplateInfo getTemplateInfo(String key) {
        return templateCache.get(key);
    }
    
    /**
     * 获取所有模板信息
     */
    public Map<String, ExcelTemplateInfo> getAllTemplates() {
        return new HashMap<>(templateCache);
    }
    
    /**
     * 检查处理器是否存在
     */
    public boolean hasHandler(String key) {
        return handlerMap.containsKey(key);
    }
    
    /**
     * 刷新缓存
     */
    public void refreshCache() {
        templateCache.clear();
        for (Map.Entry<String, ExcelImportHandler<?>> entry : handlerMap.entrySet()) {
            String key = entry.getKey();
            ExcelImportHandler<?> handler = entry.getValue();
            ExcelTemplateInfo templateInfo = handler.getTemplateInfo();
            templateCache.put(key, templateInfo);
        }
    }
}
