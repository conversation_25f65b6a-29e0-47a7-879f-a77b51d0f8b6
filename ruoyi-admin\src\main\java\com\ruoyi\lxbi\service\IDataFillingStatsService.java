package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DataFillingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataFillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataFillingStopeStats;

import java.util.List;

/**
 * 充填数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IDataFillingStatsService {

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    public List<DataFillingTotalWithPlanStats> selectTotalWithPlanStatsList(DataFillingStatsRequest request, String viewType);

    /**
     * 查询采场统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 采场统计数据集合
     */
    public List<DataFillingStopeStats> selectStopeStatsList(DataFillingStatsRequest request, String viewType);
}
