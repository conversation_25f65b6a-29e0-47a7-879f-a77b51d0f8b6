package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.KafkaVehiclePosition;

/**
 * 车辆定位数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IKafkaVehiclePositionService 
{
    /**
     * 查询车辆定位数据
     * 
     * @param id 车辆定位数据主键
     * @return 车辆定位数据
     */
    public KafkaVehiclePosition selectKafkaVehiclePositionById(Long id);

    /**
     * 查询车辆定位数据列表
     * 
     * @param kafkaVehiclePosition 车辆定位数据
     * @return 车辆定位数据集合
     */
    public List<KafkaVehiclePosition> selectKafkaVehiclePositionList(KafkaVehiclePosition kafkaVehiclePosition);

    /**
     * 新增车辆定位数据
     * 
     * @param kafkaVehiclePosition 车辆定位数据
     * @return 结果
     */
    public int insertKafkaVehiclePosition(KafkaVehiclePosition kafkaVehiclePosition);

    /**
     * 修改车辆定位数据
     * 
     * @param kafkaVehiclePosition 车辆定位数据
     * @return 结果
     */
    public int updateKafkaVehiclePosition(KafkaVehiclePosition kafkaVehiclePosition);

    /**
     * 批量删除车辆定位数据
     * 
     * @param ids 需要删除的车辆定位数据主键集合
     * @return 结果
     */
    public int deleteKafkaVehiclePositionByIds(Long[] ids);

    /**
     * 删除车辆定位数据信息
     *
     * @param id 车辆定位数据主键
     * @return 结果
     */
    public int deleteKafkaVehiclePositionById(Long id);

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    public boolean processKafkaMessage(String kafkaMessage);

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    public KafkaVehiclePosition parseKafkaMessage(String kafkaMessage);
}
