package com.ruoyi.lxbi.admin.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.admin.mapper.KafkaPeopleSosMapper;
import com.ruoyi.lxbi.admin.domain.KafkaPeopleSos;
import com.ruoyi.lxbi.admin.service.IKafkaPeopleSosService;
import lombok.extern.slf4j.Slf4j;

/**
 * 人员求救数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class KafkaPeopleSosServiceImpl implements IKafkaPeopleSosService 
{
    @Autowired
    private KafkaPeopleSosMapper kafkaPeopleSosMapper;

    /**
     * 查询人员求救数据
     * 
     * @param id 人员求救数据主键
     * @return 人员求救数据
     */
    @Override
    public KafkaPeopleSos selectKafkaPeopleSosById(Long id)
    {
        return kafkaPeopleSosMapper.selectKafkaPeopleSosById(id);
    }

    /**
     * 查询人员求救数据列表
     * 
     * @param kafkaPeopleSos 人员求救数据
     * @return 人员求救数据
     */
    @Override
    public List<KafkaPeopleSos> selectKafkaPeoplesosList(KafkaPeopleSos kafkaPeopleSos)
    {
        return kafkaPeopleSosMapper.selectKafkaPeoplesosList(kafkaPeopleSos);
    }

    /**
     * 新增人员求救数据
     * 
     * @param kafkaPeopleSos 人员求救数据
     * @return 结果
     */
    @Override
    public int insertKafkaPeopleSos(KafkaPeopleSos kafkaPeopleSos)
    {
        kafkaPeopleSos.setCreateTime(DateUtils.getNowDate());
        return kafkaPeopleSosMapper.insertKafkaPeopleSos(kafkaPeopleSos);
    }

    /**
     * 修改人员求救数据
     * 
     * @param kafkaPeopleSos 人员求救数据
     * @return 结果
     */
    @Override
    public int updateKafkaPeopleSos(KafkaPeopleSos kafkaPeopleSos)
    {
        kafkaPeopleSos.setUpdateTime(DateUtils.getNowDate());
        return kafkaPeopleSosMapper.updateKafkaPeopleSos(kafkaPeopleSos);
    }

    /**
     * 批量删除人员求救数据
     * 
     * @param ids 需要删除的数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeoplesosByIds(Long[] ids)
    {
        return kafkaPeopleSosMapper.deleteKafkaPeoplesosByIds(ids);
    }

    /**
     * 删除人员求救数据信息
     * 
     * @param id 人员求救数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaPeopleSosById(Long id)
    {
        return kafkaPeopleSosMapper.deleteKafkaPeopleSosById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage) {
        try {
            log.debug("开始处理Kafka人员求救消息");

            // 解析Kafka消息
            KafkaPeopleSos peopleSos = parseKafkaMessage(kafkaMessage);
            if (peopleSos == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(peopleSos.getPersonCardCode())) {
                log.warn("人员卡编码为空，跳过处理");
                return false;
            }

            if (peopleSos.getSosStartTime() == null) {
                log.warn("求救开始时间为空，人员卡编码: {}", peopleSos.getPersonCardCode());
                return false;
            }

            // 设置默认值
            peopleSos.setCreateTime(DateUtils.getNowDate());
            peopleSos.setUpdateTime(DateUtils.getNowDate());
            peopleSos.setCreateBy("kafka_system");
            peopleSos.setUpdateBy("kafka_system");
            
            if (peopleSos.getStatus() == null) {
                peopleSos.setStatus(1L);
            }
            if (peopleSos.getIsDeleted() == null) {
                peopleSos.setIsDeleted(0L);
            }

            // 尝试使用UPSERT操作，如果失败则使用传统的查询-插入/更新方式
            int result = 0;
            try {
                result = kafkaPeopleSosMapper.upsertKafkaPeopleSos(peopleSos);
                log.info("使用UPSERT处理人员求救记录成功，人员卡编码: {}, 姓名: {}, 求救时间: {}", 
                        peopleSos.getPersonCardCode(), peopleSos.getPersonName(), peopleSos.getSosStartTime());
            } catch (Exception e) {
                log.warn("UPSERT操作失败，尝试使用传统方式处理，人员卡编码: {}, 求救时间: {}", 
                        peopleSos.getPersonCardCode(), peopleSos.getSosStartTime(), e);
                
                // 备用方案：先查询是否存在，然后决定插入或更新
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String sosStartTimeStr = sdf.format(peopleSos.getSosStartTime());
                
                KafkaPeopleSos existing = kafkaPeopleSosMapper
                        .selectByPersonCardCodeAndSosStartTime(peopleSos.getPersonCardCode(), sosStartTimeStr);
                
                if (existing != null) {
                    // 更新现有记录
                    peopleSos.setId(existing.getId());
                    peopleSos.setCreateBy(existing.getCreateBy());
                    peopleSos.setCreateTime(existing.getCreateTime());
                    result = kafkaPeopleSosMapper.updateKafkaPeopleSos(peopleSos);
                    log.info("更新人员求救记录成功，人员卡编码: {}, 姓名: {}", 
                            peopleSos.getPersonCardCode(), peopleSos.getPersonName());
                } else {
                    // 插入新记录
                    result = kafkaPeopleSosMapper.insertKafkaPeopleSos(peopleSos);
                    log.info("插入人员求救记录成功，人员卡编码: {}, 姓名: {}", 
                            peopleSos.getPersonCardCode(), peopleSos.getPersonName());
                }
            }
            
            return result > 0;

        } catch (Exception e) {
            log.error("处理Kafka人员求救消息失败: {}", kafkaMessage, e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     */
    @Override
    public KafkaPeopleSos parseKafkaMessage(String kafkaMessage) {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            log.debug("解析JSON字符串: {}", jsonStr);

            // 使用Jackson解析JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonStr);

            KafkaPeopleSos peopleSos = new KafkaPeopleSos();

            // 基本信息
            peopleSos.setMineCode(getStringValue(jsonNode, "煤矿编码"));
            peopleSos.setMineName(getStringValue(jsonNode, "矿井名称"));
            
            // 时间字段处理
            peopleSos.setDataUploadTime(getDateValue(jsonNode, "数据上传时间"));
            peopleSos.setSosStartTime(getDateValue(jsonNode, "求救开始时间"));
            peopleSos.setSosEndTime(getDateValue(jsonNode, "求救结束时间"));
            peopleSos.setEnterWellTime(getDateValue(jsonNode, "入井时间"));
            peopleSos.setEnterCurrentAreaTime(getDateValue(jsonNode, "进入当前区域时刻"));
            peopleSos.setEnterCurrentBaseStationTime(getDateValue(jsonNode, "进入当前所处基站时刻"));

            // 人员信息
            peopleSos.setPersonCardCode(getStringValue(jsonNode, "人员卡编码"));
            peopleSos.setPersonName(getStringValue(jsonNode, "姓名"));
            
            // 位置信息
            peopleSos.setCurrentAreaCode(getStringValue(jsonNode, "当前所在区域编码"));
            peopleSos.setCurrentBaseStationCode(getStringValue(jsonNode, "当前所在基站编码"));

            // 默认值
            peopleSos.setStatus(1L);
            peopleSos.setIsDeleted(0L);

            return peopleSos;

        } catch (Exception e) {
            log.error("解析Kafka消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 统计指定日期范围内的求救次数
     */
    @Override
    public Long countSosCallsByDateRange(String startDate, String endDate) {
        return kafkaPeopleSosMapper.countSosCallsByDateRange(startDate, endDate);
    }

    /**
     * 统计指定日期范围内的求救人员数量
     */
    @Override
    public Long countSosPersonnelByDateRange(String startDate, String endDate) {
        return kafkaPeopleSosMapper.countSosPersonnelByDateRange(startDate, endDate);
    }

    /**
     * 查询指定日期范围内的求救记录
     */
    @Override
    public List<KafkaPeopleSos> selectSosRecordsByDateRange(String startDate, String endDate, Integer limit) {
        return kafkaPeopleSosMapper.selectSosRecordsByDateRange(startDate, endDate, limit);
    }

    /**
     * 按日期统计求救次数（用于趋势分析）
     */
    @Override
    public List<Map<String, Object>> selectSosCountByDate(String startDate, String endDate) {
        return kafkaPeopleSosMapper.selectSosCountByDate(startDate, endDate);
    }

    /**
     * 获取最近的求救记录（用于实时监控）
     */
    @Override
    public List<KafkaPeopleSos> selectRecentSosRecords(Integer limit) {
        return kafkaPeopleSosMapper.selectRecentSosRecords(limit);
    }

    /**
     * 统计各区域的求救次数分布
     */
    @Override
    public List<Map<String, Object>> selectSosDistributionByArea(String startDate, String endDate) {
        return kafkaPeopleSosMapper.selectSosDistributionByArea(startDate, endDate);
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return (node != null && !node.isNull()) ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private Date getDateValue(JsonNode jsonNode, String fieldName) {
        String dateStr = getStringValue(jsonNode, fieldName);
        if (!StringUtils.hasText(dateStr)) {
            return null;
        }

        try {
            // 尝试多种日期格式
            String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd'T'HH:mm:ss",
                "yyyy-MM-dd HH:mm:ss.SSS",
                "yyyy-MM-dd'T'HH:mm:ss.SSS",
                "yyyy-MM-dd"
            };

            for (String pattern : patterns) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    return sdf.parse(dateStr);
                } catch (Exception ignored) {
                    // 继续尝试下一个格式
                }
            }

            log.warn("无法解析日期字段 {}: {}", fieldName, dateStr);
            return null;
        } catch (Exception e) {
            log.warn("解析日期字段 {} 失败: {}", fieldName, dateStr, e);
            return null;
        }
    }
}
