package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataDrillingStatsRequest;
import com.ruoyi.lxbi.domain.response.*;
import com.ruoyi.lxbi.mapper.DataDthStatsMapper;
import com.ruoyi.lxbi.service.IDataDthStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 潜孔施工数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Service
public class DataDthStatsServiceImpl implements IDataDthStatsService {
    @Autowired
    private DataDthStatsMapper dataDthStatsMapper;
    
    /**
     * 查询总体统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合
     */
    @Override
    public List<DataDrillingTotalStats> selectTotalStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDthStatsMapper.selectDailyTotalStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDthStatsMapper.selectWeeklyTotalStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDthStatsMapper.selectYearlyTotalStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDthStatsMapper.selectMonthlyTotalStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    @Override
    public List<DataDrillingTotalWithPlanStats> selectTotalWithPlanStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDthStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDthStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDthStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDthStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }
    
    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    @Override
    public List<DataDrillingPeriodStats> selectPeriodStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDthStatsMapper.selectDailyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDthStatsMapper.selectWeeklyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDthStatsMapper.selectYearlyPeriodStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDthStatsMapper.selectMonthlyPeriodStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询项目部门统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合
     */
    @Override
    public List<DataDrillingDepartmentStats> selectDepartmentStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDthStatsMapper.selectDailyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDthStatsMapper.selectWeeklyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDthStatsMapper.selectYearlyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDthStatsMapper.selectMonthlyDepartmentStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询采场统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 采场统计数据集合
     */
    @Override
    public List<DataDrillingStopeStats> selectStopeStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDthStatsMapper.selectDailyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDthStatsMapper.selectWeeklyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDthStatsMapper.selectYearlyStopeStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDthStatsMapper.selectMonthlyStopeStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询工作面统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 工作面统计数据集合
     */
    @Override
    public List<DataDrillingWorkingFaceStats> selectWorkingFaceStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDthStatsMapper.selectDailyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDthStatsMapper.selectWeeklyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDthStatsMapper.selectYearlyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDthStatsMapper.selectMonthlyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合（含计划量）
     */
    @Override
    public List<DataDrillingDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDthStatsMapper.selectDailyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDthStatsMapper.selectWeeklyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDthStatsMapper.selectYearlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDthStatsMapper.selectMonthlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

}
