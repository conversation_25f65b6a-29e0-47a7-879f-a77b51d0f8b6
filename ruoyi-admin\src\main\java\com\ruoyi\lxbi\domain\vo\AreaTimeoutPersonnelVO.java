package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 区域超时人员VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AreaTimeoutPersonnelVO {
    
    /**
     * 次数
     */
    private Long timeoutCount;
    
    /**
     * 姓名
     */
    private String personnelName;
    
    /**
     * 当前超时区域
     */
    private String currentTimeoutArea;
    
    /**
     * 驻留平均时长
     */
    private String averageStayTime;
}
