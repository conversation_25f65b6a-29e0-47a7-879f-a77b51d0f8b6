package com.ruoyi.lxbi.mapper;

import java.util.Date;
import java.util.List;
import com.ruoyi.lxbi.domain.DataFilling;
import com.ruoyi.lxbi.domain.response.DataFillingVo;
import org.apache.ibatis.annotations.Param;

/**
 * 充填数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface DataFillingMapper 
{
    /**
     * 查询充填数据
     * 
     * @param id 充填数据主键
     * @return 充填数据
     */
    public DataFilling selectDataFillingById(Long id);

    /**
     * 查询充填数据列表
     *
     * @param dataFilling 充填数据
     * @return 充填数据集合
     */
    public List<DataFillingVo> selectDataFillingList(DataFilling dataFilling);

    /**
     * 新增充填数据
     * 
     * @param dataFilling 充填数据
     * @return 结果
     */
    public int insertDataFilling(DataFilling dataFilling);

    /**
     * 修改充填数据
     * 
     * @param dataFilling 充填数据
     * @return 结果
     */
    public int updateDataFilling(DataFilling dataFilling);

    /**
     * 删除充填数据
     * 
     * @param id 充填数据主键
     * @return 结果
     */
    public int deleteDataFillingById(Long id);

    /**
     * 批量删除充填数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataFillingByIds(Long[] ids);

    /**
     * 根据作业日期和项目部门查询充填数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 充填数据集合
     */
    public List<DataFillingVo> selectDataFillingByOperationDateAndProject(@Param("operationDate") Date operationDate, @Param("projectDepartmentId") Long projectDepartmentId);

    /**
     * 批量新增充填数据
     *
     * @param dataFillingList 充填数据列表
     * @return 结果
     */
    public int batchInsertDataFilling(List<DataFilling> dataFillingList);
}
