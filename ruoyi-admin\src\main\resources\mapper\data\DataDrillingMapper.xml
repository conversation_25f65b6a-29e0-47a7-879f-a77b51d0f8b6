<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataDrillingMapper">
    
    <resultMap type="DataDrilling" id="DataDrillingResult">
        <result property="id"    column="id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="workingFaceId"    column="working_face_id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="drillingType"    column="drilling_type"    />
        <result property="progressMeters"    column="progress_meters"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.ruoyi.lxbi.domain.response.DataDrillingVo" id="DataDrillingVoResult" extends="DataDrillingResult">
        <result property="projectDepartmentName"    column="project_department_name"    />
        <result property="stopeName"    column="stope_name"    />
        <result property="workingFaceName"    column="working_face_name"    />
        <result property="workingPeriodName"    column="working_period_name"    />
    </resultMap>

    <sql id="selectDataDrillingVo">
        select dd.id,
               dd.operation_date,
               dd.project_department_id,
               dd.stope_id,
               dd.working_face_id,
               dd.working_period_id,
               dd.drilling_type,
               dd.progress_meters,
               dd.create_by,
               dd.create_time,
               dd.update_by,
               dd.update_time,
               bpd.project_department_name,
               bs.stope_name,
               bwf.working_face_name,
               bwp.working_period_name
        from data_drilling dd
                 left join base_project_department bpd on bpd.project_department_id = dd.project_department_id
                 left join base_stope bs on bs.stope_id = dd.stope_id
                 left join base_working_face bwf on bwf.working_face_id = dd.working_face_id
                 left join base_working_period bwp on bwp.working_period_id = dd.working_period_id
    </sql>

    <select id="selectDataDrillingList" parameterType="DataDrilling" resultMap="DataDrillingVoResult">
        <include refid="selectDataDrillingVo"/>
        <where>
            <if test="operationDate != null "> and dd.operation_date = #{operationDate}</if>
            <if test="projectDepartmentId != null "> and dd.project_department_id = #{projectDepartmentId}</if>
            <if test="stopeId != null "> and dd.stope_id = #{stopeId}</if>
            <if test="workingFaceId != null "> and dd.working_face_id = #{workingFaceId}</if>
            <if test="workingPeriodId != null "> and dd.working_period_id = #{workingPeriodId}</if>
            <if test="drillingType != null  and drillingType != ''"> and dd.drilling_type = #{drillingType}</if>
        </where>
    </select>
    
    <select id="selectDataDrillingById" parameterType="Long" resultMap="DataDrillingResult">
        <include refid="selectDataDrillingVo"/>
        where dd.id = #{id}
    </select>

    <insert id="insertDataDrilling" parameterType="DataDrilling" useGeneratedKeys="true" keyProperty="id">
        insert into data_drilling
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">operation_date,</if>
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="workingFaceId != null">working_face_id,</if>
            <if test="workingPeriodId != null">working_period_id,</if>
            <if test="drillingType != null and drillingType != ''">drilling_type,</if>
            <if test="progressMeters != null">progress_meters,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">#{operationDate},</if>
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="workingFaceId != null">#{workingFaceId},</if>
            <if test="workingPeriodId != null">#{workingPeriodId},</if>
            <if test="drillingType != null and drillingType != ''">#{drillingType},</if>
            <if test="progressMeters != null">#{progressMeters},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataDrilling" parameterType="DataDrilling">
        update data_drilling
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="workingFaceId != null">working_face_id = #{workingFaceId},</if>
            <if test="workingPeriodId != null">working_period_id = #{workingPeriodId},</if>
            <if test="drillingType != null and drillingType != ''">drilling_type = #{drillingType},</if>
            <if test="progressMeters != null">progress_meters = #{progressMeters},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataDrillingById" parameterType="Long">
        delete from data_drilling where id = #{id}
    </delete>

    <delete id="deleteDataDrillingByIds" parameterType="String">
        delete from data_drilling where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据作业日期查询钻孔施工数据列表 -->
    <select id="selectDataDrillingByOperationDate" parameterType="java.util.Date" resultMap="DataDrillingVoResult">
        <include refid="selectDataDrillingVo"/>
        where dd.operation_date = #{operationDate}
        order by dd.project_department_id, dd.stope_id, dd.working_face_id, dd.working_period_id
    </select>

    <!-- 批量新增钻孔施工数据 -->
    <insert id="batchInsertDataDrilling" parameterType="java.util.List">
        insert into data_drilling (operation_date, project_department_id, stope_id, working_face_id, working_period_id, drilling_type, progress_meters, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.operationDate}, #{item.projectDepartmentId}, #{item.stopeId}, #{item.workingFaceId}, #{item.workingPeriodId}, #{item.drillingType}, #{item.progressMeters}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>