package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataMineHoistingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataMineHoistingStats;
import com.ruoyi.lxbi.domain.response.DataMineHoistingPeriodStats;
import com.ruoyi.lxbi.mapper.DataMineHoistingStatsMapper;
import com.ruoyi.lxbi.service.IDataMineHoistingStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 矿井提升数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DataMineHoistingStatsServiceImpl implements IDataMineHoistingStatsService {
    @Autowired
    private DataMineHoistingStatsMapper dataMineHoistingStatsMapper;
    
    /**
     * 查询统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 统计数据集合
     */
    @Override
    public List<DataMineHoistingStats> selectStatsList(DataMineHoistingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataMineHoistingStatsMapper.selectDailyStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataMineHoistingStatsMapper.selectWeeklyStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataMineHoistingStatsMapper.selectYearlyStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataMineHoistingStatsMapper.selectMonthlyStats(request.getStartDate(), request.getEndDate());
        }
    }
    
    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    @Override
    public List<DataMineHoistingPeriodStats> selectPeriodStatsList(DataMineHoistingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataMineHoistingStatsMapper.selectDailyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataMineHoistingStatsMapper.selectWeeklyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataMineHoistingStatsMapper.selectYearlyPeriodStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataMineHoistingStatsMapper.selectMonthlyPeriodStats(request.getStartDate(), request.getEndDate());
        }
    }

}
