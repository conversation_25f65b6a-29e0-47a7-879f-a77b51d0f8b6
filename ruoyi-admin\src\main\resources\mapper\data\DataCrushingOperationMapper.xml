<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataCrushingOperationMapper">
    
    <resultMap type="DataCrushingOperation" id="DataCrushingOperationResult">
        <result property="id"    column="id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="faultTime"    column="fault_time"    />
        <result property="faultReason"    column="fault_reason"    />
        <result property="faultStartTime"    column="fault_start_time"    />
        <result property="faultEndTime"    column="fault_end_time"    />
        <result property="crushingVolume"    column="crushing_volume"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataCrushingOperationVo">
        select dco.id,
               dco.working_period_id,
               dco.operation_date,
               dco.operation_time,
               dco.fault_time,
               dco.fault_reason,
               dco.fault_start_time,
               dco.fault_end_time,
               dco.crushing_volume,
               dco.create_by,
               dco.create_time,
               dco.update_by,
               dco.update_time,
               bwp.working_period_name
        from data_crushing_operation dco
                 left join base_working_period bwp on bwp.working_period_id = dco.working_period_id
    </sql>

    <select id="selectDataCrushingOperationList" parameterType="DataCrushingOperation" resultType="com.ruoyi.lxbi.domain.response.DataCrushingOperationVo">
        <include refid="selectDataCrushingOperationVo"/>
        <where>
            <if test="operationDate != null"> and dco.operation_date = #{operationDate}</if>
            <if test="workingPeriodId != null "> and dco.working_period_id = #{workingPeriodId}</if>
            <if test="params.beginOperationDate != null and params.beginOperationDate != '' and params.endOperationDate != null and params.endOperationDate != ''"> and dco.operation_date between #{params.beginOperationDate}::date and #{params.endOperationDate}::date</if>
        </where>
    </select>
    
    <select id="selectDataCrushingOperationById" parameterType="Long" resultMap="DataCrushingOperationResult">
        <include refid="selectDataCrushingOperationVo"/>
        where dco.id = #{id}
    </select>

    <insert id="insertDataCrushingOperation" parameterType="DataCrushingOperation" useGeneratedKeys="true" keyProperty="id">
        insert into data_crushing_operation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workingPeriodId != null">working_period_id,</if>
            <if test="operationDate != null">operation_date,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="faultTime != null">fault_time,</if>
            <if test="faultReason != null">fault_reason,</if>
            <if test="faultStartTime != null">fault_start_time,</if>
            <if test="faultEndTime != null">fault_end_time,</if>
            <if test="crushingVolume != null">crushing_volume,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workingPeriodId != null">#{workingPeriodId},</if>
            <if test="operationDate != null">#{operationDate},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="faultTime != null">#{faultTime},</if>
            <if test="faultReason != null">#{faultReason},</if>
            <if test="faultStartTime != null">#{faultStartTime},</if>
            <if test="faultEndTime != null">#{faultEndTime},</if>
            <if test="crushingVolume != null">#{crushingVolume},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataCrushingOperation" parameterType="DataCrushingOperation">
        update data_crushing_operation
        <trim prefix="SET" suffixOverrides=",">
            <if test="workingPeriodId != null">working_period_id = #{workingPeriodId},</if>
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="faultTime != null">fault_time = #{faultTime},</if>
            <if test="faultReason != null">fault_reason = #{faultReason},</if>
            <if test="faultStartTime != null">fault_start_time = #{faultStartTime},</if>
            <if test="faultEndTime != null">fault_end_time = #{faultEndTime},</if>
            <if test="crushingVolume != null">crushing_volume = #{crushingVolume},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataCrushingOperationById" parameterType="Long">
        delete from data_crushing_operation where id = #{id}
    </delete>

    <delete id="deleteDataCrushingOperationByIds" parameterType="String">
        delete from data_crushing_operation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据作业日期查询破碎数据列表 -->
    <select id="selectDataCrushingOperationByOperationDate" resultType="com.ruoyi.lxbi.domain.response.DataCrushingOperationVo">
        <include refid="selectDataCrushingOperationVo"/>
        where dco.operation_date = #{operationDate}::date
        order by dco.working_period_id
    </select>

    <!-- 批量新增破碎数据 -->
    <insert id="batchInsertDataCrushingOperation" parameterType="java.util.List">
        insert into data_crushing_operation (working_period_id, operation_date, operation_time, fault_time, fault_reason, fault_start_time, fault_end_time, crushing_volume, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.workingPeriodId}, #{item.operationDate}, #{item.operationTime}, #{item.faultTime}, #{item.faultReason}, #{item.faultStartTime}, #{item.faultEndTime}, #{item.crushingVolume}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>