package com.ruoyi.lxbi.controller.kafka;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.KafkaSafeMonitorErrorInfo;
import com.ruoyi.lxbi.service.IKafkaSafeMonitorErrorInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 安全监测实时数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@RestController
@RequestMapping("/kafka/MonitorErrorInfo")
public class KafkaSafeMonitorErrorInfoController extends BaseController {
    @Autowired
    private IKafkaSafeMonitorErrorInfoService kafkaSafeMonitorErrorInfoService;

    /**
     * 查询安全监测实时数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitorErrorInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo) {
        startPage();
        List<KafkaSafeMonitorErrorInfo> list = kafkaSafeMonitorErrorInfoService.selectKafkaSafeMonitorErrorInfoList(kafkaSafeMonitorErrorInfo);
        return getDataTable(list);
    }

    /**
     * 导出安全监测实时数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitorErrorInfo:export')")
    @Log(title = "安全监测实时数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo) {
        List<KafkaSafeMonitorErrorInfo> list = kafkaSafeMonitorErrorInfoService.selectKafkaSafeMonitorErrorInfoList(kafkaSafeMonitorErrorInfo);
        ExcelUtil<KafkaSafeMonitorErrorInfo> util = new ExcelUtil<KafkaSafeMonitorErrorInfo>(KafkaSafeMonitorErrorInfo.class);
        util.exportExcel(response, list, "安全监测实时数据数据");
    }

    /**
     * 获取安全监测实时数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitorErrorInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kafkaSafeMonitorErrorInfoService.selectKafkaSafeMonitorErrorInfoById(id));
    }

    /**
     * 新增安全监测实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitorErrorInfo:add')")
    @Log(title = "安全监测实时数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo)
    {
        return toAjax(kafkaSafeMonitorErrorInfoService.insertKafkaSafeMonitorErrorInfo(kafkaSafeMonitorErrorInfo));
    }

    /**
     * 修改安全监测实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitorErrorInfo:edit')")
    @Log(title = "安全监测实时数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo)
    {
        return toAjax(kafkaSafeMonitorErrorInfoService.updateKafkaSafeMonitorErrorInfo(kafkaSafeMonitorErrorInfo));
    }

    /**
     * 删除安全监测实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:MonitorErrorInfo:remove')")
    @Log(title = "安全监测实时数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kafkaSafeMonitorErrorInfoService.deleteKafkaSafeMonitorErrorInfoByIds(ids));
    }
}
