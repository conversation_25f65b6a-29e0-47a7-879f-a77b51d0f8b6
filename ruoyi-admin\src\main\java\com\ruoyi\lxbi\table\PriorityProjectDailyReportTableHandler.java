package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.BasePriorityProject;
import com.ruoyi.lxbi.domain.request.DataTunnelingStatsRequest;
import com.ruoyi.lxbi.domain.response.BasePriorityProjectVo;
import com.ruoyi.lxbi.domain.response.DataTunnelingDepartmentWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingStopeStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.table.PriorityProjectDailyReportTableVo;
import com.ruoyi.lxbi.service.IBasePriorityProjectService;
import com.ruoyi.lxbi.service.IDataTunnelingStatsService;
import com.ruoyi.lxbi.table.params.PriorityProjectDailyReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 重点工程日报表格处理器
 */
@Component
public class PriorityProjectDailyReportTableHandler extends BaseTableHandler<PriorityProjectDailyReportTableVo, PriorityProjectDailyReportQueryParams> {

    @Autowired
    private IDataTunnelingStatsService dataTunnelingStatsService;

    @Autowired
    private IBasePriorityProjectService basePriorityProjectService;

    @Override
    public List<PriorityProjectDailyReportTableVo> queryTableData(PriorityProjectDailyReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date operationDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取重点工程数据
        PriorityProjectResult priorityProjectResult = getPriorityProjectData(operationDate);

        // 构建表格数据
        return buildTableData(priorityProjectResult, operationDate);
    }

    /**
     * 获取重点工程数据
     */
    private PriorityProjectResult getPriorityProjectData(Date operationDate) {
        // 获取财务月的开始日期到查询日期的累计数据
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(operationDate);

        DataTunnelingStatsRequest request = new DataTunnelingStatsRequest();
        request.setStartDate(financialMonthStart);
        request.setEndDate(operationDate);

        // 获取重点工程总体统计数据
        List<DataTunnelingTotalWithPlanStats> priorityTotalStats = dataTunnelingStatsService.selectPriorityProjectTotalWithPlanStatsList(request, "daily");
        List<DataTunnelingTotalWithPlanStats> priorityDailyStats = priorityTotalStats.stream()
                .filter(stats -> FinancialDateUtils.isSameDay(stats.getOperationDate(), operationDate))
                .toList();

        // 计算重点工程总体月累计
        BigDecimal priorityTotalMonthlyAccumulated = priorityTotalStats.stream()
                .filter(stats -> stats.getTotalTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算重点工程总体日产量
        BigDecimal priorityTotalDailyOutput = priorityDailyStats.stream()
                .filter(stats -> stats.getTotalTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算重点工程总体月计划
        BigDecimal priorityTotalMonthlyPlan = priorityTotalStats.stream()
                .filter(stats -> stats.getPlanTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getPlanTunnelingLength()))
                .findFirst()
                .orElse(BigDecimal.ZERO);

        // 获取重点工程列表
        List<BasePriorityProjectVo> priorityProjects = basePriorityProjectService.selectBasePriorityProjectListAll(new BasePriorityProject());

        // 获取重点工程按采场的统计数据
        List<DataTunnelingStopeStats> priorityStopeStats = dataTunnelingStatsService.selectPriorityProjectStopeStatsList(request, "daily");
        List<DataTunnelingStopeStats> priorityStopeMonthlyStats = dataTunnelingStatsService.selectPriorityProjectStopeStatsList(request, "daily");

        return new PriorityProjectResult(priorityTotalDailyOutput, priorityTotalMonthlyAccumulated,
                priorityTotalMonthlyPlan, priorityProjects, priorityStopeStats, priorityStopeMonthlyStats);
    }

    /**
     * 构建表格数据
     */
    private List<PriorityProjectDailyReportTableVo> buildTableData(PriorityProjectResult priorityProjectResult, Date operationDate) {
        List<PriorityProjectDailyReportTableVo> result = new ArrayList<>();

        // 添加重点工程总计行
        PriorityProjectDailyReportTableVo summary = new PriorityProjectDailyReportTableVo();
        summary.setBold(List.of("serialNumber", "name", "monthlyPlan", "dailyOutput", "monthlyAccumulated", "completionRate", "monthlyOverUnder"));
        summary.setSerialNumber("总计");
        summary.setName("重点工程");
        summary.setSubName("重点工程");
        summary.setUnit("m");
        summary.setMonthlyPlan(priorityProjectResult.getTotalMonthlyPlan());
        summary.setDailyOutput(priorityProjectResult.getTotalDailyOutput());
        summary.setMonthlyAccumulated(priorityProjectResult.getTotalMonthlyAccumulated());

        // 计算总完成率
        if (priorityProjectResult.getTotalMonthlyPlan().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal rate = priorityProjectResult.getTotalMonthlyAccumulated()
                    .divide(priorityProjectResult.getTotalMonthlyPlan(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            summary.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
            summary.setMonthlyOverUnder(priorityProjectResult.getTotalMonthlyAccumulated()
                    .subtract(priorityProjectResult.getTotalMonthlyPlan()));
        } else {
            summary.setCompletionRate("　");
            summary.setMonthlyOverUnder(null);
        }

        result.add(summary);

        // 添加重点工程明细项目
        int subSerialNumber = 1;
        for (BasePriorityProjectVo project : priorityProjectResult.getPriorityProjects()) {
            PriorityProjectDailyReportTableVo vo = new PriorityProjectDailyReportTableVo();
            vo.setSerialNumber(String.valueOf(subSerialNumber++));
            vo.setName(getProjectDisplayName(project));
            vo.setSubName(getProjectDisplayName(project));
            vo.setUnit("m");

            // 从真实数据中获取计划、产量等数据（基于采场）
            vo.setMonthlyPlan(getProjectMonthlyPlan(project, priorityProjectResult));
            vo.setDailyOutput(getProjectDailyOutput(project, priorityProjectResult));
            vo.setMonthlyAccumulated(getProjectMonthlyAccumulated(project, priorityProjectResult));

            // 计算完成率
            if (vo.getMonthlyPlan() != null && vo.getMonthlyPlan().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = vo.getMonthlyAccumulated().divide(vo.getMonthlyPlan(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                vo.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
                vo.setMonthlyOverUnder(vo.getMonthlyAccumulated().subtract(vo.getMonthlyPlan()));
            } else {
                vo.setCompletionRate("　");
                vo.setMonthlyOverUnder(null);
            }

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取重点工程项目显示名称
     */
    private String getProjectDisplayName(BasePriorityProjectVo project) {
        String projectName = "";
        if (project.getStopeName() != null && !project.getStopeName().trim().isEmpty()) {
            projectName = project.getStopeName();
        }
        if (project.getWorkingFaceName() != null && !project.getWorkingFaceName().trim().isEmpty()) {
            projectName = project.getWorkingFaceName() + "-" + projectName ;
        }
        return projectName;
    }

    /**
     * 获取重点工程项目月计划（基于采场）
     */
    private BigDecimal getProjectMonthlyPlan(BasePriorityProjectVo project, PriorityProjectResult result) {
        // 根据重点工程的采场ID获取计划
        if (project.getStopeId() != null) {
            // TODO: 从计划数据中获取该采场的计划数据
            // 这里需要实现基于采场的计划查询
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取重点工程项目日产量（基于采场）
     */
    private BigDecimal getProjectDailyOutput(BasePriorityProjectVo project, PriorityProjectResult result) {
        // 根据重点工程的采场ID获取当日产量
        if (project.getStopeId() != null) {
            return result.getPriorityStopeStats().stream()
                    .filter(stats -> project.getStopeId().equals(stats.getStopeId()))
                    .filter(stats -> stats.getTotalTunnelingLength() != null)
                    .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                    .reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取重点工程项目月累计（基于采场）
     */
    private BigDecimal getProjectMonthlyAccumulated(BasePriorityProjectVo project, PriorityProjectResult result) {
        // 根据重点工程的采场ID获取月累计
        if (project.getStopeId() != null) {
            return result.getPriorityStopeMonthlyStats().stream()
                    .filter(stats -> project.getStopeId().equals(stats.getStopeId()))
                    .filter(stats -> stats.getTotalTunnelingLength() != null)
                    .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                    .reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 重点工程结果包装类
     */
    private static class PriorityProjectResult {
        private final BigDecimal totalDailyOutput;
        private final BigDecimal totalMonthlyAccumulated;
        private final BigDecimal totalMonthlyPlan;
        private final List<BasePriorityProjectVo> priorityProjects;
        private final List<DataTunnelingStopeStats> priorityStopeStats;
        private final List<DataTunnelingStopeStats> priorityStopeMonthlyStats;

        public PriorityProjectResult(BigDecimal totalDailyOutput,
                                     BigDecimal totalMonthlyAccumulated,
                                     BigDecimal totalMonthlyPlan,
                                     List<BasePriorityProjectVo> priorityProjects,
                                     List<DataTunnelingStopeStats> priorityStopeStats,
                                     List<DataTunnelingStopeStats> priorityStopeMonthlyStats) {
            this.totalDailyOutput = totalDailyOutput;
            this.totalMonthlyAccumulated = totalMonthlyAccumulated;
            this.totalMonthlyPlan = totalMonthlyPlan;
            this.priorityProjects = priorityProjects;
            this.priorityStopeStats = priorityStopeStats;
            this.priorityStopeMonthlyStats = priorityStopeMonthlyStats;
        }

        public BigDecimal getTotalDailyOutput() {
            return totalDailyOutput;
        }

        public BigDecimal getTotalMonthlyAccumulated() {
            return totalMonthlyAccumulated;
        }

        public BigDecimal getTotalMonthlyPlan() {
            return totalMonthlyPlan;
        }

        public List<BasePriorityProjectVo> getPriorityProjects() {
            return priorityProjects;
        }

        public List<DataTunnelingStopeStats> getPriorityStopeStats() {
            return priorityStopeStats;
        }

        public List<DataTunnelingStopeStats> getPriorityStopeMonthlyStats() {
            return priorityStopeMonthlyStats;
        }
    }
}
