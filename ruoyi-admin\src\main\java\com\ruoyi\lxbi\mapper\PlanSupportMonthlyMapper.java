package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanSupportMonthly;
import com.ruoyi.lxbi.domain.response.PlanSupportMonthlyVo;

/**
 * 支护月计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface PlanSupportMonthlyMapper 
{
    /**
     * 查询支护月计划
     * 
     * @param id 支护月计划主键
     * @return 支护月计划
     */
    public PlanSupportMonthly selectPlanSupportMonthlyById(Long id);

    /**
     * 查询支护月计划列表
     * 
     * @param planSupportMonthly 支护月计划
     * @return 支护月计划集合
     */
    public List<PlanSupportMonthlyVo> selectPlanSupportMonthlyList(PlanSupportMonthly planSupportMonthly);

    /**
     * 新增支护月计划
     * 
     * @param planSupportMonthly 支护月计划
     * @return 结果
     */
    public int insertPlanSupportMonthly(PlanSupportMonthly planSupportMonthly);

    /**
     * 修改支护月计划
     * 
     * @param planSupportMonthly 支护月计划
     * @return 结果
     */
    public int updatePlanSupportMonthly(PlanSupportMonthly planSupportMonthly);

    /**
     * 删除支护月计划
     * 
     * @param id 支护月计划主键
     * @return 结果
     */
    public int deletePlanSupportMonthlyById(Long id);

    /**
     * 批量删除支护月计划
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanSupportMonthlyByIds(Long[] ids);
}
