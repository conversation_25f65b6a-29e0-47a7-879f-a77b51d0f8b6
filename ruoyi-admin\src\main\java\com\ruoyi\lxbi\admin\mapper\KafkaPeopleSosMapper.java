package com.ruoyi.lxbi.admin.mapper;

import java.util.List;
import com.ruoyi.lxbi.admin.domain.KafkaPeopleSos;
import org.apache.ibatis.annotations.Param;

/**
 * 人员求救数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface KafkaPeopleSosMapper 
{
    /**
     * 查询人员求救数据
     * 
     * @param id 人员求救数据主键
     * @return 人员求救数据
     */
    public KafkaPeopleSos selectKafkaPeopleSosById(Long id);

    /**
     * 查询人员求救数据列表
     * 
     * @param kafkaPeopleSos 人员求救数据
     * @return 人员求救数据集合
     */
    public List<KafkaPeopleSos> selectKafkaPeoplesosList(KafkaPeopleSos kafkaPeopleSos);

    /**
     * 新增人员求救数据
     * 
     * @param kafkaPeopleSos 人员求救数据
     * @return 结果
     */
    public int insertKafkaPeopleSos(KafkaPeopleSos kafkaPeopleSos);

    /**
     * 修改人员求救数据
     * 
     * @param kafkaPeopleSos 人员求救数据
     * @return 结果
     */
    public int updateKafkaPeopleSos(KafkaPeopleSos kafkaPeopleSos);

    /**
     * 删除人员求救数据
     * 
     * @param id 人员求救数据主键
     * @return 结果
     */
    public int deleteKafkaPeopleSosById(Long id);

    /**
     * 批量删除人员求救数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKafkaPeoplesosByIds(Long[] ids);

    /**
     * 根据人员卡编码和求救开始时间查询求救记录
     * 
     * @param personCardCode 人员卡编码
     * @param sosStartTime 求救开始时间
     * @return 人员求救数据
     */
    public KafkaPeopleSos selectByPersonCardCodeAndSosStartTime(
            @Param("personCardCode") String personCardCode, 
            @Param("sosStartTime") String sosStartTime);

    /**
     * 统计指定日期范围内的求救次数
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 求救次数
     */
    public Long countSosCallsByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计指定日期范围内的求救人员数量
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 求救人员数量
     */
    public Long countSosPersonnelByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询指定日期范围内的求救记录
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 求救记录列表
     */
    public List<KafkaPeopleSos> selectSosRecordsByDateRange(
            @Param("startDate") String startDate, 
            @Param("endDate") String endDate, 
            @Param("limit") Integer limit);

    /**
     * 按日期统计求救次数（用于趋势分析）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期和求救次数的映射
     */
    public List<java.util.Map<String, Object>> selectSosCountByDate(
            @Param("startDate") String startDate, 
            @Param("endDate") String endDate);

    /**
     * 获取最近的求救记录（用于实时监控）
     * 
     * @param limit 限制数量
     * @return 最近的求救记录
     */
    public List<KafkaPeopleSos> selectRecentSosRecords(@Param("limit") Integer limit);

    /**
     * 统计各区域的求救次数分布
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 区域求救统计列表
     */
    public List<java.util.Map<String, Object>> selectSosDistributionByArea(
            @Param("startDate") String startDate, 
            @Param("endDate") String endDate);

    /**
     * 批量插入或更新（PostgreSQL UPSERT）
     * 根据人员卡编码和求救开始时间进行唯一性判断
     *
     * @param kafkaPeopleSos 人员求救数据
     * @return 结果
     */
    public int upsertKafkaPeopleSos(KafkaPeopleSos kafkaPeopleSos);
}
