package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * AI平台报警响应VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiAlarmResponseVO {
    
    /**
     * 响应数据
     */
    private AiAlarmDataResponseVO data;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AiAlarmDataResponseVO {
        
        /**
         * 报警数据列表
         */
        @JsonProperty("alarmDataList")
        private List<AiAlarmDataVO> alarmDataList;
        
        /**
         * 总报警数量
         */
        private Long total;
    }
}
