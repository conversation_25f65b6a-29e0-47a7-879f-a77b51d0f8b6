package com.ruoyi.lxbi.admin.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 地表监测API响应实体类
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Data
public class SurfaceMonitoringApiResponse {

    /** 接口调用成功与否的说明 */
    private Boolean result;

    /** 数据列表 */
    private List<SurfaceMonitoringDataItem> data;

    /** 消息 */
    private String message;

    /**
     * 地表监测数据项
     */
    @Data
    public static class SurfaceMonitoringDataItem {
        
        /** 日期 */
        private String date;

        /** 基站名称 */
        @JsonProperty("station_name")
        private String stationName;

        /** 终端编号 */
        private String wgbh;

        /** x偏移在0-2.5mm的次数占比 */
        private Integer x1;

        /** x偏移在2.5-5mm的次数占比 */
        private Integer x2;

        /** x偏移在5-10mm的次数占比 */
        private Integer x3;

        /** x偏移>10mm的次数占比 */
        private Integer x4;

        /** y偏移在0-2.5mm的次数占比 */
        private Integer y1;

        /** y偏移在2.5-5mm的次数占比 */
        private Integer y2;

        /** y偏移在5-10mm的次数占比 */
        private Integer y3;

        /** y偏移>10mm的次数占比 */
        private Integer y4;

        /** 高度偏移在0-2.5mm的次数占比 */
        private Integer h1;

        /** 高度偏移在2.5-5mm的次数占比 */
        private Integer h2;

        /** 高度偏移在5-10mm的次数占比 */
        private Integer h3;

        /** 高度偏移>10mm的次数占比 */
        private Integer h4;

        /** 原始数据ID */
        private Integer id;

        /** 当日y偏移距离总和 */
        @JsonProperty("ystackedTotalOffset")
        private BigDecimal ystackedTotalOffset;

        /** 当日高度偏移距离总和 */
        @JsonProperty("hstackedTotalOffset")
        private BigDecimal hstackedTotalOffset;

        /** 当日x偏移距离总和 */
        @JsonProperty("xstackedTotalOffset")
        private BigDecimal xstackedTotalOffset;
    }
}
