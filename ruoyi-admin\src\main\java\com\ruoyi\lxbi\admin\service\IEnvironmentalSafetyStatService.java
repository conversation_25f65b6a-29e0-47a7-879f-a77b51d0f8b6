package com.ruoyi.lxbi.admin.service;

import com.ruoyi.lxbi.domain.vo.*;

import java.util.List;

/**
 * 环境安全统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IEnvironmentalSafetyStatService {

    /**
     * 获取环境安全概览统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 环境安全概览统计
     */
    EnvironmentalSafetyOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取环境安全报警位置分布统计 (雷达图数据)
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 环境安全报警位置分布统计列表
     */
    List<EnvironmentalSafetyLocationDistributionVO> getLocationDistribution(String viewType, String startDate, String endDate);

    /**
     * 获取环境安全报警数量趋势统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 环境安全报警数量趋势统计列表
     */
    List<EnvironmentalSafetyDataTrendVO> getDataTrend(String viewType, String startDate, String endDate);
}
