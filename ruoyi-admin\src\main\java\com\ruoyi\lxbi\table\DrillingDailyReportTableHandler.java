package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.BaseStope;
import com.ruoyi.lxbi.domain.PlanDeepHoleMonthly;
import com.ruoyi.lxbi.domain.PlanDthMonthly;
import com.ruoyi.lxbi.domain.request.DataDrillingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataDrillingStopeStats;
import com.ruoyi.lxbi.domain.response.PlanDeepHoleMonthlyVo;
import com.ruoyi.lxbi.domain.response.PlanDthMonthlyVo;
import com.ruoyi.lxbi.domain.table.DrillingDailyReportTableVo;
import com.ruoyi.lxbi.service.*;
import com.ruoyi.lxbi.table.params.DrillingDailyReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 钻孔施工数据日报表格处理器 - 潜孔和中深孔合一
 */
@Component
public class DrillingDailyReportTableHandler extends BaseTableHandler<DrillingDailyReportTableVo, DrillingDailyReportQueryParams> {

    @Autowired
    private IDataDthStatsService dataDthStatsService;

    @Autowired
    private IDataDeepHoleStatsService dataDeepHoleStatsService;

    @Autowired
    private IPlanDthMonthlyService planDthMonthlyService;

    @Autowired
    private IPlanDeepHoleMonthlyService planDeepHoleMonthlyService;

    @Autowired
    private IBaseStopeService baseStopeService;

    @Override
    public List<DrillingDailyReportTableVo> queryTableData(DrillingDailyReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date operationDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取月度计划数据
        MonthlyPlansResult monthlyPlans = getMonthlyPlans(operationDate);

        // 获取月累计数据和当日数据（一次查询获取）
        MonthlyStatsResult monthlyResult = getMonthlyAccumulatedWithDaily(operationDate);

        // 构建表格数据
        return buildTableData(monthlyResult, monthlyPlans);
    }

    /**
     * 获取月度计划数据（按财务月：上月29号到本月28号）
     */
    private MonthlyPlansResult getMonthlyPlans(Date operationDate) {
        // 计算财务月
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        // 获取潜孔月度计划
        PlanDthMonthly dthQuery = new PlanDthMonthly();
        dthQuery.setPlanDate(financialMonth);
        List<PlanDthMonthlyVo> dthPlans = planDthMonthlyService.selectPlanDthMonthlyList(dthQuery);

        // 获取中深孔月度计划
        PlanDeepHoleMonthly deepHoleQuery = new PlanDeepHoleMonthly();
        deepHoleQuery.setPlanDate(financialMonth);
        List<PlanDeepHoleMonthlyVo> deepHolePlans = planDeepHoleMonthlyService.selectPlanDeepHoleMonthlyList(deepHoleQuery);

        // 按采场ID汇总潜孔计划
        Map<Long, BigDecimal> dthPlansByStope = dthPlans.stream()
                .filter(plan -> plan.getDthMeter() != null)
                .collect(Collectors.groupingBy(
                        PlanDthMonthly::getStopeId,
                        Collectors.reducing(BigDecimal.ZERO,
                                PlanDthMonthly::getDthMeter,
                                BigDecimal::add)
                ));

        // 按采场ID汇总中深孔计划
        Map<Long, BigDecimal> deepHolePlansByStope = deepHolePlans.stream()
                .filter(plan -> plan.getDeepHoleMeter() != null)
                .collect(Collectors.groupingBy(
                        PlanDeepHoleMonthly::getStopeId,
                        Collectors.reducing(BigDecimal.ZERO,
                                PlanDeepHoleMonthly::getDeepHoleMeter,
                                BigDecimal::add)
                ));

        return new MonthlyPlansResult(dthPlansByStope, deepHolePlansByStope);
    }

    /**
     * 获取月累计数据和当日数据（按财务月：上月29号到本月28号）
     */
    private MonthlyStatsResult getMonthlyAccumulatedWithDaily(Date operationDate) {
        // 获取财务月的开始日期到查询日期的累计数据
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(operationDate);

        DataDrillingStatsRequest request = new DataDrillingStatsRequest();
        request.setStartDate(financialMonthStart);
        request.setEndDate(operationDate);

        // 获取潜孔统计数据
        List<DataDrillingStopeStats> dthMonthlyStats = dataDthStatsService.selectStopeStatsList(request, "daily");
        List<DataDrillingStopeStats> dthDailyStats = dthMonthlyStats.stream()
                .filter(stats -> FinancialDateUtils.isSameDay(stats.getOperationDate(), operationDate))
                .collect(Collectors.toList());

        // 获取中深孔统计数据
        List<DataDrillingStopeStats> deepHoleMonthlyStats = dataDeepHoleStatsService.selectStopeStatsList(request, "daily");
        List<DataDrillingStopeStats> deepHoleDailyStats = deepHoleMonthlyStats.stream()
                .filter(stats -> FinancialDateUtils.isSameDay(stats.getOperationDate(), operationDate))
                .collect(Collectors.toList());

        // 按采场ID汇总潜孔累计数据
        Map<Long, BigDecimal> dthMonthlyAccumulated = dthMonthlyStats.stream()
                .collect(Collectors.groupingBy(
                        DataDrillingStopeStats::getStopeId,
                        Collectors.summingDouble(stats -> stats.getTotalProgressMeters() != null ? stats.getTotalProgressMeters() : 0.0)
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> BigDecimal.valueOf(entry.getValue())
                ));

        // 按采场ID汇总中深孔累计数据
        Map<Long, BigDecimal> deepHoleMonthlyAccumulated = deepHoleMonthlyStats.stream()
                .collect(Collectors.groupingBy(
                        DataDrillingStopeStats::getStopeId,
                        Collectors.summingDouble(stats -> stats.getTotalProgressMeters() != null ? stats.getTotalProgressMeters() : 0.0)
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> BigDecimal.valueOf(entry.getValue())
                ));

        return new MonthlyStatsResult(dthDailyStats, deepHoleDailyStats, dthMonthlyAccumulated, deepHoleMonthlyAccumulated);
    }

    /**
     * 构建表格数据
     */
    private List<DrillingDailyReportTableVo> buildTableData(MonthlyStatsResult monthlyResult, MonthlyPlansResult monthlyPlans) {
        List<DrillingDailyReportTableVo> result = new ArrayList<>();

        // 构建潜孔数据
        buildDrillingTypeData(result, "总计", "潜孔", "1",
                monthlyResult.getDthDailyStats(),
                monthlyResult.getDthMonthlyAccumulated(),
                monthlyPlans.getDthPlansByWorkingFace());

        // 构建中深孔数据
        buildDrillingTypeData(result, "总计", "中深孔", "2",
                monthlyResult.getDeepHoleDailyStats(),
                monthlyResult.getDeepHoleMonthlyAccumulated(),
                monthlyPlans.getDeepHolePlansByWorkingFace());

        return result;
    }

    /**
     * 构建特定钻孔类型的数据
     */
    private void buildDrillingTypeData(List<DrillingDailyReportTableVo> result,
                                       String serialNumber, String typeName, String drillingType,
                                       List<DataDrillingStopeStats> dailyStats,
                                       Map<Long, BigDecimal> monthlyAccumulated,
                                       Map<Long, BigDecimal> monthlyPlans) {

        // 计算总计数据
        BigDecimal totalDailyOutput = BigDecimal.ZERO;
        BigDecimal totalMonthlyPlan = BigDecimal.ZERO;
        BigDecimal totalMonthlyAccumulated = BigDecimal.ZERO;

        // 按采场ID分组，避免重复
        Map<Long, DataDrillingStopeStats> stopeStatsMap = dailyStats.stream()
                .collect(Collectors.toMap(
                        DataDrillingStopeStats::getStopeId,
                        stats -> stats,
                        (existing, replacement) -> {
                            // 如果有重复，累加进尺量
                            existing.setTotalProgressMeters((existing.getTotalProgressMeters() != null ? existing.getTotalProgressMeters() : 0.0) +
                                    (replacement.getTotalProgressMeters() != null ? replacement.getTotalProgressMeters() : 0.0));
                            return existing;
                        }
                ));

        // 获取所有涉及的采场ID（包括有计划但当日无产量的）
        Set<Long> allStopeIds = new HashSet<>();
        allStopeIds.addAll(stopeStatsMap.keySet());
        allStopeIds.addAll(monthlyPlans.keySet());
        allStopeIds.addAll(monthlyAccumulated.keySet());

        // 批量获取采场名称
        Map<Long, String> stopeNameMap = getStopeNameMap(allStopeIds);

        int subSerialNumber = 1;
        for (Long stopeId : allStopeIds) {
            DataDrillingStopeStats stats = stopeStatsMap.get(stopeId);
            BigDecimal dailyOutput = stats != null && stats.getTotalProgressMeters() != null ?
                    BigDecimal.valueOf(stats.getTotalProgressMeters()) : BigDecimal.ZERO;
            BigDecimal monthlyPlan = monthlyPlans.getOrDefault(stopeId, BigDecimal.ZERO);
            BigDecimal accumulated = monthlyAccumulated.getOrDefault(stopeId, BigDecimal.ZERO);

            DrillingDailyReportTableVo vo = new DrillingDailyReportTableVo();
            vo.setSerialNumber(String.valueOf(subSerialNumber++));
            vo.setName(getStopeNameFromMap(stopeId, stats, stopeNameMap));
            vo.setSubName(getStopeNameFromMap(stopeId, stats, stopeNameMap));
            vo.setUnit("m");

            // 设置月计划，如果为0则显示"/"
            if (monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                vo.setMonthlyPlan(monthlyPlan);
            } else {
                vo.setMonthlyPlan(null); // 前端可以根据null显示"/"
            }

            vo.setDailyOutput(dailyOutput);
            vo.setMonthlyAccumulated(accumulated);

            // 计算完成率
            if (monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = accumulated.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                vo.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
            } else {
                vo.setCompletionRate("　");
            }

            // 计算超欠量
            if (monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                vo.setMonthlyOverUnder(accumulated.subtract(monthlyPlan));
            } else {
                vo.setMonthlyOverUnder(null); // 前端可以根据null显示"　"
            }

            result.add(vo);

            // 累加总计
            totalDailyOutput = totalDailyOutput.add(dailyOutput);
            totalMonthlyPlan = totalMonthlyPlan.add(monthlyPlan);
            totalMonthlyAccumulated = totalMonthlyAccumulated.add(accumulated);
        }

        // 添加总计行
        if (!result.isEmpty() || totalMonthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
            DrillingDailyReportTableVo summary = new DrillingDailyReportTableVo();
            summary.setBold(List.of("serialNumber", "name", "monthlyPlan", "dailyOutput", "monthlyAccumulated", "completionRate", "monthlyOverUnder"));
            summary.setSerialNumber(serialNumber);
            summary.setName(typeName);
            summary.setSubName(typeName);
            summary.setUnit("m");
            summary.setMonthlyPlan(totalMonthlyPlan);
            summary.setDailyOutput(totalDailyOutput);
            summary.setMonthlyAccumulated(totalMonthlyAccumulated);

            // 计算总完成率
            if (totalMonthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = totalMonthlyAccumulated.divide(totalMonthlyPlan, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                summary.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
                summary.setMonthlyOverUnder(totalMonthlyAccumulated.subtract(totalMonthlyPlan));
            } else {
                summary.setCompletionRate("　");
                summary.setMonthlyOverUnder(null); // 前端可以根据null显示"　"
            }

            // 插入到当前类型数据的开头
            int insertIndex = result.size() - allStopeIds.size();
            if (insertIndex < 0) insertIndex = 0;
            result.add(insertIndex, summary);
        }
    }

    /**
     * 月度计划结果包装类
     */
    private static class MonthlyPlansResult {
        private final Map<Long, BigDecimal> dthPlansByStope;
        private final Map<Long, BigDecimal> deepHolePlansByStope;

        public MonthlyPlansResult(Map<Long, BigDecimal> dthPlansByStope, Map<Long, BigDecimal> deepHolePlansByStope) {
            this.dthPlansByStope = dthPlansByStope;
            this.deepHolePlansByStope = deepHolePlansByStope;
        }

        public Map<Long, BigDecimal> getDthPlansByWorkingFace() {
            return dthPlansByStope;
        }

        public Map<Long, BigDecimal> getDeepHolePlansByWorkingFace() {
            return deepHolePlansByStope;
        }
    }

    /**
     * 月度统计结果包装类
     */
    private static class MonthlyStatsResult {
        private final List<DataDrillingStopeStats> dthDailyStats;
        private final List<DataDrillingStopeStats> deepHoleDailyStats;
        private final Map<Long, BigDecimal> dthMonthlyAccumulated;
        private final Map<Long, BigDecimal> deepHoleMonthlyAccumulated;

        public MonthlyStatsResult(List<DataDrillingStopeStats> dthDailyStats,
                                  List<DataDrillingStopeStats> deepHoleDailyStats,
                                  Map<Long, BigDecimal> dthMonthlyAccumulated,
                                  Map<Long, BigDecimal> deepHoleMonthlyAccumulated) {
            this.dthDailyStats = dthDailyStats;
            this.deepHoleDailyStats = deepHoleDailyStats;
            this.dthMonthlyAccumulated = dthMonthlyAccumulated;
            this.deepHoleMonthlyAccumulated = deepHoleMonthlyAccumulated;
        }

        public List<DataDrillingStopeStats> getDthDailyStats() {
            return dthDailyStats;
        }

        public List<DataDrillingStopeStats> getDeepHoleDailyStats() {
            return deepHoleDailyStats;
        }

        public Map<Long, BigDecimal> getDthMonthlyAccumulated() {
            return dthMonthlyAccumulated;
        }

        public Map<Long, BigDecimal> getDeepHoleMonthlyAccumulated() {
            return deepHoleMonthlyAccumulated;
        }
    }

    /**
     * 批量获取采场名称映射
     */
    private Map<Long, String> getStopeNameMap(Set<Long> stopeIds) {
        Map<Long, String> stopeNameMap = new HashMap<>();

        if (stopeIds == null || stopeIds.isEmpty()) {
            return stopeNameMap;
        }

        // 批量查询采场信息
        for (Long stopeId : stopeIds) {
            try {
                BaseStope stope = baseStopeService.selectBaseStopeByStopeId(stopeId);
                if (stope != null && stope.getStopeName() != null) {
                    stopeNameMap.put(stopeId, stope.getStopeName());
                }
            } catch (Exception e) {
                // 查询失败时记录日志，但不影响整体流程
                System.err.println("获取采场名称失败，采场ID: " + stopeId + ", 错误: " + e.getMessage());
            }
        }

        return stopeNameMap;
    }

    /**
     * 从映射中获取采场名称
     */
    private String getStopeNameFromMap(Long stopeId, DataDrillingStopeStats stats, Map<Long, String> stopeNameMap) {
        // 优先使用统计数据中的采场名称
        if (stats != null && stats.getStopeName() != null && !stats.getStopeName().trim().isEmpty()) {
            return stats.getStopeName();
        }

        // 其次使用基础数据表中的采场名称
        String stopeNameFromBase = stopeNameMap.get(stopeId);
        if (stopeNameFromBase != null && !stopeNameFromBase.trim().isEmpty()) {
            return stopeNameFromBase;
        }

        // 最后使用默认格式（但这种情况应该很少出现）
        return "未知采场-" + stopeId;
    }
}
