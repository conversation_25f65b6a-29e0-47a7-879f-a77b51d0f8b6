package com.ruoyi.lxbi.controller.base;

import java.util.List;

import com.ruoyi.common.core.domain.R;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.BaseProjectDepartment;
import com.ruoyi.lxbi.service.IBaseProjectDepartmentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目部门配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/base/projectDepartment")
public class BaseProjectDepartmentController extends BaseController {
    @Autowired
    private IBaseProjectDepartmentService baseProjectDepartmentService;

    /**
     * 查询项目部门配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:projectDepartment:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaseProjectDepartment baseProjectDepartment) {
        startPage();
        List<BaseProjectDepartment> list = baseProjectDepartmentService.selectBaseProjectDepartmentList(baseProjectDepartment);
        return getDataTable(list);
    }

    /**
     * 查询项目部门配置列表（不分页）
     */
    @GetMapping("/listAll")
    public R<List<BaseProjectDepartment>> listAll(BaseProjectDepartment baseProjectDepartment) {
        List<BaseProjectDepartment> list = baseProjectDepartmentService.selectBaseProjectDepartmentList(baseProjectDepartment);
        return R.ok(list);
    }

    /**
     * 导出项目部门配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:projectDepartment:export')")
    @Log(title = "项目部门配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseProjectDepartment baseProjectDepartment) {
        List<BaseProjectDepartment> list = baseProjectDepartmentService.selectBaseProjectDepartmentList(baseProjectDepartment);
        ExcelUtil<BaseProjectDepartment> util = new ExcelUtil<BaseProjectDepartment>(BaseProjectDepartment.class);
        util.exportExcel(response, list, "项目部门配置数据");
    }

    /**
     * 获取项目部门配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:projectDepartment:query')")
    @GetMapping(value = "/{projectDepartmentId}")
    public AjaxResult getInfo(@PathVariable("projectDepartmentId") Long projectDepartmentId) {
        return success(baseProjectDepartmentService.selectBaseProjectDepartmentByProjectDepartmentId(projectDepartmentId));
    }

    /**
     * 新增项目部门配置
     */
    @PreAuthorize("@ss.hasPermi('base:projectDepartment:add')")
    @Log(title = "项目部门配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseProjectDepartment baseProjectDepartment)
    {
        return toAjax(baseProjectDepartmentService.insertBaseProjectDepartment(baseProjectDepartment));
    }

    /**
     * 修改项目部门配置
     */
    @PreAuthorize("@ss.hasPermi('base:projectDepartment:edit')")
    @Log(title = "项目部门配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaseProjectDepartment baseProjectDepartment)
    {
        return toAjax(baseProjectDepartmentService.updateBaseProjectDepartment(baseProjectDepartment));
    }

    /**
     * 删除项目部门配置
     */
    @PreAuthorize("@ss.hasPermi('base:projectDepartment:remove')")
    @Log(title = "项目部门配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{projectDepartmentIds}")
    public AjaxResult remove(@PathVariable Long[] projectDepartmentIds)
    {
        return toAjax(baseProjectDepartmentService.deleteBaseProjectDepartmentByProjectDepartmentIds(projectDepartmentIds));
    }
}
