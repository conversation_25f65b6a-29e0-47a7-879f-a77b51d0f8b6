package com.ruoyi.lxbi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 车辆超速告警数据对象 kafka_vehicle_speed_over
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KafkaVehicleSpeedOver extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long isDeleted;

    /** 文件编码 */
    private String fileEncoding;

    /** 煤矿代码 */
    private String mineCode;

    /** 数据生成时间 */
    private Date dataGenerateTime;

    /** 车辆标识卡 */
    private String vehicleIdentificationCard;

    /** 进矿时间 */
    private Date inMineTime;

    /** 告警开始时间 */
    private Date alarmStartTime;

    /** 告警结束时间 */
    private Date alarmEndTime;

}
