<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.BaseOrePassMapper">
    
    <resultMap type="BaseOrePass" id="BaseOrePassResult">
        <result property="orePassId"    column="ore_pass_id"    />
        <result property="orePassName"    column="ore_pass_name"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectBaseOrePassVo">
        select ore_pass_id, ore_pass_name, status, create_by, create_time, update_by, update_time, start_time, end_time, is_delete from base_ore_pass
    </sql>

    <select id="selectBaseOrePassList" parameterType="BaseOrePass" resultMap="BaseOrePassResult">
        <include refid="selectBaseOrePassVo"/>
        <where>  
            <if test="orePassName != null  and orePassName != ''"> and ore_pass_name like concat('%', #{orePassName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and start_time between #{params.beginStartTime}::date and #{params.endStartTime}::date</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and end_time between #{params.beginEndTime}::date and #{params.endEndTime}::date</if>
            and is_delete = 0
        </where>
    </select>
    
    <select id="selectBaseOrePassByOrePassId" parameterType="Long" resultMap="BaseOrePassResult">
        <include refid="selectBaseOrePassVo"/>
        where ore_pass_id = #{orePassId}
    </select>

    <select id="getBaseOrePassByOrePassIds"  parameterType="String" resultType="java.lang.Integer">
        select count(1) as count from base_ore_pass where ore_pass_id in
        <foreach item="orePassId" collection="array" open="(" separator="," close=")">
            #{orePassId}
        </foreach>
        and is_delete = 0 and status = 1;
    </select>

    <insert id="insertBaseOrePass" parameterType="BaseOrePass" useGeneratedKeys="true" keyProperty="orePassId">
        insert into base_ore_pass
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orePassName != null">ore_pass_name,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orePassName != null">#{orePassName},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updateBaseOrePass" parameterType="BaseOrePass">
        update base_ore_pass
        <trim prefix="SET" suffixOverrides=",">
            <if test="orePassName != null">ore_pass_name = #{orePassName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where ore_pass_id = #{orePassId}
    </update>

    <delete id="deleteBaseOrePassByOrePassId" parameterType="Long">
        update base_ore_pass set is_delete = 1 where ore_pass_id = #{orePassId}
    </delete>

    <delete id="deleteBaseOrePassByOrePassIds" parameterType="String">
        update base_ore_pass set is_delete = 1 where ore_pass_id in
        <foreach item="orePassId" collection="array" open="(" separator="," close=")">
            #{orePassId}
        </foreach>
    </delete>
</mapper>