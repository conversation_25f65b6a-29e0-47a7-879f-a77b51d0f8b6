package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 安全监测实时数据对象 kafka_safe_monitor_error_info
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KafkaSafeMonitorErrorInfo extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long isDeleted;

    /** 文件编码 */
    private String fileEncoding;

    /** 煤矿代码 */
    private String mineCode;

    /** 煤矿名称 */
    private String mineName;

    /** 数据上传时间 */
    private Date dataUploadTime;

    /** 测点编码 */
    private String monitoringPointCode;

    /** 传感器类型名称 */
    private String sensorTypeName;

    /** 测点安装位置 */
    private String monitoringPointLocation;

    /** 测点数值单位 */
    private String monitoringPointUnit;

    /** 异常类型 */
    private String abnormalType;

    /** 异常开始时间 */
    private Date abnormalStartTime;

    /** 异常结束时间 */
    private Date abnormalEndTime;

    /** 异常期间最大值 */
    private BigDecimal abnormalMaxValue;

    /** 最大值时刻 */
    private Date abnormalMaxTime;

    /** 异常期间最小值 */
    private BigDecimal abnormalMinValue;

    /** 最小值时刻 */
    private Date abnormalMinTime;

    /** 异常期间平均值 */
    private BigDecimal abnormalAverageValue;

    /** 异常原因 */
    private String abnormalReason;

    /** 处理措施 */
    private String handlingMeasures;

    /** 数据时间 */
    private Date dataTime;

}
