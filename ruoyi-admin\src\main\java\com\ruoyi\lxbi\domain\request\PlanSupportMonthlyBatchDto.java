package com.ruoyi.lxbi.domain.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 支护月计划批量操作DTO
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Data
public class PlanSupportMonthlyBatchDto {

    /** 主键ID */
    private Long id;

    /** 计划月份 */
    private String planMonth;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 锚网支护米数 */
    private BigDecimal boltMeshSupportMeter;

    /** 喷浆支护米数 */
    private BigDecimal shotcreteSupportMeter;

    /** 备注 */
    private String remark;

    /** 操作类型（新增、更新、删除） */
    private String operationType;

}
