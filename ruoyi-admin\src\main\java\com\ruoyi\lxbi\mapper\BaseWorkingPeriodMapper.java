package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.BaseWorkingPeriod;

/**
 * 作业时段配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface BaseWorkingPeriodMapper 
{
    /**
     * 查询作业时段配置
     * 
     * @param workingPeriodId 作业时段配置主键
     * @return 作业时段配置
     */
    public BaseWorkingPeriod selectBaseWorkingPeriodByWorkingPeriodId(Long workingPeriodId);

    /**
     * 查询作业时段配置列表
     * 
     * @param baseWorkingPeriod 作业时段配置
     * @return 作业时段配置集合
     */
    public List<BaseWorkingPeriod> selectBaseWorkingPeriodList(BaseWorkingPeriod baseWorkingPeriod);

    /**
     * 新增作业时段配置
     * 
     * @param baseWorkingPeriod 作业时段配置
     * @return 结果
     */
    public int insertBaseWorkingPeriod(BaseWorkingPeriod baseWorkingPeriod);

    /**
     * 修改作业时段配置
     * 
     * @param baseWorkingPeriod 作业时段配置
     * @return 结果
     */
    public int updateBaseWorkingPeriod(BaseWorkingPeriod baseWorkingPeriod);

    /**
     * 删除作业时段配置
     * 
     * @param workingPeriodId 作业时段配置主键
     * @return 结果
     */
    public int deleteBaseWorkingPeriodByWorkingPeriodId(Long workingPeriodId);

    /**
     * 批量删除作业时段配置
     * 
     * @param workingPeriodIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseWorkingPeriodByWorkingPeriodIds(Long[] workingPeriodIds);

    int getBaseWorkingPeriodByWorkingPeriodIds(Long[] workingPeriodIds);
}
