<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.PlanMiningMonthlyMapper">
    
    <resultMap type="PlanMiningMonthly" id="PlanMiningMonthlyResult">
        <result property="id"    column="id"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="driftMeter"    column="drift_meter"    />
        <result property="rawOreVolume"    column="raw_ore_volume"    />
        <result property="supportMeter"    column="support_meter"    />
        <result property="fillingVolume"    column="filling_volume"    />
        <result property="dthMeter"    column="dth_meter"    />
        <result property="deepHoleMeter"    column="deep_hole_meter"    />
        <result property="oreOutputVolume"    column="ore_output_volume"    />
        <result property="planDate"    column="plan_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPlanMiningMonthlyVo">
        select pmm.*, bpd.project_department_name from plan_mining_monthly pmm left join base_project_department bpd on pmm.project_department_id = bpd.project_department_id
    </sql>

    <select id="selectPlanMiningMonthlyList" parameterType="PlanMiningMonthly" resultType="com.ruoyi.lxbi.domain.response.PlanMiningMonthlyVo">
        <include refid="selectPlanMiningMonthlyVo"/>
        <where>  
            <if test="projectDepartmentId != null "> and pmm.project_department_id = #{projectDepartmentId}</if>
            <if test="planDate != null  and planDate != ''"> and pmm.plan_date = #{planDate}</if>
        </where>
    </select>
    
    <select id="selectPlanMiningMonthlyById" parameterType="Long" resultType="com.ruoyi.lxbi.domain.response.PlanMiningMonthlyVo">
        <include refid="selectPlanMiningMonthlyVo"/>
        where pmm.id = #{id}
    </select>

    <insert id="insertPlanMiningMonthly" parameterType="PlanMiningMonthly" useGeneratedKeys="true" keyProperty="id">
        insert into plan_mining_monthly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="driftMeter != null">drift_meter,</if>
            <if test="rawOreVolume != null">raw_ore_volume,</if>
            <if test="supportMeter != null">support_meter,</if>
            <if test="fillingVolume != null">filling_volume,</if>
            <if test="dthMeter != null">dth_meter,</if>
            <if test="deepHoleMeter != null">deep_hole_meter,</if>
            <if test="oreOutputVolume != null">ore_output_volume,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="driftMeter != null">#{driftMeter},</if>
            <if test="rawOreVolume != null">#{rawOreVolume},</if>
            <if test="supportMeter != null">#{supportMeter},</if>
            <if test="fillingVolume != null">#{fillingVolume},</if>
            <if test="dthMeter != null">#{dthMeter},</if>
            <if test="deepHoleMeter != null">#{deepHoleMeter},</if>
            <if test="oreOutputVolume != null">#{oreOutputVolume},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePlanMiningMonthly" parameterType="PlanMiningMonthly">
        update plan_mining_monthly
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="driftMeter != null">drift_meter = #{driftMeter},</if>
            <if test="rawOreVolume != null">raw_ore_volume = #{rawOreVolume},</if>
            <if test="supportMeter != null">support_meter = #{supportMeter},</if>
            <if test="fillingVolume != null">filling_volume = #{fillingVolume},</if>
            <if test="dthMeter != null">dth_meter = #{dthMeter},</if>
            <if test="deepHoleMeter != null">deep_hole_meter = #{deepHoleMeter},</if>
            <if test="oreOutputVolume != null">ore_output_volume = #{oreOutputVolume},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanMiningMonthlyById" parameterType="Long">
        delete from plan_mining_monthly where id = #{id}
    </delete>

    <delete id="deletePlanMiningMonthlyByIds" parameterType="String">
        delete from plan_mining_monthly where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>