package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 超期隐患统计VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OverdueStatisticsVO {
    
    /**
     * 隐患总数
     */
    private Long totalCount;
    
    /**
     * 超期数
     */
    private Long overdueCount;
    
    /**
     * 超期率
     */
    private BigDecimal overdueRate;
}
