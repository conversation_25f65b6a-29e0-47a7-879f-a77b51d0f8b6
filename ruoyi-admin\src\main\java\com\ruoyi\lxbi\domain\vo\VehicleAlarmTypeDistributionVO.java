package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 车辆告警类型分布VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleAlarmTypeDistributionVO {
    
    /**
     * 告警类型名称
     */
    private String alarmTypeName;
    
    /**
     * 告警数量
     */
    private Long alarmCount;
    
    /**
     * 占比百分比
     */
    private BigDecimal percentage;
    
    /**
     * 告警类型代码
     */
    private String alarmTypeCode;
}
