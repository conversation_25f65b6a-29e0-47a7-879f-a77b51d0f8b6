package com.ruoyi.lxbi.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 钻孔施工数据统计请求参数
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
public class DataDrillingStatsRequest {
    /**
     * 统计类型：daily-日统计, weekly-周统计, monthly-月统计, yearly-年统计
     */
    private String statsType;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
}
