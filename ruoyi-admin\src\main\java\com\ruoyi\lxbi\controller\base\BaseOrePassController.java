package com.ruoyi.lxbi.controller.base;

import java.util.List;

import com.ruoyi.common.core.domain.R;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.BaseOrePass;
import com.ruoyi.lxbi.service.IBaseOrePassService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 溜井配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/base/orePass")
public class BaseOrePassController extends BaseController {
    @Autowired
    private IBaseOrePassService baseOrePassService;

    /**
     * 查询溜井配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:orePass:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaseOrePass baseOrePass) {
        startPage();
        List<BaseOrePass> list = baseOrePassService.selectBaseOrePassList(baseOrePass);
        return getDataTable(list);
    }

    /**
     * 查询溜井配置列表（不分页）
     */
    @GetMapping("/listAll")
    public R<List<BaseOrePass>> listAll(BaseOrePass baseOrePass) {
        List<BaseOrePass> list = baseOrePassService.selectBaseOrePassList(baseOrePass);
        return R.ok(list);
    }

    /**
     * 导出溜井配置列表
     */
    @PreAuthorize("@ss.hasPermi('base:orePass:export')")
    @Log(title = "溜井配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseOrePass baseOrePass) {
        List<BaseOrePass> list = baseOrePassService.selectBaseOrePassList(baseOrePass);
        ExcelUtil<BaseOrePass> util = new ExcelUtil<BaseOrePass>(BaseOrePass.class);
        util.exportExcel(response, list, "溜井配置数据");
    }

    /**
     * 获取溜井配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:orePass:query')")
    @GetMapping(value = "/{orePassId}")
    public AjaxResult getInfo(@PathVariable("orePassId") Long orePassId) {
        return success(baseOrePassService.selectBaseOrePassByOrePassId(orePassId));
    }

    /**
     * 新增溜井配置
     */
    @PreAuthorize("@ss.hasPermi('base:orePass:add')")
    @Log(title = "溜井配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseOrePass baseOrePass)
    {
        return toAjax(baseOrePassService.insertBaseOrePass(baseOrePass));
    }

    /**
     * 修改溜井配置
     */
    @PreAuthorize("@ss.hasPermi('base:orePass:edit')")
    @Log(title = "溜井配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaseOrePass baseOrePass)
    {
        return toAjax(baseOrePassService.updateBaseOrePass(baseOrePass));
    }

    /**
     * 删除溜井配置
     */
    @PreAuthorize("@ss.hasPermi('base:orePass:remove')")
    @Log(title = "溜井配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{orePassIds}")
    public AjaxResult remove(@PathVariable Long[] orePassIds)
    {
        return toAjax(baseOrePassService.deleteBaseOrePassByOrePassIds(orePassIds));
    }
}
