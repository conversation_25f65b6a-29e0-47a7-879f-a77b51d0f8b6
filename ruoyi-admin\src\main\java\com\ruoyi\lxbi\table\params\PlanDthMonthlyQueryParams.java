package com.ruoyi.lxbi.table.params;

/**
 * 潜孔月计划查询参数
 */
public class PlanDthMonthlyQueryParams {
    
    /** 开始日期 */
    private String startDate;
    
    /** 结束日期 */
    private String endDate;
    
    /** 计划日期范围 */
    private String[] planDateRange;
    
    /** 页码 */
    private Integer pageNum;
    
    /** 页大小 */
    private Integer pageSize;

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String[] getPlanDateRange() {
        return planDateRange;
    }

    public void setPlanDateRange(String[] planDateRange) {
        this.planDateRange = planDateRange;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "PlanDthMonthlyQueryParams{" +
                "startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", planDateRange=" + java.util.Arrays.toString(planDateRange) +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }
}
