package com.ruoyi.lxbi.admin.task;

import com.ruoyi.lxbi.admin.service.IApiHiddenTroubleRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * 隐患数据同步定时任务
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Slf4j
@Component("hiddenTroubleSyncTask")
public class HiddenTroubleSyncTask {

    @Autowired
    private IApiHiddenTroubleRecordService apiHiddenTroubleRecordService;

    /**
     * 每天凌晨1点同步前一天的隐患数据
     * 
     * 在定时任务管理中配置 cron 表达式: 0 0 1 * * ?
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void syncYesterdayData() {
        try {
            log.info("开始执行定时任务：同步前一天隐患数据");
            
            // 计算前一天的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterday = calendar.getTime();
            
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String yesterdayStr = dateFormat.format(yesterday);
            
            log.info("同步日期: {}", yesterdayStr);
            
            // 调用同步方法
            Map<String, Object> result = apiHiddenTroubleRecordService.syncHiddenTroubleDataByDateRange(yesterdayStr, yesterdayStr);
            
            // 记录同步结果
            boolean success = (Boolean) result.get("success");
            if (success) {
                Object stateCountMap = result.get("stateCountMap");
                log.info("定时任务执行成功 - 同步前一天({})隐患数据完成，各状态数量: {}, 处理结果: {}",
                    yesterdayStr, stateCountMap, result);
            } else {
                log.error("定时任务执行失败 - 同步前一天({})隐患数据失败: {}", yesterdayStr, result.get("message"));
            }
            
        } catch (Exception e) {
            log.error("定时任务执行异常 - 同步前一天隐患数据", e);
        }
    }

    /**
     * 每天凌晨2点同步前一周的隐患数据（补偿机制）
     * 
     * 在定时任务管理中配置 cron 表达式: 0 0 2 * * ?
     */
    public void syncLastWeekData() {
        try {
            log.info("开始执行定时任务：同步前一周隐患数据（补偿机制）");
            
            // 计算前一周的日期范围
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -7);
            Date weekAgo = calendar.getTime();
            
            calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterday = calendar.getTime();
            
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String startDate = dateFormat.format(weekAgo);
            String endDate = dateFormat.format(yesterday);
            
            log.info("同步日期范围: {} 到 {}", startDate, endDate);
            
            // 调用同步方法
            Map<String, Object> result = apiHiddenTroubleRecordService.syncHiddenTroubleDataByDateRange(startDate, endDate);
            
            // 记录同步结果
            boolean success = (Boolean) result.get("success");
            if (success) {
                Object stateCountMap = result.get("stateCountMap");
                log.info("定时任务执行成功 - 同步前一周({} 到 {})隐患数据完成，各状态数量: {}, 处理结果: {}",
                    startDate, endDate, stateCountMap, result);
            } else {
                log.error("定时任务执行失败 - 同步前一周({} 到 {})隐患数据失败: {}", startDate, endDate, result.get("message"));
            }
            
        } catch (Exception e) {
            log.error("定时任务执行异常 - 同步前一周隐患数据", e);
        }
    }

    /**
     * 手动触发同步指定日期的数据（用于测试）
     * 
     * @param dateStr 日期字符串，格式：yyyy-MM-dd
     */
    public void syncSpecificDate(String dateStr) {
        try {
            log.info("开始执行手动任务：同步指定日期({})隐患数据", dateStr);
            
            // 调用同步方法
            Map<String, Object> result = apiHiddenTroubleRecordService.syncHiddenTroubleDataByDateRange(dateStr, dateStr);
            
            // 记录同步结果
            boolean success = (Boolean) result.get("success");
            if (success) {
                log.info("手动任务执行成功 - 同步指定日期({})隐患数据完成: {}", dateStr, result);
            } else {
                log.error("手动任务执行失败 - 同步指定日期({})隐患数据失败: {}", dateStr, result.get("message"));
            }
            
        } catch (Exception e) {
            log.error("手动任务执行异常 - 同步指定日期({})隐患数据", dateStr, e);
        }
    }

    /**
     * 手动触发同步指定日期范围的数据（用于测试）
     * 
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     */
    public void syncDateRange(String startDate, String endDate) {
        try {
            log.info("开始执行手动任务：同步指定日期范围({} 到 {})隐患数据", startDate, endDate);
            
            // 调用同步方法
            Map<String, Object> result = apiHiddenTroubleRecordService.syncHiddenTroubleDataByDateRange(startDate, endDate);
            
            // 记录同步结果
            boolean success = (Boolean) result.get("success");
            if (success) {
                log.info("手动任务执行成功 - 同步指定日期范围({} 到 {})隐患数据完成: {}", startDate, endDate, result);
            } else {
                log.error("手动任务执行失败 - 同步指定日期范围({} 到 {})隐患数据失败: {}", startDate, endDate, result.get("message"));
            }
            
        } catch (Exception e) {
            log.error("手动任务执行异常 - 同步指定日期范围({} 到 {})隐患数据", startDate, endDate, e);
        }
    }
}
