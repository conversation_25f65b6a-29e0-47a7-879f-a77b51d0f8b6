package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 地表监测站点详细信息VO
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurfaceMonitoringStationDetailVO {

    /**
     * 站点基本信息
     */
    private SurfaceMonitoringStationVO stationInfo;

    /**
     * 当前状态
     */
    private SurfaceMonitoringStationStatusVO currentStatus;

    /**
     * 历史数据趋势
     */
    private List<SurfaceMonitoringDataTrendVO> historicalTrend;

    /**
     * 最近报警事件
     */
    private List<SurfaceMonitoringAlarmVO> recentAlarms;

    /**
     * 统计信息
     */
    private StationStatistics statistics;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StationStatistics {
        
        /**
         * 统计周期
         */
        private String period;

        /**
         * 最大X偏移量(mm)
         */
        private BigDecimal maxXOffset;

        /**
         * 最大Y偏移量(mm)
         */
        private BigDecimal maxYOffset;

        /**
         * 最大H偏移量(mm)
         */
        private BigDecimal maxHOffset;

        /**
         * 平均X偏移量(mm)
         */
        private BigDecimal avgXOffset;

        /**
         * 平均Y偏移量(mm)
         */
        private BigDecimal avgYOffset;

        /**
         * 平均H偏移量(mm)
         */
        private BigDecimal avgHOffset;

        /**
         * 报警次数
         */
        private Long alarmCount;

        /**
         * 数据采集次数
         */
        private Long dataCollectionCount;

        /**
         * 在线率(%)
         */
        private BigDecimal onlineRate;

        /**
         * 数据完整率(%)
         */
        private BigDecimal dataIntegrityRate;
    }
}
