package com.ruoyi.lxbi.admin.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.math.BigDecimal;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.lxbi.admin.service.SurfaceMonitoringExternalService;
import com.ruoyi.lxbi.admin.domain.dto.SurfaceMonitoringApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.admin.mapper.SurfaceMonitoringDataMapper;
import com.ruoyi.lxbi.admin.domain.SurfaceMonitoringData;
import com.ruoyi.lxbi.admin.service.ISurfaceMonitoringDataService;
import lombok.extern.slf4j.Slf4j;

/**
 * 地表监测数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Slf4j
@Service
public class SurfaceMonitoringDataServiceImpl implements ISurfaceMonitoringDataService {
    
    @Autowired
    private SurfaceMonitoringDataMapper surfaceMonitoringDataMapper;

    @Autowired
    private SurfaceMonitoringExternalService surfaceMonitoringExternalService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询地表监测数据
     * 
     * @param id 地表监测数据主键
     * @return 地表监测数据
     */
    @Override
    public SurfaceMonitoringData selectSurfaceMonitoringDataById(Long id) {
        return surfaceMonitoringDataMapper.selectSurfaceMonitoringDataById(id);
    }

    /**
     * 查询地表监测数据列表
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 地表监测数据
     */
    @Override
    public List<SurfaceMonitoringData> selectSurfaceMonitoringDataList(SurfaceMonitoringData surfaceMonitoringData) {
        return surfaceMonitoringDataMapper.selectSurfaceMonitoringDataList(surfaceMonitoringData);
    }

    /**
     * 新增地表监测数据
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 结果
     */
    @Override
    public int insertSurfaceMonitoringData(SurfaceMonitoringData surfaceMonitoringData) {
        surfaceMonitoringData.setCreateTime(DateUtils.getNowDate());
        return surfaceMonitoringDataMapper.insertSurfaceMonitoringData(surfaceMonitoringData);
    }

    /**
     * 修改地表监测数据
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 结果
     */
    @Override
    public int updateSurfaceMonitoringData(SurfaceMonitoringData surfaceMonitoringData) {
        surfaceMonitoringData.setUpdateTime(DateUtils.getNowDate());
        return surfaceMonitoringDataMapper.updateSurfaceMonitoringData(surfaceMonitoringData);
    }

    /**
     * 批量删除地表监测数据
     * 
     * @param ids 需要删除的地表监测数据主键
     * @return 结果
     */
    @Override
    public int deleteSurfaceMonitoringDataByIds(Long[] ids) {
        return surfaceMonitoringDataMapper.deleteSurfaceMonitoringDataByIds(ids);
    }

    /**
     * 删除地表监测数据信息
     * 
     * @param id 地表监测数据主键
     * @return 结果
     */
    @Override
    public int deleteSurfaceMonitoringDataById(Long id) {
        return surfaceMonitoringDataMapper.deleteSurfaceMonitoringDataById(id);
    }

    /**
     * 根据日期、基站名称和终端编号查询地表监测数据
     * 
     * @param date 日期
     * @param stationName 基站名称
     * @param wgbh 终端编号
     * @return 地表监测数据
     */
    @Override
    public SurfaceMonitoringData selectByDateStationWgbh(Date date, String stationName, String wgbh) {
        return surfaceMonitoringDataMapper.selectByDateStationWgbh(date, stationName, wgbh);
    }

    /**
     * 根据原始数据ID查询地表监测数据
     * 
     * @param originalId 原始数据ID
     * @return 地表监测数据
     */
    @Override
    public SurfaceMonitoringData selectByOriginalId(Integer originalId) {
        return surfaceMonitoringDataMapper.selectByOriginalId(originalId);
    }

    /**
     * UPSERT操作（根据日期、基站名称、终端编号插入或更新）
     * 
     * @param surfaceMonitoringData 地表监测数据
     * @return 结果
     */
    @Override
    public int upsertSurfaceMonitoringData(SurfaceMonitoringData surfaceMonitoringData) {
        // 检查是否已存在（基于日期、基站名称、终端编号的复合唯一性）
        SurfaceMonitoringData existingRecord = selectByDateStationWgbh(
            surfaceMonitoringData.getDate(), 
            surfaceMonitoringData.getStationName(),
            surfaceMonitoringData.getWgbh()
        );
        boolean isUpdate = existingRecord != null;
        
        // 设置时间字段
        Date now = DateUtils.getNowDate();
        if (isUpdate) {
            // 更新操作：保留原创建时间，更新修改时间
            surfaceMonitoringData.setCreateTime(existingRecord.getCreateTime());
            surfaceMonitoringData.setCreateBy(existingRecord.getCreateBy());
        } else {
            // 新增操作：设置创建时间
            surfaceMonitoringData.setCreateTime(now);
            if (!StringUtils.hasText(surfaceMonitoringData.getCreateBy())) {
                surfaceMonitoringData.setCreateBy("api-system");
            }
        }
        
        // 设置更新时间
        surfaceMonitoringData.setUpdateTime(now);
        if (!StringUtils.hasText(surfaceMonitoringData.getUpdateBy())) {
            surfaceMonitoringData.setUpdateBy("api-system");
        }
        
        // 设置同步时间
        if (surfaceMonitoringData.getSyncTime() == null) {
            surfaceMonitoringData.setSyncTime(now);
        }
        
        // 设置数据来源
        if (!StringUtils.hasText(surfaceMonitoringData.getDataSource())) {
            surfaceMonitoringData.setDataSource("api");
        }
        
        // 执行UPSERT操作
        try {
            int result = surfaceMonitoringDataMapper.upsertSurfaceMonitoringData(surfaceMonitoringData);
            
            if (result > 0) {
                String operation = isUpdate ? "更新" : "新增";
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String dateStr = surfaceMonitoringData.getDate() != null ? 
                    dateFormat.format(surfaceMonitoringData.getDate()) : "null";
                log.debug("{}地表监测数据成功，日期: {}, 基站: {}, 终端: {}", 
                    operation, dateStr, surfaceMonitoringData.getStationName(), surfaceMonitoringData.getWgbh());
            }
            
            return result;
        } catch (Exception e) {
            log.error("UPSERT地表监测数据失败，日期: {}, 基站: {}, 终端: {}, 错误: {}", 
                surfaceMonitoringData.getDate(), surfaceMonitoringData.getStationName(), 
                surfaceMonitoringData.getWgbh(), e.getMessage());
            return 0;
        }
    }

    /**
     * 同步第三方地表监测数据
     * 
     * @return 同步结果
     */
    @Override
    public Map<String, Object> syncSurfaceMonitoringData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始同步第三方地表监测数据");

            // 先进行登录认证
            Map<String, Object> loginResult = surfaceMonitoringExternalService.login();
            if (!(Boolean) loginResult.getOrDefault("success", false)) {
                result.put("success", false);
                result.put("message", "登录失败: " + loginResult.get("message"));
                return result;
            }

            log.info("第三方系统登录成功，开始获取数据");

            // 调用第三方API获取地表监测数据
            SurfaceMonitoringApiResponse apiResponse = surfaceMonitoringExternalService.getLatest7DayMpptShiftingTotal();
            
            if (apiResponse != null && apiResponse.getResult() != null && apiResponse.getResult()) {
                if (apiResponse.getData() != null && !apiResponse.getData().isEmpty()) {
                    // 批量处理数据
                    Map<String, Object> batchResult = batchProcessApiData(
                        apiResponse.getData().stream()
                            .map(item -> {
                                Map<String, Object> map = new HashMap<>();
                                try {
                                    String json = objectMapper.writeValueAsString(item);
                                    map = objectMapper.readValue(json, Map.class);
                                } catch (Exception e) {
                                    log.warn("转换API数据失败", e);
                                }
                                return map;
                            })
                            .collect(java.util.stream.Collectors.toList())
                    );
                    
                    result.put("success", true);
                    result.put("syncTime", new Date());
                    result.putAll(batchResult);
                    
                    log.info("同步第三方地表监测数据完成: {}", batchResult);
                } else {
                    result.put("success", false);
                    result.put("message", "第三方API返回数据为空");
                }
            } else {
                result.put("success", false);
                result.put("message", "调用第三方API失败: " + 
                    (apiResponse != null ? apiResponse.getMessage() : "响应为空"));
            }
            
        } catch (Exception e) {
            log.error("同步第三方地表监测数据异常", e);
            result.put("success", false);
            result.put("message", "同步异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 处理第三方API地表监测数据（插入或更新）
     *
     * @param apiData 第三方API数据
     * @return 处理结果
     */
    @Override
    public boolean processApiData(Map<String, Object> apiData) {
        try {
            log.debug("开始处理第三方API地表监测数据");

            // 解析API数据
            SurfaceMonitoringData data = parseApiData(apiData);
            if (data == null) {
                log.warn("解析第三方API数据失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (data.getDate() == null) {
                log.warn("监测日期为空，跳过处理");
                return false;
            }

            if (!StringUtils.hasText(data.getStationName())) {
                log.warn("基站名称为空，跳过处理");
                return false;
            }

            if (!StringUtils.hasText(data.getWgbh())) {
                log.warn("终端编号为空，跳过处理");
                return false;
            }

            // 执行UPSERT操作
            int result = upsertSurfaceMonitoringData(data);

            if (result > 0) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String dateStr = data.getDate() != null ? dateFormat.format(data.getDate()) : "null";
                log.info("成功处理地表监测数据，日期: {}, 基站: {}, 终端: {}",
                    dateStr, data.getStationName(), data.getWgbh());
                return true;
            } else {
                log.warn("处理地表监测数据失败，日期: {}, 基站: {}, 终端: {}",
                    data.getDate(), data.getStationName(), data.getWgbh());
                return false;
            }

        } catch (Exception e) {
            log.error("处理第三方API地表监测数据异常", e);
            return false;
        }
    }

    /**
     * 批量处理第三方API地表监测数据
     *
     * @param apiDataList 第三方API数据列表
     * @return 处理结果统计
     */
    @Override
    public Map<String, Object> batchProcessApiData(List<Map<String, Object>> apiDataList) {
        Map<String, Object> result = new HashMap<>();
        int totalCount = apiDataList.size();
        int successCount = 0;
        int failCount = 0;

        log.info("开始批量处理地表监测数据，总数: {}", totalCount);

        for (Map<String, Object> apiData : apiDataList) {
            try {
                boolean success = processApiData(apiData);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                failCount++;
                log.warn("批量处理中单条数据失败", e);
            }
        }

        result.put("totalCount", totalCount);
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("message", String.format("批量处理完成，总数: %d, 成功: %d, 失败: %d", totalCount, successCount, failCount));

        log.info("批量处理地表监测数据完成，总数: {}, 成功: {}, 失败: {}", totalCount, successCount, failCount);
        return result;
    }

    /**
     * 解析第三方API数据为实体对象
     *
     * @param apiData 第三方API数据
     * @return 解析后的实体对象
     */
    @Override
    public SurfaceMonitoringData parseApiData(Map<String, Object> apiData) {
        try {
            SurfaceMonitoringData data = new SurfaceMonitoringData();

            // 基本信息映射
            data.setStationName(getStringValueWithDefault(apiData, "station_name", ""));
            data.setWgbh(getStringValueWithDefault(apiData, "wgbh", ""));

            // 日期字段处理
            String dateStr = getStringValue(apiData, "date");
            if (StringUtils.hasText(dateStr)) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    data.setDate(dateFormat.parse(dateStr));
                } catch (Exception e) {
                    log.warn("解析日期失败: {}", dateStr);
                }
            }

            // X轴偏移统计
            data.setX1(getIntegerValue(apiData, "x1"));
            data.setX2(getIntegerValue(apiData, "x2"));
            data.setX3(getIntegerValue(apiData, "x3"));
            data.setX4(getIntegerValue(apiData, "x4"));

            // Y轴偏移统计
            data.setY1(getIntegerValue(apiData, "y1"));
            data.setY2(getIntegerValue(apiData, "y2"));
            data.setY3(getIntegerValue(apiData, "y3"));
            data.setY4(getIntegerValue(apiData, "y4"));

            // 高度偏移统计
            data.setH1(getIntegerValue(apiData, "h1"));
            data.setH2(getIntegerValue(apiData, "h2"));
            data.setH3(getIntegerValue(apiData, "h3"));
            data.setH4(getIntegerValue(apiData, "h4"));

            // 偏移距离总和
            data.setXstackedTotalOffset(getBigDecimalValue(apiData, "xstackedTotalOffset"));
            data.setYstackedTotalOffset(getBigDecimalValue(apiData, "ystackedTotalOffset"));
            data.setHstackedTotalOffset(getBigDecimalValue(apiData, "hstackedTotalOffset"));

            // 原始数据ID
            data.setOriginalId(getIntegerValue(apiData, "id"));

            // 存储原始数据
            try {
                data.setOriginalData(objectMapper.writeValueAsString(apiData));
            } catch (Exception e) {
                log.warn("序列化原始数据失败", e);
            }

            return data;

        } catch (Exception e) {
            log.error("解析第三方API数据失败", e);
            return null;
        }
    }

    /**
     * 获取地表监测统计数据
     *
     * @return 统计结果
     */
    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 按日期统计
            List<Map<String, Object>> dateStats = surfaceMonitoringDataMapper.countByDate();
            statistics.put("dateStats", dateStats);

            // 按基站统计
            List<Map<String, Object>> stationStats = surfaceMonitoringDataMapper.countByStation();
            statistics.put("stationStats", stationStats);

            // 获取最新数据
            List<SurfaceMonitoringData> latestData = surfaceMonitoringDataMapper.selectLatestData(10);
            statistics.put("latestData", latestData);

            // 获取异常数据（偏移量大于10mm的数据）
            List<SurfaceMonitoringData> abnormalData = surfaceMonitoringDataMapper.selectAbnormalOffsetData(10.0);
            statistics.put("abnormalData", abnormalData);

            // 计算总数
            int totalCount = 0;
            for (Map<String, Object> stat : dateStats) {
                totalCount += ((Number) stat.get("count")).intValue();
            }
            statistics.put("totalCount", totalCount);

            log.info("获取地表监测统计数据成功，总数: {}", totalCount);

        } catch (Exception e) {
            log.error("获取地表监测统计数据失败", e);
        }

        return statistics;
    }

    /**
     * 按日期范围查询地表监测数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 地表监测数据集合
     */
    @Override
    public List<SurfaceMonitoringData> selectByDateRange(Date startDate, Date endDate) {
        return surfaceMonitoringDataMapper.selectByDateRange(startDate, endDate);
    }

    /**
     * 按基站名称查询地表监测数据
     *
     * @param stationName 基站名称
     * @return 地表监测数据集合
     */
    @Override
    public List<SurfaceMonitoringData> selectByStationName(String stationName) {
        return surfaceMonitoringDataMapper.selectByStationName(stationName);
    }

    /**
     * 获取最新的监测数据
     *
     * @param limit 限制数量
     * @return 最新监测数据
     */
    @Override
    public List<SurfaceMonitoringData> selectLatestData(Integer limit) {
        return surfaceMonitoringDataMapper.selectLatestData(limit);
    }

    /**
     * 获取偏移异常数据
     *
     * @param threshold 偏移阈值
     * @return 异常数据列表
     */
    @Override
    public List<SurfaceMonitoringData> selectAbnormalOffsetData(Double threshold) {
        return surfaceMonitoringDataMapper.selectAbnormalOffsetData(threshold);
    }

    /**
     * 按日期获取统计信息
     *
     * @param date 日期
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getStatisticsByDate(Date date) {
        return surfaceMonitoringDataMapper.getStatisticsByDate(date);
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取字符串值（带默认值）
     */
    private String getStringValueWithDefault(Map<String, Object> data, String key, String defaultValue) {
        Object value = data.get(key);
        if (value == null || value.toString().trim().isEmpty()) {
            return defaultValue;
        }
        return value.toString().trim();
    }

    /**
     * 获取整数值
     */
    private Integer getIntegerValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return null;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            log.warn("转换整数失败，key: {}, value: {}", key, value);
            return null;
        }
    }

    /**
     * 获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return null;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            log.warn("转换BigDecimal失败，key: {}, value: {}", key, value);
            return null;
        }
    }
}
