package com.ruoyi.common.core.domain.excel;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel导入上下文
 * 用于在导入过程中共享数据
 * 
 * <AUTHOR>
 */
@Data
public class ExcelImportContext {
    
    /** 模板key */
    private String templateKey;
    
    /** 当前处理的行号 */
    private Integer currentRowIndex;
    
    /** 总行数 */
    private Integer totalRows;
    
    /** 缓存数据，用于存储查询结果等 */
    private Map<String, Object> cache = new HashMap<>();
    
    /** 选项数据映射 */
    private Map<String, List<ExcelOptionInfo>> optionMap = new HashMap<>();

    /** 选项映射数据 */
    private Map<String, ExcelOptionMapping> optionMappings = new HashMap<>();
    
    /** 扩展属性 */
    private Map<String, Object> attributes = new HashMap<>();

    private List<ExcelFieldInfo> fields = new ArrayList<>();
    
    /**
     * 获取缓存数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getCache(String key) {
        return (T) cache.get(key);
    }
    
    /**
     * 设置缓存数据
     */
    public void setCache(String key, Object value) {
        cache.put(key, value);
    }
    
    /**
     * 获取选项数据
     */
    public List<ExcelOptionInfo> getOptions(String key) {
        return optionMap.get(key);
    }
    
    /**
     * 设置选项数据
     */
    public void setOptions(String key, List<ExcelOptionInfo> options) {
        optionMap.put(key, options);
    }
    
    /**
     * 获取扩展属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }
    
    /**
     * 设置扩展属性
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    /**
     * 检查缓存是否存在
     */
    public boolean hasCache(String key) {
        return cache.containsKey(key);
    }
    
    /**
     * 检查选项是否存在
     */
    public boolean hasOptions(String key) {
        return optionMap.containsKey(key);
    }
    
    /**
     * 清空缓存
     */
    public void clearCache() {
        cache.clear();
    }
    
    /**
     * 清空选项
     */
    public void clearOptions() {
        optionMap.clear();
    }
}
