package com.ruoyi.lxbi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalTime;
import java.util.Date;

/**
 * 矿井提升数据对象 data_mine_hoisting
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataMineHoisting extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 提升数据ID */
    private Long id;

    /** 作业时段ID */
    private Long workingPeriodId;

    /** 作业日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "作业日期", sort = 1,mergeByValue = true, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /** 运行时间（分钟） */
    @Excel(name = "运行时间")
    private Long operationTime;

    /** 故障时长（分钟） */
    @Excel(name = "故障时长")
    private Long faultTime;

    /** 提升斗数 */
    @Excel(name = "提升斗数")
    private Long buckets;

    /** 提升量（吨） */
    @Excel(name = "提升量")
    private Double weight;

    /** 故障原因 */
    @Excel(name = "故障原因")
    private String faultReason;

    /** 故障开始时间 */
    @Excel(name = "故障开始时间")
    private LocalTime faultStartTime;

    /** 故障结束时间 */
    @Excel(name = "故障结束时间")
    private LocalTime faultEndTime;

}
