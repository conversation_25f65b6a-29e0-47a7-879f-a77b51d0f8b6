# 地表监测数据同步登录认证说明

## 概述

本文档说明了地表监测数据同步功能的登录认证机制。在调用第三方API获取地表监测数据之前，系统会自动进行登录认证，获取访问token。

## 修改内容

### 1. SurfaceMonitoringExternalService 更新

**新增功能**:
- 添加了 `authToken` 字段用于存储登录后的认证token
- 新增了 `login()` 方法实现登录认证
- 修改了 `getLatest7DayMpptShiftingTotal()` 方法，在请求头中添加认证token

### 2. SurfaceMonitoringDataServiceImpl 更新

**修改内容**:
- 在 `syncSurfaceMonitoringData()` 方法中添加了登录步骤
- 登录失败时会直接返回错误信息，不再继续执行数据同步

## 登录认证流程

### 1. 登录请求

```java
// 登录凭据
{
  "username": "meike",
  "password": "meike"
}

// 请求方式
POST /login
Content-Type: application/json
User-Agent: LXBI-System/1.0
```

### 2. 登录响应处理

系统会解析登录响应，提取认证token：

```java
// 支持的响应格式
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  // 或者
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 3. Token使用

登录成功后，后续的API请求会在请求头中携带认证token：

```java
// 请求头格式（支持多种格式）
Authorization: Bearer {token}
// 或者根据实际API要求：
// token: {token}
// X-Auth-Token: {token}
```

## 数据同步流程

### 完整流程图

```
开始同步
    ↓
调用登录API
    ↓
登录成功？ → 否 → 返回登录失败
    ↓ 是
保存认证token
    ↓
调用数据API（携带token）
    ↓
获取数据成功？ → 否 → 返回数据获取失败
    ↓ 是
处理和保存数据
    ↓
返回同步成功
```

### 代码实现

```java
@Override
public Map<String, Object> syncSurfaceMonitoringData() {
    Map<String, Object> result = new HashMap<>();
    
    try {
        log.info("开始同步第三方地表监测数据");
        
        // 1. 先进行登录认证
        Map<String, Object> loginResult = surfaceMonitoringExternalService.login();
        if (!(Boolean) loginResult.getOrDefault("success", false)) {
            result.put("success", false);
            result.put("message", "登录失败: " + loginResult.get("message"));
            return result;
        }
        
        log.info("第三方系统登录成功，开始获取数据");
        
        // 2. 调用第三方API获取地表监测数据（自动携带token）
        SurfaceMonitoringApiResponse apiResponse = 
            surfaceMonitoringExternalService.getLatest7DayMpptShiftingTotal();
        
        // 3. 处理返回的数据...
        
    } catch (Exception e) {
        log.error("同步第三方地表监测数据异常", e);
        result.put("success", false);
        result.put("message", "同步异常: " + e.getMessage());
    }
    
    return result;
}
```

## 错误处理

### 1. 登录失败处理

```java
// 登录失败的返回格式
{
  "success": false,
  "message": "登录失败: 用户名或密码错误"
}
```

### 2. Token失效处理

如果在数据请求过程中token失效，系统会返回相应的错误信息。建议在实际项目中添加token刷新机制：

```java
// 建议的token刷新逻辑
if (response.getStatusCode() == HttpStatus.UNAUTHORIZED) {
    // Token可能已失效，重新登录
    Map<String, Object> reLoginResult = login();
    if ((Boolean) reLoginResult.get("success")) {
        // 重新发送原请求
        return retryRequest();
    }
}
```

## 配置说明

### 应用配置文件

```yaml
# application.yml
surface:
  monitoring:
    api:
      base-url: http://third-party-api.example.com
      timeout: 30000
      
# 登录凭据（建议加密存储）
third-party:
  credentials:
    username: meike
    password: meike  # 实际项目中应该加密存储
```

### 安全建议

1. **密码加密**: 在实际项目中，密码应该加密存储
2. **Token缓存**: 可以将token缓存到Redis中，避免频繁登录
3. **Token刷新**: 实现token自动刷新机制
4. **请求重试**: 添加请求失败重试机制

## 使用示例

### 1. 手动触发同步

```bash
curl -X POST "http://localhost:8080/lxbi/surface-monitoring/sync" \
  -H "Content-Type: application/json"
```

### 2. 定时任务同步

```java
@Scheduled(cron = "0 0 */6 * * ?") // 每6小时执行一次
public void scheduledSync() {
    log.info("开始定时同步地表监测数据");
    Map<String, Object> result = surfaceMonitoringDataService.syncSurfaceMonitoringData();
    log.info("定时同步结果: {}", result);
}
```

### 3. 同步结果示例

```json
// 成功响应
{
  "success": true,
  "syncTime": "2025-08-25T10:30:00",
  "totalRecords": 168,
  "successCount": 165,
  "failureCount": 3,
  "message": "数据同步完成"
}

// 失败响应
{
  "success": false,
  "message": "登录失败: 用户名或密码错误"
}
```

## 监控和日志

### 日志记录

系统会记录详细的登录和同步日志：

```
2025-08-25 10:30:00 INFO  - 开始同步第三方地表监测数据
2025-08-25 10:30:01 INFO  - 开始登录第三方地表监测系统: http://api.example.com/login
2025-08-25 10:30:02 INFO  - 第三方系统登录成功，token已保存
2025-08-25 10:30:02 INFO  - 第三方系统登录成功，开始获取数据
2025-08-25 10:30:05 INFO  - 同步第三方地表监测数据完成: {totalRecords=168, successCount=165}
```

### 监控指标

建议监控以下指标：
- 登录成功率
- 数据同步成功率
- API响应时间
- Token有效期

## 总结

通过添加登录认证机制，地表监测数据同步功能现在具备了：

1. **自动登录**: 在数据同步前自动进行登录认证
2. **Token管理**: 自动获取和使用认证token
3. **错误处理**: 完善的登录失败和认证失效处理
4. **安全性**: 支持多种token格式和认证方式
5. **可扩展性**: 为token刷新和缓存预留了接口

这确保了数据同步过程的安全性和可靠性，为后续的功能扩展奠定了基础。
