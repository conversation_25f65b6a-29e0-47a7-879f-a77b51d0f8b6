package com.ruoyi.lxbi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 破碎数据管理对象 data_crushing_operation
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataCrushingOperation extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 破碎数据ID */
    private Long id;

    /** 作业时段ID */
    private Long workingPeriodId;

    /** 作业日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "作业日期", dateFormat = "yyyy-MM-dd", mergeByValue = true, sort = 1)
    private Date operationDate;

    /** 运行时间（分钟） */
    @Excel(name = "运行时间")
    private Long operationTime;

    /** 破碎量（吨） */
    @Excel(name = "破碎量")
    private Double crushingVolume;

    /** 故障时长（分钟） */
    @Excel(name = "故障时长")
    private Long faultTime;

    /** 故障原因 */
    @Excel(name = "故障原因")
    private String faultReason;

    /** 故障开始时间 */
    @Excel(name = "故障开始时间")
    private String faultStartTime;

    /** 故障结束时间 */
    @Excel(name = "故障结束时间")
    private String faultEndTime;

}
