package com.ruoyi.lxbi.service;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanStopeMonthly;
import com.ruoyi.lxbi.domain.request.PlanStopeMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanStopeMonthlyVo;

/**
 * 采场月度计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface IPlanStopeMonthlyService 
{
    /**
     * 查询采场月度计划
     * 
     * @param id 采场月度计划主键
     * @return 采场月度计划
     */
    public PlanStopeMonthly selectPlanStopeMonthlyById(Long id);

    /**
     * 查询采场月度计划列表
     *
     * @param planStopeMonthly 采场月度计划
     * @return 采场月度计划集合
     */
    public List<PlanStopeMonthlyVo> selectPlanStopeMonthlyList(PlanStopeMonthly planStopeMonthly);

    /**
     * 新增采场月度计划
     * 
     * @param planStopeMonthly 采场月度计划
     * @return 结果
     */
    public int insertPlanStopeMonthly(PlanStopeMonthly planStopeMonthly);

    /**
     * 修改采场月度计划
     * 
     * @param planStopeMonthly 采场月度计划
     * @return 结果
     */
    public int updatePlanStopeMonthly(PlanStopeMonthly planStopeMonthly);

    /**
     * 批量删除采场月度计划
     * 
     * @param ids 需要删除的采场月度计划主键集合
     * @return 结果
     */
    public int deletePlanStopeMonthlyByIds(Long[] ids);

    /**
     * 删除采场月度计划信息
     *
     * @param id 采场月度计划主键
     * @return 结果
     */
    public int deletePlanStopeMonthlyById(Long id);

    /**
     * 批量保存采场月度计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    public int batchSavePlanStopeMonthly(List<PlanStopeMonthlyBatchDto> batchDataList);
}
