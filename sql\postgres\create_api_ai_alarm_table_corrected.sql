-- AI报警数据表 (PostgreSQL版本)
-- 用于存储从第三方AI报警系统同步的数据

-- 删除表（如果存在）
DROP TABLE IF EXISTS api_ai_alarm;

-- 创建表
CREATE TABLE api_ai_alarm (
    -- 主键ID
    id BIGSERIAL PRIMARY KEY,
    
    -- 第三方系统的原始ID
    external_id VARCHAR(100),
    
    -- 报警基本信息
    alarm_type VARCHAR(50),
    alarm_level VARCHAR(20),
    alarm_status VARCHAR(20),
    alarm_title VARCHAR(200),
    alarm_description TEXT,
    
    -- 时间信息
    alarm_time TIMESTAMP,
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    
    -- 位置信息
    location_name VARCHAR(100),
    location_code VARCHAR(50),
    coordinate_x DECIMAL(10,6),
    coordinate_y DECIMAL(10,6),
    coordinate_z DECIMAL(10,6),
    
    -- 设备信息
    device_id VARCHAR(100),
    device_name VARCHAR(100),
    device_type VARCHAR(50),
    
    -- 报警详细数据
    alarm_value DECIMAL(10,4),
    threshold_value DECIMAL(10,4),
    unit VARCHAR(20),
    
    -- 处理信息
    handler_name VARCHAR(100),
    handle_time TIMESTAMP,
    handle_status VARCHAR(20),
    handle_remark TEXT,
    
    -- 图片和视频
    image_url TEXT,
    video_url TEXT,
    
    -- 扩展字段
    extra_data JSONB,
    
    -- 原始数据
    raw_data JSONB,
    
    -- 数据同步信息
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_status VARCHAR(20) DEFAULT 'SUCCESS',
    sync_source VARCHAR(50) DEFAULT 'AUTO',
    
    -- 数据版本和状态
    data_version VARCHAR(20),
    is_deleted BOOLEAN DEFAULT FALSE,
    
    -- 审计字段
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_api_ai_alarm_external_id ON api_ai_alarm(external_id);
CREATE INDEX idx_api_ai_alarm_alarm_time ON api_ai_alarm(alarm_time);
CREATE INDEX idx_api_ai_alarm_alarm_type ON api_ai_alarm(alarm_type);
CREATE INDEX idx_api_ai_alarm_alarm_level ON api_ai_alarm(alarm_level);
CREATE INDEX idx_api_ai_alarm_sync_time ON api_ai_alarm(sync_time);
CREATE INDEX idx_api_ai_alarm_device_id ON api_ai_alarm(device_id);
CREATE INDEX idx_api_ai_alarm_location_code ON api_ai_alarm(location_code);
CREATE INDEX idx_api_ai_alarm_created_at ON api_ai_alarm(created_at);

-- 创建复合索引
CREATE INDEX idx_api_ai_alarm_time_type ON api_ai_alarm(alarm_time, alarm_type);
CREATE INDEX idx_api_ai_alarm_status_level ON api_ai_alarm(alarm_status, alarm_level);

-- 创建唯一索引（防止重复数据）
CREATE UNIQUE INDEX idx_api_ai_alarm_external_id_unique ON api_ai_alarm(external_id) WHERE is_deleted = FALSE;

-- 添加表注释
COMMENT ON TABLE api_ai_alarm IS 'AI报警数据表，存储从第三方AI报警系统同步的数据';

-- 添加字段注释
COMMENT ON COLUMN api_ai_alarm.id IS '主键ID';
COMMENT ON COLUMN api_ai_alarm.external_id IS '第三方系统的原始ID';
COMMENT ON COLUMN api_ai_alarm.alarm_type IS '报警类型';
COMMENT ON COLUMN api_ai_alarm.alarm_level IS '报警等级';
COMMENT ON COLUMN api_ai_alarm.alarm_status IS '报警状态';
COMMENT ON COLUMN api_ai_alarm.alarm_title IS '报警标题';
COMMENT ON COLUMN api_ai_alarm.alarm_description IS '报警描述';
COMMENT ON COLUMN api_ai_alarm.alarm_time IS '报警时间';
COMMENT ON COLUMN api_ai_alarm.create_time IS '创建时间';
COMMENT ON COLUMN api_ai_alarm.update_time IS '更新时间';
COMMENT ON COLUMN api_ai_alarm.location_name IS '位置名称';
COMMENT ON COLUMN api_ai_alarm.location_code IS '位置编码';
COMMENT ON COLUMN api_ai_alarm.coordinate_x IS 'X坐标';
COMMENT ON COLUMN api_ai_alarm.coordinate_y IS 'Y坐标';
COMMENT ON COLUMN api_ai_alarm.coordinate_z IS 'Z坐标';
COMMENT ON COLUMN api_ai_alarm.device_id IS '设备ID';
COMMENT ON COLUMN api_ai_alarm.device_name IS '设备名称';
COMMENT ON COLUMN api_ai_alarm.device_type IS '设备类型';
COMMENT ON COLUMN api_ai_alarm.alarm_value IS '报警数值';
COMMENT ON COLUMN api_ai_alarm.threshold_value IS '阈值';
COMMENT ON COLUMN api_ai_alarm.unit IS '单位';
COMMENT ON COLUMN api_ai_alarm.handler_name IS '处理人';
COMMENT ON COLUMN api_ai_alarm.handle_time IS '处理时间';
COMMENT ON COLUMN api_ai_alarm.handle_status IS '处理状态';
COMMENT ON COLUMN api_ai_alarm.handle_remark IS '处理备注';
COMMENT ON COLUMN api_ai_alarm.image_url IS '报警图片URL';
COMMENT ON COLUMN api_ai_alarm.video_url IS '报警视频URL';
COMMENT ON COLUMN api_ai_alarm.extra_data IS '扩展数据(JSON格式)';
COMMENT ON COLUMN api_ai_alarm.raw_data IS '原始数据(完整的API响应)';
COMMENT ON COLUMN api_ai_alarm.sync_time IS '数据同步时间';
COMMENT ON COLUMN api_ai_alarm.sync_status IS '同步状态';
COMMENT ON COLUMN api_ai_alarm.sync_source IS '同步来源(AUTO-自动同步/MANUAL-手动同步)';
COMMENT ON COLUMN api_ai_alarm.data_version IS '数据版本';
COMMENT ON COLUMN api_ai_alarm.is_deleted IS '是否删除';
COMMENT ON COLUMN api_ai_alarm.created_by IS '创建人';
COMMENT ON COLUMN api_ai_alarm.created_at IS '创建时间';
COMMENT ON COLUMN api_ai_alarm.updated_by IS '更新人';
COMMENT ON COLUMN api_ai_alarm.updated_at IS '更新时间';

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_api_ai_alarm_updated_at 
    BEFORE UPDATE ON api_ai_alarm 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 插入测试数据（可选）
INSERT INTO api_ai_alarm (
    external_id, alarm_type, alarm_level, alarm_status, alarm_title, alarm_description,
    alarm_time, device_id, device_name, location_name, sync_source
) VALUES (
    'TEST_001', 'FIRE', 'HIGH', 'ACTIVE', '火灾报警测试', '这是一条测试数据',
    CURRENT_TIMESTAMP, 'DEV_001', '烟感器001', '1号厂房', 'MANUAL'
);

-- 查询验证
SELECT 
    id, external_id, alarm_type, alarm_level, alarm_title, 
    device_name, location_name, sync_time, sync_source
FROM api_ai_alarm 
WHERE external_id = 'TEST_001';

-- 显示表结构
\d api_ai_alarm;
