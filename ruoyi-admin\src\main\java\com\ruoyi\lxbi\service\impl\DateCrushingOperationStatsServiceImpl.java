package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DateCrushingOperationStatsRequest;
import com.ruoyi.lxbi.domain.response.DateCrushingOperationStats;
import com.ruoyi.lxbi.domain.response.DateCrushingOperationPeriodStats;
import com.ruoyi.lxbi.mapper.DateCrushingOperationStatsMapper;
import com.ruoyi.lxbi.service.IDateCrushingOperationStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 破碎操作数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DateCrushingOperationStatsServiceImpl implements IDateCrushingOperationStatsService {
    @Autowired
    private DateCrushingOperationStatsMapper dateCrushingOperationStatsMapper;
    
    /**
     * 查询统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 统计数据集合
     */
    @Override
    public List<DateCrushingOperationStats> selectStatsList(DateCrushingOperationStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dateCrushingOperationStatsMapper.selectDailyStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dateCrushingOperationStatsMapper.selectWeeklyStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dateCrushingOperationStatsMapper.selectYearlyStats(request.getStartDate(), request.getEndDate());
        } else {
            return dateCrushingOperationStatsMapper.selectMonthlyStats(request.getStartDate(), request.getEndDate());
        }
    }
    
    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    @Override
    public List<DateCrushingOperationPeriodStats> selectPeriodStatsList(DateCrushingOperationStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dateCrushingOperationStatsMapper.selectDailyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dateCrushingOperationStatsMapper.selectWeeklyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dateCrushingOperationStatsMapper.selectYearlyPeriodStats(request.getStartDate(), request.getEndDate());
        } else {
            return dateCrushingOperationStatsMapper.selectMonthlyPeriodStats(request.getStartDate(), request.getEndDate());
        }
    }

}
