package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.PlanMineralSaleMonthly;

/**
 * 选矿销售月计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface PlanMineralSaleMonthlyMapper 
{
    /**
     * 查询选矿销售月计划
     * 
     * @param id 选矿销售月计划主键
     * @return 选矿销售月计划
     */
    public PlanMineralSaleMonthly selectPlanMineralSaleMonthlyById(Long id);

    /**
     * 查询选矿销售月计划列表
     * 
     * @param planMineralSaleMonthly 选矿销售月计划
     * @return 选矿销售月计划集合
     */
    public List<PlanMineralSaleMonthly> selectPlanMineralSaleMonthlyList(PlanMineralSaleMonthly planMineralSaleMonthly);

    /**
     * 新增选矿销售月计划
     * 
     * @param planMineralSaleMonthly 选矿销售月计划
     * @return 结果
     */
    public int insertPlanMineralSaleMonthly(PlanMineralSaleMonthly planMineralSaleMonthly);

    /**
     * 修改选矿销售月计划
     * 
     * @param planMineralSaleMonthly 选矿销售月计划
     * @return 结果
     */
    public int updatePlanMineralSaleMonthly(PlanMineralSaleMonthly planMineralSaleMonthly);

    /**
     * 删除选矿销售月计划
     * 
     * @param id 选矿销售月计划主键
     * @return 结果
     */
    public int deletePlanMineralSaleMonthlyById(Long id);

    /**
     * 批量删除选矿销售月计划
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanMineralSaleMonthlyByIds(Long[] ids);
}
