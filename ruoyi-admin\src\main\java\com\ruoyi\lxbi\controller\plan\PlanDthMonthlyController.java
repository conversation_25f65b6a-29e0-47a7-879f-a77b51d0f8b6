package com.ruoyi.lxbi.controller.plan;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.PlanDthMonthly;
import com.ruoyi.lxbi.domain.request.PlanDthMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanDthMonthlyVo;
import com.ruoyi.lxbi.service.IPlanDthMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 潜孔月计划Controller
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/plan/planDthMonthly")
public class PlanDthMonthlyController extends BaseController {
    @Autowired
    private IPlanDthMonthlyService planDthMonthlyService;

    /**
     * 查询潜孔月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planDthMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlanDthMonthly planDthMonthly) {
        startPage();
        List<PlanDthMonthlyVo> list = planDthMonthlyService.selectPlanDthMonthlyList(planDthMonthly);
        return getDataTable(list);
    }

    /**
     * 导出潜孔月计划列表
     */
    @PreAuthorize("@ss.hasPermi('plan:planDthMonthly:export')")
    @Log(title = "潜孔月计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlanDthMonthly planDthMonthly) {
        List<PlanDthMonthlyVo> list = planDthMonthlyService.selectPlanDthMonthlyList(planDthMonthly);
        ExcelUtil<PlanDthMonthlyVo> util = new ExcelUtil<PlanDthMonthlyVo>(PlanDthMonthlyVo.class);
        util.exportExcel(response, list, "潜孔月计划数据");
    }

    /**
     * 获取潜孔月计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('plan:planDthMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(planDthMonthlyService.selectPlanDthMonthlyById(id));
    }

    /**
     * 新增潜孔月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planDthMonthly:add')")
    @Log(title = "潜孔月计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlanDthMonthly planDthMonthly)
    {
        return toAjax(planDthMonthlyService.insertPlanDthMonthly(planDthMonthly));
    }

    /**
     * 修改潜孔月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planDthMonthly:edit')")
    @Log(title = "潜孔月计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlanDthMonthly planDthMonthly)
    {
        return toAjax(planDthMonthlyService.updatePlanDthMonthly(planDthMonthly));
    }

    /**
     * 删除潜孔月计划
     */
    @PreAuthorize("@ss.hasPermi('plan:planDthMonthly:remove')")
    @Log(title = "潜孔月计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(planDthMonthlyService.deletePlanDthMonthlyByIds(ids));
    }

    /**
     * 批量保存潜孔月计划（增删改查）
     * 传入批量列表，验证是否同一个月份的数据，然后查询这个月份的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('plan:planDthMonthly:edit')")
    @Log(title = "潜孔月计划批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<PlanDthMonthlyBatchDto> batchDataList)
    {
        try {
            int result = planDthMonthlyService.batchSavePlanDthMonthly(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
