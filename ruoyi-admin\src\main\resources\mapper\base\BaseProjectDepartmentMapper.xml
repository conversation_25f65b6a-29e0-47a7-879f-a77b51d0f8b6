<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.BaseProjectDepartmentMapper">
    
    <resultMap type="BaseProjectDepartment" id="BaseProjectDepartmentResult">
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="projectDepartmentName"    column="project_department_name"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectBaseProjectDepartmentVo">
        select project_department_id, project_department_name, status, create_by, create_time, update_by, update_time, start_time, end_time, is_delete from base_project_department
    </sql>

    <select id="selectBaseProjectDepartmentList" parameterType="BaseProjectDepartment" resultMap="BaseProjectDepartmentResult">
        <include refid="selectBaseProjectDepartmentVo"/>
        <where>  
            <if test="projectDepartmentName != null  and projectDepartmentName != ''"> and project_department_name like concat('%', #{projectDepartmentName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and start_time between #{params.beginStartTime}::date and #{params.endStartTime}::date</if>
            <if test="params.beginEndTime != null and params.beginEndTime != '' and params.endEndTime != null and params.endEndTime != ''"> and end_time between #{params.beginEndTime}::date and #{params.endEndTime}::date</if>
            and is_delete = 0
        </where>
    </select>
    
    <select id="selectBaseProjectDepartmentByProjectDepartmentId" parameterType="Long" resultMap="BaseProjectDepartmentResult">
        <include refid="selectBaseProjectDepartmentVo"/>
        where project_department_id = #{projectDepartmentId}
    </select>
    <select id="getBaseProjectDepartmentByProjectDepartmentIds" resultType="java.lang.Integer">
        select count(1) as count from base_project_department where project_department_id =
        <foreach item="projectDepartmentId" collection="array" open="(" separator="," close=")">
            #{projectDepartmentId}
        </foreach>
        and is_delete = 0 and status = 1;
    </select>

    <insert id="insertBaseProjectDepartment" parameterType="BaseProjectDepartment" useGeneratedKeys="true" keyProperty="projectDepartmentId">
        insert into base_project_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentName != null">project_department_name,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectDepartmentName != null">#{projectDepartmentName},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updateBaseProjectDepartment" parameterType="BaseProjectDepartment">
        update base_project_department
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectDepartmentName != null">project_department_name = #{projectDepartmentName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where project_department_id = #{projectDepartmentId}
    </update>

    <delete id="deleteBaseProjectDepartmentByProjectDepartmentId" parameterType="Long">
        update base_project_department set is_delete=1 where project_department_id = #{projectDepartmentId}
    </delete>

    <delete id="deleteBaseProjectDepartmentByProjectDepartmentIds" parameterType="String">
        update base_project_department set is_delete=1 where project_department_id in
        <foreach item="projectDepartmentId" collection="array" open="(" separator="," close=")">
            #{projectDepartmentId}
        </foreach>
    </delete>
</mapper>