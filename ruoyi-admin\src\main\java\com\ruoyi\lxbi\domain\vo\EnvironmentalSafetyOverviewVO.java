package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 环境安全概览VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EnvironmentalSafetyOverviewVO {
    
    /**
     * 一氧化碳数值
     */
    private Long carbonMonoxideValue;
    
    /**
     * 二氧化碳数值
     */
    private Long carbonDioxideValue;
    
    /**
     * 一氧化氮数值
     */
    private Long nitrogenMonoxideValue;
    
    /**
     * 氧气数值
     */
    private Long oxygenValue;
    
    /**
     * 温度数值
     */
    private Long temperatureValue;
    
    /**
     * 压力数值
     */
    private Long pressureValue;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 统计周期
     */
    private String period;
}
