<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataIronConcentrateStatsMapper">

    <!-- 总体统计结果映射（含计划量） -->
    <resultMap type="DataIronConcentrateTotalWithPlanStats" id="DataIronConcentrateTotalWithPlanStatsResult">
        <result property="year"                     column="year"                   />
        <result property="month"                    column="month"                  />
        <result property="weekNumber"               column="week"                   />
        <result property="operationDate"            column="operation_date"         />
        <result property="weekStartDate"            column="week_start_date"        />
        <result property="weekEndDate"              column="week_end_date"          />
        <result property="recordCount"              column="record_count"           />
        <result property="totalProductionVolume"    column="total_production_volume" />
        <result property="avgTfeContent"            column="avg_tfe_content"        />
        <result property="avgFinenessMinus500"      column="avg_fineness_minus_500" />
        <result property="avgMoistureContent"       column="avg_moisture_content"   />
        <result property="planProductionVolume"     column="plan_production_volume" />
    </resultMap>

    <!-- ========== 总体统计查询方法（含计划量） ========== -->
    
    <!-- 查询总体统计数据列表（含计划量） (日) -->
    <select id="selectDailyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataIronConcentrateTotalWithPlanStatsResult">
        SELECT 
            NULL as year,
            NULL as month,
            NULL as week,
            v.operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.record_count,
            v.total_production_volume,
            v.avg_tfe_content,
            v.avg_fineness_minus_500,
            v.avg_moisture_content,
            COALESCE(p.plan_production_volume, 0) as plan_production_volume
        FROM vdata_iron_concentrate_daily_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                SUM(pmsm.iron_concentrate_volume) / EXTRACT(DAY FROM DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1)) + INTERVAL '1 month' - INTERVAL '1 day') as plan_production_volume
            FROM plan_mineral_sale_monthly pmsm
            CROSS JOIN get_financial_month(TO_DATE(pmsm.plan_date, 'YYYYMM')) AS fm
            GROUP BY fm.financial_year, fm.financial_month
        ) p ON EXTRACT(YEAR FROM v.operation_date) = p.year AND EXTRACT(MONTH FROM v.operation_date) = p.month
        <where>
            <if test="startDate != null">
                AND v.operation_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.operation_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.operation_date
    </select>

    <!-- 查询总体统计数据列表（含计划量） (周) -->
    <select id="selectWeeklyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataIronConcentrateTotalWithPlanStatsResult">
        SELECT 
            v.year,
            NULL as month,
            v.week_number as week,
            NULL as operation_date,
            v.week_start_date,
            v.week_end_date,
            v.record_count,
            v.total_production_volume,
            v.avg_tfe_content,
            v.avg_fineness_minus_500,
            v.avg_moisture_content,
            COALESCE(p.plan_production_volume, 0) as plan_production_volume
        FROM vdata_iron_concentrate_weekly_stats v
        LEFT JOIN (
            SELECT
                wk.week_year as year,
                wk.week_number as week,
                SUM(pmsm.iron_concentrate_volume) / 4 as plan_production_volume
            FROM plan_mineral_sale_monthly pmsm
            CROSS JOIN get_financial_month(TO_DATE(pmsm.plan_date, 'YYYYMM')) AS fm
            CROSS JOIN get_week_thu_to_wed(MAKE_DATE(fm.financial_year, fm.financial_month, 15)) AS wk
            GROUP BY wk.week_year, wk.week_number
        ) p ON v.year = p.year AND v.week_number = p.week
        <where>
            <if test="startDate != null">
                AND v.week_start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.week_end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.week_number
    </select>

    <!-- 查询总体统计数据列表（含计划量） (月) -->
    <select id="selectMonthlyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataIronConcentrateTotalWithPlanStatsResult">
        SELECT
            v.year,
            v.month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.record_count,
            v.total_production_volume,
            v.avg_tfe_content,
            v.avg_fineness_minus_500,
            v.avg_moisture_content,
            COALESCE(p.plan_production_volume, 0) as plan_production_volume
        FROM vdata_iron_concentrate_monthly_stats v
        LEFT JOIN (
            SELECT
                fm.financial_year as year,
                fm.financial_month as month,
                SUM(pmsm.iron_concentrate_volume) as plan_production_volume
            FROM plan_mineral_sale_monthly pmsm
            CROSS JOIN get_financial_month(TO_DATE(pmsm.plan_date, 'YYYYMM')) AS fm
            GROUP BY fm.financial_year, fm.financial_month
        ) p ON v.year = p.year AND v.month = p.month
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, v.month, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year, v.month
    </select>

    <!-- 查询总体统计数据列表（含计划量） (年) -->
    <select id="selectYearlyTotalWithPlanStats" parameterType="java.util.Date" resultMap="DataIronConcentrateTotalWithPlanStatsResult">
        SELECT
            v.year,
            NULL as month,
            NULL as week,
            NULL as operation_date,
            NULL as week_start_date,
            NULL as week_end_date,
            v.record_count,
            v.total_production_volume,
            v.avg_tfe_content,
            v.avg_fineness_minus_500,
            v.avg_moisture_content,
            COALESCE(p.plan_production_volume, 0) as plan_production_volume
        FROM vdata_iron_concentrate_yearly_stats v
        LEFT JOIN (
            SELECT
                fy.financial_year as year,
                SUM(pmsm.iron_concentrate_volume) as plan_production_volume
            FROM plan_mineral_sale_monthly pmsm
            CROSS JOIN get_financial_year(TO_DATE(pmsm.plan_date, 'YYYYMM')) AS fy
            GROUP BY fy.financial_year
        ) p ON v.year = p.year
        <where>
            <if test="startDate != null">
                AND MAKE_DATE(v.year, 1, 1) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND MAKE_DATE(v.year, 1, 1) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY v.year
    </select>

</mapper>
