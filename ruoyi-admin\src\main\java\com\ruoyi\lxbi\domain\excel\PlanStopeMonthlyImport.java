package com.ruoyi.lxbi.domain.excel;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelImportTemplate;
import com.ruoyi.common.annotation.ExcelRequired;
import com.ruoyi.common.annotation.ExcelSelected;
import lombok.Data;

/**
 * 采场月度计划导入对象
 */
@Data
@ExcelImportTemplate(
        key = "plan_stope_monthly",
        name = "采场月度计划导入",
        description = "用于导入采场月度计划数据",
        sheetName = "采场月度计划"
)
public class PlanStopeMonthlyImport {

    /**
     * 计划月份
     */
    @ExcelProperty(value = "计划月份", index = 0)
    @ExcelRequired(message = "计划月份不能为空")
    @ExcelSelected(prompt = "请输入计划月份，格式：yyyyMM，例如：202501")
    private String planDate;

    /**
     * 采场ID（存储值）
     */
    @ExcelSelected(
            optionKey = "stope",
            link = ExcelSelected.FieldLinkType.VALUE
    )
    @ExcelIgnore
    private Long stopeId;

    /**
     * 采场名称（显示值）
     */
    @ExcelProperty(value = "采场", index = 1)
    @ExcelRequired(message = "采场不能为空")
    @ExcelSelected(
            optionKey = "stope",
            prompt = "请选择采场",
            link = ExcelSelected.FieldLinkType.LABEL
    )
    private String stopeName;

    /**
     * 出矿量
     */
    @ExcelProperty(value = "出矿量", index = 2)
    @ExcelRequired(message = "出矿量不能为空")
    @ExcelSelected(prompt = "请输入出矿量")
    private String oreOutput;
}
