package com.ruoyi.lxbi.admin.service.impl;

import com.ruoyi.lxbi.admin.service.IMicroseismicStatService;
import com.ruoyi.lxbi.domain.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.lxbi.admin.mapper.MicroseismicStatMapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 微震统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class MicroseismicStatServiceImpl implements IMicroseismicStatService {

    @Autowired
    private MicroseismicStatMapper microseismicStatMapper;

    /**
     * 获取微震概览统计
     */
    @Override
    public MicroseismicOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate) {
        try {
            log.info("获取微震概览统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 真实查询：从 excel_microseismic_events 统计
            long networkEventCount = microseismicStatMapper.countEvents(startDate, endDate);
            long targetEventCount = microseismicStatMapper.countTargetEvents(startDate, endDate);
            long deviceEventTotal = microseismicStatMapper.sumDeviceEventTotal(startDate, endDate);
            long microseismicDeviceCount = microseismicStatMapper.countMicroseismicDevice(startDate, endDate);

            MicroseismicOverviewVO overview = new MicroseismicOverviewVO();
            overview.setNetworkEventCount(networkEventCount);
            overview.setTargetEventCount(targetEventCount);
            overview.setDeviceEventTotal(deviceEventTotal);
            overview.setMicroseismicDeviceCount(microseismicDeviceCount);
            overview.setStartDate(startDate);
            overview.setEndDate(endDate);
            overview.setPeriod(viewType);

            return overview;
        } catch (Exception e) {
            log.error("获取微震概览统计失败", e);
            throw new RuntimeException("获取微震概览统计失败: " + e.getMessage());
        }
    }



    /**
     * 获取微震事件分布统计
     */
    @Override
    public List<MicroseismicEventDistributionVO> getEventDistribution(String viewType, String startDate, String endDate) {
        try {
            log.info("获取微震事件分布统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 查询真实数据：各等级震级次数（使用 @MapKey 返回 Map）
            Map<String, Map<String, Object>> magnitudeLevelData = microseismicStatMapper.getMagnitudeLevelDistribution(startDate, endDate);

            List<MicroseismicEventDistributionVO> distributionList = new ArrayList<>();

            // 按固定顺序返回 1级-5级，缺失的补 0
            String[] levels = {"1级", "2级", "3级", "4级", "5级"};
            for (int i = 0; i < levels.length; i++) {
                String level = levels[i];
                Map<String, Object> row = magnitudeLevelData != null ? magnitudeLevelData.get(level) : null;
                long eventCount = 0L;
                if (row != null && row.get("event_count") != null) {
                    eventCount = ((Number) row.get("event_count")).longValue();
                }

                MicroseismicEventDistributionVO distribution = new MicroseismicEventDistributionVO();
                distribution.setTimePeriod(level);
                distribution.setEventCount(eventCount);
                distribution.setPeriodCode("MAGNITUDE_" + (i + 1));
                distributionList.add(distribution);
            }

            return distributionList;
        } catch (Exception e) {
            log.error("获取微震事件分布统计失败", e);
            throw new RuntimeException("获取微震事件分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取微震散点图数据
     */
    @Override
    public List<MicroseismicScatterDataVO> getScatterData(String viewType, String startDate, String endDate) {
        try {
            log.info("获取微震散点图数据，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 查询真实数据
            List<Map<String, Object>> rawData = microseismicStatMapper.getScatterRawData(startDate, endDate);
            List<MicroseismicScatterDataVO> scatterDataList = new ArrayList<>();

            // 规则网格统计参数 (根据图片中的计算方案A)
            final double GRID_SIZE = 20.0; // 网格分辨率 Δx×Δy (如 20m×20m)
            final double GRID_AREA = GRID_SIZE * GRID_SIZE; // 每格面积 A=ΔxΔy
            final int TIME_PERIOD_DAYS = calculateTimePeriodDays(viewType); // 时间周期T (天)

            // 网格统计Map: key为网格坐标"x_y", value为该网格内的事件列表
            Map<String, List<Map<String, Object>>> gridEventMap = new HashMap<>();

            // 将事件按网格分组
            for (Map<String, Object> event : rawData) {
                Double xCoord = getDoubleValue(event.get("x_coordinate"));
                Double yCoord = getDoubleValue(event.get("y_coordinate"));

                if (xCoord != null && yCoord != null) {
                    // 计算网格坐标
                    int gridX = (int) Math.floor(xCoord / GRID_SIZE);
                    int gridY = (int) Math.floor(yCoord / GRID_SIZE);
                    String gridKey = gridX + "_" + gridY;

                    gridEventMap.computeIfAbsent(gridKey, k -> new ArrayList<>()).add(event);
                }
            }

            // 为每个网格生成散点数据
            for (Map.Entry<String, List<Map<String, Object>>> entry : gridEventMap.entrySet()) {
                String[] gridCoords = entry.getKey().split("_");
                int gridX = Integer.parseInt(gridCoords[0]);
                int gridY = Integer.parseInt(gridCoords[1]);
                List<Map<String, Object>> gridEvents = entry.getValue();

                // 计算网格中心坐标
                double centerX = (gridX + 0.5) * GRID_SIZE;
                double centerY = (gridY + 0.5) * GRID_SIZE;

                // 计算事件密度热力 H^(count) = N_cell / (A·T)
                int eventCount = gridEvents.size(); // N_cell: 每格内事件数
                double density = (double) eventCount / (GRID_AREA * TIME_PERIOD_DAYS); // 单位: 次/m²/天

                // 选择代表性事件（最新的或震级最大的）
                Map<String, Object> representativeEvent = selectRepresentativeEvent(gridEvents);

                // 创建散点数据
                MicroseismicScatterDataVO scatterData = new MicroseismicScatterDataVO();
                scatterData.setXCoordinate(new BigDecimal(centerX).setScale(1, RoundingMode.HALF_UP));
                scatterData.setYCoordinate(new BigDecimal(centerY).setScale(1, RoundingMode.HALF_UP));
                scatterData.setMagnitude(new BigDecimal(getDoubleValue(representativeEvent.get("moment_magnitude"), 0.0)).setScale(2, RoundingMode.HALF_UP));
                scatterData.setEventType(getEventTypeBySignal(getIntValue(representativeEvent.get("signal_type"))));
                scatterData.setEventId(String.valueOf(representativeEvent.get("event_name")));
                scatterData.setOccurTime(formatEventTime(representativeEvent.get("event_date"), representativeEvent.get("event_time")));

                scatterDataList.add(scatterData);
            }

            // 按密度排序，密度高的在前
            scatterDataList.sort((a, b) -> b.getMagnitude().compareTo(a.getMagnitude()));

            return scatterDataList;
        } catch (Exception e) {
            log.error("获取微震散点图数据失败", e);
            throw new RuntimeException("获取微震散点图数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取微震P波/S波能量占比统计
     */
    @Override
    public MicroseismicEnergyRatioVO getEnergyRatio(String viewType, String startDate, String endDate) {
        try {
            log.info("获取微震P波/S波能量占比统计，视图类型: {}, 开始日期: {}, 结束日期: {}", viewType, startDate, endDate);

            // 查询P波辐射能总和
            BigDecimal totalPWaveEnergy = microseismicStatMapper.sumPWaveRadiatedEnergy(startDate, endDate);
            // 查询S波辐射能总和
            BigDecimal totalSWaveEnergy = microseismicStatMapper.sumSWaveRadiatedEnergy(startDate, endDate);
            // 查询微震事件辐射能总和
            BigDecimal totalRadiatedEnergy = microseismicStatMapper.sumRadiatedEnergy(startDate, endDate);

            // 计算占比
            BigDecimal pWaveEnergyRatio = BigDecimal.ZERO;
            BigDecimal sWaveEnergyRatio = BigDecimal.ZERO;

            if (totalRadiatedEnergy.compareTo(BigDecimal.ZERO) > 0) {
                pWaveEnergyRatio = totalPWaveEnergy.divide(totalRadiatedEnergy, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                sWaveEnergyRatio = totalSWaveEnergy.divide(totalRadiatedEnergy, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
            }

            MicroseismicEnergyRatioVO energyRatio = new MicroseismicEnergyRatioVO();
            energyRatio.setPWaveEnergyRatio(pWaveEnergyRatio);
            energyRatio.setSWaveEnergyRatio(sWaveEnergyRatio);
            energyRatio.setTotalPWaveEnergy(totalPWaveEnergy);
            energyRatio.setTotalSWaveEnergy(totalSWaveEnergy);
            energyRatio.setTotalRadiatedEnergy(totalRadiatedEnergy);
            energyRatio.setStartDate(startDate);
            energyRatio.setEndDate(endDate);
            energyRatio.setPeriod(viewType);

            return energyRatio;
        } catch (Exception e) {
            log.error("获取微震P波/S波能量占比统计失败", e);
            throw new RuntimeException("获取微震P波/S波能量占比统计失败: " + e.getMessage());
        }
    }

    /**
     * 计算时间周期天数
     */
    private int calculateTimePeriodDays(String viewType) {
        switch (viewType.toLowerCase()) {
            case "daily":
                return 1;
            case "weekly":
                return 7;
            case "monthly":
                return 30;
            default:
                return 1;
        }
    }

    /**
     * 安全获取Double值
     */
    private Double getDoubleValue(Object value) {
        return getDoubleValue(value, null);
    }

    private Double getDoubleValue(Object value, Double defaultValue) {
        if (value == null) return defaultValue;
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 安全获取Integer值
     */
    private Integer getIntValue(Object value) {
        if (value == null) return null;
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 选择代表性事件（选择震级最大的事件）
     */
    private Map<String, Object> selectRepresentativeEvent(List<Map<String, Object>> events) {
        return events.stream()
                .max((e1, e2) -> {
                    Double mag1 = getDoubleValue(e1.get("moment_magnitude"), -999.0);
                    Double mag2 = getDoubleValue(e2.get("moment_magnitude"), -999.0);
                    return mag1.compareTo(mag2);
                })
                .orElse(events.get(0));
    }

    /**
     * 根据信号类型获取事件类型
     */
    private String getEventTypeBySignal(Integer signalType) {
        if (signalType == null) return "未知事件";
        switch (signalType) {
            case 12:
                return "微震事件";
            case 10:
                return "爆破事件";
            case 8:
                return "噪声事件";
            default:
                return "其他事件";
        }
    }

    /**
     * 格式化事件时间
     */
    private String formatEventTime(Object eventDate, Object eventTime) {
        try {
            if (eventDate != null && eventTime != null) {
                LocalDate date = LocalDate.parse(eventDate.toString());
                LocalTime time = LocalTime.parse(eventTime.toString());
                return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " +
                       time.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            }
        } catch (Exception e) {
            log.warn("格式化事件时间失败: {}", e.getMessage());
        }
        return "未知时间";
    }
}
