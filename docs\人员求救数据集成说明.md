# 人员求救数据集成说明

## 概述

本文档说明了PeoplePos_People_SOS队列的集成实现，该队列包含人员求救数据（RYQJ），用于支持人员安全统计中的求救报警数据统计和分析。

## 数据字段映射

### Kafka队列字段

| 中文字段名 | 数据库字段名 | 类型 | 说明 |
|-----------|-------------|------|------|
| 煤矿编码 | mine_code | VARCHAR(50) | 煤矿的唯一标识 |
| 矿井名称 | mine_name | VARCHAR(100) | 矿井的名称 |
| 数据上传时间 | data_upload_time | TIMESTAMP | 数据上传的时间戳 |
| 人员卡编码 | person_card_code | VARCHAR(50) | 人员卡的唯一标识 |
| 姓名 | person_name | VARCHAR(100) | 求救人员的姓名 |
| 求救开始时间 | sos_start_time | TIMESTAMP | 求救信号开始时间 |
| 求救结束时间 | sos_end_time | TIMESTAMP | 求救信号结束时间 |
| 入井时间 | enter_well_time | TIMESTAMP | 人员入井时间 |
| 当前所在区域编码 | current_area_code | VARCHAR(50) | 求救时所在区域编码 |
| 进入当前区域时刻 | enter_current_area_time | TIMESTAMP | 进入当前区域的时间 |
| 当前所在基站编码 | current_base_station_code | VARCHAR(50) | 求救时所在基站编码 |
| 进入当前所处基站时刻 | enter_current_base_station_time | TIMESTAMP | 进入当前基站的时间 |

## 系统架构

### 1. 数据流程

```
Kafka队列 → KafkaListener → Service解析 → 数据库存储 → 统计分析
```

### 2. 核心组件

**数据模型：**
- `KafkaPeopleSos` - 人员求救数据实体类

**数据访问：**
- `KafkaPeopleSosMapper` - 数据访问接口
- `KafkaPeopleSosMapper.xml` - SQL映射文件

**业务逻辑：**
- `IKafkaPeopleSosService` - 服务接口
- `KafkaPeopleSosServiceImpl` - 服务实现

**消息处理：**
- `PeopleSosKafkaListener` - Kafka消息监听器

## 数据库设计

### 表结构

```sql
CREATE TABLE kafka_people_sos (
    id BIGSERIAL PRIMARY KEY,
    mine_code VARCHAR(50),
    mine_name VARCHAR(100),
    data_upload_time TIMESTAMP,
    person_card_code VARCHAR(50) NOT NULL,
    person_name VARCHAR(100),
    sos_start_time TIMESTAMP NOT NULL,
    sos_end_time TIMESTAMP,
    enter_well_time TIMESTAMP,
    current_area_code VARCHAR(50),
    enter_current_area_time TIMESTAMP,
    current_base_station_code VARCHAR(50),
    enter_current_base_station_time TIMESTAMP,
    status BIGINT DEFAULT 1,
    is_deleted BIGINT DEFAULT 0,
    create_by VARCHAR(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500) DEFAULT NULL
);
```

### 索引设计

```sql
-- 基础索引
CREATE INDEX idx_kps_person_card_code ON kafka_people_sos(person_card_code);
CREATE INDEX idx_kps_sos_start_time ON kafka_people_sos(sos_start_time);
CREATE INDEX idx_kps_current_area_code ON kafka_people_sos(current_area_code);

-- 唯一性约束
ALTER TABLE kafka_people_sos 
ADD CONSTRAINT uk_person_sos_time UNIQUE (person_card_code, sos_start_time);
```

## 业务集成

### 1. 人员安全统计增强

在人员安全统计中，系统现在能够：

**真实求救数据统计：**
```java
// 获取求救报警数据
Long distressAlarms = kafkaPeopleSosService.countSosCallsByDateRange(startDate, endDate);
summary.setPersonnelDistressAlarms(distressAlarms != null ? distressAlarms : 0L);

// 获取求救人员数量
Long distressCalls = kafkaPeopleSosService.countSosCallsByDateRange(startDate, endDate);
simpleOverview.setDistressCalls(distressCalls != null ? distressCalls : 0L);
```

**求救响应时间分析：**
- 计算求救开始到结束的响应时间
- 分析不同区域的求救响应效率
- 识别高风险区域和时段

### 2. 数据处理特性

**自动去重：**
- 基于人员卡编码和求救开始时间的唯一性约束
- 使用PostgreSQL的UPSERT操作

**数据验证：**
- 必填字段验证（人员卡编码、求救开始时间）
- 数据格式验证
- 异常处理和日志记录

**响应时间计算：**
- 自动计算求救响应时间
- 响应效率分级（快速/正常/较慢）

## 接口说明

### 核心统计方法

```java
// 统计求救次数
Long countSosCallsByDateRange(String startDate, String endDate);

// 统计求救人员数量
Long countSosPersonnelByDateRange(String startDate, String endDate);

// 查询求救记录
List<KafkaPeopleSos> selectSosRecordsByDateRange(String startDate, String endDate, Integer limit);

// 按日期统计求救次数
List<Map<String, Object>> selectSosCountByDate(String startDate, String endDate);

// 获取最近的求救记录
List<KafkaPeopleSos> selectRecentSosRecords(Integer limit);

// 统计各区域的求救次数分布
List<Map<String, Object>> selectSosDistributionByArea(String startDate, String endDate);
```

### 消息处理

```java
// 处理Kafka消息
boolean processKafkaMessage(String kafkaMessage);

// 解析Kafka消息
KafkaPeopleSos parseKafkaMessage(String kafkaMessage);
```

## 测试数据

系统预置了以下测试数据：

### 求救事件类型

| 求救类型 | 数量 | 平均响应时间 | 示例区域 |
|---------|------|-------------|----------|
| 紧急求救 | 2 | 10分钟 | 主井口作业区、瓦斯监测区 |
| 设备故障 | 1 | 10分钟 | 副井口作业区 |
| 人员受伤 | 1 | 10分钟 | 运输巷道区 |
| 迷路求救 | 1 | 15分钟 | 井下避难硐室 |
| 环境异常 | 1 | 15分钟 | 高压电气区 |
| 通信中断 | 1 | 15分钟 | 主井口作业区 |
| 设备检修 | 1 | 15分钟 | 井下休息室 |

### 示例数据

```sql
-- 紧急求救示例
('MINE001', '示例煤矿', 'CARD001', '张三', '2025-08-25 14:25:00', '2025-08-25 14:35:00', 'AREA001')

-- 设备故障求救示例  
('MINE001', '示例煤矿', 'CARD003', '王五', '2025-08-24 13:15:00', '2025-08-24 13:25:00', 'AREA002')
```

## 统计分析功能

### 1. 响应时间分析

```sql
-- 计算平均响应时间
SELECT 
    current_area_code,
    AVG(EXTRACT(EPOCH FROM (sos_end_time - sos_start_time))/60) as avg_response_time_minutes
FROM kafka_people_sos 
WHERE is_deleted = 0 AND sos_end_time IS NOT NULL
GROUP BY current_area_code;
```

### 2. 求救趋势分析

```sql
-- 按日期统计求救次数
SELECT 
    data_upload_time::date as date,
    count(*) as sos_count,
    count(distinct person_card_code) as sos_personnel_count
FROM kafka_people_sos 
WHERE is_deleted = 0
GROUP BY data_upload_time::date
ORDER BY date;
```

### 3. 区域风险评估

```sql
-- 各区域求救频率统计
SELECT 
    current_area_code,
    count(*) as sos_count,
    count(distinct person_card_code) as unique_personnel_count
FROM kafka_people_sos 
WHERE is_deleted = 0
GROUP BY current_area_code
ORDER BY sos_count DESC;
```

## 监控和维护

### 1. 数据质量监控

```sql
-- 检查求救数据完整性
SELECT 
    COUNT(*) as total_sos,
    COUNT(CASE WHEN person_name IS NULL THEN 1 END) as missing_names,
    COUNT(CASE WHEN sos_end_time IS NULL THEN 1 END) as unresolved_sos
FROM kafka_people_sos 
WHERE is_deleted = 0;
```

### 2. 响应效率监控

```sql
-- 响应时间分布统计
SELECT 
    CASE 
        WHEN EXTRACT(EPOCH FROM (sos_end_time - sos_start_time))/60 <= 5 THEN '快速响应'
        WHEN EXTRACT(EPOCH FROM (sos_end_time - sos_start_time))/60 <= 15 THEN '正常响应'
        ELSE '响应较慢'
    END as response_level,
    COUNT(*) as count
FROM kafka_people_sos 
WHERE is_deleted = 0 AND sos_end_time IS NOT NULL
GROUP BY response_level;
```

### 3. 实时监控

```sql
-- 获取最近的求救记录
SELECT 
    person_name,
    sos_start_time,
    current_area_code,
    CASE 
        WHEN sos_end_time IS NULL THEN '进行中'
        ELSE '已处理'
    END as status
FROM kafka_people_sos 
WHERE is_deleted = 0
ORDER BY sos_start_time DESC 
LIMIT 10;
```

## 业务价值

### 1. 安全管理提升

- **实时求救监控**: 及时发现和响应人员求救
- **响应效率分析**: 优化应急响应流程
- **风险区域识别**: 识别高风险作业区域

### 2. 数据驱动决策

- **求救趋势分析**: 了解求救事件的时间和空间分布
- **资源配置优化**: 基于求救数据优化救援资源配置
- **培训需求识别**: 基于求救类型制定针对性培训

### 3. 合规性支持

- **事件记录完整**: 完整记录所有求救事件
- **响应时间追踪**: 满足安全管理规范要求
- **数据可追溯**: 支持事故调查和分析

## 扩展功能

### 1. 智能预警

基于历史求救数据进行风险预警：

```java
public boolean isHighRiskArea(String areaCode) {
    List<Map<String, Object>> areaStats = kafkaPeopleSosService.selectSosDistributionByArea(
        LocalDate.now().minusDays(30).toString(), 
        LocalDate.now().toString()
    );
    
    // 如果某区域30天内求救次数超过阈值，则标记为高风险
    return areaStats.stream()
        .anyMatch(stat -> areaCode.equals(stat.get("area_code")) && 
                         ((Number) stat.get("sos_count")).intValue() > 5);
}
```

### 2. 响应效率评估

评估不同时段和区域的响应效率：

```java
public String assessResponseEfficiency(String areaCode, Date sosStartTime, Date sosEndTime) {
    if (sosEndTime == null) return "未完成";
    
    long responseMinutes = (sosEndTime.getTime() - sosStartTime.getTime()) / (1000 * 60);
    
    if (responseMinutes <= 5) return "快速响应";
    else if (responseMinutes <= 15) return "正常响应";
    else return "响应较慢";
}
```

## 总结

人员求救数据的集成为人员安全统计系统提供了重要的求救报警数据支持，使得：

1. **数据真实性提升**：求救报警数据从模拟数据变为真实数据
2. **响应效率监控**：能够监控和分析求救响应效率
3. **风险识别增强**：基于求救数据识别高风险区域和时段
4. **决策支持完善**：为安全管理决策提供数据支持

系统现在能够自动接收和处理人员求救数据，为人员安全统计提供了完整的求救报警数据支撑，填补了之前缺失的重要数据源。
