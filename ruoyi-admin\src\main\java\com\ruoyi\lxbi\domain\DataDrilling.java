package com.ruoyi.lxbi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 钻孔施工数据对象 data_drilling
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataDrilling extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 记录 */
    private Long id;

    /** 施工日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "施工日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /** 项目部门ID */
    private Long projectDepartmentId;

    /** 采场ID */
    private Long stopeId;

    /** 工作面ID */
    private Long workingFaceId;

    /** 工作时段ID */
    private Long workingPeriodId;

    /** 钻孔类型：1-潜孔，2-中深孔 */
    @Excel(name = "钻孔类型：1-潜孔，2-中深孔")
    private String drillingType;

    /** 进尺米数 */
    @Excel(name = "进尺米数")
    private Double progressMeters;

}
