package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

/**
 * 安全小结文本VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SafetySummaryTextVO {
    
    /**
     * 安全小结标题
     */
    private String title;
    
    /**
     * 安全小结文本列表
     */
    private List<String> summaryTexts;
    
    /**
     * 统计日期范围开始
     */
    private String startDate;
    
    /**
     * 统计日期范围结束
     */
    private String endDate;
    
    /**
     * 视图类型
     */
    private String viewType;
    
    /**
     * 生成时间
     */
    private String generateTime;
}
