package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 部门隐患分布VO
 * 
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DepartmentDistributionVO {
    
    /**
     * 部门名称
     */
    private String department;
    
    /**
     * 隐患总数
     */
    private Long count;
    
    /**
     * 重大隐患数
     */
    private Long majorCount;
    
    /**
     * 一般隐患数
     */
    private Long generalCount;
}
