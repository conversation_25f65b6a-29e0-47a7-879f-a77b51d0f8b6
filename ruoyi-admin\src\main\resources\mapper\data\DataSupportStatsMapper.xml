<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataSupportStatsMapper">

    <!-- 总体统计结果映射（含计划量） -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataSupportTotalWithPlanStats" id="DataSupportTotalWithPlanStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="totalSupportLength"   column="total_support_length"   />
        <result property="totalSupportVolume"   column="total_support_volume"   />
        <result property="planSupportLength"    column="plan_support_length"    />
        <result property="planSupportVolume"    column="plan_support_volume"    />
    </resultMap>

    <!-- 项目部门统计结果映射（含计划量） -->
    <resultMap type="com.ruoyi.lxbi.domain.response.DataSupportDepartmentWithPlanStats" id="DataSupportDepartmentWithPlanStatsResult">
        <result property="operationDate"        column="operation_date"         />
        <result property="year"                 column="year"                   />
        <result property="month"                column="month"                  />
        <result property="weekNumber"           column="week_number"            />
        <result property="weekStartDate"        column="week_start_date"        />
        <result property="weekEndDate"          column="week_end_date"          />
        <result property="projectDepartmentId"  column="project_department_id"  />
        <result property="projectDepartmentName" column="project_department_name" />
        <result property="totalSupportLength"   column="total_support_length"   />
        <result property="totalSupportVolume"   column="total_support_volume"   />
        <result property="planSupportLength"    column="plan_support_length"    />
        <result property="planSupportVolume"    column="plan_support_volume"    />
    </resultMap>

    <!-- ========== 总体统计查询方法（含计划量） ========== -->

    <select id="selectDailyTotalWithPlanStats" resultMap="DataSupportTotalWithPlanStatsResult">
        SELECT
            da.operation_date,
            da.total_support_length,
            da.total_support_volume,
            ROUND(psm.support_length / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_support_length,
            ROUND(psm.support_volume / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_support_volume
        FROM vdata_support_daily_total_stats da
        CROSS JOIN get_financial_month(da.operation_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(bolt_mesh_support_meter + shotcrete_support_meter) as support_length,
                SUM(support_volume) as support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM'))
        ) psm ON fm.financial_year = psm.year AND fm.financial_month = psm.month
        <if test="startDate != null">
            WHERE operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            <if test="startDate != null">AND</if>
            <if test="startDate == null">WHERE</if>
            operation_date &lt;= #{endDate}
        </if>
        ORDER BY da.operation_date
    </select>

    <select id="selectWeeklyTotalWithPlanStats" resultMap="DataSupportTotalWithPlanStatsResult">
        SELECT
            da.year,
            da.week_number,
            da.week_start_date,
            da.week_end_date,
            da.total_support_length,
            da.total_support_volume,
            ROUND(psm.support_length / 4, 2) as plan_support_length,
            ROUND(psm.support_volume / 4, 2) as plan_support_volume
        FROM vdata_support_weekly_total_stats da
        CROSS JOIN get_financial_month(da.week_start_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(bolt_mesh_support_meter + shotcrete_support_meter) as support_length,
                SUM(support_volume) as support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM'))
        ) psm ON fm.financial_year = psm.year AND fm.financial_month = psm.month
        <if test="startDate != null">
            WHERE week_start_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            <if test="startDate != null">AND</if>
            <if test="startDate == null">WHERE</if>
            week_end_date &lt;= #{endDate}
        </if>
        ORDER BY da.year, da.week_number
    </select>

    <select id="selectMonthlyTotalWithPlanStats" resultMap="DataSupportTotalWithPlanStatsResult">
        SELECT
            da.year,
            da.month,
            da.total_support_length,
            da.total_support_volume,
            psm.support_length as plan_support_length,
            psm.support_volume as plan_support_volume
        FROM vdata_support_monthly_total_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                SUM(bolt_mesh_support_meter + shotcrete_support_meter) as support_length,
                SUM(support_volume) as support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM'))
        ) psm ON da.year = psm.year AND da.month = psm.month
        <if test="startDate != null">
            WHERE MAKE_DATE(da.year, da.month, 1) &gt;= DATE_TRUNC('month', #{startDate}::date)
        </if>
        <if test="endDate != null">
            <if test="startDate != null">AND</if>
            <if test="startDate == null">WHERE</if>
            MAKE_DATE(da.year, da.month, 1) &lt;= DATE_TRUNC('month', #{endDate}::date)
        </if>
        ORDER BY da.year, da.month
    </select>

    <select id="selectYearlyTotalWithPlanStats" resultMap="DataSupportTotalWithPlanStatsResult">
        SELECT
            da.year,
            da.total_support_length,
            da.total_support_volume,
            psm.support_length as plan_support_length,
            psm.support_volume as plan_support_volume
        FROM vdata_support_yearly_total_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                SUM(bolt_mesh_support_meter + shotcrete_support_meter) as support_length,
                SUM(support_volume) as support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM'))
        ) psm ON da.year = psm.year
        <if test="startDate != null">
            WHERE da.year &gt;= EXTRACT(YEAR FROM #{startDate})
        </if>
        <if test="endDate != null">
            <if test="startDate != null">AND</if>
            <if test="startDate == null">WHERE</if>
            da.year &lt;= EXTRACT(YEAR FROM #{endDate})
        </if>
        ORDER BY da.year
    </select>

    <!-- ========== 项目部门统计查询方法（含计划量） ========== -->

    <select id="selectDailyDepartmentWithPlanStats" resultMap="DataSupportDepartmentWithPlanStatsResult">
        SELECT
            da.operation_date,
            da.project_department_id,
            da.project_department_name,
            da.total_support_length,
            da.total_support_volume,
            ROUND(psm.support_length / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_support_length,
            ROUND(psm.support_volume / EXTRACT(DAY FROM (DATE_TRUNC('month', MAKE_DATE(fm.financial_year, fm.financial_month, 1) - INTERVAL '1 month') + INTERVAL '1 month' - INTERVAL '1 day')), 2) as plan_support_volume
        FROM vdata_support_daily_department_stats da
        CROSS JOIN get_financial_month(da.operation_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                project_department_id,
                SUM(bolt_mesh_support_meter + shotcrete_support_meter) as support_length,
                SUM(support_volume) as support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) psm ON fm.financial_year = psm.year AND fm.financial_month = psm.month AND da.project_department_id = psm.project_department_id
        <if test="startDate != null">
            WHERE operation_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            <if test="startDate != null">AND</if>
            <if test="startDate == null">WHERE</if>
            operation_date &lt;= #{endDate}
        </if>
        ORDER BY da.operation_date, da.project_department_id
    </select>

    <select id="selectWeeklyDepartmentWithPlanStats" resultMap="DataSupportDepartmentWithPlanStatsResult">
        SELECT
            da.year,
            da.week_number,
            da.week_start_date,
            da.week_end_date,
            da.project_department_id,
            da.project_department_name,
            da.total_support_length,
            da.total_support_volume,
            ROUND(psm.support_length / 4, 2) as plan_support_length,
            ROUND(psm.support_volume / 4, 2) as plan_support_volume
        FROM vdata_support_weekly_department_stats da
        CROSS JOIN get_financial_month(da.week_start_date) fm(financial_year, financial_month)
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                project_department_id,
                SUM(bolt_mesh_support_meter + shotcrete_support_meter) as support_length,
                SUM(support_volume) as support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) psm ON fm.financial_year = psm.year AND fm.financial_month = psm.month AND da.project_department_id = psm.project_department_id
        <if test="startDate != null">
            WHERE week_start_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            <if test="startDate != null">AND</if>
            <if test="startDate == null">WHERE</if>
            week_end_date &lt;= #{endDate}
        </if>
        ORDER BY da.year, da.week_number, da.project_department_id
    </select>

    <select id="selectMonthlyDepartmentWithPlanStats" resultMap="DataSupportDepartmentWithPlanStatsResult">
        SELECT
            da.year,
            da.month,
            da.project_department_id,
            da.project_department_name,
            da.total_support_length,
            da.total_support_volume,
            psm.support_length as plan_support_length,
            psm.support_volume as plan_support_volume
        FROM vdata_support_monthly_department_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')) as month,
                project_department_id,
                SUM(bolt_mesh_support_meter + shotcrete_support_meter) as support_length,
                SUM(support_volume) as support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), EXTRACT(MONTH FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) psm ON da.year = psm.year AND da.month = psm.month AND da.project_department_id = psm.project_department_id
        <if test="startDate != null">
            WHERE MAKE_DATE(da.year, da.month, 1) &gt;= DATE_TRUNC('month', #{startDate}::date)
        </if>
        <if test="endDate != null">
            <if test="startDate != null">AND</if>
            <if test="startDate == null">WHERE</if>
            MAKE_DATE(da.year, da.month, 1) &lt;= DATE_TRUNC('month', #{endDate}::date)
        </if>
        ORDER BY da.year, da.month, da.project_department_id
    </select>

    <select id="selectYearlyDepartmentWithPlanStats" resultMap="DataSupportDepartmentWithPlanStatsResult">
        SELECT
            da.year,
            da.project_department_id,
            da.project_department_name,
            da.total_support_length,
            da.total_support_volume,
            psm.support_length as plan_support_length,
            psm.support_volume as plan_support_volume
        FROM vdata_support_yearly_department_stats da
        LEFT JOIN (
            SELECT
                EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')) as year,
                project_department_id,
                SUM(bolt_mesh_support_meter + shotcrete_support_meter) as support_length,
                SUM(support_volume) as support_volume
            FROM plan_support_monthly
            GROUP BY EXTRACT(YEAR FROM TO_DATE(plan_date, 'YYYYMM')), project_department_id
        ) psm ON da.year = psm.year AND da.project_department_id = psm.project_department_id
        <if test="startDate != null">
            WHERE da.year &gt;= EXTRACT(YEAR FROM #{startDate})
        </if>
        <if test="endDate != null">
            <if test="startDate != null">AND</if>
            <if test="startDate == null">WHERE</if>
            da.year &lt;= EXTRACT(YEAR FROM #{endDate})
        </if>
        ORDER BY da.year, da.project_department_id
    </select>

</mapper>
