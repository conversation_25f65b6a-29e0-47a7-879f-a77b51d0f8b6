package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.DataIronConcentrate;
import com.ruoyi.lxbi.domain.PlanMineralMonthly;
import com.ruoyi.lxbi.domain.table.DailyMineralReportTableVo;
import com.ruoyi.lxbi.service.IDataIronConcentrateService;
import com.ruoyi.lxbi.service.IPlanMineralMonthlyService;
import com.ruoyi.lxbi.table.params.DailyMineralReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 选矿数据日报表格处理器
 */
@Component
public class DailyMineralReportTableHandler extends BaseTableHandler<DailyMineralReportTableVo, DailyMineralReportQueryParams> {

    @Autowired
    private IPlanMineralMonthlyService planMineralMonthlyService;

    @Autowired
    private IDataIronConcentrateService dataIronConcentrateService;

    @Override
    public List<DailyMineralReportTableVo> queryTableData(DailyMineralReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date operationDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取月度计划数据
        Map<String, BigDecimal> monthlyPlans = getMonthlyPlans(operationDate);

        Map<String, BigDecimal> dailyData = getDailyData(operationDate);
        Map<String, BigDecimal> monthlyAccumulated = getMonthlyAccumulated(operationDate);

        // 构建表格数据
        return buildTableData(monthlyPlans, dailyData, monthlyAccumulated);
    }

    /**
     * 获取月度计划数据
     */
    private Map<String, BigDecimal> getMonthlyPlans(Date operationDate) {
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        PlanMineralMonthly queryParam = new PlanMineralMonthly();
        queryParam.setPlanDate(financialMonth);

        List<PlanMineralMonthly> plans = planMineralMonthlyService.selectPlanMineralMonthlyList(queryParam);

        Map<String, BigDecimal> result = new HashMap<>();
        if (!plans.isEmpty()) {
            PlanMineralMonthly plan = plans.get(0);
            
            // 原矿处理量相关
            result.put("rawOreProcessingVolume", plan.getRawOreProcessingVolume());
            result.put("drySeparationVolume", plan.getDrySeparationVolume());
            result.put("grindingFeedVolume", plan.getGrindingFeedVolume());
            
            // 原矿品位相关
            result.put("rawOreGradeTfe", plan.getRawOreGradeTfe());
            result.put("rawOreGradeMfe", plan.getRawOreGradeMfe());
            
            // 精粉相关
            result.put("concentrateGrade", plan.getConcentrateGrade());
            result.put("concentrateFineness", plan.getConcentrateFineness());
            result.put("concentrateVolume", plan.getConcentrateVolume());
            
            // 铁精粉水分
            result.put("ironConcentrateMoisture", plan.getIronConcentrateMoisture());
            
            // 尾矿品位相关
            result.put("tailingGradeTfe", plan.getTailingGradeTfe());
            result.put("tailingGradeMfe", plan.getTailingGradeMfe());
            
            // 选比相关
            result.put("comprehensiveRatio", plan.getComprehensiveRatio());
            result.put("grindingRatio", plan.getGrindingRatio());
            
            // 尾矿相关
            result.put("tailingsAgitatorTank", plan.getTailingsAgitatorTank());
            result.put("overflowOfTailings", plan.getOverflowOfTailings());
        }

        return result;
    }

    /**
     * 获取日产数据（从铁精粉数据表获取实际数据）
     */
    private Map<String, BigDecimal> getDailyData(Date operationDate) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 从铁精粉数据表获取当日数据
        DataIronConcentrate queryParam = new DataIronConcentrate();
        queryParam.setOperationDate(operationDate);
        List<DataIronConcentrate> dailyDataList = dataIronConcentrateService.selectDataIronConcentrateList(queryParam);

        if (!dailyDataList.isEmpty()) {
            DataIronConcentrate dailyData = dailyDataList.get(0);

            // 铁精粉相关数据（从实际数据获取）
            result.put("concentrateGrade", dailyData.getTfeContent());
            result.put("concentrateFineness", dailyData.getFinenessMinus500());
            result.put("concentrateVolume", dailyData.getProductionVolume());
            result.put("ironConcentrateMoisture", dailyData.getMoistureContent());
        }

        // 其他数据暂时设为空（没有对应的数据表）
        result.put("rawOreProcessingVolume", null);
        result.put("drySeparationVolume", null);
        result.put("grindingFeedVolume", null);
        result.put("rawOreGradeTfe", null);
        result.put("rawOreGradeMfe", null);
        result.put("tailingGradeTfe", null);
        result.put("tailingGradeMfe", null);
        result.put("comprehensiveRatio", null);
        result.put("grindingRatio", null);
        result.put("tailingsAgitatorTank", null);
        result.put("overflowOfTailings", null);

        return result;
    }

    /**
     * 获取月累计数据（从铁精粉数据表获取实际数据）
     */
    private Map<String, BigDecimal> getMonthlyAccumulated(Date operationDate) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 获取财务月范围
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(operationDate);
        Date financialMonthEnd = FinancialDateUtils.getFinancialMonthEndDate(operationDate);

        // 查询财务月内的所有铁精粉数据
        DataIronConcentrate queryParam = new DataIronConcentrate();
        queryParam.getParams().put("startDate", financialMonthStart);
        queryParam.getParams().put("endDate", financialMonthEnd);
        List<DataIronConcentrate> monthlyDataList = dataIronConcentrateService.selectDataIronConcentrateList(queryParam);

        if (!monthlyDataList.isEmpty()) {
            // 计算月累计数据
            BigDecimal totalVolume = monthlyDataList.stream()
                    .map(DataIronConcentrate::getProductionVolume)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算平均值
            double avgTfeContent = monthlyDataList.stream()
                    .filter(data -> data.getTfeContent() != null)
                    .mapToDouble(data -> data.getTfeContent().doubleValue())
                    .average().orElse(0.0);

            double avgFineness = monthlyDataList.stream()
                    .filter(data -> data.getFinenessMinus500() != null)
                    .mapToDouble(data -> data.getFinenessMinus500().doubleValue())
                    .average().orElse(0.0);

            double avgMoisture = monthlyDataList.stream()
                    .filter(data -> data.getMoistureContent() != null)
                    .mapToDouble(data -> data.getMoistureContent().doubleValue())
                    .average().orElse(0.0);

            // 铁精粉相关数据（从实际数据计算）
            result.put("concentrateVolume", totalVolume.compareTo(BigDecimal.ZERO) > 0 ? totalVolume : null);
            result.put("concentrateGrade", avgTfeContent > 0 ? BigDecimal.valueOf(avgTfeContent) : null);
            result.put("concentrateFineness", avgFineness > 0 ? BigDecimal.valueOf(avgFineness) : null);
            result.put("ironConcentrateMoisture", avgMoisture > 0 ? BigDecimal.valueOf(avgMoisture) : null);
        } else {
            // 如果没有数据，设置铁精粉相关数据为null
            result.put("concentrateVolume", null);
            result.put("concentrateGrade", null);
            result.put("concentrateFineness", null);
            result.put("ironConcentrateMoisture", null);
        }

        // 其他数据暂时设为空（没有对应的数据表）
        result.put("rawOreProcessingVolume", null);
        result.put("drySeparationVolume", null);
        result.put("grindingFeedVolume", null);
        result.put("rawOreGradeTfe", null);
        result.put("rawOreGradeMfe", null);
        result.put("tailingGradeTfe", null);
        result.put("tailingGradeMfe", null);
        result.put("comprehensiveRatio", null);
        result.put("grindingRatio", null);
        result.put("tailingsAgitatorTank", null);
        result.put("overflowOfTailings", null);

        return result;
    }

    /**
     * 构建表格数据
     */
    private List<DailyMineralReportTableVo> buildTableData(Map<String, BigDecimal> monthlyPlans,
                                                           Map<String, BigDecimal> dailyData,
                                                           Map<String, BigDecimal> monthlyAccumulated) {
        List<DailyMineralReportTableVo> result = new ArrayList<>();
        int serialNumber = 1;

        // 原矿处理量
        result.add(createReportItem(String.valueOf(serialNumber++), "原矿处理量", "原矿处理量", "t",
                monthlyPlans.get("rawOreProcessingVolume"), dailyData.get("rawOreProcessingVolume"), 
                monthlyAccumulated.get("rawOreProcessingVolume")));
        result.add(createReportItem("", "", "干抛量", "t",
                monthlyPlans.get("drySeparationVolume"), dailyData.get("drySeparationVolume"), 
                monthlyAccumulated.get("drySeparationVolume")));
        result.add(createReportItem("", "", "入磨量", "t",
                monthlyPlans.get("grindingFeedVolume"), dailyData.get("grindingFeedVolume"), 
                monthlyAccumulated.get("grindingFeedVolume")));

        // 原矿入磨品位
        result.add(createReportItem(String.valueOf(serialNumber++), "原矿入磨品位", "TFe", "%",
                monthlyPlans.get("rawOreGradeTfe"), dailyData.get("rawOreGradeTfe"), 
                monthlyAccumulated.get("rawOreGradeTfe")));
        result.add(createReportItem("", "", "mFe", "%",
                monthlyPlans.get("rawOreGradeMfe"), dailyData.get("rawOreGradeMfe"), 
                monthlyAccumulated.get("rawOreGradeMfe")));

        // 精粉
        result.add(createReportItem(String.valueOf(serialNumber++), "精粉", "TFe", "%",
                monthlyPlans.get("concentrateGrade"), dailyData.get("concentrateGrade"), 
                monthlyAccumulated.get("concentrateGrade")));
        result.add(createReportItem("", "", "精矿细度(-500目含量)", "%",
                monthlyPlans.get("concentrateFineness"), dailyData.get("concentrateFineness"), 
                monthlyAccumulated.get("concentrateFineness")));
        result.add(createReportItem("", "", "产量", "t",
                monthlyPlans.get("concentrateVolume"), dailyData.get("concentrateVolume"), 
                monthlyAccumulated.get("concentrateVolume")));

        // 铁精粉水分
        result.add(createReportItem(String.valueOf(serialNumber++), "铁精粉水分", "水分", "%",
                monthlyPlans.get("ironConcentrateMoisture"), dailyData.get("ironConcentrateMoisture"), 
                monthlyAccumulated.get("ironConcentrateMoisture")));

        // 尾矿品位
        result.add(createReportItem(String.valueOf(serialNumber++), "尾矿品位", "TFe", "%",
                monthlyPlans.get("tailingGradeTfe"), dailyData.get("tailingGradeTfe"), 
                monthlyAccumulated.get("tailingGradeTfe")));
        result.add(createReportItem("", "", "mFe", "%",
                monthlyPlans.get("tailingGradeMfe"), dailyData.get("tailingGradeMfe"), 
                monthlyAccumulated.get("tailingGradeMfe")));

        // 综合选比
        result.add(createReportItem(String.valueOf(serialNumber++), "综合选比", "综合选比", "倍",
                monthlyPlans.get("comprehensiveRatio"), dailyData.get("comprehensiveRatio"), 
                monthlyAccumulated.get("comprehensiveRatio")));

        // 入磨选比
        result.add(createReportItem(String.valueOf(serialNumber++), "入磨选比", "入磨选比", "倍",
                monthlyPlans.get("grindingRatio"), dailyData.get("grindingRatio"), 
                monthlyAccumulated.get("grindingRatio")));

        // 尾矿搅拌槽
        result.add(createReportItem(String.valueOf(serialNumber++), "尾矿搅拌槽", "尾矿粒级(-200目)", "",
                monthlyPlans.get("tailingsAgitatorTank"), dailyData.get("tailingsAgitatorTank"), 
                monthlyAccumulated.get("tailingsAgitatorTank")));

        // 尾矿脱水筛上量
        result.add(createReportItem(String.valueOf(serialNumber++), "尾矿脱水筛上量", "尾矿脱水筛上量", "t",
                monthlyPlans.get("overflowOfTailings"), dailyData.get("overflowOfTailings"), 
                monthlyAccumulated.get("overflowOfTailings")));

        // 综合回收率（计算得出）- 由于原矿处理量数据暂时为空，回收率暂时设为null
        BigDecimal concentrateVolumePlan = monthlyPlans.get("concentrateVolume");
        BigDecimal rawOreVolumePlan = monthlyPlans.get("rawOreProcessingVolume");
        BigDecimal recoveryRatePlan = calculateRecoveryRate(concentrateVolumePlan, rawOreVolumePlan);

        BigDecimal concentrateVolumeDaily = dailyData.get("concentrateVolume");
        BigDecimal rawOreVolumeDaily = dailyData.get("rawOreProcessingVolume");
        BigDecimal recoveryRateDaily = calculateRecoveryRate(concentrateVolumeDaily, rawOreVolumeDaily);

        BigDecimal concentrateVolumeAccumulated = monthlyAccumulated.get("concentrateVolume");
        BigDecimal rawOreVolumeAccumulated = monthlyAccumulated.get("rawOreProcessingVolume");
        BigDecimal recoveryRateAccumulated = calculateRecoveryRate(concentrateVolumeAccumulated, rawOreVolumeAccumulated);

        result.add(createReportItem(String.valueOf(serialNumber++), "综合回收率", "综合回收率", "%",
                recoveryRatePlan, recoveryRateDaily, recoveryRateAccumulated));

        return result;
    }

    /**
     * 计算回收率
     */
    private BigDecimal calculateRecoveryRate(BigDecimal concentrateVolume, BigDecimal rawOreVolume) {
        if (rawOreVolume == null || rawOreVolume.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (concentrateVolume == null) {
            return BigDecimal.ZERO;
        }
        return concentrateVolume.divide(rawOreVolume, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 创建报表项
     */
    private DailyMineralReportTableVo createReportItem(String serialNumber, String name, String subName, String unit,
                                                       BigDecimal monthlyPlan, BigDecimal dailyOutput,
                                                       BigDecimal monthlyAccumulated) {
        DailyMineralReportTableVo vo = new DailyMineralReportTableVo();
        vo.setSerialNumber(serialNumber);
        vo.setName(name);
        vo.setSubName(subName);
        vo.setUnit(unit);
        vo.setMonthlyPlan(monthlyPlan != null ? monthlyPlan : BigDecimal.ZERO);
        vo.setDailyOutput(dailyOutput != null ? dailyOutput : BigDecimal.ZERO);
        vo.setMonthlyAccumulated(monthlyAccumulated != null ? monthlyAccumulated : BigDecimal.ZERO);

        // 计算完成率
        if (monthlyPlan != null && monthlyPlan.compareTo(BigDecimal.ZERO) > 0 && monthlyAccumulated != null) {
            BigDecimal rate = monthlyAccumulated.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setCompletionRate("0.00%");
        }

        // 计算月累计超欠
        BigDecimal monthlyAccumulatedSafe = monthlyAccumulated != null ? monthlyAccumulated : BigDecimal.ZERO;
        BigDecimal monthlyPlanSafe = monthlyPlan != null ? monthlyPlan : BigDecimal.ZERO;
        BigDecimal overUnder = monthlyAccumulatedSafe.subtract(monthlyPlanSafe);
        vo.setMonthlyOverUnder(overUnder);

        return vo;
    }
}
