package com.ruoyi.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Excel导入字段选项配置注解
 * 用于配置下拉选项的映射关系
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelSelected {

    /**
     * 选项类型：dict-字典, key-通过key获取
     */
    String type() default "key";

    /**
     * 字典类型（当type=dict时使用）
     */
    String dictCode() default "";

    /**
     * 选项key（当type=key时使用）
     * 在handler中通过此key设置具体的选项值
     */
    String optionKey() default "";

    /**
     * 提示信息
     */
    String prompt() default "";

    /**
     * 错误信息
     */
    String error() default "请选择有效值";

    /**
     * 字段类型
     */
    FieldLinkType link() default FieldLinkType.VALUE;

    /**
     * 字段类型枚举
     */
    enum FieldLinkType {
        /**
         * 值字段，存储实际的value
         */
        VALUE,
        /**
         * 标签字段，存储显示的label，用于Excel显示和用户输入
         */
        LABEL
    }
}
