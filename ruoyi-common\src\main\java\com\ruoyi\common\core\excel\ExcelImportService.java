package com.ruoyi.common.core.excel;

import com.ruoyi.common.core.domain.excel.ExcelDataInfo;
import com.ruoyi.common.core.domain.excel.ExcelImportResult;
import com.ruoyi.common.core.domain.excel.ExcelTemplateInfo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Excel导入统一服务
 *
 * <AUTHOR>
 */
@Service
public class ExcelImportService {

    @Autowired
    private ExcelImportRegistry registry;

    /**
     * 获取所有模板信息
     */
    public Map<String, ExcelTemplateInfo> getAllTemplates() {
        return registry.getAllTemplates();
    }

    /**
     * 获取模板信息
     */
    public ExcelTemplateInfo getTemplateInfo(String key) {
        return registry.getTemplateInfo(key);
    }

    /**
     * 下载模板
     */
    public void downloadTemplate(String key, HttpServletResponse response) {
        ExcelImportHandler<?> handler = registry.getHandler(key);
        if (handler == null) {
            throw new RuntimeException("未找到对应的导入处理器：" + key);
        }
        handler.template(response);
    }

    /**
     * 验证Excel数据
     */
    @SuppressWarnings("unchecked")
    public <T> ExcelImportResult<T> validateExcel(String key, MultipartFile file) {
        ExcelImportHandler<T> handler = (ExcelImportHandler<T>) registry.getHandler(key);
        if (handler == null) {
            throw new RuntimeException("未找到对应的导入处理器：" + key);
        }
        try {
            return handler.validateExcel(file.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException("读取Excel文件失败：" + e.getMessage());
        }
    }

    /**
     * 验证单行数据
     */
    @SuppressWarnings("unchecked")
    public <T> ExcelDataInfo<T> validateRow(String key, ExcelDataInfo<T> rowData) {
        ExcelImportHandler<T> handler = (ExcelImportHandler<T>) registry.getHandler(key);
        if (handler == null) {
            throw new RuntimeException("未找到对应的导入处理器：" + key);
        }
        return handler.validateRow(rowData);
    }

    /**
     * 导入Excel数据（通过缓存ID）
     */
    @SuppressWarnings("unchecked")
    public <T> ExcelImportResult<T> importExcel(String key, String cacheId, List<ExcelDataInfo<T>> dataList) {
        ExcelImportHandler<T> handler = (ExcelImportHandler<T>) registry.getHandler(key);
        if (handler == null) {
            throw new RuntimeException("未找到对应的导入处理器：" + key);
        }
        return handler.processImport(cacheId, dataList);
    }

    /**
     * 导入Excel文件（一步完成验证和导入）
     */
    public <T> ExcelImportResult<T> importExcelFile(String key, MultipartFile file) {
        // 先验证
        ExcelImportResult<T> validateResult = validateExcel(key, file);
        // 再导入（使用缓存ID）
        return importExcel(key, validateResult.getId(), new ArrayList<>());
    }

    /**
     * 下载错误信息
     */
    public void downloadFailures(String key, String cacheId, HttpServletResponse response) {
        ExcelImportHandler<?> handler = registry.getHandler(key);
        if (handler == null) {
            throw new RuntimeException("未找到对应的导入处理器：" + key);
        }
        try {
            handler.downloadFailures(cacheId, response);
        } catch (IOException e) {
            throw new RuntimeException("下载失败数据失败：" + e.getMessage());
        }
    }

    /**
     * 检查处理器是否存在
     */
    public boolean hasHandler(String key) {
        return registry.hasHandler(key);
    }


}
