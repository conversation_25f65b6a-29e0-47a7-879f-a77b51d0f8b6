package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.DataIronConcentrate;
import com.ruoyi.lxbi.domain.PlanMineralMonthly;
import com.ruoyi.lxbi.domain.table.MonthlyMineralReportTableVo;
import com.ruoyi.lxbi.service.IDataIronConcentrateService;
import com.ruoyi.lxbi.service.IPlanMineralMonthlyService;
import com.ruoyi.lxbi.table.params.MonthlyMineralReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 选矿数据月报表格处理器
 */
@Component
public class MonthlyMineralReportTableHandler extends BaseTableHandler<MonthlyMineralReportTableVo, MonthlyMineralReportQueryParams> {

    @Autowired
    private IPlanMineralMonthlyService planMineralMonthlyService;

    @Autowired
    private IDataIronConcentrateService dataIronConcentrateService;

    @Override
    public List<MonthlyMineralReportTableVo> queryTableData(MonthlyMineralReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date queryDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取财务月范围
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(queryDate);
        Date financialMonthEnd = FinancialDateUtils.getFinancialMonthEndDate(queryDate);

        // 获取年度范围（1月1日到当前财务月结束）
        Calendar cal = Calendar.getInstance();
        cal.setTime(queryDate);
        cal.set(Calendar.MONTH, Calendar.JANUARY);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date yearStart = cal.getTime();

        // 获取当月计划数据
        Map<String, BigDecimal> monthlyPlans = getMonthlyPlans(queryDate);

        // 获取当月完成数据
        Map<String, BigDecimal> monthlyCompleted = getMonthlyCompleted(financialMonthStart, financialMonthEnd);

        // 获取年度计划数据（1-当前月的累计）
        Map<String, BigDecimal> yearlyPlans = getYearlyPlans(queryDate);

        // 获取年度预算计划数据
        Map<String, BigDecimal> yearlyBudgetPlans = getYearlyBudgetPlans(queryDate);

        // 获取年度完成数据
        Map<String, BigDecimal> yearlyCompleted = getYearlyCompleted(yearStart, financialMonthEnd);

        // 构建表格数据
        return buildTableData(monthlyPlans, monthlyCompleted, yearlyBudgetPlans, yearlyPlans, yearlyCompleted);
    }

    /**
     * 获取月度计划数据
     */
    private Map<String, BigDecimal> getMonthlyPlans(Date operationDate) {
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        PlanMineralMonthly queryParam = new PlanMineralMonthly();
        queryParam.setPlanDate(financialMonth);

        List<PlanMineralMonthly> plans = planMineralMonthlyService.selectPlanMineralMonthlyList(queryParam);

        Map<String, BigDecimal> result = new HashMap<>();
        if (!plans.isEmpty()) {
            PlanMineralMonthly plan = plans.get(0);
            
            // 原矿处理量相关
            result.put("rawOreProcessingVolume", plan.getRawOreProcessingVolume());
            result.put("drySeparationVolume", plan.getDrySeparationVolume());
            result.put("grindingFeedVolume", plan.getGrindingFeedVolume());
            
            // 原矿品位相关
            result.put("rawOreGradeTfe", plan.getRawOreGradeTfe());
            result.put("rawOreGradeMfe", plan.getRawOreGradeMfe());
            
            // 精粉相关
            result.put("concentrateGrade", plan.getConcentrateGrade());
            result.put("concentrateFineness", plan.getConcentrateFineness());
            result.put("concentrateVolume", plan.getConcentrateVolume());
            
            // 铁精粉水分
            result.put("ironConcentrateMoisture", plan.getIronConcentrateMoisture());
            
            // 尾矿品位相关
            result.put("tailingGradeTfe", plan.getTailingGradeTfe());
            result.put("tailingGradeMfe", plan.getTailingGradeMfe());
            
            // 选比相关
            result.put("comprehensiveRatio", plan.getComprehensiveRatio());
            result.put("grindingRatio", plan.getGrindingRatio());
            
            // 尾矿相关
            result.put("tailingsAgitatorTank", plan.getTailingsAgitatorTank());
            result.put("overflowOfTailings", plan.getOverflowOfTailings());
        }

        return result;
    }

    /**
     * 获取月完成数据（从铁精粉数据表获取实际数据）
     */
    private Map<String, BigDecimal> getMonthlyCompleted(Date startDate, Date endDate) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 查询月范围内的所有铁精粉数据
        DataIronConcentrate queryParam = new DataIronConcentrate();
        queryParam.getParams().put("startDate", startDate);
        queryParam.getParams().put("endDate", endDate);
        List<DataIronConcentrate> monthlyDataList = dataIronConcentrateService.selectDataIronConcentrateList(queryParam);

        if (!monthlyDataList.isEmpty()) {
            // 计算月完成数据
            BigDecimal totalVolume = monthlyDataList.stream()
                    .map(DataIronConcentrate::getProductionVolume)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算平均值
            double avgTfeContent = monthlyDataList.stream()
                    .filter(data -> data.getTfeContent() != null)
                    .mapToDouble(data -> data.getTfeContent().doubleValue())
                    .average().orElse(0.0);

            double avgFineness = monthlyDataList.stream()
                    .filter(data -> data.getFinenessMinus500() != null)
                    .mapToDouble(data -> data.getFinenessMinus500().doubleValue())
                    .average().orElse(0.0);

            double avgMoisture = monthlyDataList.stream()
                    .filter(data -> data.getMoistureContent() != null)
                    .mapToDouble(data -> data.getMoistureContent().doubleValue())
                    .average().orElse(0.0);

            // 铁精粉相关数据（从实际数据计算）
            result.put("concentrateVolume", totalVolume.compareTo(BigDecimal.ZERO) > 0 ? totalVolume : null);
            result.put("concentrateGrade", avgTfeContent > 0 ? BigDecimal.valueOf(avgTfeContent) : null);
            result.put("concentrateFineness", avgFineness > 0 ? BigDecimal.valueOf(avgFineness) : null);
            result.put("ironConcentrateMoisture", avgMoisture > 0 ? BigDecimal.valueOf(avgMoisture) : null);
        } else {
            // 如果没有数据，设置铁精粉相关数据为null
            result.put("concentrateVolume", null);
            result.put("concentrateGrade", null);
            result.put("concentrateFineness", null);
            result.put("ironConcentrateMoisture", null);
        }

        // 其他数据暂时设为空（没有对应的数据表）
        result.put("rawOreProcessingVolume", null);
        result.put("drySeparationVolume", null);
        result.put("grindingFeedVolume", null);
        result.put("rawOreGradeTfe", null);
        result.put("rawOreGradeMfe", null);
        result.put("tailingGradeTfe", null);
        result.put("tailingGradeMfe", null);
        result.put("comprehensiveRatio", null);
        result.put("grindingRatio", null);
        result.put("tailingsAgitatorTank", null);
        result.put("overflowOfTailings", null);

        return result;
    }

    /**
     * 获取年度计划数据（1-当前月的累计）
     */
    private Map<String, BigDecimal> getYearlyPlans(Date queryDate) {
        Map<String, BigDecimal> result = new HashMap<>();
        
        // TODO: 实际应该查询1月到当前月的所有计划数据并累计
        // 这里使用模拟数据（假设当前是第7个月）
        result.put("rawOreProcessingVolume", new BigDecimal("210000.00"));
        result.put("drySeparationVolume", new BigDecimal("140000.00"));
        result.put("grindingFeedVolume", new BigDecimal("70000.00"));
        result.put("rawOreGradeTfe", new BigDecimal("28.0"));
        result.put("rawOreGradeMfe", new BigDecimal("26.3"));
        result.put("concentrateGrade", new BigDecimal("65.0"));
        result.put("concentrateFineness", new BigDecimal("88.0"));
        result.put("concentrateVolume", new BigDecimal("73500.00"));
        result.put("ironConcentrateMoisture", new BigDecimal("8.0"));
        result.put("tailingGradeTfe", new BigDecimal("12.0"));
        result.put("tailingGradeMfe", new BigDecimal("11.3"));
        result.put("comprehensiveRatio", new BigDecimal("2.86"));
        result.put("grindingRatio", new BigDecimal("3.00"));
        result.put("tailingsAgitatorTank", new BigDecimal("76.0"));
        result.put("overflowOfTailings", new BigDecimal("31500.00"));
        
        return result;
    }

    /**
     * 获取年度预算计划数据
     */
    private Map<String, BigDecimal> getYearlyBudgetPlans(Date queryDate) {
        Map<String, BigDecimal> result = new HashMap<>();
        
        // TODO: 实际应该查询年度预算计划数据
        // 这里使用模拟数据
        result.put("rawOreProcessingVolume", new BigDecimal("360000.00"));
        result.put("drySeparationVolume", new BigDecimal("240000.00"));
        result.put("grindingFeedVolume", new BigDecimal("120000.00"));
        result.put("rawOreGradeTfe", new BigDecimal("28.0"));
        result.put("rawOreGradeMfe", new BigDecimal("26.3"));
        result.put("concentrateGrade", new BigDecimal("65.0"));
        result.put("concentrateFineness", new BigDecimal("88.0"));
        result.put("concentrateVolume", new BigDecimal("126000.00"));
        result.put("ironConcentrateMoisture", new BigDecimal("8.0"));
        result.put("tailingGradeTfe", new BigDecimal("12.0"));
        result.put("tailingGradeMfe", new BigDecimal("11.3"));
        result.put("comprehensiveRatio", new BigDecimal("2.86"));
        result.put("grindingRatio", new BigDecimal("3.00"));
        result.put("tailingsAgitatorTank", new BigDecimal("76.0"));
        result.put("overflowOfTailings", new BigDecimal("54000.00"));
        
        return result;
    }

    /**
     * 获取年度完成数据（从铁精粉数据表获取实际数据）
     */
    private Map<String, BigDecimal> getYearlyCompleted(Date yearStart, Date endDate) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 查询年度范围内的所有铁精粉数据
        DataIronConcentrate queryParam = new DataIronConcentrate();
        queryParam.getParams().put("startDate", yearStart);
        queryParam.getParams().put("endDate", endDate);
        List<DataIronConcentrate> yearlyDataList = dataIronConcentrateService.selectDataIronConcentrateList(queryParam);

        if (!yearlyDataList.isEmpty()) {
            // 计算年度完成数据
            BigDecimal totalVolume = yearlyDataList.stream()
                    .map(DataIronConcentrate::getProductionVolume)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算平均值
            double avgTfeContent = yearlyDataList.stream()
                    .filter(data -> data.getTfeContent() != null)
                    .mapToDouble(data -> data.getTfeContent().doubleValue())
                    .average().orElse(0.0);

            double avgFineness = yearlyDataList.stream()
                    .filter(data -> data.getFinenessMinus500() != null)
                    .mapToDouble(data -> data.getFinenessMinus500().doubleValue())
                    .average().orElse(0.0);

            double avgMoisture = yearlyDataList.stream()
                    .filter(data -> data.getMoistureContent() != null)
                    .mapToDouble(data -> data.getMoistureContent().doubleValue())
                    .average().orElse(0.0);

            // 铁精粉相关数据（从实际数据计算）
            result.put("concentrateVolume", totalVolume.compareTo(BigDecimal.ZERO) > 0 ? totalVolume : null);
            result.put("concentrateGrade", avgTfeContent > 0 ? BigDecimal.valueOf(avgTfeContent) : null);
            result.put("concentrateFineness", avgFineness > 0 ? BigDecimal.valueOf(avgFineness) : null);
            result.put("ironConcentrateMoisture", avgMoisture > 0 ? BigDecimal.valueOf(avgMoisture) : null);
        } else {
            // 如果没有数据，设置铁精粉相关数据为null
            result.put("concentrateVolume", null);
            result.put("concentrateGrade", null);
            result.put("concentrateFineness", null);
            result.put("ironConcentrateMoisture", null);
        }

        // 其他数据暂时设为空（没有对应的数据表）
        result.put("rawOreProcessingVolume", null);
        result.put("drySeparationVolume", null);
        result.put("grindingFeedVolume", null);
        result.put("rawOreGradeTfe", null);
        result.put("rawOreGradeMfe", null);
        result.put("tailingGradeTfe", null);
        result.put("tailingGradeMfe", null);
        result.put("comprehensiveRatio", null);
        result.put("grindingRatio", null);
        result.put("tailingsAgitatorTank", null);
        result.put("overflowOfTailings", null);

        return result;
    }

    /**
     * 构建表格数据
     */
    private List<MonthlyMineralReportTableVo> buildTableData(Map<String, BigDecimal> monthlyPlans,
                                                             Map<String, BigDecimal> monthlyCompleted,
                                                             Map<String, BigDecimal> yearlyBudgetPlans,
                                                             Map<String, BigDecimal> yearlyPlans,
                                                             Map<String, BigDecimal> yearlyCompleted) {
        List<MonthlyMineralReportTableVo> result = new ArrayList<>();
        int serialNumber = 1;

        // 原矿处理量
        result.add(createReportItem(String.valueOf(serialNumber++), "原矿处理量", "原矿处理量", "t",
                monthlyPlans.get("rawOreProcessingVolume"), monthlyCompleted.get("rawOreProcessingVolume"),
                yearlyBudgetPlans.get("rawOreProcessingVolume"), yearlyPlans.get("rawOreProcessingVolume"),
                yearlyCompleted.get("rawOreProcessingVolume")));
        result.add(createReportItem("", "", "干抛量", "t",
                monthlyPlans.get("drySeparationVolume"), monthlyCompleted.get("drySeparationVolume"),
                yearlyBudgetPlans.get("drySeparationVolume"), yearlyPlans.get("drySeparationVolume"),
                yearlyCompleted.get("drySeparationVolume")));
        result.add(createReportItem("", "", "入磨量", "t",
                monthlyPlans.get("grindingFeedVolume"), monthlyCompleted.get("grindingFeedVolume"),
                yearlyBudgetPlans.get("grindingFeedVolume"), yearlyPlans.get("grindingFeedVolume"),
                yearlyCompleted.get("grindingFeedVolume")));

        // 原矿入磨品位
        result.add(createReportItem(String.valueOf(serialNumber++), "原矿入磨品位", "TFe", "%",
                monthlyPlans.get("rawOreGradeTfe"), monthlyCompleted.get("rawOreGradeTfe"),
                yearlyBudgetPlans.get("rawOreGradeTfe"), yearlyPlans.get("rawOreGradeTfe"),
                yearlyCompleted.get("rawOreGradeTfe")));
        result.add(createReportItem("", "", "mFe", "%",
                monthlyPlans.get("rawOreGradeMfe"), monthlyCompleted.get("rawOreGradeMfe"),
                yearlyBudgetPlans.get("rawOreGradeMfe"), yearlyPlans.get("rawOreGradeMfe"),
                yearlyCompleted.get("rawOreGradeMfe")));

        // 精粉
        result.add(createReportItem(String.valueOf(serialNumber++), "精粉", "TFe", "%",
                monthlyPlans.get("concentrateGrade"), monthlyCompleted.get("concentrateGrade"),
                yearlyBudgetPlans.get("concentrateGrade"), yearlyPlans.get("concentrateGrade"),
                yearlyCompleted.get("concentrateGrade")));
        result.add(createReportItem("", "", "精矿细度(-500目含量)", "%",
                monthlyPlans.get("concentrateFineness"), monthlyCompleted.get("concentrateFineness"),
                yearlyBudgetPlans.get("concentrateFineness"), yearlyPlans.get("concentrateFineness"),
                yearlyCompleted.get("concentrateFineness")));
        result.add(createReportItem("", "", "产量", "t",
                monthlyPlans.get("concentrateVolume"), monthlyCompleted.get("concentrateVolume"),
                yearlyBudgetPlans.get("concentrateVolume"), yearlyPlans.get("concentrateVolume"),
                yearlyCompleted.get("concentrateVolume")));

        // 铁精粉水分
        result.add(createReportItem(String.valueOf(serialNumber++), "铁精粉水分", "水分", "%",
                monthlyPlans.get("ironConcentrateMoisture"), monthlyCompleted.get("ironConcentrateMoisture"),
                yearlyBudgetPlans.get("ironConcentrateMoisture"), yearlyPlans.get("ironConcentrateMoisture"),
                yearlyCompleted.get("ironConcentrateMoisture")));

        // 尾矿品位
        result.add(createReportItem(String.valueOf(serialNumber++), "尾矿品位", "TFe", "%",
                monthlyPlans.get("tailingGradeTfe"), monthlyCompleted.get("tailingGradeTfe"),
                yearlyBudgetPlans.get("tailingGradeTfe"), yearlyPlans.get("tailingGradeTfe"),
                yearlyCompleted.get("tailingGradeTfe")));
        result.add(createReportItem("", "", "mFe", "%",
                monthlyPlans.get("tailingGradeMfe"), monthlyCompleted.get("tailingGradeMfe"),
                yearlyBudgetPlans.get("tailingGradeMfe"), yearlyPlans.get("tailingGradeMfe"),
                yearlyCompleted.get("tailingGradeMfe")));

        // 综合选比
        result.add(createReportItem(String.valueOf(serialNumber++), "综合选比", "综合选比", "倍",
                monthlyPlans.get("comprehensiveRatio"), monthlyCompleted.get("comprehensiveRatio"),
                yearlyBudgetPlans.get("comprehensiveRatio"), yearlyPlans.get("comprehensiveRatio"),
                yearlyCompleted.get("comprehensiveRatio")));

        // 入磨选比
        result.add(createReportItem(String.valueOf(serialNumber++), "入磨选比", "入磨选比", "倍",
                monthlyPlans.get("grindingRatio"), monthlyCompleted.get("grindingRatio"),
                yearlyBudgetPlans.get("grindingRatio"), yearlyPlans.get("grindingRatio"),
                yearlyCompleted.get("grindingRatio")));

        // 尾矿搅拌槽
        result.add(createReportItem(String.valueOf(serialNumber++), "尾矿搅拌槽", "尾矿粒级(-200目)", "",
                monthlyPlans.get("tailingsAgitatorTank"), monthlyCompleted.get("tailingsAgitatorTank"),
                yearlyBudgetPlans.get("tailingsAgitatorTank"), yearlyPlans.get("tailingsAgitatorTank"),
                yearlyCompleted.get("tailingsAgitatorTank")));

        // 尾矿脱水筛上量
        result.add(createReportItem(String.valueOf(serialNumber++), "尾矿脱水筛上量", "尾矿脱水筛上量", "t",
                monthlyPlans.get("overflowOfTailings"), monthlyCompleted.get("overflowOfTailings"),
                yearlyBudgetPlans.get("overflowOfTailings"), yearlyPlans.get("overflowOfTailings"),
                yearlyCompleted.get("overflowOfTailings")));

        // 综合回收率（计算得出）
        BigDecimal concentrateVolumePlan = monthlyPlans.get("concentrateVolume");
        BigDecimal rawOreVolumePlan = monthlyPlans.get("rawOreProcessingVolume");
        BigDecimal recoveryRatePlan = calculateRecoveryRate(concentrateVolumePlan, rawOreVolumePlan);
        
        BigDecimal concentrateVolumeCompleted = monthlyCompleted.get("concentrateVolume");
        BigDecimal rawOreVolumeCompleted = monthlyCompleted.get("rawOreProcessingVolume");
        BigDecimal recoveryRateCompleted = calculateRecoveryRate(concentrateVolumeCompleted, rawOreVolumeCompleted);
        
        BigDecimal concentrateVolumeYearlyBudget = yearlyBudgetPlans.get("concentrateVolume");
        BigDecimal rawOreVolumeYearlyBudget = yearlyBudgetPlans.get("rawOreProcessingVolume");
        BigDecimal recoveryRateYearlyBudget = calculateRecoveryRate(concentrateVolumeYearlyBudget, rawOreVolumeYearlyBudget);
        
        BigDecimal concentrateVolumeYearlyPlan = yearlyPlans.get("concentrateVolume");
        BigDecimal rawOreVolumeYearlyPlan = yearlyPlans.get("rawOreProcessingVolume");
        BigDecimal recoveryRateYearlyPlan = calculateRecoveryRate(concentrateVolumeYearlyPlan, rawOreVolumeYearlyPlan);
        
        BigDecimal concentrateVolumeYearlyCompleted = yearlyCompleted.get("concentrateVolume");
        BigDecimal rawOreVolumeYearlyCompleted = yearlyCompleted.get("rawOreProcessingVolume");
        BigDecimal recoveryRateYearlyCompleted = calculateRecoveryRate(concentrateVolumeYearlyCompleted, rawOreVolumeYearlyCompleted);
        
        result.add(createReportItem(String.valueOf(serialNumber++), "综合回收率", "综合回收率", "%",
                recoveryRatePlan, recoveryRateCompleted, recoveryRateYearlyBudget, 
                recoveryRateYearlyPlan, recoveryRateYearlyCompleted));

        return result;
    }

    /**
     * 计算回收率
     */
    private BigDecimal calculateRecoveryRate(BigDecimal concentrateVolume, BigDecimal rawOreVolume) {
        if (rawOreVolume == null || rawOreVolume.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (concentrateVolume == null) {
            return BigDecimal.ZERO;
        }
        return concentrateVolume.divide(rawOreVolume, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 创建报表项
     */
    private MonthlyMineralReportTableVo createReportItem(String serialNumber, String name, String subName, String unit,
                                                         BigDecimal monthlyPlan, BigDecimal monthlyCompleted,
                                                         BigDecimal yearlyBudgetPlan, BigDecimal yearlyPlan,
                                                         BigDecimal yearlyCompleted) {
        MonthlyMineralReportTableVo vo = new MonthlyMineralReportTableVo();
        vo.setSerialNumber(serialNumber);
        vo.setName(name);
        vo.setSubName(subName);
        vo.setUnit(unit);
        vo.setMonthlyPlan(monthlyPlan != null ? monthlyPlan : BigDecimal.ZERO);
        vo.setMonthlyCompleted(monthlyCompleted != null ? monthlyCompleted : BigDecimal.ZERO);
        vo.setYearlyBudgetPlan(yearlyBudgetPlan != null ? yearlyBudgetPlan : BigDecimal.ZERO);
        vo.setYearlyPlan(yearlyPlan != null ? yearlyPlan : BigDecimal.ZERO);
        vo.setYearlyCompleted(yearlyCompleted != null ? yearlyCompleted : BigDecimal.ZERO);

        // 计算月完成率
        if (monthlyPlan != null && monthlyPlan.compareTo(BigDecimal.ZERO) > 0 && monthlyCompleted != null) {
            BigDecimal monthlyRate = monthlyCompleted.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setMonthlyCompletionRate(monthlyRate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setMonthlyCompletionRate("0.00%");
        }

        // 计算年度完成率
        if (yearlyPlan != null && yearlyPlan.compareTo(BigDecimal.ZERO) > 0 && yearlyCompleted != null) {
            BigDecimal yearlyRate = yearlyCompleted.divide(yearlyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setYearlyCompletionRate(yearlyRate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setYearlyCompletionRate("0.00%");
        }

        // 计算累计超欠
        BigDecimal yearlyCompletedSafe = yearlyCompleted != null ? yearlyCompleted : BigDecimal.ZERO;
        BigDecimal yearlyPlanSafe = yearlyPlan != null ? yearlyPlan : BigDecimal.ZERO;
        BigDecimal cumulativeVariance = yearlyCompletedSafe.subtract(yearlyPlanSafe);
        vo.setCumulativeVariance(cumulativeVariance);

        return vo;
    }
}
