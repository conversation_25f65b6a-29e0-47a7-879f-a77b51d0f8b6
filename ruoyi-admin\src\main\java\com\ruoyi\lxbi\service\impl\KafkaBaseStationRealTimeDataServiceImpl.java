package com.ruoyi.lxbi.service.impl;

import java.text.SimpleDateFormat;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaBaseStationRealTimeDataMapper;
import com.ruoyi.lxbi.domain.KafkaBaseStationRealTimeData;
import com.ruoyi.lxbi.service.IKafkaBaseStationRealTimeDataService;

/**
 * 基站实时数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class KafkaBaseStationRealTimeDataServiceImpl implements IKafkaBaseStationRealTimeDataService
{
    @Autowired
    private KafkaBaseStationRealTimeDataMapper kafkaBaseStationRealTimeDataMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询基站实时数据
     * 
     * @param id 基站实时数据主键
     * @return 基站实时数据
     */
    @Override
    public KafkaBaseStationRealTimeData selectKafkaBaseStationRealTimeDataById(Long id)
    {
        return kafkaBaseStationRealTimeDataMapper.selectKafkaBaseStationRealTimeDataById(id);
    }

    /**
     * 查询基站实时数据列表
     * 
     * @param kafkaBaseStationRealTimeData 基站实时数据
     * @return 基站实时数据
     */
    @Override
    public List<KafkaBaseStationRealTimeData> selectKafkaBaseStationRealTimeDataList(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData)
    {
        return kafkaBaseStationRealTimeDataMapper.selectKafkaBaseStationRealTimeDataList(kafkaBaseStationRealTimeData);
    }

    /**
     * 新增基站实时数据
     * 
     * @param kafkaBaseStationRealTimeData 基站实时数据
     * @return 结果
     */
    @Override
    public int insertKafkaBaseStationRealTimeData(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData)
    {
        kafkaBaseStationRealTimeData.setCreateTime(DateUtils.getNowDate());
        return kafkaBaseStationRealTimeDataMapper.insertKafkaBaseStationRealTimeData(kafkaBaseStationRealTimeData);
    }

    /**
     * 修改基站实时数据
     * 
     * @param kafkaBaseStationRealTimeData 基站实时数据
     * @return 结果
     */
    @Override
    public int updateKafkaBaseStationRealTimeData(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData)
    {
        kafkaBaseStationRealTimeData.setUpdateTime(DateUtils.getNowDate());
        return kafkaBaseStationRealTimeDataMapper.updateKafkaBaseStationRealTimeData(kafkaBaseStationRealTimeData);
    }

    /**
     * 批量删除基站实时数据
     * 
     * @param ids 需要删除的基站实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaBaseStationRealTimeDataByIds(Long[] ids)
    {
        return kafkaBaseStationRealTimeDataMapper.deleteKafkaBaseStationRealTimeDataByIds(ids);
    }

    /**
     * 删除基站实时数据信息
     *
     * @param id 基站实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaBaseStationRealTimeDataById(Long id)
    {
        return kafkaBaseStationRealTimeDataMapper.deleteKafkaBaseStationRealTimeDataById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka基站实时数据消息");

            // 解析Kafka消息
            KafkaBaseStationRealTimeData baseStationData = parseKafkaMessage(kafkaMessage);
            if (baseStationData == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(baseStationData.getBaseStationCode())) {
                log.warn("基站编码为空，跳过处理");
                return false;
            }

            // 执行插入操作
            int result = insertKafkaBaseStationRealTimeData(baseStationData);

            if (result > 0) {
                log.info("成功插入基站实时数据，基站编码: {}, 煤矿代码: {}",
                    baseStationData.getBaseStationCode(), baseStationData.getMineCode());
                return true;
            } else {
                log.warn("插入基站实时数据失败，基站编码: {}",
                    baseStationData.getBaseStationCode());
                return false;
            }

        } catch (Exception e) {
            log.error("处理Kafka基站实时数据消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaBaseStationRealTimeData parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaBaseStationRealTimeData baseStationData = new KafkaBaseStationRealTimeData();

            // 基础信息
            baseStationData.setFileEncoding(getStringValue(jsonNode, "文件前缀"));
            baseStationData.setMineCode(getStringValue(jsonNode, "煤矿编码"));
            baseStationData.setMineName(getStringValue(jsonNode, "矿井名称"));
            baseStationData.setDataUploadTime(getDateValue(jsonNode, "数据上传时间"));
            baseStationData.setBaseStationCode(getStringValue(jsonNode, "基站编码"));
            baseStationData.setBaseStationRunningStatus(getStringValue(jsonNode, "基站运行状态"));
            baseStationData.setBaseStationPowerSupplyStatus(getStringValue(jsonNode, "基站供电状态"));
            baseStationData.setDataTime(getDateValue(jsonNode, "数据时间"));

            // 默认值
            baseStationData.setIsDeleted(0L);
            baseStationData.setCreateTime(DateUtils.getNowDate());
            baseStationData.setUpdateTime(DateUtils.getNowDate());

            return baseStationData;

        } catch (Exception e) {
            log.error("解析Kafka基站实时数据消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null && !node.isNull() ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private java.util.Date getDateValue(JsonNode jsonNode, String fieldName) {
        try {
            String dateStr = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            }
        } catch (Exception e) {
            log.warn("解析日期字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }
}
