package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * AI平台登录响应VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiLoginResponseVO {

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应代码
     */
    private String code;

    /**
     * 响应数据
     */
    private AiLoginDataVO data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AiLoginDataVO {

        /**
         * 访问令牌
         */
        private String accessToken;

        /**
         * 用户数据字典
         */
        private UserDataDict userDataDict;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class UserDataDict {
            private Integer id;
            private String name;
            private String account;
            private String password;
            private String phone;
            private String e_mail;
            private String face_img;
            private String role_id;
            private Integer status;
            private String creation_time;
            private String revision_time;
            private String role_name;
            private String status_name;
        }
    }
}
