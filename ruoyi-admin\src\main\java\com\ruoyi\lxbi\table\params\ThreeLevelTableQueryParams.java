package com.ruoyi.lxbi.table.params;

/**
 * 三层表头查询参数
 */
public class ThreeLevelTableQueryParams {
    
    /** 页码 */
    private Integer pageNum;
    
    /** 页大小 */
    private Integer pageSize;
    
    /** 过滤条件 */
    private String filter;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    @Override
    public String toString() {
        return "ThreeLevelTableQueryParams{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", filter='" + filter + '\'' +
                '}';
    }
}
