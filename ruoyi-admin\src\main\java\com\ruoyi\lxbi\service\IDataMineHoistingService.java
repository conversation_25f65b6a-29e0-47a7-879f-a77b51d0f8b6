package com.ruoyi.lxbi.service;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.domain.DataMineHoisting;
import com.ruoyi.lxbi.domain.response.DataMineHoistingVo;
import com.ruoyi.lxbi.domain.request.DataMineHoistingBatchDto;

/**
 * 矿井提升数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IDataMineHoistingService 
{
    /**
     * 查询矿井提升数据
     * 
     * @param id 矿井提升数据主键
     * @return 矿井提升数据
     */
    public DataMineHoisting selectDataMineHoistingById(Long id);

    /**
     * 查询矿井提升数据列表
     *
     * @param dataMineHoisting 矿井提升数据
     * @return 矿井提升数据集合
     */
    public List<DataMineHoistingVo> selectDataMineHoistingList(DataMineHoisting dataMineHoisting);

    /**
     * 新增矿井提升数据
     * 
     * @param dataMineHoisting 矿井提升数据
     * @return 结果
     */
    public int insertDataMineHoisting(DataMineHoisting dataMineHoisting);

    /**
     * 修改矿井提升数据
     * 
     * @param dataMineHoisting 矿井提升数据
     * @return 结果
     */
    public int updateDataMineHoisting(DataMineHoisting dataMineHoisting);

    /**
     * 批量删除矿井提升数据
     * 
     * @param ids 需要删除的矿井提升数据主键集合
     * @return 结果
     */
    public int deleteDataMineHoistingByIds(Long[] ids);

    /**
     * 删除矿井提升数据信息
     *
     * @param id 矿井提升数据主键
     * @return 结果
     */
    public int deleteDataMineHoistingById(Long id);

    /**
     * 根据作业日期查询矿井提升数据列表
     *
     * @param operationDate 作业日期
     * @return 矿井提升数据集合
     */
    public List<DataMineHoistingVo> selectDataMineHoistingByOperationDate(Date operationDate);

    /**
     * 批量保存矿井提升数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    public int batchSaveDataMineHoisting(List<DataMineHoistingBatchDto> batchDataList);
}
