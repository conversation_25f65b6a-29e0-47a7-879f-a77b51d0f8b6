package com.ruoyi.lxbi.domain.response;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.lxbi.domain.DataOrepassOperation;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DataOrepassOperationVo extends DataOrepassOperation {

    @Excel(name = "作业时段名称", sort = 3, mergeByValue = true)
    private String workingPeriodName;

    @Excel(name = "项目部门名称", sort = 2, mergeByValue = true)
    private String projectDepartmentName;

    @Excel(name = "溜井名称", sort = 4)
    private String orePassName;

}
