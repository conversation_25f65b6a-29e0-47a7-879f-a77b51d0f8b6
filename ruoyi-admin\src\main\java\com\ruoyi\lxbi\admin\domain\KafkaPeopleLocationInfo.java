package com.ruoyi.lxbi.admin.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 区域基本信息数据对象 kafka_people_location_info
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public class KafkaPeopleLocationInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 煤矿编码 */
    @Excel(name = "煤矿编码")
    private String mineCode;

    /** 矿井名称 */
    @Excel(name = "矿井名称")
    private String mineName;

    /** 数据上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "数据上传时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dataUploadTime;

    /** 区域类型 */
    @Excel(name = "区域类型")
    private String areaType;

    /** 区域编码 */
    @Excel(name = "区域编码")
    private String areaCode;

    /** 区域核定人数 */
    @Excel(name = "区域核定人数")
    private Long areaApprovedPersonnel;

    /** 区域名称 */
    @Excel(name = "区域名称")
    private String areaName;

    /** 状态(1:正常 0:异常) */
    private Long status;

    /** 是否删除(0:未删除 1:已删除) */
    private Long isDeleted;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMineCode(String mineCode) 
    {
        this.mineCode = mineCode;
    }

    public String getMineCode() 
    {
        return mineCode;
    }
    public void setMineName(String mineName) 
    {
        this.mineName = mineName;
    }

    public String getMineName() 
    {
        return mineName;
    }
    public void setDataUploadTime(Date dataUploadTime) 
    {
        this.dataUploadTime = dataUploadTime;
    }

    public Date getDataUploadTime() 
    {
        return dataUploadTime;
    }
    public void setAreaType(String areaType) 
    {
        this.areaType = areaType;
    }

    public String getAreaType() 
    {
        return areaType;
    }
    public void setAreaCode(String areaCode) 
    {
        this.areaCode = areaCode;
    }

    public String getAreaCode() 
    {
        return areaCode;
    }
    public void setAreaApprovedPersonnel(Long areaApprovedPersonnel) 
    {
        this.areaApprovedPersonnel = areaApprovedPersonnel;
    }

    public Long getAreaApprovedPersonnel() 
    {
        return areaApprovedPersonnel;
    }
    public void setAreaName(String areaName) 
    {
        this.areaName = areaName;
    }

    public String getAreaName() 
    {
        return areaName;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setIsDeleted(Long isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Long getIsDeleted() 
    {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("mineCode", getMineCode())
            .append("mineName", getMineName())
            .append("dataUploadTime", getDataUploadTime())
            .append("areaType", getAreaType())
            .append("areaCode", getAreaCode())
            .append("areaApprovedPersonnel", getAreaApprovedPersonnel())
            .append("areaName", getAreaName())
            .append("status", getStatus())
            .append("isDeleted", getIsDeleted())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
