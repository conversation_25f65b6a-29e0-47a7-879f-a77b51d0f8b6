package com.ruoyi.lxbi.service.impl;

import java.text.SimpleDateFormat;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaSubstationRealTimeDataMapper;
import com.ruoyi.lxbi.domain.KafkaSubstationRealTimeData;
import com.ruoyi.lxbi.service.IKafkaSubstationRealTimeDataService;

/**
 * 分站实时数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class KafkaSubstationRealTimeDataServiceImpl implements IKafkaSubstationRealTimeDataService
{
    @Autowired
    private KafkaSubstationRealTimeDataMapper kafkaSubstationRealTimeDataMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询分站实时数据
     * 
     * @param id 分站实时数据主键
     * @return 分站实时数据
     */
    @Override
    public KafkaSubstationRealTimeData selectKafkaSubstationRealTimeDataById(Long id)
    {
        return kafkaSubstationRealTimeDataMapper.selectKafkaSubstationRealTimeDataById(id);
    }

    /**
     * 查询分站实时数据列表
     * 
     * @param kafkaSubstationRealTimeData 分站实时数据
     * @return 分站实时数据
     */
    @Override
    public List<KafkaSubstationRealTimeData> selectKafkaSubstationRealTimeDataList(KafkaSubstationRealTimeData kafkaSubstationRealTimeData)
    {
        return kafkaSubstationRealTimeDataMapper.selectKafkaSubstationRealTimeDataList(kafkaSubstationRealTimeData);
    }

    /**
     * 新增分站实时数据
     * 
     * @param kafkaSubstationRealTimeData 分站实时数据
     * @return 结果
     */
    @Override
    public int insertKafkaSubstationRealTimeData(KafkaSubstationRealTimeData kafkaSubstationRealTimeData)
    {
        kafkaSubstationRealTimeData.setCreateTime(DateUtils.getNowDate());
        return kafkaSubstationRealTimeDataMapper.insertKafkaSubstationRealTimeData(kafkaSubstationRealTimeData);
    }

    /**
     * 修改分站实时数据
     * 
     * @param kafkaSubstationRealTimeData 分站实时数据
     * @return 结果
     */
    @Override
    public int updateKafkaSubstationRealTimeData(KafkaSubstationRealTimeData kafkaSubstationRealTimeData)
    {
        kafkaSubstationRealTimeData.setUpdateTime(DateUtils.getNowDate());
        return kafkaSubstationRealTimeDataMapper.updateKafkaSubstationRealTimeData(kafkaSubstationRealTimeData);
    }

    /**
     * 批量删除分站实时数据
     * 
     * @param ids 需要删除的分站实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaSubstationRealTimeDataByIds(Long[] ids)
    {
        return kafkaSubstationRealTimeDataMapper.deleteKafkaSubstationRealTimeDataByIds(ids);
    }

    /**
     * 删除分站实时数据信息
     *
     * @param id 分站实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaSubstationRealTimeDataById(Long id)
    {
        return kafkaSubstationRealTimeDataMapper.deleteKafkaSubstationRealTimeDataById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka分站实时数据消息");

            // 解析Kafka消息
            KafkaSubstationRealTimeData realtimeData = parseKafkaMessage(kafkaMessage);
            if (realtimeData == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(realtimeData.getSubstationCode())) {
                log.warn("分站编码为空，跳过处理");
                return false;
            }

            // 执行插入操作
            int result = insertKafkaSubstationRealTimeData(realtimeData);

            if (result > 0) {
                log.info("成功插入分站实时数据，分站编码: {}, 煤矿代码: {}",
                    realtimeData.getSubstationCode(), realtimeData.getMineCode());
                return true;
            } else {
                log.warn("插入分站实时数据失败，分站编码: {}",
                    realtimeData.getSubstationCode());
                return false;
            }

        } catch (Exception e) {
            log.error("处理Kafka分站实时数据消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaSubstationRealTimeData parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaSubstationRealTimeData realtimeData = new KafkaSubstationRealTimeData();

            // 基础信息
            realtimeData.setFileEncoding(getStringValue(jsonNode, "文件前缀"));
            realtimeData.setMineCode(getStringValue(jsonNode, "煤矿编码"));
            realtimeData.setMineName(getStringValue(jsonNode, "矿井名称"));
            realtimeData.setDataUploadTime(getDateValue(jsonNode, "数据上传时间"));
            realtimeData.setSubstationCode(getStringValue(jsonNode, "分站编码"));
            realtimeData.setSubstationRunningStatus(getStringValue(jsonNode, "分站运行状态"));
            realtimeData.setSubstationPowerSupplyStatus(getStringValue(jsonNode, "分站供电状态"));
            realtimeData.setDataTime(getDateValue(jsonNode, "数据时间"));

            // 默认值
            realtimeData.setIsDeleted(0L);
            realtimeData.setCreateTime(DateUtils.getNowDate());
            realtimeData.setUpdateTime(DateUtils.getNowDate());

            return realtimeData;

        } catch (Exception e) {
            log.error("解析Kafka分站实时数据消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null && !node.isNull() ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private java.util.Date getDateValue(JsonNode jsonNode, String fieldName) {
        try {
            String dateStr = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            }
        } catch (Exception e) {
            log.warn("解析日期字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }
}
