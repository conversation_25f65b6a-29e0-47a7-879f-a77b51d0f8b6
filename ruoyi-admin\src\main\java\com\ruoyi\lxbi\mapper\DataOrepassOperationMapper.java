package com.ruoyi.lxbi.mapper;

import java.util.List;
import java.util.Date;
import com.ruoyi.lxbi.domain.DataOrepassOperation;
import com.ruoyi.lxbi.domain.response.DataOrepassOperationVo;
import org.apache.ibatis.annotations.Param;

/**
 * 溜井运行数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface DataOrepassOperationMapper 
{
    /**
     * 查询溜井运行数据
     * 
     * @param id 溜井运行数据主键
     * @return 溜井运行数据
     */
    public DataOrepassOperation selectDataOrepassOperationById(Long id);

    /**
     * 查询溜井运行数据列表
     *
     * @param dataOrepassOperation 溜井运行数据
     * @return 溜井运行数据集合
     */
    public List<DataOrepassOperationVo> selectDataOrepassOperationList(DataOrepassOperation dataOrepassOperation);

    /**
     * 新增溜井运行数据
     * 
     * @param dataOrepassOperation 溜井运行数据
     * @return 结果
     */
    public int insertDataOrepassOperation(DataOrepassOperation dataOrepassOperation);

    /**
     * 修改溜井运行数据
     * 
     * @param dataOrepassOperation 溜井运行数据
     * @return 结果
     */
    public int updateDataOrepassOperation(DataOrepassOperation dataOrepassOperation);

    /**
     * 删除溜井运行数据
     * 
     * @param id 溜井运行数据主键
     * @return 结果
     */
    public int deleteDataOrepassOperationById(Long id);

    /**
     * 批量删除溜井运行数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataOrepassOperationByIds(Long[] ids);

    /**
     * 根据作业日期和项目部门查询溜井运行数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 溜井运行数据集合
     */
    public List<DataOrepassOperationVo> selectDataOrepassOperationByOperationDateAndProject(@Param("operationDate") Date operationDate, @Param("projectDepartmentId") Long projectDepartmentId);

    /**
     * 批量新增溜井运行数据
     *
     * @param dataOrepassOperationList 溜井运行数据列表
     * @return 结果
     */
    public int batchInsertDataOrepassOperation(List<DataOrepassOperation> dataOrepassOperationList);
}
