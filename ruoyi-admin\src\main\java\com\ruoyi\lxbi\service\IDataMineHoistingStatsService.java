package com.ruoyi.lxbi.service;

import com.ruoyi.lxbi.domain.request.DataMineHoistingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataMineHoistingStats;
import com.ruoyi.lxbi.domain.response.DataMineHoistingPeriodStats;

import java.util.List;

/**
 * 矿井提升数据统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IDataMineHoistingStatsService {

    /**
     * 查询统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 统计数据集合
     */
    public List<DataMineHoistingStats> selectStatsList(DataMineHoistingStatsRequest request, String viewType);

    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    public List<DataMineHoistingPeriodStats> selectPeriodStatsList(DataMineHoistingStatsRequest request, String viewType);

}
