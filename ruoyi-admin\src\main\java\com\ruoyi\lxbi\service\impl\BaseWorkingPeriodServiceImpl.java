package com.ruoyi.lxbi.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.lxbi.mapper.BaseWorkingPeriodMapper;
import com.ruoyi.lxbi.domain.BaseWorkingPeriod;
import com.ruoyi.lxbi.service.IBaseWorkingPeriodService;

/**
 * 作业时段配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Service
public class BaseWorkingPeriodServiceImpl implements IBaseWorkingPeriodService 
{
    @Autowired
    private BaseWorkingPeriodMapper baseWorkingPeriodMapper;

    /**
     * 查询作业时段配置
     * 
     * @param workingPeriodId 作业时段配置主键
     * @return 作业时段配置
     */
    @Override
    public BaseWorkingPeriod selectBaseWorkingPeriodByWorkingPeriodId(Long workingPeriodId)
    {
        return baseWorkingPeriodMapper.selectBaseWorkingPeriodByWorkingPeriodId(workingPeriodId);
    }

    /**
     * 查询作业时段配置列表
     * 
     * @param baseWorkingPeriod 作业时段配置
     * @return 作业时段配置
     */
    @Override
    public List<BaseWorkingPeriod> selectBaseWorkingPeriodList(BaseWorkingPeriod baseWorkingPeriod)
    {
        return baseWorkingPeriodMapper.selectBaseWorkingPeriodList(baseWorkingPeriod);
    }

    /**
     * 新增作业时段配置
     * 
     * @param baseWorkingPeriod 作业时段配置
     * @return 结果
     */
    @Override
    public int insertBaseWorkingPeriod(BaseWorkingPeriod baseWorkingPeriod)
    {
        baseWorkingPeriod.setCreateTime(DateUtils.getNowDate());
        return baseWorkingPeriodMapper.insertBaseWorkingPeriod(baseWorkingPeriod);
    }

    /**
     * 修改作业时段配置
     * 
     * @param baseWorkingPeriod 作业时段配置
     * @return 结果
     */
    @Override
    public int updateBaseWorkingPeriod(BaseWorkingPeriod baseWorkingPeriod)
    {
        baseWorkingPeriod.setUpdateTime(DateUtils.getNowDate());
        return baseWorkingPeriodMapper.updateBaseWorkingPeriod(baseWorkingPeriod);
    }

    /**
     * 批量删除作业时段配置
     * 
     * @param workingPeriodIds 需要删除的作业时段配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseWorkingPeriodByWorkingPeriodIds(Long[] workingPeriodIds)
    {
        if (workingPeriodIds == null || workingPeriodIds.length == 0) {
            throw new ServiceException("作业时段配置主键不能为空");
        }
        if (baseWorkingPeriodMapper.getBaseWorkingPeriodByWorkingPeriodIds(workingPeriodIds) > 0) {
            throw new ServiceException("存在使用中的作业时段配置，无法删除");
        }
        return baseWorkingPeriodMapper.deleteBaseWorkingPeriodByWorkingPeriodIds(workingPeriodIds);
    }

    /**
     * 删除作业时段配置信息
     * 
     * @param workingPeriodId 作业时段配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseWorkingPeriodByWorkingPeriodId(Long workingPeriodId)
    {
        return baseWorkingPeriodMapper.deleteBaseWorkingPeriodByWorkingPeriodId(workingPeriodId);
    }
}
