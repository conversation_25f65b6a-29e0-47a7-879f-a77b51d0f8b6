#!/bin/bash

# 地表监测数据API测试脚本
# 作者: ruoyi
# 日期: 2025-08-23

BASE_URL="http://localhost:8080"

echo "=========================================="
echo "地表监测数据API功能测试"
echo "=========================================="

# 1. 测试API健康状态
echo "1. 测试API健康状态..."
curl -X GET "${BASE_URL}/lxbi/surfaceMonitoring/health" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n"

# 2. 测试第三方API连接
echo "2. 测试第三方API连接..."
curl -X POST "${BASE_URL}/lxbi/surfaceMonitoring/testConnection" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n"

# 3. 同步第三方地表监测数据
echo "3. 同步第三方地表监测数据..."
curl -X POST "${BASE_URL}/lxbi/surfaceMonitoring/sync" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n"

# 4. 获取统计数据
echo "4. 获取统计数据..."
curl -X GET "${BASE_URL}/lxbi/surfaceMonitoring/statistics" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n"

# 5. 获取最新监测数据
echo "5. 获取最新监测数据（前5条）..."
curl -X GET "${BASE_URL}/lxbi/surfaceMonitoring/latest?limit=5" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n"

# 6. 获取偏移异常数据
echo "6. 获取偏移异常数据（阈值5.0mm）..."
curl -X GET "${BASE_URL}/lxbi/surfaceMonitoring/abnormal?threshold=5.0" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n"

# 7. 按日期范围查询数据
echo "7. 按日期范围查询数据（最近7天）..."
START_DATE=$(date -d "7 days ago" +%Y-%m-%d)
END_DATE=$(date +%Y-%m-%d)
curl -X GET "${BASE_URL}/lxbi/surfaceMonitoring/dateRange?startDate=${START_DATE}&endDate=${END_DATE}" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n"

# 8. 测试处理单条API数据
echo "8. 测试处理单条API数据..."
curl -X POST "${BASE_URL}/lxbi/surfaceMonitoring/test/processApiData" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2025-08-23",
    "station_name": "测试基站001",
    "wgbh": "TEST001",
    "x1": 10,
    "x2": 5,
    "x3": 3,
    "x4": 2,
    "y1": 8,
    "y2": 6,
    "y3": 4,
    "y4": 2,
    "h1": 12,
    "h2": 8,
    "h3": 5,
    "h4": 3,
    "xstackedTotalOffset": 15.5,
    "ystackedTotalOffset": 12.3,
    "hstackedTotalOffset": 18.7,
    "id": 12345
  }' \
  | jq '.'

echo -e "\n"

# 9. 按今天日期获取统计信息
echo "9. 按今天日期获取统计信息..."
TODAY=$(date +%Y-%m-%d)
curl -X GET "${BASE_URL}/lxbi/surfaceMonitoring/statistics/${TODAY}" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n"

# 10. 查询数据列表（分页）
echo "10. 查询数据列表（分页）..."
curl -X GET "${BASE_URL}/lxbi/surfaceMonitoring/list?pageNum=1&pageSize=10" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n"
echo "=========================================="
echo "测试完成"
echo "=========================================="
