package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.Date;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.lxbi.domain.response.DataCrushingOperationVo;
import com.ruoyi.lxbi.domain.request.DataCrushingOperationBatchDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;
import com.ruoyi.lxbi.mapper.DataCrushingOperationMapper;
import com.ruoyi.lxbi.domain.DataCrushingOperation;
import com.ruoyi.lxbi.service.IDataCrushingOperationService;

/**
 * 破碎数据管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DataCrushingOperationServiceImpl implements IDataCrushingOperationService 
{
    @Autowired
    private DataCrushingOperationMapper dataCrushingOperationMapper;

    /**
     * 查询破碎数据管理
     * 
     * @param id 破碎数据管理主键
     * @return 破碎数据管理
     */
    @Override
    public DataCrushingOperation selectDataCrushingOperationById(Long id)
    {
        return dataCrushingOperationMapper.selectDataCrushingOperationById(id);
    }

    /**
     * 查询破碎数据管理列表
     *
     * @param dataCrushingOperation 破碎数据管理
     * @return 破碎数据管理
     */
    @Override
    public List<DataCrushingOperationVo> selectDataCrushingOperationList(DataCrushingOperation dataCrushingOperation)
    {
        return dataCrushingOperationMapper.selectDataCrushingOperationList(dataCrushingOperation);
    }

    /**
     * 新增破碎数据管理
     * 
     * @param dataCrushingOperation 破碎数据管理
     * @return 结果
     */
    @Override
    public int insertDataCrushingOperation(DataCrushingOperation dataCrushingOperation)
    {
        dataCrushingOperation.setCreateTime(DateUtils.getNowDate());
        return dataCrushingOperationMapper.insertDataCrushingOperation(dataCrushingOperation);
    }

    /**
     * 修改破碎数据管理
     * 
     * @param dataCrushingOperation 破碎数据管理
     * @return 结果
     */
    @Override
    public int updateDataCrushingOperation(DataCrushingOperation dataCrushingOperation)
    {
        dataCrushingOperation.setUpdateTime(DateUtils.getNowDate());
        return dataCrushingOperationMapper.updateDataCrushingOperation(dataCrushingOperation);
    }

    /**
     * 批量删除破碎数据管理
     * 
     * @param ids 需要删除的破碎数据管理主键
     * @return 结果
     */
    @Override
    public int deleteDataCrushingOperationByIds(Long[] ids)
    {
        return dataCrushingOperationMapper.deleteDataCrushingOperationByIds(ids);
    }

    /**
     * 删除破碎数据管理信息
     *
     * @param id 破碎数据管理主键
     * @return 结果
     */
    @Override
    public int deleteDataCrushingOperationById(Long id)
    {
        return dataCrushingOperationMapper.deleteDataCrushingOperationById(id);
    }

    /**
     * 根据作业日期查询破碎数据列表
     *
     * @param operationDate 作业日期
     * @return 破碎数据集合
     */
    @Override
    public List<DataCrushingOperationVo> selectDataCrushingOperationByOperationDate(Date operationDate)
    {
        return dataCrushingOperationMapper.selectDataCrushingOperationByOperationDate(operationDate);
    }

    /**
     * 批量保存破碎数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDataCrushingOperation(List<DataCrushingOperationBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个日期的数据
        Date operationDate = batchDataList.get(0).getOperationDate();
        if (operationDate == null) {
            throw new ServiceException("作业日期不能为空");
        }

        boolean allSameDate = batchDataList.stream()
                .allMatch(data -> operationDate.equals(data.getOperationDate()));
        if (!allSameDate) {
            throw new ServiceException("批量数据必须是同一个作业日期");
        }

        // 查询现有数据
        List<DataCrushingOperationVo> existingDataList = selectDataCrushingOperationByOperationDate(operationDate);
        Map<Long, DataCrushingOperationVo> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(DataCrushingOperationVo::getId, data -> data));

        List<DataCrushingOperation> toInsert = new ArrayList<>();
        List<DataCrushingOperation> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (DataCrushingOperationBatchDto batchData : batchDataList) {
            if (Boolean.TRUE.equals(batchData.getIsNew())) {
                // 新增数据
                DataCrushingOperation newData = new DataCrushingOperation();
                BeanUtils.copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if (batchData.getId() != null && existingDataMap.containsKey(batchData.getId())) {
                // 更新现有数据（根据ID判断）
                DataCrushingOperation updateData = new DataCrushingOperation();
                BeanUtils.copyProperties(batchData, updateData);
                updateData.setId(batchData.getId());
                updateData.setUpdateBy(SecurityUtils.getUsername());
                updateData.setUpdateTime(DateUtils.getNowDate());
                toUpdate.add(updateData);

                // 从现有数据中移除，剩下的就是要删除的
                toDelete.remove(batchData.getId());
            }
        }

        int result = 0;

        // 执行删除
        if (!toDelete.isEmpty()) {
            result += dataCrushingOperationMapper.deleteDataCrushingOperationByIds(toDelete.toArray(new Long[0]));
        }

        // 执行新增
        if (!toInsert.isEmpty()) {
            result += dataCrushingOperationMapper.batchInsertDataCrushingOperation(toInsert);
        }

        // 执行更新
        for (DataCrushingOperation updateData : toUpdate) {
            result += dataCrushingOperationMapper.updateDataCrushingOperation(updateData);
        }

        return result;
    }
}
