package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * AI平台报警数据VO
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiAlarmDataVO {
    
    /**
     * 报警时间
     */
    @JsonProperty("alarm_time")
    private String alarmTime;
    
    /**
     * 报警截图的网络路径
     */
    @JsonProperty("alarm_img")
    private String alarmImg;
    
    /**
     * 产生报警数据的摄像头码流名称
     */
    @JsonProperty("stream_name")
    private String streamName;
    
    /**
     * 该摄像头码流所属的场景名称
     */
    @JsonProperty("scene_name")
    private String sceneName;
    
    /**
     * 产生报警的算法名称
     */
    @JsonProperty("algorithm_name")
    private String algorithmName;
    
    /**
     * 获取完整的报警图片URL
     * @param baseUrl 基础URL
     * @return 完整的图片URL
     */
    public String getFullAlarmImgUrl(String baseUrl) {
        if (alarmImg != null && !alarmImg.startsWith("http")) {
            return baseUrl + (alarmImg.startsWith("/") ? alarmImg : "/" + alarmImg);
        }
        return alarmImg;
    }
}
