package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.Date;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.DataSupportMapper;
import com.ruoyi.lxbi.domain.DataSupport;
import com.ruoyi.lxbi.domain.response.DataSupportVo;
import com.ruoyi.lxbi.domain.request.DataSupportBatchDto;
import com.ruoyi.lxbi.service.IDataSupportService;

/**
 * 支护数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class DataSupportServiceImpl implements IDataSupportService 
{
    @Autowired
    private DataSupportMapper dataSupportMapper;

    /**
     * 查询支护数据
     * 
     * @param id 支护数据主键
     * @return 支护数据
     */
    @Override
    public DataSupport selectDataSupportById(Long id)
    {
        return dataSupportMapper.selectDataSupportById(id);
    }

    /**
     * 查询支护数据列表
     *
     * @param dataSupport 支护数据
     * @return 支护数据
     */
    @Override
    public List<DataSupport> selectDataSupportList(DataSupport dataSupport)
    {
        return dataSupportMapper.selectDataSupportList(dataSupport);
    }

    /**
     * 查询支护数据列表（含关联字段翻译）
     *
     * @param dataSupport 支护数据
     * @return 支护数据集合（含关联字段翻译）
     */
    @Override
    public List<DataSupportVo> selectDataSupportVoList(DataSupport dataSupport)
    {
        return dataSupportMapper.selectDataSupportVoList(dataSupport);
    }

    /**
     * 新增支护数据
     * 
     * @param dataSupport 支护数据
     * @return 结果
     */
    @Override
    public int insertDataSupport(DataSupport dataSupport)
    {
        dataSupport.setCreateTime(DateUtils.getNowDate());
        return dataSupportMapper.insertDataSupport(dataSupport);
    }

    /**
     * 修改支护数据
     * 
     * @param dataSupport 支护数据
     * @return 结果
     */
    @Override
    public int updateDataSupport(DataSupport dataSupport)
    {
        dataSupport.setUpdateTime(DateUtils.getNowDate());
        return dataSupportMapper.updateDataSupport(dataSupport);
    }

    /**
     * 批量删除支护数据
     * 
     * @param ids 需要删除的支护数据主键
     * @return 结果
     */
    @Override
    public int deleteDataSupportByIds(Long[] ids)
    {
        return dataSupportMapper.deleteDataSupportByIds(ids);
    }

    /**
     * 删除支护数据信息
     *
     * @param id 支护数据主键
     * @return 结果
     */
    @Override
    public int deleteDataSupportById(Long id)
    {
        return dataSupportMapper.deleteDataSupportById(id);
    }

    /**
     * 根据作业日期查询支护数据列表（含关联字段翻译）
     *
     * @param operationDate 作业日期
     * @return 支护数据集合（含关联字段翻译）
     */
    @Override
    public List<DataSupportVo> selectDataSupportVoByOperationDate(Date operationDate)
    {
        return dataSupportMapper.selectDataSupportVoByOperationDate(operationDate);
    }

    /**
     * 批量保存支护数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDataSupport(List<DataSupportBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个日期的数据
        Date operationDate = batchDataList.get(0).getOperationDate();
        if (operationDate == null) {
            throw new ServiceException("作业日期不能为空");
        }

        boolean allSameDate = batchDataList.stream()
                .allMatch(data -> operationDate.equals(data.getOperationDate()));
        if (!allSameDate) {
            throw new ServiceException("批量数据必须是同一个作业日期");
        }

        // 查询该日期的现有数据
        List<DataSupportVo> existingDataList = selectDataSupportVoByOperationDate(operationDate);
        Map<String, DataSupportVo> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(this::generateDataKey, data -> data));

        List<DataSupport> toInsertList = new ArrayList<>();
        List<DataSupport> toUpdateList = new ArrayList<>();
        List<Long> toDeleteIds = new ArrayList<>();

        // 处理批量数据
        for (DataSupportBatchDto batchData : batchDataList) {
            String dataKey = generateBatchDataKey(batchData);
            DataSupport dataSupport = new DataSupport();
            BeanUtils.copyProperties(batchData, dataSupport);

            if (Boolean.TRUE.equals(batchData.getIsNew())) {
                // 新增数据
                dataSupport.setCreateBy(SecurityUtils.getUsername());
                dataSupport.setCreateTime(DateUtils.getNowDate());
                toInsertList.add(dataSupport);
            } else {
                // 更新数据
                DataSupportVo existingData = existingDataMap.get(dataKey);
                if (existingData != null) {
                    dataSupport.setId(existingData.getId());
                    dataSupport.setUpdateBy(SecurityUtils.getUsername());
                    dataSupport.setUpdateTime(DateUtils.getNowDate());
                    toUpdateList.add(dataSupport);
                    existingDataMap.remove(dataKey);
                }
            }
        }

        // 剩余的现有数据需要删除
        toDeleteIds.addAll(existingDataMap.values().stream()
                .map(DataSupportVo::getId)
                .collect(Collectors.toList()));

        int totalProcessed = 0;

        // 执行批量操作
        if (!toInsertList.isEmpty()) {
            totalProcessed += dataSupportMapper.batchInsertDataSupport(toInsertList);
        }

        if (!toUpdateList.isEmpty()) {
            totalProcessed += dataSupportMapper.batchUpdateDataSupport(toUpdateList);
        }

        if (!toDeleteIds.isEmpty()) {
            totalProcessed += dataSupportMapper.deleteDataSupportByIds(toDeleteIds.toArray(new Long[0]));
        }

        return totalProcessed;
    }

    /**
     * 生成数据唯一键
     */
    private String generateDataKey(DataSupportVo data) {
        return String.format("%s_%s_%s_%s_%s",
                data.getProjectDepartmentId(),
                data.getStopeId(),
                data.getWorkingFaceId(),
                data.getWorkingPeriodId(),
                data.getSupportType());
    }

    /**
     * 生成批量数据唯一键
     */
    private String generateBatchDataKey(DataSupportBatchDto data) {
        return String.format("%s_%s_%s_%s_%s",
                data.getProjectDepartmentId(),
                data.getStopeId(),
                data.getWorkingFaceId(),
                data.getWorkingPeriodId(),
                data.getSupportType());
    }
}
