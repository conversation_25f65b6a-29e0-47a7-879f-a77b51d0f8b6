<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataMuckingOutMapper">
    
    <resultMap type="DataMuckingOut" id="DataMuckingOutResult">
        <result property="id"    column="id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="tons"    column="tons"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataMuckingOutVo">
        select dmo.id,
               dmo.operation_date,
               dmo.project_department_id,
               dmo.stope_id,
               dmo.working_period_id,
               dmo.tons,
               dmo.create_by,
               dmo.create_time,
               dmo.update_by,
               dmo.update_time,
               bwp.working_period_name,
               bpd.project_department_name,
               bs.stope_name
        from data_mucking_out dmo
                 left join base_working_period bwp on bwp.working_period_id = dmo.working_period_id
                 left join base_project_department bpd on bpd.project_department_id = dmo.project_department_id
                 left join base_stope bs on bs.stope_id = dmo.stope_id
    </sql>

    <select id="selectDataMuckingOutList" parameterType="DataMuckingOut" resultType="com.ruoyi.lxbi.domain.response.DataMuckingOutVo">
        <include refid="selectDataMuckingOutVo"/>
        <where>
            <if test="operationDate != null"> and dmo.operation_date = #{operationDate}</if>
            <if test="params.beginOperationDate != null and params.beginOperationDate != '' and params.endOperationDate != null and params.endOperationDate != ''"> and dmo.operation_date between #{params.beginOperationDate}::date and #{params.endOperationDate}::date</if>
            <if test="projectDepartmentId != null "> and dmo.project_department_id = #{projectDepartmentId}</if>
            <if test="stopeId != null "> and dmo.stope_id = #{stopeId}</if>
            <if test="workingPeriodId != null "> and dmo.working_period_id = #{workingPeriodId}</if>
        </where>
    </select>
    
    <select id="selectDataMuckingOutById" parameterType="Long" resultMap="DataMuckingOutResult">
        <include refid="selectDataMuckingOutVo"/>
        where dmo.id = #{id}
    </select>

    <insert id="insertDataMuckingOut" parameterType="DataMuckingOut" useGeneratedKeys="true" keyProperty="id">
        insert into data_mucking_out
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">operation_date,</if>
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="workingPeriodId != null">working_period_id,</if>
            <if test="tons != null">tons,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">#{operationDate},</if>
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="workingPeriodId != null">#{workingPeriodId},</if>
            <if test="tons != null">#{tons},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataMuckingOut" parameterType="DataMuckingOut">
        update data_mucking_out
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="workingPeriodId != null">working_period_id = #{workingPeriodId},</if>
            <if test="tons != null">tons = #{tons},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataMuckingOutById" parameterType="Long">
        delete from data_mucking_out where id = #{id}
    </delete>

    <delete id="deleteDataMuckingOutByIds" parameterType="String">
        delete from data_mucking_out where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据作业日期和项目部门查询出矿数据列表 -->
    <select id="selectDataMuckingOutByOperationDateAndProject" resultType="com.ruoyi.lxbi.domain.response.DataMuckingOutVo">
        <include refid="selectDataMuckingOutVo"/>
        where dmo.operation_date = #{operationDate}::date
        and dmo.project_department_id = #{projectDepartmentId}
        order by dmo.working_period_id, dmo.stope_id
    </select>

    <!-- 批量新增出矿数据 -->
    <insert id="batchInsertDataMuckingOut" parameterType="java.util.List">
        insert into data_mucking_out (operation_date, project_department_id, stope_id, working_period_id, tons, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.operationDate}, #{item.projectDepartmentId}, #{item.stopeId}, #{item.workingPeriodId}, #{item.tons}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>