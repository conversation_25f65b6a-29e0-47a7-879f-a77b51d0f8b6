package com.ruoyi.lxbi.domain.response;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * Excel合并单元格示例VO
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@Data
public class ExcelMergeExampleVo {

    /** 项目部门名称 - 第一级合并（sort=1，优先级最高） */
    @Excel(name = "项目部门", sort = 1, mergeByValue = true)
    private String projectDepartmentName;

    /** 工作面名称 - 第二级合并（sort=2，在项目部门合并基础上合并） */
    @Excel(name = "工作面", sort = 2, mergeByValue = true)
    private String workingFaceName;

    /** 采场名称 - 第三级合并（sort=3，在工作面合并基础上合并） */
    @Excel(name = "采场名称", sort = 3, mergeByValue = true)
    private String stopeName;

    /** 作业日期 - 不合并 */
    @Excel(name = "作业日期", sort = 4, dateFormat = "yyyy-MM-dd")
    private String operationDate;

    /** 出矿量 - 不合并 */
    @Excel(name = "出矿量(吨)", sort = 5)
    private Double tons;

    /** 备注 - 不合并 */
    @Excel(name = "备注", sort = 6)
    private String remark;

}
