package com.ruoyi.lxbi.mapper;

import java.util.List;
import com.ruoyi.lxbi.domain.BaseWorkingFace;

/**
 * 中段-工作面配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface BaseWorkingFaceMapper 
{
    /**
     * 查询中段-工作面配置
     * 
     * @param workingFaceId 中段-工作面配置主键
     * @return 中段-工作面配置
     */
    public BaseWorkingFace selectBaseWorkingFaceByWorkingFaceId(Long workingFaceId);

    /**
     * 查询中段-工作面配置列表
     * 
     * @param baseWorkingFace 中段-工作面配置
     * @return 中段-工作面配置集合
     */
    public List<BaseWorkingFace> selectBaseWorkingFaceList(BaseWorkingFace baseWorkingFace);

    /**
     * 新增中段-工作面配置
     * 
     * @param baseWorkingFace 中段-工作面配置
     * @return 结果
     */
    public int insertBaseWorkingFace(BaseWorkingFace baseWorkingFace);

    /**
     * 修改中段-工作面配置
     * 
     * @param baseWorkingFace 中段-工作面配置
     * @return 结果
     */
    public int updateBaseWorkingFace(BaseWorkingFace baseWorkingFace);

    /**
     * 删除中段-工作面配置
     * 
     * @param workingFaceId 中段-工作面配置主键
     * @return 结果
     */
    public int deleteBaseWorkingFaceByWorkingFaceId(Long workingFaceId);

    /**
     * 批量删除中段-工作面配置
     * 
     * @param workingFaceIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseWorkingFaceByWorkingFaceIds(Long[] workingFaceIds);

    int getBaseWorkingFaceByWorkingFaceIds(Long[] workingFaceIds);
}
