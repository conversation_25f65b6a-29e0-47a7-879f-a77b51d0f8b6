package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 掘进数据对象 data_tunneling
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataTunneling extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 掘进数据ID */
    private Long id;

    /** 作业日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "作业日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationDate;

    /** 项目部门ID */
    @Excel(name = "项目部门ID")
    private Long projectDepartmentId;

    /** 工作面ID */
    @Excel(name = "工作面ID")
    private Long workingFaceId;

    /** 采场ID */
    @Excel(name = "采场ID")
    private Long stopeId;

    /** 作业时段ID */
    @Excel(name = "作业时段ID")
    private Long workingPeriodId;

    /** 掘进长度(m) */
    @Excel(name = "掘进长度(m)")
    private BigDecimal tunnelingLength;

    /** 掘进体积(m³) */
    @Excel(name = "掘进体积(m³)")
    private BigDecimal tunnelingVolume;

    /** 工作内容 */
    @Excel(name = "工作内容")
    private String workContent;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

}
