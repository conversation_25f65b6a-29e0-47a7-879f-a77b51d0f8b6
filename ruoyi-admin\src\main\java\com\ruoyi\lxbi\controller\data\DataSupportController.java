package com.ruoyi.lxbi.controller.data;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.DataSupport;
import com.ruoyi.lxbi.domain.response.DataSupportVo;
import com.ruoyi.lxbi.domain.request.DataSupportBatchDto;
import com.ruoyi.lxbi.service.IDataSupportService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 支护数据Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/data/support")
public class DataSupportController extends BaseController {
    @Autowired
    private IDataSupportService dataSupportService;

    /**
     * 查询支护数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:support:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataSupport dataSupport) {
        startPage();
        List<DataSupportVo> list = dataSupportService.selectDataSupportVoList(dataSupport);
        return getDataTable(list);
    }

    /**
     * 导出支护数据列表
     */
    @PreAuthorize("@ss.hasPermi('data:support:export')")
    @Log(title = "支护数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataSupport dataSupport) {
        List<DataSupportVo> list = dataSupportService.selectDataSupportVoList(dataSupport);
        ExcelUtil<DataSupportVo> util = new ExcelUtil<DataSupportVo>(DataSupportVo.class);
        util.exportExcel(response, list, "支护数据数据");
    }

    /**
     * 获取支护数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:support:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dataSupportService.selectDataSupportById(id));
    }

    /**
     * 新增支护数据
     */
    @PreAuthorize("@ss.hasPermi('data:support:add')")
    @Log(title = "支护数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataSupport dataSupport)
    {
        return toAjax(dataSupportService.insertDataSupport(dataSupport));
    }

    /**
     * 修改支护数据
     */
    @PreAuthorize("@ss.hasPermi('data:support:edit')")
    @Log(title = "支护数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataSupport dataSupport)
    {
        return toAjax(dataSupportService.updateDataSupport(dataSupport));
    }

    /**
     * 删除支护数据
     */
    @PreAuthorize("@ss.hasPermi('data:support:remove')")
    @Log(title = "支护数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataSupportService.deleteDataSupportByIds(ids));
    }

    /**
     * 批量保存支护数据（增删改查）
     * 传入批量列表，验证是否同一个日期的数据，然后查询这个日期的所有数据，对比后进行增删改查
     */
    @PreAuthorize("@ss.hasPermi('data:support:edit')")
    @Log(title = "支护数据批量操作", businessType = BusinessType.UPDATE)
    @PostMapping("/batch")
    public R<String> batchSave(@RequestBody List<DataSupportBatchDto> batchDataList)
    {
        try {
            int result = dataSupportService.batchSaveDataSupport(batchDataList);
            return R.ok("批量操作成功，共处理 " + result + " 条数据");
        } catch (Exception e) {
            return R.fail("批量操作失败：" + e.getMessage());
        }
    }
}
