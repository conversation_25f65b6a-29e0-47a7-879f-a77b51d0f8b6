package com.ruoyi.lxbi.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 测点基本信息对象 kafka_monitoring_point_basic_info
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KafkaMonitoringPointBasicInfo extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long isDeleted;

    /** 文件编码 */
    @Excel(name = "文件编码")
    private String fileEncoding;

    /** 煤矿代码 */
    @Excel(name = "煤矿代码")
    private String mineCode;

    /** 煤矿名称 */
    @Excel(name = "煤矿名称")
    private String mineName;

    /** 数据上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataUploadTime;

    /** 测点编码 */
    @Excel(name = "测点编码")
    private String monitoringPointCode;

    /** 系统编码 */
    @Excel(name = "系统编码")
    private String systemCode;

    /** 分站编码 */
    @Excel(name = "分站编码")
    private String substationCode;

    /** 传感器类型 */
    @Excel(name = "传感器类型")
    private String sensorType;

    /** 测点数值类型 */
    @Excel(name = "测点数值类型")
    private String monitoringPointValueType;

    /** 测点数值单位 */
    @Excel(name = "测点数值单位")
    private String monitoringPointValueUnit;

    /** 高量程 */
    @Excel(name = "高量程")
    private BigDecimal highRange;

    /** 低量程 */
    @Excel(name = "低量程")
    private BigDecimal lowRange;

    /** 上限报警门限 */
    @Excel(name = "上限报警门限")
    private BigDecimal upperLimitAlarmThreshold;

    /** 上限解报门限 */
    @Excel(name = "上限解报门限")
    private BigDecimal upperLimitReportThreshold;

    /** 下限报警门限 */
    @Excel(name = "下限报警门限")
    private BigDecimal lowerLimitAlarmThreshold;

    /** 下限解报门限 */
    @Excel(name = "下限解报门限")
    private BigDecimal lowerLimitReportThreshold;

    /** 上限断电门限 */
    @Excel(name = "上限断电门限")
    private BigDecimal upperLimitPowerOffThreshold;

    /** 上限复电门限 */
    @Excel(name = "上限复电门限")
    private BigDecimal upperLimitPowerOnThreshold;

    /** 下限断电门限 */
    @Excel(name = "下限断电门限")
    private BigDecimal lowerLimitPowerOffThreshold;

    /** 下限复电门限 */
    @Excel(name = "下限复电门限")
    private BigDecimal lowerLimitPowerOnThreshold;

    /** 开描述 */
    @Excel(name = "开描述")
    private String startDescription;

    /** 停描述 */
    @Excel(name = "停描述")
    private String stopDescription;

    /** 测点安装位置 */
    @Excel(name = "测点安装位置")
    private String monitoringPointInstallationLocation;

    /** 位置X */
    @Excel(name = "位置X")
    private BigDecimal positionX;

    /** 位置Y */
    @Excel(name = "位置Y")
    private BigDecimal positionY;

    /** 位置Z */
    @Excel(name = "位置Z")
    private BigDecimal positionZ;

    /** 传感器关联关系 */
    @Excel(name = "传感器关联关系")
    private String sensorAssociationRelationship;

    /** 数据时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataTime;

}
