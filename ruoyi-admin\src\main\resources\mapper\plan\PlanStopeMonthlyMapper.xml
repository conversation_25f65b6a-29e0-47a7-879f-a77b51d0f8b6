<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.PlanStopeMonthlyMapper">
    
    <resultMap type="PlanStopeMonthly" id="PlanStopeMonthlyResult">
        <result property="id"    column="id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="planDate"    column="plan_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="oreOutput"    column="ore_output"    />
    </resultMap>

    <sql id="selectPlanStopeMonthlyVo">
        select smp.*, bs.stope_name from plan_stope_monthly smp
       left join base_stope bs on bs.stope_id = smp.stope_id
    </sql>
    <select id="selectPlanStopeMonthlyList" parameterType="PlanStopeMonthly" resultType="com.ruoyi.lxbi.domain.response.PlanStopeMonthlyVo">
        <include refid="selectPlanStopeMonthlyVo"/>
        <where>  
            <if test="stopeId != null "> and smp.stope_id = #{stopeId}</if>
            <if test="planDate != null  and planDate != ''"> and smp.plan_date = #{planDate}</if>
        </where>
    </select>
    
    <select id="selectPlanStopeMonthlyById" parameterType="Long" resultType="com.ruoyi.lxbi.domain.response.PlanStopeMonthlyVo">
        <include refid="selectPlanStopeMonthlyVo"/>
        where smp.id = #{id}
    </select>

    <insert id="insertPlanStopeMonthly" parameterType="PlanStopeMonthly">
        insert into plan_stope_monthly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="planDate != null">plan_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="oreOutput != null">ore_output,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="oreOutput != null">#{oreOutput},</if>
         </trim>
    </insert>

    <update id="updatePlanStopeMonthly" parameterType="PlanStopeMonthly">
        update plan_stope_monthly
        <trim prefix="SET" suffixOverrides=",">
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="planDate != null">plan_date = #{planDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="oreOutput != null">ore_output = #{oreOutput},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanStopeMonthlyById" parameterType="Long">
        delete from plan_stope_monthly where id = #{id}
    </delete>

    <delete id="deletePlanStopeMonthlyByIds" parameterType="String">
        delete from plan_stope_monthly where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>