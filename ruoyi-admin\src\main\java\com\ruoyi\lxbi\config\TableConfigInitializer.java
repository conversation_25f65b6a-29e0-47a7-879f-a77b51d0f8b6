package com.ruoyi.lxbi.config;

import com.ruoyi.common.utils.TableConfigProcessor;
import com.ruoyi.lxbi.domain.table.MineOutputTableVo;
import com.ruoyi.lxbi.domain.table.MonthlyReportTableVo;
import com.ruoyi.lxbi.domain.table.RawOreVolumeTableVo;
import com.ruoyi.lxbi.domain.table.WeeklyReportTableVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 表格配置初始化器
 */
@Component
public class TableConfigInitializer implements ApplicationRunner {
    
    @Autowired
    private TableConfigProcessor tableConfigProcessor;
    
    @Override
    public void run(ApplicationArguments args) {
        // 初始化表格配置
        tableConfigProcessor.processTableConfig(MineOutputTableVo.class);
        tableConfigProcessor.processTableConfig(RawOreVolumeTableVo.class);
        tableConfigProcessor.processTableConfig(WeeklyReportTableVo.class);
        tableConfigProcessor.processTableConfig(MonthlyReportTableVo.class);
        // 可以继续添加其他表格配置类
    }
}
