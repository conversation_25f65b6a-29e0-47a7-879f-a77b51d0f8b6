package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.PlanMiningMonthly;
import com.ruoyi.lxbi.domain.request.DataMineHoistingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataMineHoistingStats;
import com.ruoyi.lxbi.domain.response.PlanMiningMonthlyVo;
import com.ruoyi.lxbi.domain.table.RawOreVolumeTableVo;
import com.ruoyi.lxbi.service.IDataMineHoistingStatsService;
import com.ruoyi.lxbi.service.IPlanMiningMonthlyService;
import com.ruoyi.lxbi.table.params.RawOreVolumeQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 原矿量数据表格处理器（日报）
 */
@Component
public class RawOreVolumeTableHandler extends BaseTableHandler<RawOreVolumeTableVo, RawOreVolumeQueryParams> {

    @Autowired
    private IDataMineHoistingStatsService dataMineHoistingStatsService;

    @Autowired
    private IPlanMiningMonthlyService planMiningMonthlyService;

    @Override
    public List<RawOreVolumeTableVo> queryTableData(RawOreVolumeQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date operationDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取月度计划数据
        BigDecimal monthlyPlan = getMonthlyPlan(operationDate);

        // 获取月累计数据和当日数据（一次查询获取）
        MonthlyStatsResult monthlyResult = getMonthlyAccumulatedWithDaily(operationDate);

        // 构建表格数据
        return buildTableData(monthlyResult.getDailyOutput(), monthlyPlan, monthlyResult.getMonthlyAccumulated());
    }

    /**
     * 获取月度计划数据（按财务月：上月29号到本月28号）
     */
    private BigDecimal getMonthlyPlan(Date operationDate) {
        // 计算财务月
        String financialMonth = FinancialDateUtils.getFinancialMonth(operationDate);

        PlanMiningMonthly queryParam = new PlanMiningMonthly();
        queryParam.setPlanDate(financialMonth);

        List<PlanMiningMonthlyVo> plans = planMiningMonthlyService.selectPlanMiningMonthlyList(queryParam);

        return plans.stream()
                .map(PlanMiningMonthly::getRawOreVolume)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取月累计数据和当日数据（按财务月：上月29号到本月28号）
     */
    private MonthlyStatsResult getMonthlyAccumulatedWithDaily(Date operationDate) {
        // 获取财务月的开始日期到查询日期的累计数据
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(operationDate);

        DataMineHoistingStatsRequest request = new DataMineHoistingStatsRequest();
        request.setStartDate(financialMonthStart);
        request.setEndDate(operationDate);

        List<DataMineHoistingStats> monthlyStats = dataMineHoistingStatsService.selectStatsList(request, "daily");

        // 提取当日数据（最新一天的数据）
        BigDecimal dailyOutput = monthlyStats.stream()
                .filter(stats -> FinancialDateUtils.isSameDay(stats.getOperationDate(), operationDate))
                .map(stats -> stats.getTotalWeight() != null ? BigDecimal.valueOf(stats.getTotalWeight()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算月累计数据
        BigDecimal monthlyAccumulated = monthlyStats.stream()
                .map(stats -> stats.getTotalWeight() != null ? BigDecimal.valueOf(stats.getTotalWeight()) : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return new MonthlyStatsResult(dailyOutput, monthlyAccumulated);
    }

    /**
     * 构建表格数据
     */
    private List<RawOreVolumeTableVo> buildTableData(BigDecimal dailyOutput,
                                                     BigDecimal monthlyPlan,
                                                     BigDecimal monthlyAccumulated) {
        List<RawOreVolumeTableVo> result = new ArrayList<>();

        // 创建原矿量统计行
        RawOreVolumeTableVo vo = new RawOreVolumeTableVo();
        vo.setBold(List.of("serialNumber", "name", "monthlyPlan", "dailyOutput", "monthlyAccumulated", "completionRate", "monthlyOverUnder"));
        vo.setSerialNumber("总计");
        vo.setName("原矿量");
        vo.setSubName("原矿量");
        vo.setUnit("t");
        vo.setMonthlyPlan(monthlyPlan);
        vo.setDailyOutput(dailyOutput);
        vo.setMonthlyAccumulated(monthlyAccumulated);

        // 计算完成率
        if (monthlyPlan != null && monthlyPlan.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal rate = monthlyAccumulated.divide(monthlyPlan, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            vo.setCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setCompletionRate("0.00%");
        }

        // 计算超欠量
        vo.setMonthlyOverUnder(monthlyAccumulated.subtract(monthlyPlan != null ? monthlyPlan : BigDecimal.ZERO));

        result.add(vo);
        return result;
    }



    /**
     * 月度统计结果包装类
     */
    private static class MonthlyStatsResult {
        private final BigDecimal dailyOutput;
        private final BigDecimal monthlyAccumulated;

        public MonthlyStatsResult(BigDecimal dailyOutput, BigDecimal monthlyAccumulated) {
            this.dailyOutput = dailyOutput;
            this.monthlyAccumulated = monthlyAccumulated;
        }

        public BigDecimal getDailyOutput() {
            return dailyOutput;
        }

        public BigDecimal getMonthlyAccumulated() {
            return monthlyAccumulated;
        }
    }
}
