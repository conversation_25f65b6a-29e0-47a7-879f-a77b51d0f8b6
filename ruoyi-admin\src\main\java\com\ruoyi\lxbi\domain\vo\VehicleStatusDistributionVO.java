package com.ruoyi.lxbi.domain.vo;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 车辆状态分布VO
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleStatusDistributionVO {
    
    /**
     * 车辆状态名称
     */
    private String vehicleStatusName;
    
    /**
     * 车辆数量
     */
    private Long vehicleCount;
    
    /**
     * 占比百分比
     */
    private BigDecimal percentage;
    
    /**
     * 车辆状态代码
     */
    private String vehicleStatusCode;
}
