package com.ruoyi.lxbi.admin.kafka;

import com.ruoyi.lxbi.service.IKafkaVehicleSpeedOverService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

/**
 * 车辆超速告警数据Kafka监听器
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class VehicleSpeedOverKafkaListener {

    @Autowired
    private IKafkaVehicleSpeedOverService kafkaVehicleSpeedOverService;

    /**
     * 监听车辆超速告警数据主题
     * 
     * @param record Kafka消息记录
     * @param ack 手动确认
     */
    @KafkaListener(topics = "DB14_VehicleSpeedOver", groupId = "lxbi-consumer-test2")
    public void handleVehicleSpeedOver(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try {
            String message = record.value();
            log.info("接收到车辆超速告警数据Kafka消息，主题: {}, 分区: {}, 偏移量: {}", 
                record.topic(), record.partition(), record.offset());
            log.debug("消息内容: {}", message);

            // 处理消息
            boolean success = kafkaVehicleSpeedOverService.processKafkaMessage(message);
            
            if (success) {
                log.info("车辆超速告警数据消息处理成功，偏移量: {}", record.offset());
                // 手动确认消息
                if (ack != null) {
                    ack.acknowledge();
                }
            } else {
                log.error("车辆超速告警数据消息处理失败，偏移量: {}, 消息: {}", record.offset(), message);
            }

        } catch (Exception e) {
            log.error("处理车辆超速告警数据Kafka消息异常，偏移量: {}", record.offset(), e);
        }
    }

}
