package com.ruoyi.lxbi.table;

import com.ruoyi.common.core.table.BaseTableHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FinancialDateUtils;
import com.ruoyi.lxbi.domain.BasePriorityProject;
import com.ruoyi.lxbi.domain.request.DataTunnelingStatsRequest;
import com.ruoyi.lxbi.domain.response.BasePriorityProjectVo;
import com.ruoyi.lxbi.domain.response.DataTunnelingStopeStats;
import com.ruoyi.lxbi.domain.response.DataTunnelingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.table.PriorityProjectMonthlyReportTableVo;
import com.ruoyi.lxbi.service.IBasePriorityProjectService;
import com.ruoyi.lxbi.service.IDataTunnelingStatsService;
import com.ruoyi.lxbi.table.params.PriorityProjectMonthlyReportQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 重点工程月报表格处理器
 */
@Component
public class PriorityProjectMonthlyReportTableHandler extends BaseTableHandler<PriorityProjectMonthlyReportTableVo, PriorityProjectMonthlyReportQueryParams> {

    @Autowired
    private IDataTunnelingStatsService dataTunnelingStatsService;

    @Autowired
    private IBasePriorityProjectService basePriorityProjectService;

    @Override
    public List<PriorityProjectMonthlyReportTableVo> queryTableData(PriorityProjectMonthlyReportQueryParams params) {
        // 获取查询日期，默认为当前日期
        Date queryDate = params != null && params.getDate() != null ?
                params.getDate() : DateUtils.getNowDate();

        // 获取财务月范围
        Date financialMonthStart = FinancialDateUtils.getFinancialMonthStartDate(queryDate);
        Date financialMonthEnd = FinancialDateUtils.getFinancialMonthEndDate(queryDate);

        // 获取重点工程数据
        PriorityProjectMonthlyResult priorityProjectResult = getPriorityProjectMonthlyData(financialMonthStart, financialMonthEnd);

        // 构建表格数据
        return buildTableData(priorityProjectResult);
    }

    /**
     * 获取重点工程月数据
     */
    private PriorityProjectMonthlyResult getPriorityProjectMonthlyData(Date monthStart, Date monthEnd) {
        DataTunnelingStatsRequest request = new DataTunnelingStatsRequest();
        request.setStartDate(monthStart);
        request.setEndDate(monthEnd);

        // 获取重点工程总体统计数据
        List<DataTunnelingTotalWithPlanStats> priorityTotalStats = dataTunnelingStatsService.selectPriorityProjectTotalWithPlanStatsList(request, "daily");

        // 计算重点工程总体月完成
        BigDecimal priorityTotalMonthlyCompleted = priorityTotalStats.stream()
                .filter(stats -> stats.getTotalTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算重点工程总体月计划
        BigDecimal priorityTotalMonthlyPlan = priorityTotalStats.stream()
                .filter(stats -> stats.getPlanTunnelingLength() != null)
                .map(stats -> BigDecimal.valueOf(stats.getPlanTunnelingLength()))
                .findFirst()
                .orElse(BigDecimal.ZERO);

        // 获取重点工程列表
        List<BasePriorityProjectVo> priorityProjects = basePriorityProjectService.selectBasePriorityProjectListAll(new BasePriorityProject());

        // 获取重点工程按采场的统计数据
        List<DataTunnelingStopeStats> priorityStopeStats = dataTunnelingStatsService.selectPriorityProjectStopeStatsList(request, "daily");

        return new PriorityProjectMonthlyResult(priorityTotalMonthlyCompleted, priorityTotalMonthlyPlan,
                priorityProjects, priorityStopeStats);
    }

    /**
     * 构建表格数据
     */
    private List<PriorityProjectMonthlyReportTableVo> buildTableData(PriorityProjectMonthlyResult priorityProjectResult) {
        List<PriorityProjectMonthlyReportTableVo> result = new ArrayList<>();

        // 添加重点工程总计行
        PriorityProjectMonthlyReportTableVo summary = new PriorityProjectMonthlyReportTableVo();
        summary.setBold(List.of("serialNumber", "name", "monthlyPlan", "monthlyCompleted", "monthlyCompletionRate", "monthlyOverUnder"));
        summary.setSerialNumber("总计");
        summary.setName("重点工程");
        summary.setUnit("m");
        summary.setMonthlyPlan(priorityProjectResult.getTotalMonthlyPlan());
        summary.setMonthlyCompleted(priorityProjectResult.getTotalMonthlyCompleted());

        // 计算总完成率
        if (priorityProjectResult.getTotalMonthlyPlan().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal rate = priorityProjectResult.getTotalMonthlyCompleted()
                    .divide(priorityProjectResult.getTotalMonthlyPlan(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            summary.setMonthlyCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
            summary.setMonthlyOverUnder(priorityProjectResult.getTotalMonthlyCompleted()
                    .subtract(priorityProjectResult.getTotalMonthlyPlan()));
        } else {
            summary.setMonthlyCompletionRate("　");
            summary.setMonthlyOverUnder(null);
        }

        result.add(summary);

        // 添加重点工程明细项目
        int subSerialNumber = 1;
        for (BasePriorityProjectVo project : priorityProjectResult.getPriorityProjects()) {
            PriorityProjectMonthlyReportTableVo vo = new PriorityProjectMonthlyReportTableVo();
            vo.setSerialNumber(String.valueOf(subSerialNumber++));
            vo.setName(getProjectDisplayName(project));
            vo.setUnit("m");

            // 从真实数据中获取计划、产量等数据（基于采场）
            vo.setMonthlyPlan(getProjectMonthlyPlan(project, priorityProjectResult));
            vo.setMonthlyCompleted(getProjectMonthlyCompleted(project, priorityProjectResult));

            // 计算完成率
            if (vo.getMonthlyPlan() != null && vo.getMonthlyPlan().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = vo.getMonthlyCompleted().divide(vo.getMonthlyPlan(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                vo.setMonthlyCompletionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
                vo.setMonthlyOverUnder(vo.getMonthlyCompleted().subtract(vo.getMonthlyPlan()));
            } else {
                vo.setMonthlyCompletionRate("　");
                vo.setMonthlyOverUnder(null);
            }

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取重点工程项目显示名称
     */
    private String getProjectDisplayName(BasePriorityProjectVo project) {
        String projectName = "";
        if (project.getStopeName() != null && !project.getStopeName().trim().isEmpty()) {
            projectName = project.getStopeName();
        }
        if (project.getWorkingFaceName() != null && !project.getWorkingFaceName().trim().isEmpty()) {
            projectName = project.getWorkingFaceName() + "-" + projectName ;
        }
        return projectName;
    }

    /**
     * 获取重点工程项目月计划（基于采场）
     */
    private BigDecimal getProjectMonthlyPlan(BasePriorityProjectVo project, PriorityProjectMonthlyResult result) {
        // 根据重点工程的采场ID获取计划
        if (project.getStopeId() != null) {
            // TODO: 从计划数据中获取该采场的月计划数据
            // 这里需要实现基于采场的计划查询
            return BigDecimal.ZERO;
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取重点工程项目月完成（基于采场）
     */
    private BigDecimal getProjectMonthlyCompleted(BasePriorityProjectVo project, PriorityProjectMonthlyResult result) {
        // 根据重点工程的采场ID获取月完成
        if (project.getStopeId() != null) {
            return result.getPriorityStopeStats().stream()
                    .filter(stats -> project.getStopeId().equals(stats.getStopeId()))
                    .filter(stats -> stats.getTotalTunnelingLength() != null)
                    .map(stats -> BigDecimal.valueOf(stats.getTotalTunnelingLength()))
                    .reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 重点工程月结果包装类
     */
    private static class PriorityProjectMonthlyResult {
        private final BigDecimal totalMonthlyCompleted;
        private final BigDecimal totalMonthlyPlan;
        private final List<BasePriorityProjectVo> priorityProjects;
        private final List<DataTunnelingStopeStats> priorityStopeStats;

        public PriorityProjectMonthlyResult(BigDecimal totalMonthlyCompleted,
                                            BigDecimal totalMonthlyPlan,
                                            List<BasePriorityProjectVo> priorityProjects,
                                            List<DataTunnelingStopeStats> priorityStopeStats) {
            this.totalMonthlyCompleted = totalMonthlyCompleted;
            this.totalMonthlyPlan = totalMonthlyPlan;
            this.priorityProjects = priorityProjects;
            this.priorityStopeStats = priorityStopeStats;
        }

        public BigDecimal getTotalMonthlyCompleted() {
            return totalMonthlyCompleted;
        }

        public BigDecimal getTotalMonthlyPlan() {
            return totalMonthlyPlan;
        }

        public List<BasePriorityProjectVo> getPriorityProjects() {
            return priorityProjects;
        }

        public List<DataTunnelingStopeStats> getPriorityStopeStats() {
            return priorityStopeStats;
        }
    }
}
