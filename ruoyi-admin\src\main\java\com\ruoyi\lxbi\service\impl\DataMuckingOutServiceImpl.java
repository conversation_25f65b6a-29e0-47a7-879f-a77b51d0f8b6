package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.Date;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.lxbi.domain.response.DataMuckingOutVo;
import com.ruoyi.lxbi.domain.request.DataMuckingOutBatchDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;
import com.ruoyi.lxbi.mapper.DataMuckingOutMapper;
import com.ruoyi.lxbi.domain.DataMuckingOut;
import com.ruoyi.lxbi.service.IDataMuckingOutService;

/**
 * 出矿数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DataMuckingOutServiceImpl implements IDataMuckingOutService 
{
    @Autowired
    private DataMuckingOutMapper dataMuckingOutMapper;

    /**
     * 查询出矿数据
     * 
     * @param id 出矿数据主键
     * @return 出矿数据
     */
    @Override
    public DataMuckingOut selectDataMuckingOutById(Long id)
    {
        return dataMuckingOutMapper.selectDataMuckingOutById(id);
    }

    /**
     * 查询出矿数据列表
     *
     * @param dataMuckingOut 出矿数据
     * @return 出矿数据
     */
    @Override
    public List<DataMuckingOutVo> selectDataMuckingOutList(DataMuckingOut dataMuckingOut)
    {
        return dataMuckingOutMapper.selectDataMuckingOutList(dataMuckingOut);
    }

    /**
     * 新增出矿数据
     * 
     * @param dataMuckingOut 出矿数据
     * @return 结果
     */
    @Override
    public int insertDataMuckingOut(DataMuckingOut dataMuckingOut)
    {
        dataMuckingOut.setCreateTime(DateUtils.getNowDate());
        return dataMuckingOutMapper.insertDataMuckingOut(dataMuckingOut);
    }

    /**
     * 修改出矿数据
     * 
     * @param dataMuckingOut 出矿数据
     * @return 结果
     */
    @Override
    public int updateDataMuckingOut(DataMuckingOut dataMuckingOut)
    {
        dataMuckingOut.setUpdateTime(DateUtils.getNowDate());
        return dataMuckingOutMapper.updateDataMuckingOut(dataMuckingOut);
    }

    /**
     * 批量删除出矿数据
     * 
     * @param ids 需要删除的出矿数据主键
     * @return 结果
     */
    @Override
    public int deleteDataMuckingOutByIds(Long[] ids)
    {
        return dataMuckingOutMapper.deleteDataMuckingOutByIds(ids);
    }

    /**
     * 删除出矿数据信息
     *
     * @param id 出矿数据主键
     * @return 结果
     */
    @Override
    public int deleteDataMuckingOutById(Long id)
    {
        return dataMuckingOutMapper.deleteDataMuckingOutById(id);
    }

    /**
     * 根据作业日期和项目部门查询出矿数据列表
     *
     * @param operationDate 作业日期
     * @param projectDepartmentId 项目部门ID
     * @return 出矿数据集合
     */
    @Override
    public List<DataMuckingOutVo> selectDataMuckingOutByOperationDateAndProject(Date operationDate, Long projectDepartmentId)
    {
        return dataMuckingOutMapper.selectDataMuckingOutByOperationDateAndProject(operationDate, projectDepartmentId);
    }

    /**
     * 批量保存出矿数据（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveDataMuckingOut(List<DataMuckingOutBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个日期和项目部的数据
        Date operationDate = batchDataList.get(0).getOperationDate();
        Long projectDepartmentId = batchDataList.get(0).getProjectDepartmentId();
        if (operationDate == null) {
            throw new ServiceException("作业日期不能为空");
        }
        if (projectDepartmentId == null) {
            throw new ServiceException("项目部门不能为空");
        }

        boolean allSameDateAndProject = batchDataList.stream()
                .allMatch(data -> operationDate.equals(data.getOperationDate())
                    && projectDepartmentId.equals(data.getProjectDepartmentId()));
        if (!allSameDateAndProject) {
            throw new ServiceException("批量数据必须是同一个作业日期和项目部门");
        }

        // 查询现有数据
        List<DataMuckingOutVo> existingDataList = selectDataMuckingOutByOperationDateAndProject(operationDate, projectDepartmentId);
        Map<Long, DataMuckingOutVo> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(DataMuckingOutVo::getId, data -> data));

        List<DataMuckingOut> toInsert = new ArrayList<>();
        List<DataMuckingOut> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (DataMuckingOutBatchDto batchData : batchDataList) {
            if (Boolean.TRUE.equals(batchData.getIsNew())) {
                // 新增数据
                DataMuckingOut newData = new DataMuckingOut();
                BeanUtils.copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if (batchData.getId() != null && existingDataMap.containsKey(batchData.getId())) {
                // 更新现有数据（根据ID判断）
                DataMuckingOut updateData = new DataMuckingOut();
                BeanUtils.copyProperties(batchData, updateData);
                updateData.setId(batchData.getId());
                updateData.setUpdateBy(SecurityUtils.getUsername());
                updateData.setUpdateTime(DateUtils.getNowDate());
                toUpdate.add(updateData);

                // 从现有数据中移除，剩下的就是要删除的
                toDelete.remove(batchData.getId());
            }
        }

        int result = 0;

        // 执行删除
        if (!toDelete.isEmpty()) {
            result += dataMuckingOutMapper.deleteDataMuckingOutByIds(toDelete.toArray(new Long[0]));
        }

        // 执行新增
        if (!toInsert.isEmpty()) {
            result += dataMuckingOutMapper.batchInsertDataMuckingOut(toInsert);
        }

        // 执行更新
        for (DataMuckingOut updateData : toUpdate) {
            result += dataMuckingOutMapper.updateDataMuckingOut(updateData);
        }

        return result;
    }
}
