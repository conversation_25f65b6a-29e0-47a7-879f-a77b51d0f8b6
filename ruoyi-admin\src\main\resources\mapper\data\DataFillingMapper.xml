<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.mapper.DataFillingMapper">
    
    <resultMap type="DataFilling" id="DataFillingResult">
        <result property="id"    column="id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="slurryVolume"    column="slurry_volume"    />
        <result property="cementWeight"    column="cement_weight"    />
        <result property="fillingRatio"    column="filling_ratio"    />
        <result property="fillingConcentration"    column="filling_concentration"    />
        <result property="firstFillingTime"    column="first_filling_time"    />
        <result property="endFillingTime"    column="end_filling_time"    />
        <result property="remarks"    column="remarks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataFillingVo">
        select df.id, df.operation_date, df.project_department_id, df.stope_id, df.working_period_id,
               df.slurry_volume, df.cement_weight, df.filling_ratio, df.filling_concentration,
               df.first_filling_time, df.end_filling_time, df.remarks,
               df.create_by, df.create_time, df.update_by, df.update_time,
               bwp.working_period_name,
               bpd.project_department_name,
               bs.stope_name
        from data_filling df
        left join base_working_period bwp on df.working_period_id = bwp.working_period_id
        left join base_project_department bpd on df.project_department_id = bpd.project_department_id
        left join base_stope bs on df.stope_id = bs.stope_id
    </sql>

    <select id="selectDataFillingList" parameterType="DataFilling" resultMap="DataFillingVoResult">
        <include refid="selectDataFillingVo"/>
        <where>
            <if test="operationDate != null "> and df.operation_date = #{operationDate}</if>
            <if test="projectDepartmentId != null "> and df.project_department_id = #{projectDepartmentId}</if>
            <if test="stopeId != null "> and df.stope_id = #{stopeId}</if>
            <if test="workingPeriodId != null "> and df.working_period_id = #{workingPeriodId}</if>
            <if test="slurryVolume != null "> and df.slurry_volume = #{slurryVolume}</if>
            <if test="cementWeight != null "> and df.cement_weight = #{cementWeight}</if>
            <if test="fillingRatio != null  and fillingRatio != ''"> and df.filling_ratio = #{fillingRatio}</if>
            <if test="fillingConcentration != null "> and df.filling_concentration = #{fillingConcentration}</if>
            <if test="firstFillingTime != null "> and df.first_filling_time = #{firstFillingTime}</if>
            <if test="endFillingTime != null "> and df.end_filling_time = #{endFillingTime}</if>
            <if test="remarks != null  and remarks != ''"> and df.remarks = #{remarks}</if>
        </where>
    </select>
    
    <select id="selectDataFillingById" parameterType="Long" resultMap="DataFillingResult">
        <include refid="selectDataFillingVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataFilling" parameterType="DataFilling" useGeneratedKeys="true" keyProperty="id">
        insert into data_filling
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">operation_date,</if>
            <if test="projectDepartmentId != null">project_department_id,</if>
            <if test="stopeId != null">stope_id,</if>
            <if test="workingPeriodId != null">working_period_id,</if>
            <if test="slurryVolume != null">slurry_volume,</if>
            <if test="cementWeight != null">cement_weight,</if>
            <if test="fillingRatio != null">filling_ratio,</if>
            <if test="fillingConcentration != null">filling_concentration,</if>
            <if test="firstFillingTime != null">first_filling_time,</if>
            <if test="endFillingTime != null">end_filling_time,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationDate != null">#{operationDate},</if>
            <if test="projectDepartmentId != null">#{projectDepartmentId},</if>
            <if test="stopeId != null">#{stopeId},</if>
            <if test="workingPeriodId != null">#{workingPeriodId},</if>
            <if test="slurryVolume != null">#{slurryVolume},</if>
            <if test="cementWeight != null">#{cementWeight},</if>
            <if test="fillingRatio != null">#{fillingRatio},</if>
            <if test="fillingConcentration != null">#{fillingConcentration},</if>
            <if test="firstFillingTime != null">#{firstFillingTime},</if>
            <if test="endFillingTime != null">#{endFillingTime},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDataFilling" parameterType="DataFilling">
        update data_filling
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationDate != null">operation_date = #{operationDate},</if>
            <if test="projectDepartmentId != null">project_department_id = #{projectDepartmentId},</if>
            <if test="stopeId != null">stope_id = #{stopeId},</if>
            <if test="workingPeriodId != null">working_period_id = #{workingPeriodId},</if>
            <if test="slurryVolume != null">slurry_volume = #{slurryVolume},</if>
            <if test="cementWeight != null">cement_weight = #{cementWeight},</if>
            <if test="fillingRatio != null">filling_ratio = #{fillingRatio},</if>
            <if test="fillingConcentration != null">filling_concentration = #{fillingConcentration},</if>
            <if test="firstFillingTime != null">first_filling_time = #{firstFillingTime},</if>
            <if test="endFillingTime != null">end_filling_time = #{endFillingTime},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataFillingById" parameterType="Long">
        delete from data_filling where id = #{id}
    </delete>

    <delete id="deleteDataFillingByIds" parameterType="String">
        delete from data_filling where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据作业日期和项目部门查询充填数据列表 -->
    <resultMap type="DataFillingVo" id="DataFillingVoResult">
        <result property="id"    column="id"    />
        <result property="operationDate"    column="operation_date"    />
        <result property="projectDepartmentId"    column="project_department_id"    />
        <result property="stopeId"    column="stope_id"    />
        <result property="workingPeriodId"    column="working_period_id"    />
        <result property="slurryVolume"    column="slurry_volume"    />
        <result property="cementWeight"    column="cement_weight"    />
        <result property="fillingRatio"    column="filling_ratio"    />
        <result property="fillingConcentration"    column="filling_concentration"    />
        <result property="firstFillingTime"    column="first_filling_time"    />
        <result property="endFillingTime"    column="end_filling_time"    />
        <result property="remarks"    column="remarks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="workingPeriodName"    column="working_period_name"    />
        <result property="projectDepartmentName"    column="project_department_name"    />
        <result property="stopeName"    column="stope_name"    />
    </resultMap>

    <select id="selectDataFillingByOperationDateAndProject" resultMap="DataFillingVoResult">
        SELECT
            df.id, df.operation_date, df.project_department_id, df.stope_id, df.working_period_id,
            df.slurry_volume, df.cement_weight, df.filling_ratio, df.filling_concentration,
            df.first_filling_time, df.end_filling_time, df.remarks,
            df.create_by, df.create_time, df.update_by, df.update_time,
            bwp.working_period_name,
            bpd.project_department_name,
            bs.stope_name
        FROM data_filling df
        LEFT JOIN base_working_period bwp ON df.working_period_id = bwp.working_period_id
        LEFT JOIN base_project_department bpd ON df.project_department_id = bpd.project_department_id
        LEFT JOIN base_stope bs ON df.stope_id = bs.stope_id
        WHERE df.operation_date = #{operationDate}
        AND df.project_department_id = #{projectDepartmentId}
        ORDER BY df.working_period_id, df.stope_id
    </select>

    <!-- 批量新增充填数据 -->
    <insert id="batchInsertDataFilling" parameterType="java.util.List">
        INSERT INTO data_filling (
            operation_date, project_department_id, stope_id, working_period_id,
            slurry_volume, cement_weight, filling_ratio, filling_concentration,
            first_filling_time, end_filling_time, remarks,
            create_by, create_time, update_by, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.operationDate}, #{item.projectDepartmentId}, #{item.stopeId}, #{item.workingPeriodId},
                #{item.slurryVolume}, #{item.cementWeight}, #{item.fillingRatio}, #{item.fillingConcentration},
                #{item.firstFillingTime}, #{item.endFillingTime}, #{item.remarks},
                #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}
            )
        </foreach>
    </insert>
</mapper>