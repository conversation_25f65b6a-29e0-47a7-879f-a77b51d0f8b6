package com.ruoyi.lxbi.controller.kafka;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.lxbi.domain.KafkaBaseStationRealTimeData;
import com.ruoyi.lxbi.service.IKafkaBaseStationRealTimeDataService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基站实时数据Controller
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@RestController
@RequestMapping("/kafka/StationRealTimeData")
public class KafkaBaseStationRealTimeDataController extends BaseController {
    @Autowired
    private IKafkaBaseStationRealTimeDataService kafkaBaseStationRealTimeDataService;

    /**
     * 查询基站实时数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:StationRealTimeData:list')")
    @GetMapping("/list")
    public TableDataInfo list(KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData) {
        startPage();
        List<KafkaBaseStationRealTimeData> list = kafkaBaseStationRealTimeDataService.selectKafkaBaseStationRealTimeDataList(kafkaBaseStationRealTimeData);
        return getDataTable(list);
    }

    /**
     * 导出基站实时数据列表
     */
    @PreAuthorize("@ss.hasPermi('kafka:StationRealTimeData:export')")
    @Log(title = "基站实时数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData) {
        List<KafkaBaseStationRealTimeData> list = kafkaBaseStationRealTimeDataService.selectKafkaBaseStationRealTimeDataList(kafkaBaseStationRealTimeData);
        ExcelUtil<KafkaBaseStationRealTimeData> util = new ExcelUtil<KafkaBaseStationRealTimeData>(KafkaBaseStationRealTimeData.class);
        util.exportExcel(response, list, "基站实时数据数据");
    }

    /**
     * 获取基站实时数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('kafka:StationRealTimeData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kafkaBaseStationRealTimeDataService.selectKafkaBaseStationRealTimeDataById(id));
    }

    /**
     * 新增基站实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:StationRealTimeData:add')")
    @Log(title = "基站实时数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData)
    {
        return toAjax(kafkaBaseStationRealTimeDataService.insertKafkaBaseStationRealTimeData(kafkaBaseStationRealTimeData));
    }

    /**
     * 修改基站实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:StationRealTimeData:edit')")
    @Log(title = "基站实时数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KafkaBaseStationRealTimeData kafkaBaseStationRealTimeData)
    {
        return toAjax(kafkaBaseStationRealTimeDataService.updateKafkaBaseStationRealTimeData(kafkaBaseStationRealTimeData));
    }

    /**
     * 删除基站实时数据
     */
    @PreAuthorize("@ss.hasPermi('kafka:StationRealTimeData:remove')")
    @Log(title = "基站实时数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kafkaBaseStationRealTimeDataService.deleteKafkaBaseStationRealTimeDataByIds(ids));
    }
}
