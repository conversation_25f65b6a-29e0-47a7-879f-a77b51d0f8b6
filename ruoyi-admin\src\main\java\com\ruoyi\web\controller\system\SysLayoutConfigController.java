package com.ruoyi.web.controller.system;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysLayoutConfig;
import com.ruoyi.system.service.ISysLayoutConfigService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 布局配置Controller
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/system/layout")
public class SysLayoutConfigController extends BaseController {
    @Autowired
    private ISysLayoutConfigService sysLayoutConfigService;

    /**
     * 查询布局配置列表
     */
    @GetMapping("/list")
    public R<PageResult<SysLayoutConfig>> list(SysLayoutConfig sysLayoutConfig) {
        startPage();
        List<SysLayoutConfig> list = sysLayoutConfigService.selectSysLayoutConfigList(sysLayoutConfig);
        PageResult<SysLayoutConfig> data = new PageResult<>();
        data.setRows(list);
        data.setTotal(new PageInfo<>(list).getTotal());
        return R.ok(data);
    }

    /**
     * 导出布局配置列表
     */
    @Log(title = "布局配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLayoutConfig sysLayoutConfig) {
        List<SysLayoutConfig> list = sysLayoutConfigService.selectSysLayoutConfigList(sysLayoutConfig);
        ExcelUtil<SysLayoutConfig> util = new ExcelUtil<SysLayoutConfig>(SysLayoutConfig.class);
        util.exportExcel(response, list, "布局配置数据");
    }

    /**
     * 获取布局配置详细信息
     */
    @GetMapping(value = "/{id}")
    public R<SysLayoutConfig> getInfo(@PathVariable("id") Long id) {
        return R.ok(sysLayoutConfigService.selectSysLayoutConfigById(id));
    }

    /**
     * 新增布局配置
     */
    @Log(title = "布局配置", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody SysLayoutConfig sysLayoutConfig) {
        return R.ok(sysLayoutConfigService.insertSysLayoutConfig(sysLayoutConfig));
    }

    /**
     * 修改布局配置
     */
    @Log(title = "布局配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody SysLayoutConfig sysLayoutConfig) {
        return R.ok(sysLayoutConfigService.updateSysLayoutConfig(sysLayoutConfig));
    }

    /**
     * 删除布局配置
     */
    @Log(title = "布局配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids) {
        return R.ok(sysLayoutConfigService.deleteSysLayoutConfigByIds(ids));
    }
}
