package com.ruoyi.lxbi.service.impl;

import com.ruoyi.lxbi.domain.request.DataDrillingStatsRequest;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalStats;
import com.ruoyi.lxbi.domain.response.DataDrillingTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataDrillingPeriodStats;
import com.ruoyi.lxbi.domain.response.DataDrillingDepartmentStats;
import com.ruoyi.lxbi.domain.response.DataDrillingDepartmentWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataDrillingStopeStats;
import com.ruoyi.lxbi.domain.response.DataDrillingWorkingFaceStats;
import com.ruoyi.lxbi.mapper.DataDeepHoleStatsMapper;
import com.ruoyi.lxbi.service.IDataDeepHoleStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 中深孔施工数据统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Service
public class DataDeepHoleStatsServiceImpl implements IDataDeepHoleStatsService {
    @Autowired
    private DataDeepHoleStatsMapper dataDeepHoleStatsMapper;
    
    /**
     * 查询总体统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合
     */
    @Override
    public List<DataDrillingTotalStats> selectTotalStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectDailyTotalStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectWeeklyTotalStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectYearlyTotalStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDeepHoleStatsMapper.selectMonthlyTotalStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询总体统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 总体统计数据集合（含计划量）
     */
    @Override
    public List<DataDrillingTotalWithPlanStats> selectTotalWithPlanStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectDailyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectWeeklyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectYearlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDeepHoleStatsMapper.selectMonthlyTotalWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }
    
    /**
     * 查询班次统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 班次统计数据集合
     */
    @Override
    public List<DataDrillingPeriodStats> selectPeriodStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectDailyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectWeeklyPeriodStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectYearlyPeriodStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDeepHoleStatsMapper.selectMonthlyPeriodStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询项目部门统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合
     */
    @Override
    public List<DataDrillingDepartmentStats> selectDepartmentStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectDailyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectWeeklyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectYearlyDepartmentStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDeepHoleStatsMapper.selectMonthlyDepartmentStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询采场统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 采场统计数据集合
     */
    @Override
    public List<DataDrillingStopeStats> selectStopeStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectDailyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectWeeklyStopeStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectYearlyStopeStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDeepHoleStatsMapper.selectMonthlyStopeStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询工作面统计数据列表 (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 工作面统计数据集合
     */
    @Override
    public List<DataDrillingWorkingFaceStats> selectWorkingFaceStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectDailyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectWeeklyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectYearlyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDeepHoleStatsMapper.selectMonthlyWorkingFaceStats(request.getStartDate(), request.getEndDate());
        }
    }

    /**
     * 查询项目部门统计数据列表（含计划量） (日/周/月/年)
     *
     * @param request 统计请求参数
     * @param viewType 视图类型 (daily/weekly/monthly/yearly)
     * @return 项目部门统计数据集合（含计划量）
     */
    @Override
    public List<DataDrillingDepartmentWithPlanStats> selectDepartmentWithPlanStatsList(DataDrillingStatsRequest request, String viewType) {
        if ("daily".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectDailyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("weekly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectWeeklyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else if ("yearly".equals(viewType)) {
            return dataDeepHoleStatsMapper.selectYearlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        } else {
            return dataDeepHoleStatsMapper.selectMonthlyDepartmentWithPlanStats(request.getStartDate(), request.getEndDate());
        }
    }

}
