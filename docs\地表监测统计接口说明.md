# 地表监测统计接口说明

## 概述

本文档说明了地表监测统计模块的接口设计和实现，该模块提供地表监测相关的统计分析功能，包括偏移距离概览、偏移等级分布和七日偏移趋势等统计数据。

## 接口列表

### 1. 地表监测概览统计

**接口地址**: `GET /lxbi/stat/surface-monitoring/overview`

**功能描述**: 获取地表监测的总体概览数据

**请求参数**:
- `viewType`: 视图类型 (day/week/month)
- `startDate`: 开始日期 (yyyy-MM-dd)
- `endDate`: 结束日期 (yyyy-MM-dd)

**响应数据**:
```json
{
  "xOffsetDistanceTotal": 200,
  "yOffsetDistanceTotal": 40,
  "hOffsetDistanceTotal": 2,
  "offsetAlarmCount": 20,
  "startDate": "2025-08-20",
  "endDate": "2025-08-25",
  "period": "day"
}
```

### 2. 地表监测偏移等级分布统计

**接口地址**: `GET /lxbi/stat/surface-monitoring/level-distribution`

**功能描述**: 获取地表监测偏移等级分布数据，用于柱状图展示

**响应数据**:
```json
[
  {
    "offsetDirection": "X",
    "offsetLevel": 4,
    "offsetCount": 200,
    "percentage": 100.00,
    "directionCode": "X_DIRECTION"
  },
  {
    "offsetDirection": "Y",
    "offsetLevel": 4,
    "offsetCount": 40,
    "percentage": 100.00,
    "directionCode": "Y_DIRECTION"
  },
  {
    "offsetDirection": "H",
    "offsetLevel": 4,
    "offsetCount": 2,
    "percentage": 100.00,
    "directionCode": "H_DIRECTION"
  }
]
```

### 3. 地表监测七日偏移趋势统计

**接口地址**: `GET /lxbi/stat/surface-monitoring/seven-day-trend`

**功能描述**: 获取地表监测七日偏移趋势数据，用于折线图展示

**响应数据**:
```json
[
  {
    "date": "2025-08-19",
    "shortDate": "1日",
    "xOffsetValue": 7.5,
    "yOffsetValue": 8,
    "hOffsetValue": 6.4
  },
  {
    "date": "2025-08-20",
    "shortDate": "2日",
    "xOffsetValue": 7.5,
    "yOffsetValue": 9,
    "hOffsetValue": 5
  },
  {
    "date": "2025-08-21",
    "shortDate": "3日",
    "xOffsetValue": 7.5,
    "yOffsetValue": 6,
    "hOffsetValue": 6
  },
  {
    "date": "2025-08-22",
    "shortDate": "4日",
    "xOffsetValue": 7.5,
    "yOffsetValue": 4,
    "hOffsetValue": 7
  },
  {
    "date": "2025-08-23",
    "shortDate": "5日",
    "xOffsetValue": 7.5,
    "yOffsetValue": 7,
    "hOffsetValue": 7
  },
  {
    "date": "2025-08-24",
    "shortDate": "6日",
    "xOffsetValue": 7.5,
    "yOffsetValue": 6,
    "hOffsetValue": 9
  },
  {
    "date": "2025-08-25",
    "shortDate": "7日",
    "xOffsetValue": 8,
    "yOffsetValue": 5,
    "hOffsetValue": 6
  }
]
```

## 数据结构说明

### VO类设计

#### 1. SurfaceMonitoringOverviewVO
- **用途**: 地表监测概览数据
- **字段**: X/Y/H偏移距离总和、偏移报警数

#### 2. SurfaceMonitoringLevelDistributionVO
- **用途**: 地表监测偏移等级分布数据（柱状图）
- **字段**: 偏移方向、偏移等级、偏移数量、占比百分比

#### 3. SurfaceMonitoringTrendVO
- **用途**: 地表监测七日偏移趋势数据（折线图）
- **字段**: 日期、X/Y/H方向偏移值

## 业务逻辑

### 1. 数据统计维度

**时间维度**:
- 日统计: 按天统计地表监测数据
- 周统计: 按周统计地表监测数据
- 月统计: 按月统计地表监测数据

**空间维度**:
- X方向偏移: 东西方向的地表位移
- Y方向偏移: 南北方向的地表位移
- H方向偏移: 垂直方向的地表位移

### 2. 统计指标

**基础指标**:
- X偏移距离总和: X方向所有偏移距离的累计值
- Y偏移距离总和: Y方向所有偏移距离的累计值
- H偏移距离总和: H方向所有偏移距离的累计值
- 偏移报警数: 超过阈值的偏移报警数量

**等级分布指标**:
- 偏移等级: 根据偏移程度划分的等级（1-5级）
- 等级占比: 各等级偏移在总数中的占比
- 方向分布: 不同方向的偏移等级分布

**趋势分析指标**:
- 七日趋势: 连续七天的偏移值变化
- 方向对比: X/Y/H三个方向的偏移趋势对比
- 变化幅度: 偏移值的波动范围

### 3. 数据展示

**概览卡片**:
- X偏移距离总和: 200
- Y偏移距离总和: 40
- H偏移距离总和: 2
- 偏移报警数: 20

**柱状图展示**:
- 偏移等级分布
- X/Y/H三个方向均为100%的4级偏移

**折线图展示**:
- 七日偏移趋势
- 三条线分别代表X/Y/H方向的偏移变化

## 模拟数据说明

### 1. 概览数据
- X偏移距离总和: 200
- Y偏移距离总和: 40
- H偏移距离总和: 2
- 偏移报警数: 20

### 2. 偏移等级分布
- **X方向**: 4级偏移，200个点，占比100%
- **Y方向**: 4级偏移，40个点，占比100%
- **H方向**: 4级偏移，2个点，占比100%

### 3. 七日偏移趋势数据
**X方向数据** (蓝色线):
- 1-6日: 稳定在7.5
- 7日: 略微上升到8

**Y方向数据** (浅蓝色线):
- 1日: 8 → 2日: 9 → 3日: 6 → 4日: 4 → 5日: 7 → 6日: 6 → 7日: 5
- 呈现波动变化，整体在4-9之间

**H方向数据** (绿色线):
- 1日: 6.4 → 2日: 5 → 3日: 6 → 4日: 7 → 5日: 7 → 6日: 9 → 7日: 6
- 呈现波动变化，整体在5-9之间

## 扩展接入点

### 1. 地表监测系统接入
```java
// 可接入的数据源
- GPS地表监测系统
- 激光测距监测系统
- 倾斜仪监测系统
- 沉降监测系统
```

### 2. 数据接入方式
- **实时数据**: 通过Kafka队列接收监测设备数据
- **定时同步**: 定时从监测系统同步数据
- **API接口**: 通过REST API获取监测数据

### 3. 数据库设计建议
```sql
-- 地表监测数据表
CREATE TABLE surface_monitoring_data (
    id BIGSERIAL PRIMARY KEY,
    monitoring_point_code VARCHAR(50),
    monitoring_time TIMESTAMP,
    x_offset_value DECIMAL(10,3),
    y_offset_value DECIMAL(10,3),
    h_offset_value DECIMAL(10,3),
    offset_level INTEGER,
    alarm_status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 监测点基础信息表
CREATE TABLE monitoring_point_info (
    id BIGSERIAL PRIMARY KEY,
    point_code VARCHAR(50) UNIQUE,
    point_name VARCHAR(100),
    longitude DECIMAL(10,6),
    latitude DECIMAL(10,6),
    elevation DECIMAL(8,3),
    point_type VARCHAR(50),
    status VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 偏移阈值配置表
CREATE TABLE offset_threshold_config (
    id BIGSERIAL PRIMARY KEY,
    offset_direction VARCHAR(10),
    level_1_threshold DECIMAL(10,3),
    level_2_threshold DECIMAL(10,3),
    level_3_threshold DECIMAL(10,3),
    level_4_threshold DECIMAL(10,3),
    level_5_threshold DECIMAL(10,3),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 测试说明

### 1. 单元测试
- 所有Service方法的功能测试
- 不同视图类型的测试
- 数据完整性和一致性测试
- 七日趋势数据天数验证

### 2. 接口测试
```bash
# 测试概览统计
curl -X GET "http://localhost:8080/lxbi/stat/surface-monitoring/overview?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试偏移等级分布
curl -X GET "http://localhost:8080/lxbi/stat/surface-monitoring/level-distribution?viewType=day&startDate=2025-08-20&endDate=2025-08-25"

# 测试七日偏移趋势
curl -X GET "http://localhost:8080/lxbi/stat/surface-monitoring/seven-day-trend?viewType=day&startDate=2025-08-20&endDate=2025-08-25"
```

### 3. 数据验证
- 七日趋势数据天数验证 (必须为7天)
- 偏移方向数量验证 (必须为X/Y/H三个方向)
- 偏移等级100%验证 (所有方向都是100%的4级)
- 数值范围合理性验证 (0-25范围内)

## 后续开发计划

### 1. 数据接入
- 接入真实的地表监测系统
- 建立GPS监测数据流
- 实现偏移异常自动检测

### 2. 功能增强
- 偏移阈值动态配置
- 预警机制建立
- 历史数据对比分析

### 3. 可视化优化
- 地表监测点地图展示
- 实时数据刷新
- 3D地表变形展示

## 业务价值

### 1. 安全管理提升
- **实时监控**: 及时发现地表变形问题
- **预警机制**: 提前预警地表沉降风险
- **精确定位**: 准确定位变形区域

### 2. 运营效率优化
- **趋势分析**: 了解地表变形发展趋势
- **资源配置**: 基于数据优化监测点布局
- **维护计划**: 制定针对性的加固措施

### 3. 科学决策支持
- **数据驱动**: 基于监测数据进行决策
- **风险评估**: 评估地表变形风险等级
- **应急响应**: 支持应急预案制定

## 总结

地表监测统计模块提供了完整的地表变形监控和分析功能，通过多维度的统计分析，帮助管理人员及时了解地表变形状况，预防地质灾害，保障矿山安全生产。

当前实现使用模拟数据，完美对应了界面图片中的所有功能模块，为后续接入真实监测数据预留了扩展接口，可以根据实际业务需求进行数据源的接入和功能的扩展。
