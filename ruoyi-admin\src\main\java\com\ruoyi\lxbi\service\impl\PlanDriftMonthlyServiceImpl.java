package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.lxbi.domain.response.PlanDriftMonthlyVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.PlanDriftMonthlyMapper;
import com.ruoyi.lxbi.domain.PlanDriftMonthly;
import com.ruoyi.lxbi.domain.request.PlanDriftMonthlyBatchDto;
import com.ruoyi.lxbi.service.IPlanDriftMonthlyService;

/**
 * 掘进月计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class PlanDriftMonthlyServiceImpl implements IPlanDriftMonthlyService 
{
    @Autowired
    private PlanDriftMonthlyMapper planDriftMonthlyMapper;

    /**
     * 查询掘进月计划
     * 
     * @param id 掘进月计划主键
     * @return 掘进月计划
     */
    @Override
    public PlanDriftMonthly selectPlanDriftMonthlyById(Long id)
    {
        return planDriftMonthlyMapper.selectPlanDriftMonthlyById(id);
    }

    /**
     * 查询掘进月计划列表
     * 
     * @param planDriftMonthly 掘进月计划
     * @return 掘进月计划
     */
    @Override
    public List<PlanDriftMonthlyVo> selectPlanDriftMonthlyList(PlanDriftMonthly planDriftMonthly)
    {
        return planDriftMonthlyMapper.selectPlanDriftMonthlyList(planDriftMonthly);
    }

    /**
     * 新增掘进月计划
     * 
     * @param planDriftMonthly 掘进月计划
     * @return 结果
     */
    @Override
    public int insertPlanDriftMonthly(PlanDriftMonthly planDriftMonthly)
    {
        planDriftMonthly.setCreateTime(DateUtils.getNowDate());
        return planDriftMonthlyMapper.insertPlanDriftMonthly(planDriftMonthly);
    }

    /**
     * 修改掘进月计划
     * 
     * @param planDriftMonthly 掘进月计划
     * @return 结果
     */
    @Override
    public int updatePlanDriftMonthly(PlanDriftMonthly planDriftMonthly)
    {
        planDriftMonthly.setUpdateTime(DateUtils.getNowDate());
        return planDriftMonthlyMapper.updatePlanDriftMonthly(planDriftMonthly);
    }

    /**
     * 批量删除掘进月计划
     * 
     * @param ids 需要删除的掘进月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanDriftMonthlyByIds(Long[] ids)
    {
        return planDriftMonthlyMapper.deletePlanDriftMonthlyByIds(ids);
    }

    /**
     * 删除掘进月计划信息
     *
     * @param id 掘进月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanDriftMonthlyById(Long id)
    {
        return planDriftMonthlyMapper.deletePlanDriftMonthlyById(id);
    }

    /**
     * 批量保存掘进月计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePlanDriftMonthly(List<PlanDriftMonthlyBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个月份的数据
        String planMonth = batchDataList.get(0).getPlanMonth();
        if (StringUtils.isBlank(planMonth)) {
            throw new ServiceException("计划月份不能为空");
        }
        // 验证是否同一个日期和项目部的数据
        Long projectDepartmentId = batchDataList.get(0).getDepartmentId();
        if (projectDepartmentId == null) {
            throw new ServiceException("项目部门不能为空");
        }
        boolean allSameDateAndProject = batchDataList.stream()
                .allMatch(data -> planMonth.equals(data.getPlanMonth())
                        && projectDepartmentId.equals(data.getDepartmentId()));
        if (!allSameDateAndProject) {
            throw new ServiceException("批量数据必须是同一个计划月份和项目部门");
        }

        // 查询该月份的现有数据
        PlanDriftMonthly queryParam = new PlanDriftMonthly();
        queryParam.setPlanDate(planMonth);
        queryParam.setProjectDepartmentId(projectDepartmentId);
        List<PlanDriftMonthlyVo> existingDataList = planDriftMonthlyMapper.selectPlanDriftMonthlyList(queryParam);
        Map<Long, PlanDriftMonthly> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(PlanDriftMonthly::getId, data -> data));

        List<PlanDriftMonthly> toInsert = new ArrayList<>();
        List<PlanDriftMonthly> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (PlanDriftMonthlyBatchDto batchData : batchDataList) {
            if ("add".equals(batchData.getOperationType())) {
                // 新增数据
                PlanDriftMonthly newData = new PlanDriftMonthly();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if ("edit".equals(batchData.getOperationType()) && batchData.getId() != null) {
                // 更新现有数据
                if (existingDataMap.containsKey(batchData.getId())) {
                    PlanDriftMonthly updateData = new PlanDriftMonthly();
                    copyProperties(batchData, updateData);
                    updateData.setId(batchData.getId());
                    updateData.setUpdateBy(SecurityUtils.getUsername());
                    updateData.setUpdateTime(DateUtils.getNowDate());
                    toUpdate.add(updateData);
                    toDelete.remove(batchData.getId());
                }
            }
        }

        int totalProcessed = 0;

        // 执行批量操作
        if (!toInsert.isEmpty()) {
            for (PlanDriftMonthly data : toInsert) {
                totalProcessed += planDriftMonthlyMapper.insertPlanDriftMonthly(data);
            }
        }

        if (!toUpdate.isEmpty()) {
            for (PlanDriftMonthly data : toUpdate) {
                totalProcessed += planDriftMonthlyMapper.updatePlanDriftMonthly(data);
            }
        }

        if (!toDelete.isEmpty()) {
            totalProcessed += planDriftMonthlyMapper.deletePlanDriftMonthlyByIds(toDelete.toArray(new Long[0]));
        }

        return totalProcessed;
    }

    /**
     * 复制属性
     */
    private void copyProperties(PlanDriftMonthlyBatchDto source, PlanDriftMonthly target) {
        target.setPlanDate(source.getPlanMonth());
        target.setDriftMeter(source.getDriftDistance());
        target.setProjectDepartmentId(source.getDepartmentId());
        target.setDriftVolume(source.getRockVolume());
        if (StringUtils.isNotBlank(source.getRemark())) {
            target.setRemark(source.getRemark());
        }
        target.setStopeId(source.getStopeId());
        target.setWorkingFaceId(source.getWorkingFaceId());
    }
}
