package com.ruoyi.lxbi.admin.service;

import com.ruoyi.lxbi.domain.vo.*;

import java.util.List;

/**
 * 充填漏浆检测统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IFillingLeakageStatService {

    /**
     * 获取充填漏浆检测概览统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 充填漏浆检测概览统计
     */
    FillingLeakageOverviewVO getOverviewStatistics(String viewType, String startDate, String endDate);

    /**
     * 获取充填漏浆检测趋势统计
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 充填漏浆检测趋势统计列表
     */
    List<FillingLeakageTrendVO> getAlarmTrend(String viewType, String startDate, String endDate);

    /**
     * 获取充填漏浆检测位置分布统计 (雷达图数据)
     * 
     * @param viewType 视图类型 (day/week/month)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 充填漏浆检测位置分布统计列表
     */
    List<FillingLeakageLocationDistributionVO> getLocationDistribution(String viewType, String startDate, String endDate);
}
