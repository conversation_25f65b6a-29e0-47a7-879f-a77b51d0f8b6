package com.ruoyi.lxbi.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备台效统计对象
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
@Data
public class DataEquipmentEfficiencyStats {

    /**
     * 年份
     */
    @Excel(name = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Excel(name = "月份")
    private Integer month;

    /**
     * 周数
     */
    @Excel(name = "周数")
    private Integer weekNumber;

    /**
     * 设备类型ID
     */
    private Long equipmentType;

    /**
     * 设备类型名称
     */
    @Excel(name = "设备编号")
    private String equipmentNo;

    /**
     * 统计开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 统计结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 总处理量
     */
    @Excel(name = "总处理量")
    private BigDecimal totalProcessingVolume;

    /**
     * 总运行时长（分钟）
     */
    @Excel(name = "总运行时长(分钟)")
    private Integer totalOperationTime;

    /**
     * 台效（总处理量/运行时长）
     */
    @Excel(name = "台效")
    private BigDecimal efficiency;

    /**
     * 统计时间类型（week/month/year）
     */
    private String timeType;

}
