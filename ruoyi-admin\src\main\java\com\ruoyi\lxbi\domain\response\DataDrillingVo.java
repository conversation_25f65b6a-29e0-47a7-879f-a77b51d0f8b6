package com.ruoyi.lxbi.domain.response;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.lxbi.domain.DataDrilling;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DataDrillingVo extends DataDrilling {

    @Excel(name = "项目部门名称", sort = 2, mergeByValue = true)
    private String projectDepartmentName;

    @Excel(name = "采场名称", sort = 3, mergeByValue = true)
    private String stopeName;

    @Excel(name = "工作面名称", sort = 4, mergeByValue = true)
    private String workingFaceName;

    @Excel(name = "作业时段名称", sort = 5, mergeByValue = true)
    private String workingPeriodName;

}
