<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.lxbi.admin.mapper.SurfaceMonitoringDataMapper">
    
    <resultMap type="SurfaceMonitoringData" id="SurfaceMonitoringDataResult">
        <result property="id"    column="id"    />
        <result property="date"    column="date"    />
        <result property="stationName"    column="station_name"    />
        <result property="wgbh"    column="wgbh"    />
        <result property="x1"    column="x1"    />
        <result property="x2"    column="x2"    />
        <result property="x3"    column="x3"    />
        <result property="x4"    column="x4"    />
        <result property="y1"    column="y1"    />
        <result property="y2"    column="y2"    />
        <result property="y3"    column="y3"    />
        <result property="y4"    column="y4"    />
        <result property="h1"    column="h1"    />
        <result property="h2"    column="h2"    />
        <result property="h3"    column="h3"    />
        <result property="h4"    column="h4"    />
        <result property="ystackedTotalOffset"    column="ystacked_total_offset"    />
        <result property="hstackedTotalOffset"    column="hstacked_total_offset"    />
        <result property="xstackedTotalOffset"    column="xstacked_total_offset"    />
        <result property="originalId"    column="original_id"    />
        <result property="originalData"    column="original_data"    />
        <result property="dataSource"    column="data_source"    />
        <result property="syncTime"    column="sync_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSurfaceMonitoringDataVo">
        select id, date, station_name, wgbh, x1, x2, x3, x4, y1, y2, y3, y4, h1, h2, h3, h4, 
               ystacked_total_offset, hstacked_total_offset, xstacked_total_offset, original_id, 
               original_data, data_source, sync_time, create_by, create_time, update_by, update_time, remark 
        from surface_monitoring_data
    </sql>

    <select id="selectSurfaceMonitoringDataList" parameterType="SurfaceMonitoringData" resultMap="SurfaceMonitoringDataResult">
        <include refid="selectSurfaceMonitoringDataVo"/>
        <where>
            is_deleted = 0
            <if test="date != null">and date = #{date}</if>
            <if test="stationName != null and stationName != ''">and station_name like concat('%', #{stationName}, '%')</if>
            <if test="wgbh != null and wgbh != ''">and wgbh like concat('%', #{wgbh}, '%')</if>
            <if test="originalId != null">and original_id = #{originalId}</if>
            <if test="dataSource != null and dataSource != ''">and data_source = #{dataSource}</if>
        </where>
        order by date desc, station_name, wgbh
    </select>
    
    <select id="selectSurfaceMonitoringDataById" parameterType="Long" resultMap="SurfaceMonitoringDataResult">
        <include refid="selectSurfaceMonitoringDataVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <select id="selectByDateStationWgbh" resultMap="SurfaceMonitoringDataResult">
        <include refid="selectSurfaceMonitoringDataVo"/>
        where date = #{date} 
          and station_name = #{stationName} 
          and wgbh = #{wgbh}
          and is_deleted = 0
    </select>

    <select id="selectByOriginalId" parameterType="Integer" resultMap="SurfaceMonitoringDataResult">
        <include refid="selectSurfaceMonitoringDataVo"/>
        where original_id = #{originalId} and is_deleted = 0
    </select>

    <select id="selectByDateRange" resultMap="SurfaceMonitoringDataResult">
        <include refid="selectSurfaceMonitoringDataVo"/>
        where date BETWEEN #{startDate} AND #{endDate}
          and is_deleted = 0
        order by date desc, station_name, wgbh
    </select>

    <select id="selectByStationName" parameterType="String" resultMap="SurfaceMonitoringDataResult">
        <include refid="selectSurfaceMonitoringDataVo"/>
        where station_name = #{stationName} and is_deleted = 0
        order by date desc, wgbh
    </select>

    <select id="selectLatestData" resultMap="SurfaceMonitoringDataResult">
        <include refid="selectSurfaceMonitoringDataVo"/>
        where is_deleted = 0
        order by date desc, sync_time desc
        limit #{limit}
    </select>

    <select id="selectAbnormalOffsetData" resultMap="SurfaceMonitoringDataResult">
        <include refid="selectSurfaceMonitoringDataVo"/>
        where is_deleted = 0
          and (ABS(xstacked_total_offset) > #{threshold} 
               OR ABS(ystacked_total_offset) > #{threshold} 
               OR ABS(hstacked_total_offset) > #{threshold})
        order by date desc
    </select>
        
    <insert id="insertSurfaceMonitoringData" parameterType="SurfaceMonitoringData" useGeneratedKeys="true" keyProperty="id">
        insert into surface_monitoring_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="date != null">date,</if>
            <if test="stationName != null and stationName != ''">station_name,</if>
            <if test="wgbh != null and wgbh != ''">wgbh,</if>
            <if test="x1 != null">x1,</if>
            <if test="x2 != null">x2,</if>
            <if test="x3 != null">x3,</if>
            <if test="x4 != null">x4,</if>
            <if test="y1 != null">y1,</if>
            <if test="y2 != null">y2,</if>
            <if test="y3 != null">y3,</if>
            <if test="y4 != null">y4,</if>
            <if test="h1 != null">h1,</if>
            <if test="h2 != null">h2,</if>
            <if test="h3 != null">h3,</if>
            <if test="h4 != null">h4,</if>
            <if test="ystackedTotalOffset != null">ystacked_total_offset,</if>
            <if test="hstackedTotalOffset != null">hstacked_total_offset,</if>
            <if test="xstackedTotalOffset != null">xstacked_total_offset,</if>
            <if test="originalId != null">original_id,</if>
            <if test="originalData != null">original_data,</if>
            <if test="dataSource != null">data_source,</if>
            <if test="syncTime != null">sync_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="date != null">#{date},</if>
            <if test="stationName != null and stationName != ''">#{stationName},</if>
            <if test="wgbh != null and wgbh != ''">#{wgbh},</if>
            <if test="x1 != null">#{x1},</if>
            <if test="x2 != null">#{x2},</if>
            <if test="x3 != null">#{x3},</if>
            <if test="x4 != null">#{x4},</if>
            <if test="y1 != null">#{y1},</if>
            <if test="y2 != null">#{y2},</if>
            <if test="y3 != null">#{y3},</if>
            <if test="y4 != null">#{y4},</if>
            <if test="h1 != null">#{h1},</if>
            <if test="h2 != null">#{h2},</if>
            <if test="h3 != null">#{h3},</if>
            <if test="h4 != null">#{h4},</if>
            <if test="ystackedTotalOffset != null">#{ystackedTotalOffset},</if>
            <if test="hstackedTotalOffset != null">#{hstackedTotalOffset},</if>
            <if test="xstackedTotalOffset != null">#{xstackedTotalOffset},</if>
            <if test="originalId != null">#{originalId},</if>
            <if test="originalData != null">#{originalData},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="syncTime != null">#{syncTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSurfaceMonitoringData" parameterType="SurfaceMonitoringData">
        update surface_monitoring_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="date != null">date = #{date},</if>
            <if test="stationName != null and stationName != ''">station_name = #{stationName},</if>
            <if test="wgbh != null and wgbh != ''">wgbh = #{wgbh},</if>
            <if test="x1 != null">x1 = #{x1},</if>
            <if test="x2 != null">x2 = #{x2},</if>
            <if test="x3 != null">x3 = #{x3},</if>
            <if test="x4 != null">x4 = #{x4},</if>
            <if test="y1 != null">y1 = #{y1},</if>
            <if test="y2 != null">y2 = #{y2},</if>
            <if test="y3 != null">y3 = #{y3},</if>
            <if test="y4 != null">y4 = #{y4},</if>
            <if test="h1 != null">h1 = #{h1},</if>
            <if test="h2 != null">h2 = #{h2},</if>
            <if test="h3 != null">h3 = #{h3},</if>
            <if test="h4 != null">h4 = #{h4},</if>
            <if test="ystackedTotalOffset != null">ystacked_total_offset = #{ystackedTotalOffset},</if>
            <if test="hstackedTotalOffset != null">hstackedTotalOffset = #{hstackedTotalOffset},</if>
            <if test="xstackedTotalOffset != null">xstacked_total_offset = #{xstackedTotalOffset},</if>
            <if test="originalId != null">original_id = #{originalId},</if>
            <if test="originalData != null">original_data = #{originalData},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSurfaceMonitoringDataById" parameterType="Long">
        update surface_monitoring_data set is_deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteSurfaceMonitoringDataByIds" parameterType="String">
        update surface_monitoring_data set is_deleted = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- PostgreSQL UPSERT操作 -->
    <insert id="upsertSurfaceMonitoringData" parameterType="SurfaceMonitoringData">
        INSERT INTO surface_monitoring_data (
            date, station_name, wgbh, x1, x2, x3, x4, y1, y2, y3, y4, h1, h2, h3, h4,
            ystacked_total_offset, hstacked_total_offset, xstacked_total_offset,
            original_id, original_data, data_source, sync_time,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{date}, #{stationName}, #{wgbh}, #{x1}, #{x2}, #{x3}, #{x4},
            #{y1}, #{y2}, #{y3}, #{y4}, #{h1}, #{h2}, #{h3}, #{h4},
            #{ystackedTotalOffset}, #{hstackedTotalOffset}, #{xstackedTotalOffset},
            #{originalId}, #{originalData}, #{dataSource}, #{syncTime},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
        ON CONFLICT (date, station_name, wgbh)
        DO UPDATE SET
            x1 = EXCLUDED.x1,
            x2 = EXCLUDED.x2,
            x3 = EXCLUDED.x3,
            x4 = EXCLUDED.x4,
            y1 = EXCLUDED.y1,
            y2 = EXCLUDED.y2,
            y3 = EXCLUDED.y3,
            y4 = EXCLUDED.y4,
            h1 = EXCLUDED.h1,
            h2 = EXCLUDED.h2,
            h3 = EXCLUDED.h3,
            h4 = EXCLUDED.h4,
            ystacked_total_offset = EXCLUDED.ystacked_total_offset,
            hstacked_total_offset = EXCLUDED.hstacked_total_offset,
            xstacked_total_offset = EXCLUDED.xstacked_total_offset,
            original_id = EXCLUDED.original_id,
            original_data = EXCLUDED.original_data,
            data_source = EXCLUDED.data_source,
            sync_time = EXCLUDED.sync_time,
            update_by = EXCLUDED.update_by,
            update_time = EXCLUDED.update_time,
            remark = EXCLUDED.remark
            -- 注意：不更新 create_by 和 create_time，保持原有的创建信息
    </insert>

    <!-- 按日期统计 -->
    <select id="countByDate" resultType="java.util.Map">
        SELECT
            date,
            COUNT(*) as count,
            COUNT(DISTINCT station_name) as station_count,
            AVG(ABS(xstacked_total_offset)) as avg_x_offset,
            AVG(ABS(ystacked_total_offset)) as avg_y_offset,
            AVG(ABS(hstacked_total_offset)) as avg_h_offset,
            MAX(ABS(xstacked_total_offset)) as max_x_offset,
            MAX(ABS(ystacked_total_offset)) as max_y_offset,
            MAX(ABS(hstacked_total_offset)) as max_h_offset
        FROM surface_monitoring_data
        WHERE is_deleted = 0
        GROUP BY date
        ORDER BY date DESC
    </select>

    <!-- 按基站统计 -->
    <select id="countByStation" resultType="java.util.Map">
        SELECT
            station_name,
            COUNT(*) as count,
            COUNT(DISTINCT date) as date_count,
            AVG(ABS(xstacked_total_offset)) as avg_x_offset,
            AVG(ABS(ystacked_total_offset)) as avg_y_offset,
            AVG(ABS(hstacked_total_offset)) as avg_h_offset,
            MAX(ABS(xstacked_total_offset)) as max_x_offset,
            MAX(ABS(ystacked_total_offset)) as max_y_offset,
            MAX(ABS(hstacked_total_offset)) as max_h_offset
        FROM surface_monitoring_data
        WHERE is_deleted = 0
        GROUP BY station_name
        ORDER BY count DESC
    </select>

    <!-- 按日期获取统计信息 -->
    <select id="getStatisticsByDate" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_count,
            COUNT(DISTINCT station_name) as station_count,
            COUNT(DISTINCT wgbh) as terminal_count,
            AVG(x1 + x2 + x3 + x4) as avg_x_total,
            AVG(y1 + y2 + y3 + y4) as avg_y_total,
            AVG(h1 + h2 + h3 + h4) as avg_h_total,
            AVG(ABS(xstacked_total_offset)) as avg_x_offset,
            AVG(ABS(ystacked_total_offset)) as avg_y_offset,
            AVG(ABS(hstacked_total_offset)) as avg_h_offset,
            MAX(ABS(xstacked_total_offset)) as max_x_offset,
            MAX(ABS(ystacked_total_offset)) as max_y_offset,
            MAX(ABS(hstacked_total_offset)) as max_h_offset,
            SUM(CASE WHEN ABS(xstacked_total_offset) > 10 OR ABS(ystacked_total_offset) > 10 OR ABS(hstacked_total_offset) > 10 THEN 1 ELSE 0 END) as abnormal_count
        FROM surface_monitoring_data
        WHERE date = #{date} AND is_deleted = 0
    </select>

</mapper>
