package com.ruoyi.lxbi.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.lxbi.mapper.PlanMiningMonthlyMapper;
import com.ruoyi.lxbi.domain.PlanMiningMonthly;
import com.ruoyi.lxbi.domain.request.PlanMiningMonthlyBatchDto;
import com.ruoyi.lxbi.domain.response.PlanMiningMonthlyVo;
import com.ruoyi.lxbi.service.IPlanMiningMonthlyService;

/**
 * 采矿整体月计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@Service
public class PlanMiningMonthlyServiceImpl implements IPlanMiningMonthlyService 
{
    @Autowired
    private PlanMiningMonthlyMapper planMiningMonthlyMapper;

    /**
     * 查询采矿整体月计划
     * 
     * @param id 采矿整体月计划主键
     * @return 采矿整体月计划
     */
    @Override
    public PlanMiningMonthly selectPlanMiningMonthlyById(Long id)
    {
        return planMiningMonthlyMapper.selectPlanMiningMonthlyById(id);
    }

    /**
     * 查询采矿整体月计划列表
     *
     * @param planMiningMonthly 采矿整体月计划
     * @return 采矿整体月计划
     */
    @Override
    public List<PlanMiningMonthlyVo> selectPlanMiningMonthlyList(PlanMiningMonthly planMiningMonthly)
    {
        return planMiningMonthlyMapper.selectPlanMiningMonthlyList(planMiningMonthly);
    }

    /**
     * 新增采矿整体月计划
     * 
     * @param planMiningMonthly 采矿整体月计划
     * @return 结果
     */
    @Override
    public int insertPlanMiningMonthly(PlanMiningMonthly planMiningMonthly)
    {
        planMiningMonthly.setCreateTime(DateUtils.getNowDate());
        return planMiningMonthlyMapper.insertPlanMiningMonthly(planMiningMonthly);
    }

    /**
     * 修改采矿整体月计划
     * 
     * @param planMiningMonthly 采矿整体月计划
     * @return 结果
     */
    @Override
    public int updatePlanMiningMonthly(PlanMiningMonthly planMiningMonthly)
    {
        planMiningMonthly.setUpdateTime(DateUtils.getNowDate());
        return planMiningMonthlyMapper.updatePlanMiningMonthly(planMiningMonthly);
    }

    /**
     * 批量删除采矿整体月计划
     * 
     * @param ids 需要删除的采矿整体月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanMiningMonthlyByIds(Long[] ids)
    {
        return planMiningMonthlyMapper.deletePlanMiningMonthlyByIds(ids);
    }

    /**
     * 删除采矿整体月计划信息
     *
     * @param id 采矿整体月计划主键
     * @return 结果
     */
    @Override
    public int deletePlanMiningMonthlyById(Long id)
    {
        return planMiningMonthlyMapper.deletePlanMiningMonthlyById(id);
    }

    /**
     * 批量保存采矿整体月计划（增删改查）
     *
     * @param batchDataList 批量数据列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePlanMiningMonthly(List<PlanMiningMonthlyBatchDto> batchDataList) {
        if (batchDataList == null || batchDataList.isEmpty()) {
            throw new ServiceException("批量数据不能为空");
        }

        // 验证是否同一个月份的数据
        String planMonth = batchDataList.get(0).getPlanMonth();
        if (StringUtils.isBlank(planMonth)) {
            throw new ServiceException("计划月份不能为空");
        }
        boolean allSameDateAndProject = batchDataList.stream()
                .allMatch(data -> planMonth.equals(data.getPlanMonth()));
        if (!allSameDateAndProject) {
            throw new ServiceException("批量数据必须是同一个计划月份");
        }

        // 查询该月份的现有数据
        PlanMiningMonthly queryParam = new PlanMiningMonthly();
        queryParam.setPlanDate(planMonth);
        List<PlanMiningMonthlyVo> existingDataList = planMiningMonthlyMapper.selectPlanMiningMonthlyList(queryParam);
        Map<Long, PlanMiningMonthly> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(PlanMiningMonthly::getId, data -> data));

        List<PlanMiningMonthly> toInsert = new ArrayList<>();
        List<PlanMiningMonthly> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>(existingDataMap.keySet());

        // 处理传入的数据
        for (PlanMiningMonthlyBatchDto batchData : batchDataList) {
            if ("add".equals(batchData.getOperationType())) {
                // 新增数据
                PlanMiningMonthly newData = new PlanMiningMonthly();
                copyProperties(batchData, newData);
                newData.setCreateBy(SecurityUtils.getUsername());
                newData.setCreateTime(DateUtils.getNowDate());
                newData.setUpdateBy(SecurityUtils.getUsername());
                newData.setUpdateTime(DateUtils.getNowDate());
                toInsert.add(newData);
            } else if ("edit".equals(batchData.getOperationType()) && batchData.getId() != null) {
                // 更新现有数据
                if (existingDataMap.containsKey(batchData.getId())) {
                    PlanMiningMonthly updateData = new PlanMiningMonthly();
                    copyProperties(batchData, updateData);
                    updateData.setId(batchData.getId());
                    updateData.setUpdateBy(SecurityUtils.getUsername());
                    updateData.setUpdateTime(DateUtils.getNowDate());
                    toUpdate.add(updateData);
                    toDelete.remove(batchData.getId());
                }
            }
        }

        int totalProcessed = 0;

        // 执行批量操作
        if (!toInsert.isEmpty()) {
            for (PlanMiningMonthly data : toInsert) {
                totalProcessed += planMiningMonthlyMapper.insertPlanMiningMonthly(data);
            }
        }

        if (!toUpdate.isEmpty()) {
            for (PlanMiningMonthly data : toUpdate) {
                totalProcessed += planMiningMonthlyMapper.updatePlanMiningMonthly(data);
            }
        }

        if (!toDelete.isEmpty()) {
            totalProcessed += planMiningMonthlyMapper.deletePlanMiningMonthlyByIds(toDelete.toArray(new Long[0]));
        }

        return totalProcessed;
    }

    /**
     * 复制属性
     */
    private void copyProperties(PlanMiningMonthlyBatchDto source, PlanMiningMonthly target) {
        target.setPlanDate(source.getPlanMonth());
        target.setProjectDepartmentId(source.getProjectDepartmentId());
        target.setDriftMeter(source.getDriftMeter());
        target.setRawOreVolume(source.getRawOreVolume());
        target.setSupportMeter(source.getSupportMeter());
        target.setFillingVolume(source.getFillingVolume());
        target.setDthMeter(source.getDthMeter());
        target.setDeepHoleMeter(source.getDeepHoleMeter());
        target.setOreOutputVolume(source.getOreOutputVolume());
        if (StringUtils.isNotBlank(source.getRemark())) {
            target.setRemark(source.getRemark());
        }
    }
}
