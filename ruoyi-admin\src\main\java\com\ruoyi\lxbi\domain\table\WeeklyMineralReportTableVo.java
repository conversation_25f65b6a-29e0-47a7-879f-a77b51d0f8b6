package com.ruoyi.lxbi.domain.table;

import com.ruoyi.common.annotation.TableConfig;
import com.ruoyi.common.annotation.TableHeader;
import com.ruoyi.common.core.table.TableColumnDataBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 选矿数据周报表格VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableConfig(code = "mineral_weekly_report", name = "选矿周报", description = "选矿数据周报统计表格")
public class WeeklyMineralReportTableVo extends TableColumnDataBase {

    @TableHeader(label = "序号", order = 1, width = 5)
    private String serialNumber;

    @TableHeader(label = "指标项", order = 2, parentPath = {"名称"}, colMergeGroup = {"name"})
    private String name;

    @TableHeader(label = "指标明细", order = 3, parentPath = {"名称"}, colMergeGroup = {"name"})
    private String subName;

    @TableHeader(label = "单位", order = 4, width = 5)
    private String unit;

    @TableHeader(label = "月计划", order = 5)
    private BigDecimal monthlyPlan;

    @TableHeader(label = "月累计", order = 6)
    private BigDecimal monthlyAccumulated;

    @TableHeader(label = "月完成率（%）", order = 7)
    private String monthlyCompletionRate;

    @TableHeader(label = "周计划", order = 8)
    private BigDecimal weeklyPlan;

    @TableHeader(label = "周完成", order = 9)
    private BigDecimal weeklyCompleted;

    @TableHeader(label = "周完成率（%）", order = 10)
    private String weeklyCompletionRate;
}
