package com.ruoyi.lxbi.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.lxbi.mapper.KafkaSafeMonitorErrorInfoMapper;
import com.ruoyi.lxbi.domain.KafkaSafeMonitorErrorInfo;
import com.ruoyi.lxbi.service.IKafkaSafeMonitorErrorInfoService;

/**
 * 安全监测实时数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class KafkaSafeMonitorErrorInfoServiceImpl implements IKafkaSafeMonitorErrorInfoService
{
    @Autowired
    private KafkaSafeMonitorErrorInfoMapper kafkaSafeMonitorErrorInfoMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询安全监测实时数据
     * 
     * @param id 安全监测实时数据主键
     * @return 安全监测实时数据
     */
    @Override
    public KafkaSafeMonitorErrorInfo selectKafkaSafeMonitorErrorInfoById(Long id)
    {
        return kafkaSafeMonitorErrorInfoMapper.selectKafkaSafeMonitorErrorInfoById(id);
    }

    /**
     * 查询安全监测实时数据列表
     * 
     * @param kafkaSafeMonitorErrorInfo 安全监测实时数据
     * @return 安全监测实时数据
     */
    @Override
    public List<KafkaSafeMonitorErrorInfo> selectKafkaSafeMonitorErrorInfoList(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo)
    {
        return kafkaSafeMonitorErrorInfoMapper.selectKafkaSafeMonitorErrorInfoList(kafkaSafeMonitorErrorInfo);
    }

    /**
     * 新增安全监测实时数据
     * 
     * @param kafkaSafeMonitorErrorInfo 安全监测实时数据
     * @return 结果
     */
    @Override
    public int insertKafkaSafeMonitorErrorInfo(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo)
    {
        kafkaSafeMonitorErrorInfo.setCreateTime(DateUtils.getNowDate());
        return kafkaSafeMonitorErrorInfoMapper.insertKafkaSafeMonitorErrorInfo(kafkaSafeMonitorErrorInfo);
    }

    /**
     * 修改安全监测实时数据
     * 
     * @param kafkaSafeMonitorErrorInfo 安全监测实时数据
     * @return 结果
     */
    @Override
    public int updateKafkaSafeMonitorErrorInfo(KafkaSafeMonitorErrorInfo kafkaSafeMonitorErrorInfo)
    {
        kafkaSafeMonitorErrorInfo.setUpdateTime(DateUtils.getNowDate());
        return kafkaSafeMonitorErrorInfoMapper.updateKafkaSafeMonitorErrorInfo(kafkaSafeMonitorErrorInfo);
    }

    /**
     * 批量删除安全监测实时数据
     * 
     * @param ids 需要删除的安全监测实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaSafeMonitorErrorInfoByIds(Long[] ids)
    {
        return kafkaSafeMonitorErrorInfoMapper.deleteKafkaSafeMonitorErrorInfoByIds(ids);
    }

    /**
     * 删除安全监测实时数据信息
     *
     * @param id 安全监测实时数据主键
     * @return 结果
     */
    @Override
    public int deleteKafkaSafeMonitorErrorInfoById(Long id)
    {
        return kafkaSafeMonitorErrorInfoMapper.deleteKafkaSafeMonitorErrorInfoById(id);
    }

    /**
     * 处理Kafka消息数据（插入或更新）
     *
     * @param kafkaMessage Kafka消息内容
     * @return 处理结果
     */
    @Override
    public boolean processKafkaMessage(String kafkaMessage)
    {
        try {
            log.debug("开始处理Kafka安全监测异常信息消息");

            // 解析Kafka消息
            KafkaSafeMonitorErrorInfo errorInfo = parseKafkaMessage(kafkaMessage);
            if (errorInfo == null) {
                log.warn("解析Kafka消息失败，跳过处理");
                return false;
            }

            // 验证必要字段
            if (!StringUtils.hasText(errorInfo.getMonitoringPointCode())) {
                log.warn("测点编码为空，跳过处理");
                return false;
            }

            // 执行插入操作
            int result = insertKafkaSafeMonitorErrorInfo(errorInfo);

            if (result > 0) {
                log.info("成功插入安全监测异常信息数据，测点编码: {}, 煤矿代码: {}",
                    errorInfo.getMonitoringPointCode(), errorInfo.getMineCode());
                return true;
            } else {
                log.warn("插入安全监测异常信息数据失败，测点编码: {}",
                    errorInfo.getMonitoringPointCode());
                return false;
            }

        } catch (Exception e) {
            log.error("处理Kafka安全监测异常信息消息异常", e);
            return false;
        }
    }

    /**
     * 解析Kafka消息为实体对象
     *
     * @param kafkaMessage Kafka消息内容
     * @return 解析后的实体对象
     */
    @Override
    public KafkaSafeMonitorErrorInfo parseKafkaMessage(String kafkaMessage)
    {
        try {
            // 提取JSON部分（去掉时间戳前缀）
            String jsonStr = kafkaMessage;
            int jsonStart = kafkaMessage.indexOf("{");
            if (jsonStart != -1) {
                jsonStr = kafkaMessage.substring(jsonStart);
            }

            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            KafkaSafeMonitorErrorInfo errorInfo = new KafkaSafeMonitorErrorInfo();

            // 基础信息
            errorInfo.setFileEncoding(getStringValue(jsonNode, "文件前缀"));
            errorInfo.setMineCode(getStringValue(jsonNode, "煤矿编码"));
            errorInfo.setMineName(getStringValue(jsonNode, "矿井名称"));
            errorInfo.setDataUploadTime(getDateValue(jsonNode, "数据上传时间"));
            errorInfo.setMonitoringPointCode(getStringValue(jsonNode, "测点编码"));
            errorInfo.setSensorTypeName(getStringValue(jsonNode, "传感器类型名称"));
            errorInfo.setMonitoringPointLocation(getStringValue(jsonNode, "测点安装位置"));
            errorInfo.setMonitoringPointUnit(getStringValue(jsonNode, "测点数值单位"));
            errorInfo.setAbnormalType(getStringValue(jsonNode, "异常类型"));
            errorInfo.setAbnormalStartTime(getDateValue(jsonNode, "异常开始时间"));
            errorInfo.setAbnormalEndTime(getDateValue(jsonNode, "异常结束时间"));
            errorInfo.setAbnormalMaxValue(getBigDecimalValue(jsonNode, "异常期间最大值"));
            errorInfo.setAbnormalMaxTime(getDateValue(jsonNode, "最大值时刻"));
            errorInfo.setAbnormalMinValue(getBigDecimalValue(jsonNode, "异常期间最小值"));
            errorInfo.setAbnormalMinTime(getDateValue(jsonNode, "最小值时刻"));
            errorInfo.setAbnormalAverageValue(getBigDecimalValue(jsonNode, "异常期间平均值"));
            errorInfo.setAbnormalReason(getStringValue(jsonNode, "异常原因"));
            errorInfo.setHandlingMeasures(getStringValue(jsonNode, "处理措施"));
            errorInfo.setDataTime(getDateValue(jsonNode, "数据时间"));

            // 处理录入时间和录入人（映射到BaseEntity的字段）
            Date inputTime = getDateValue(jsonNode, "录入时间");
            String inputPerson = getStringValue(jsonNode, "录入人");

            // 默认值
            errorInfo.setIsDeleted(0L);
            errorInfo.setCreateTime(inputTime != null ? inputTime : DateUtils.getNowDate());
            errorInfo.setUpdateTime(DateUtils.getNowDate());
            errorInfo.setCreateBy(inputPerson);

            return errorInfo;

        } catch (Exception e) {
            log.error("解析Kafka安全监测异常信息消息失败: {}", kafkaMessage, e);
            return null;
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null && !node.isNull() ? node.asText() : null;
    }

    /**
     * 从JsonNode中获取日期值
     */
    private java.util.Date getDateValue(JsonNode jsonNode, String fieldName) {
        try {
            String dateStr = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(dateStr)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            }
        } catch (Exception e) {
            log.warn("解析日期字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }

    /**
     * 从JsonNode中获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(JsonNode jsonNode, String fieldName) {
        try {
            String value = getStringValue(jsonNode, fieldName);
            if (StringUtils.hasText(value)) {
                return new BigDecimal(value);
            }
        } catch (Exception e) {
            log.warn("解析BigDecimal字段失败: {} = {}", fieldName, getStringValue(jsonNode, fieldName));
        }
        return null;
    }
}
