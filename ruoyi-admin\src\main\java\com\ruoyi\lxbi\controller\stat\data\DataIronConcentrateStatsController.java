package com.ruoyi.lxbi.controller.stat.data;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.lxbi.domain.request.DataIronConcentrateStatsRequest;
import com.ruoyi.lxbi.domain.response.DataIronConcentrateTotalWithPlanStats;
import com.ruoyi.lxbi.service.IDataIronConcentrateStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 铁精粉生产数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@RestController
@RequestMapping("/data/stats/ironconcentrate")
public class DataIronConcentrateStatsController {
    @Autowired
    private IDataIronConcentrateStatsService dataIronConcentrateStatsService;

    /**
     * 查询总体铁精粉生产统计数据（含计划量）
     * 对应图表一：总体铁精粉生产统计柱状图（含计划量）
     */
//    @PreAuthorize("@ss.hasPermi('data:stats:ironconcentrate:01a')")
    @GetMapping("/01a")
    public R<List<DataIronConcentrateTotalWithPlanStats>> totalIronConcentrateWithPlan(@RequestParam(value = "viewType", defaultValue = "monthly") String viewType,
                                                                                        @RequestParam(value = "startDate", required = false) String startDate,
                                                                                        @RequestParam(value = "endDate", required = false) String endDate) {
        DataIronConcentrateStatsRequest request = new DataIronConcentrateStatsRequest();
        request.setStartDate(startDate != null ? java.sql.Date.valueOf(startDate) : null);
        request.setEndDate(endDate != null ? java.sql.Date.valueOf(endDate) : null);

        List<DataIronConcentrateTotalWithPlanStats> stats = dataIronConcentrateStatsService.selectTotalWithPlanStatsList(request, viewType);
        return R.ok(stats);
    }

}
