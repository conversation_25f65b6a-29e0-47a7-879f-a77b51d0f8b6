package com.ruoyi.lxbi.mapper;

import com.ruoyi.lxbi.domain.response.DataSupportTotalWithPlanStats;
import com.ruoyi.lxbi.domain.response.DataSupportDepartmentWithPlanStats;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 支护数据统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface DataSupportStatsMapper {

    /**
     * 查询日总体统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日总体统计数据集合（含计划量）
     */
    public List<DataSupportTotalWithPlanStats> selectDailyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询周总体统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 周总体统计数据集合（含计划量）
     */
    public List<DataSupportTotalWithPlanStats> selectWeeklyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询月总体统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 月总体统计数据集合（含计划量）
     */
    public List<DataSupportTotalWithPlanStats> selectMonthlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询年总体统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 年总体统计数据集合（含计划量）
     */
    public List<DataSupportTotalWithPlanStats> selectYearlyTotalWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询日项目部门统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日项目部门统计数据集合（含计划量）
     */
    public List<DataSupportDepartmentWithPlanStats> selectDailyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询周项目部门统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 周项目部门统计数据集合（含计划量）
     */
    public List<DataSupportDepartmentWithPlanStats> selectWeeklyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询月项目部门统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 月项目部门统计数据集合（含计划量）
     */
    public List<DataSupportDepartmentWithPlanStats> selectMonthlyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询年项目部门统计数据列表（含计划量）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 年项目部门统计数据集合（含计划量）
     */
    public List<DataSupportDepartmentWithPlanStats> selectYearlyDepartmentWithPlanStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
